package com.ehome.oc.mapper;

import java.util.List;
import com.ehome.oc.domain.Building;

/**
 * 楼栋信息Mapper接口
 */
public interface BuildingMapper {
    /**
     * 查询楼栋信息
     */
    public Building selectBuildingById(Integer buildingId);

    /**
     * 查询楼栋信息列表
     */
    public List<Building> selectBuildingList(Building building);

    /**
     * 新增楼栋信息
     */
    public int insertBuilding(Building building);

    /**
     * 修改楼栋信息
     */
    public int updateBuilding(Building building);

    /**
     * 删除楼栋信息
     */
    public int deleteBuildingById(Integer buildingId);

    /**
     * 批量删除楼栋信息
     */
    public int deleteBuildingByIds(Integer[] buildingIds);

    /**
     * 更新楼栋单元数
     */
    public int updateBuildingTotalUnits(String buildingId);
} 
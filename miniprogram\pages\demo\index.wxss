/**demo.wxss**/
page {
  background: #f7f8fa;
}

.container {
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 自定义导航栏 */
.custom-navbar {
  height: 88rpx;
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.location-wrapper {
  display: flex;
  align-items: center;
}

.navbar-location {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}



/* 功能容器 */
.function-container {
  margin: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

/* 服务标题 */
.service-title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}



/* 功能网格 */
.function-grid {
  display: flex;
  flex-direction: column;
}

.grid-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.grid-row:last-child {
  margin-bottom: 0;
}

.grid-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  margin: 0 8rpx;
  border-radius: 16rpx;
  background: transparent;
  transition: all 0.3s ease;
}

.grid-item:first-child {
  margin-left: 0;
}

.grid-item:last-child {
  margin-right: 0;
}

.grid-item:active {
  transform: scale(0.95);
}

.grid-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.grid-text {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

/* 物业公告区域 */
.news-section {
  margin: 32rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-more {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.activity-content {
  display: flex;
  flex-direction: column;
}

.activity-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:active {
  background-color: #fafafa;
}

.activity-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.activity-time {
  font-size: 24rpx;
  color: #999;
}

/* 底部登录区域 */
.login-section {
  margin: 32rpx 0;
  padding: 32rpx;
  background: #fff;
  border-radius: 0;
  box-shadow: none;
}

.login-btn {
  width: 100% !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  text-align: center !important;
  font-size: 34rpx !important;
  font-weight: 500 !important;
  border-radius: 48rpx !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  color: #fff !important;
  box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.3) !important;
}

.login-tips {
  margin-top: 24rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 隐私保护弹窗样式 */
.privacy-popup {
  padding: 48rpx 32rpx 48rpx 32rpx;
}

.privacy-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: left;
  margin-bottom: 32rpx;
}

.privacy-content {
  line-height: 1.8;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 80rpx;
  text-align: left;
}

.privacy-link {
  color: #1890ff;
  text-decoration: none;
}

.privacy-buttons {
  display: flex;
  gap: 32rpx;
  padding: 0 48rpx;
}

.privacy-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privacy-btn::after {
  border: none;
}

.privacy-btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}

.privacy-btn-confirm {
  background-color: #07c160;
  color: #fff;
}

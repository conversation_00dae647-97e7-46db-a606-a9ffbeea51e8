# 文件访问OSS兼容性实现

## 实施概述

本次实施将小程序和PC端的文件上传下载功能进行了重构，实现了OSS和本地文件的统一访问，确保前端统一通过文件ID进行文件访问。

## 主要变更

### 1. 创建WxFileController统一文件接口

**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxFileController.java`

**新增接口**:
- `POST /api/wx/file/upload` - 小程序文件上传（单个）
- `POST /api/wx/file/uploads` - 小程序文件上传（多个）
- `POST /api/wx/file/access` - 统一文件访问接口（通过文件ID获取URL）
- `GET /api/wx/file/download/{fileId}` - 文件下载接口（通过文件ID）

**核心功能**:
- 支持OSS和本地文件双重存储
- 自动处理OSS预签名URL生成
- 降级机制：OSS失败时自动使用本地文件
- 统一的文件信息数据库存储

### 2. 增强PC端FileController

**文件**: `ehome-admin/src/main/java/com/ehome/admin/controller/file/FileController.java`

**新增接口**:
- `POST /common/access` - PC端统一文件访问接口
- `GET /common/download/{fileId}` - PC端文件下载接口

**功能特点**:
- 与小程序端保持一致的文件访问逻辑
- 支持OSS预签名URL和本地文件访问
- 完整的错误处理和降级机制

### 3. 迁移小程序文件上传功能

**变更内容**:
- 将`BxController`中的upload方法标记为`@Deprecated`，保持向后兼容
- 更新`feedbackManager.js`使用新的统一上传接口
- 更新`login/index.js`和`profile/index.js`的头像上传接口

### 4. 创建小程序文件访问管理器

**文件**: `miniprogram/utils/fileAccessManager.js`

**核心功能**:
- `getFileAccessUrl(fileId)` - 通过文件ID获取访问URL
- `getSmartFileUrl(fileIdOrUrl)` - 智能识别文件ID或直接URL
- `downloadFile(fileIdOrUrl)` - 统一文件下载
- `previewDocument(fileIdOrUrl, fileName)` - 文档预览
- `previewImage(fileIdOrUrls)` - 图片预览

**智能识别逻辑**:
1. 如果是完整HTTP URL，直接返回
2. 如果是相对路径，补全为完整URL
3. 否则当作文件ID处理，调用统一访问接口
4. 失败时降级为相对路径处理

### 5. 更新小程序页面文件访问

**更新页面**:
- `pages/menu/content.js` - PDF文件预览
- `pages/service/index.js` - 服务文档预览
- `pages/bx/history.js` - 报修历史文件下载

**变更内容**:
- 导入`fileAccessManager`
- 使用统一的文件访问方法
- 保留原有方法用于向后兼容

## 数据库支持

项目已支持OSS相关字段：
- `eh_file_info.oss_url` - OSS访问URL
- `eh_file_info.oss_key` - OSS存储键名
- `eh_file_info.storage_type` - 存储类型（local/oss/both）

## 接口兼容性

### 小程序端
- ✅ 新接口：`/api/wx/file/upload`
- ✅ 兼容接口：`/api/wx/bx/upload`（已标记为deprecated）
- ✅ 统一访问：`/api/wx/file/access`
- ✅ 直接下载：`/api/wx/file/download/{fileId}`

### PC端
- ✅ 原有接口：`/common/upload`、`/common/uploads`
- ✅ 新增访问：`/common/access`
- ✅ 新增下载：`/common/download/{fileId}`
- ✅ OSS接口：`/common/oss/generateUrl`

## 文件访问流程

### 1. 文件上传流程
```
前端上传 → WxFileController/FileController → 本地存储 → OSS同步上传 → 数据库记录 → 返回文件ID
```

### 2. 文件访问流程
```
前端请求(fileId) → 统一访问接口 → 查询数据库 → 判断存储类型 → 生成访问URL → 返回给前端
```

### 3. 降级机制
```
OSS访问失败 → 自动降级到本地文件 → 确保文件可访问
```

## 测试验证

### 需要测试的场景

1. **文件上传测试**
   - [ ] 小程序文件上传（报修、投诉、头像）
   - [ ] PC端文件上传
   - [ ] OSS上传成功场景
   - [ ] OSS上传失败降级场景

2. **文件访问测试**
   - [ ] 通过文件ID访问OSS文件
   - [ ] 通过文件ID访问本地文件
   - [ ] 直接URL访问（向后兼容）
   - [ ] 文件不存在的错误处理

3. **文件下载测试**
   - [ ] 小程序PDF预览
   - [ ] 小程序图片预览
   - [ ] PC端文件下载
   - [ ] OSS预签名URL重定向

4. **兼容性测试**
   - [ ] 旧接口仍然可用
   - [ ] 新旧数据混合访问
   - [ ] 错误降级机制

## 部署注意事项

1. **数据库字段确认**
   - 确保`eh_file_info`表包含OSS相关字段
   - 检查现有数据的`storage_type`字段值

2. **OSS配置检查**
   - 验证OSS配置是否正确
   - 测试OSS连接和权限

3. **文件路径兼容**
   - 确保现有文件路径仍然可访问
   - 验证新旧URL格式的兼容性

## 后续优化建议

1. **性能优化**
   - 考虑添加文件访问缓存
   - 优化OSS预签名URL的生成频率

2. **监控告警**
   - 添加文件访问失败的监控
   - OSS服务异常的告警机制

3. **批量迁移**
   - 考虑将现有本地文件批量迁移到OSS
   - 更新历史数据的存储类型标识

## 总结

本次实施成功实现了：
- ✅ 统一的文件访问接口
- ✅ OSS和本地文件的兼容处理
- ✅ 前端统一通过文件ID访问
- ✅ 完整的降级和错误处理机制
- ✅ 向后兼容性保证

### 完成的具体工作

1. **后端接口统一**
   - 创建了WxFileController统一小程序文件处理
   - 增强了PC端FileController的文件访问功能
   - 修改了WxNavController使用统一的文件下载接口

2. **前端访问统一**
   - 创建了小程序端fileAccessManager.js工具类
   - 创建了PC端fileAccessUtils.js工具类
   - 更新了所有相关页面使用统一的文件访问方式

3. **数据库兼容**
   - 利用现有的OSS相关字段
   - 支持storage_type字段区分存储类型
   - 保持向后兼容的数据结构

4. **错误处理机制**
   - OSS访问失败自动降级到本地文件
   - 完整的错误日志和用户提示
   - 多层次的容错处理

所有文件访问现在都通过统一的接口处理，支持OSS和本地文件的无缝切换，为系统的扩展性和稳定性提供了良好的基础。

### 关键改进点

- **统一性**: 前后端都通过文件ID进行文件访问
- **兼容性**: 支持OSS和本地文件的自动切换
- **稳定性**: 完整的降级机制确保文件始终可访问
- **扩展性**: 为未来的文件存储方案提供了良好的架构基础

## Controller重构优化

### 文件上传接口统一

1. **主要上传接口**
   - `FileController` (`/common/upload`, `/common/uploads`) - 主要的文件上传接口，支持OSS
   - `WxFileController` (`/api/wx/file/upload`, `/api/wx/file/uploads`) - 小程序专用上传接口

2. **兼容性接口**
   - `BxController.upload()` - 标记为@Deprecated，保持向后兼容
   - `EhFileInfoController.upload()` - 标记为@Deprecated，保持向后兼容

3. **页面调用更新**
   - `file/select.html` - 改用 `/common/upload`，添加 `source: 'common'`
   - `file/multiSelect.html` - 改用 `/common/upload`，添加 `source: 'common'`
   - `file/uploader.html` - 改用 `/common/upload`，添加 `source: 'common'`
   - 小程序相关页面 - 改用 `/api/wx/file/upload`

### 文件访问接口统一

1. **PC端访问接口**
   - `FileController.getFileAccessUrl()` - 通过文件ID获取访问URL
   - `FileController.downloadFileById()` - 通过文件ID下载文件

2. **小程序端访问接口**
   - `WxFileController.getFileAccessUrl()` - 通过文件ID获取访问URL
   - `WxFileController.downloadFile()` - 通过文件ID下载文件

3. **前端工具类**
   - `fileAccessManager.js` - 小程序端文件访问工具
   - `fileAccessUtils.js` - PC端文件访问工具

### 数据库字段利用

- 充分利用现有的OSS相关字段（oss_url, oss_key, storage_type）
- 保持数据结构的向后兼容性
- 支持混合存储模式（本地+OSS）

### 向后兼容保证

- 所有旧的上传接口都保留并标记为@Deprecated
- 旧的文件URL访问方式仍然可用
- 新旧数据可以混合访问
- 渐进式迁移，不影响现有功能

### 智能URL生成

在WxNavController中实现了智能URL生成机制：
- 根据请求来源自动判断使用哪个下载接口
- 小程序/API请求：使用 `/api/wx/file/download/{fileId}`
- PC端请求：使用 `/common/download/{fileId}`
- 确保同一份数据在不同端都能正确访问

### 关键修改点

1. **WxNavController.generateDownloadUrl()** - 智能判断请求来源
2. **所有文件选择器页面** - 添加source参数
3. **前端工具类** - 分别为PC端和小程序端提供专用工具
4. **数据库字段利用** - 充分利用OSS相关字段实现混合存储

### 文件路径兼容性优化

针对不同环境（开发、测试、生产）的文件路径问题，实现了多层次的文件访问机制：

1. **多种路径解析方式**
   - 包含`/profile`的路径：截取后拼接配置路径
   - 相对路径：直接拼接配置路径
   - 绝对路径：直接使用

2. **文件存在性检查**
   - 检查本地文件是否存在
   - 文件不存在时自动降级到file_url重定向

3. **异常处理机制**
   - 本地文件访问异常时自动降级
   - 完整的错误日志记录
   - 多种降级策略确保文件可访问

4. **适用范围**
   - FileController (PC端)
   - WxFileController (小程序端)
   - 统一的错误处理逻辑

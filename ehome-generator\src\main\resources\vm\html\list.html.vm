<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('${functionName}列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
#foreach($column in $columns)
#if($column.query)
#set($dictType=$column.dictType)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else  
#set($comment=$column.columnComment)
#end  
#if($column.htmlType == "input")
                            <li>
                                <label>${comment}：</label>
                                <input type="text" name="${column.javaField}"/>
                            </li>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
                            <li>
                                <label>${comment}：</label>
                                <select name="${column.javaField}" th:with="type=${@dict.getType('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
#elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
                            <li>
                                <label>${comment}：</label>
                                <select name="${column.javaField}">
                                    <option value="">所有</option>
                                    <option value="-1">代码生成请选择字典属性</option>
                                </select>
                            </li>
#elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
                            <li>
                                <label>${comment}：</label>
                                <input type="text" class="time-input" placeholder="请选择${comment}" name="${column.javaField}"/>
                            </li>
#elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                            <li class="select-time">
                                <label>${comment}：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[begin${AttrName}]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[end${AttrName}]"/>
                            </li>
#end
#end
#end
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="${permissionPrefix}:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="${permissionPrefix}:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="${permissionPrefix}:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="${permissionPrefix}:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('${permissionPrefix}:edit')}]];
        var removeFlag = [[${@permission.hasPermi('${permissionPrefix}:remove')}]];
#foreach($column in $columns)
#if(${column.dictType} != '')
        var ${column.javaField}Datas = [[${@dict.getType('${column.dictType}')}]];
#end
#end
        var prefix = ctx + "${moduleName}/${businessName}";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "${functionName}",
                columns: [{
                    checkbox: true
                },
#foreach($column in $columns)
#set($dictType=$column.dictType)
#set($javaField=$column.javaField)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($column.pk)
                {
                    field: '${javaField}',
                    title: '${comment}',
                    visible: false
                },
#elseif($column.list && "" != $dictType)
                {
                    field: '${javaField}',
                    title: '${comment}',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel#if($column.htmlType == "checkbox")s#end(${javaField}Datas, value);
                    }
                },
#elseif($column.list && "" != $javaField)
                {
                    field: '${javaField}',
                    title: '${comment}'
                },
#end                
#end
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.${pkColumn.javaField} + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.${pkColumn.javaField} + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>

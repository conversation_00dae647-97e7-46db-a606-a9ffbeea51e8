// 功能权限控制管理器
import getStateManager from './stateManager.js'

class PermissionManager {
  constructor() {
    this.permissions = {
      // 基础功能（所有用户都可以使用）
      basic: [
        'view_notice',        // 查看公告
        'view_community_info', // 查看社区信息
        'contact_service',    // 联系客服
        'view_profile',       // 查看个人资料
        'update_profile'      // 更新个人资料
      ],
      
      // 需要手机号绑定的功能
      phone_required: [
        'submit_complaint',   // 提交投诉
        'view_complaint_history', // 查看投诉历史
        'participate_vote',   // 参与投票
        'view_vote_results'   // 查看投票结果
      ],
      
      // 需要房屋认证的功能
      house_required: [
        'view_bills',         // 查看账单
        'pay_bills',          // 缴费
        'view_repair_history', // 查看维修历史
        'submit_repair',      // 提交维修申请
        'view_parking_info',  // 查看停车信息
        'manage_visitors',    // 访客管理
        'view_property_fee'   // 查看物业费
      ]
    }
  }

  /**
   * 检查用户是否有指定权限
   * @param {string} permission 权限名称
   * @returns {Object} 权限检查结果
   */
  checkPermission(permission) {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    
    const result = {
      hasPermission: false,
      reason: '',
      authStatus: this.getAuthStatus(state),
      requiredAuth: this.getRequiredAuth(permission)
    }

    // 检查基础功能
    if (this.permissions.basic.includes(permission)) {
      result.hasPermission = true
      return result
    }

    // 检查需要手机号的功能
    if (this.permissions.phone_required.includes(permission)) {
      if (!state.hasBindPhone) {
        result.reason = '此功能需要绑定手机号'
        return result
      }
      result.hasPermission = true
      return result
    }

    // 检查需要房屋认证的功能
    if (this.permissions.house_required.includes(permission)) {
      if (!state.hasBindPhone) {
        result.reason = '此功能需要先绑定手机号'
        return result
      }
      if (!state.isHouseAuth) {
        result.reason = '此功能需要完成房屋认证'
        return result
      }
      result.hasPermission = true
      return result
    }

    // 未知权限，默认拒绝
    result.reason = '未知的功能权限'
    return result
  }

  /**
   * 获取用户认证状态
   * @param {Object} state 用户状态
   * @returns {string} 认证状态
   */
  getAuthStatus(state) {
    if (!state.isLogin) return 'not_login'
    if (!state.hasBindPhone) return 'no_phone'
    if (!state.isHouseAuth) return 'no_house'
    return 'full_auth'
  }

  /**
   * 获取功能所需的认证级别
   * @param {string} permission 权限名称
   * @returns {string} 所需认证级别
   */
  getRequiredAuth(permission) {
    if (this.permissions.basic.includes(permission)) return 'basic'
    if (this.permissions.phone_required.includes(permission)) return 'phone'
    if (this.permissions.house_required.includes(permission)) return 'house'
    return 'unknown'
  }

  /**
   * 显示权限不足的提示
   * @param {string} permission 权限名称
   * @param {Object} options 选项
   */
  showPermissionDenied(permission, options = {}) {
    const checkResult = this.checkPermission(permission)
    const { showGuide = true, customMessage } = options

    const message = customMessage || checkResult.reason || '权限不足'
    
    if (showGuide) {
      this.showAuthGuide(checkResult.authStatus, message)
    } else {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      })
    }
  }

  /**
   * 显示认证引导
   * @param {string} authStatus 认证状态
   * @param {string} message 提示消息
   */
  showAuthGuide(authStatus, message) {
    let title = '功能受限'
    let content = message
    let showCancel = true
    let confirmText = '去认证'
    
    switch (authStatus) {
      case 'not_login':
        title = '请先登录'
        content = '此功能需要登录后使用'
        confirmText = '去登录'
        break
      case 'no_phone':
        title = '需要绑定手机号'
        content = '此功能需要绑定手机号后使用'
        confirmText = '去绑定'
        break
      case 'no_house':
        title = '需要房屋认证'
        content = '此功能需要完成房屋认证后使用，请联系管理员进行认证'
        confirmText = '联系管理员'
        break
    }

    wx.showModal({
      title,
      content,
      showCancel,
      cancelText: '稍后',
      confirmText,
      success: (res) => {
        if (res.confirm) {
          this.handleAuthAction(authStatus)
        }
      }
    })
  }

  /**
   * 处理认证操作
   * @param {string} authStatus 认证状态
   */
  handleAuthAction(authStatus) {
    switch (authStatus) {
      case 'not_login':
        // 跳转到登录页
        wx.redirectTo({
          url: '/pages/login/index'
        })
        break
      case 'no_phone':
        // 触发手机号绑定流程
        this.triggerPhoneBinding()
        break
      case 'no_house':
        // 显示联系管理员信息
        this.showContactInfo()
        break
    }
  }

  /**
   * 触发手机号绑定
   */
  triggerPhoneBinding() {
    // 可以触发手机号绑定弹窗或跳转到绑定页面
    wx.showModal({
      title: '绑定手机号',
      content: '请在登录页面完成手机号绑定',
      showCancel: false,
      confirmText: '我知道了'
    })
  }

  /**
   * 显示联系管理员信息
   */
  showContactInfo() {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    const communityInfo = state.communityInfo
    
    let content = '请联系物业管理员进行房屋认证：'
    if (communityInfo && communityInfo.servicePhone) {
      content += `\n\n服务电话：${communityInfo.servicePhone}`
    } else {
      content += '\n\n请联系您所在小区的物业管理处'
    }
    
    wx.showModal({
      title: '联系管理员',
      content,
      showCancel: false,
      confirmText: '我知道了'
    })
  }

  /**
   * 获取用户可用的功能列表
   * @returns {Array} 可用功能列表
   */
  getAvailablePermissions() {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    
    let available = [...this.permissions.basic]
    
    if (state.hasBindPhone) {
      available.push(...this.permissions.phone_required)
    }
    
    if (state.isHouseAuth) {
      available.push(...this.permissions.house_required)
    }
    
    return available
  }

  /**
   * 检查并执行功能
   * @param {string} permission 权限名称
   * @param {Function} callback 执行回调
   * @param {Object} options 选项
   */
  executeWithPermission(permission, callback, options = {}) {
    const checkResult = this.checkPermission(permission)
    
    if (checkResult.hasPermission) {
      if (typeof callback === 'function') {
        callback()
      }
    } else {
      this.showPermissionDenied(permission, options)
    }
  }

  /**
   * 权限装饰器（用于页面方法）
   * @param {string} permission 权限名称
   * @param {Object} options 选项
   * @returns {Function} 装饰器函数
   */
  requirePermission(permission, options = {}) {
    return (target, propertyKey, descriptor) => {
      const originalMethod = descriptor.value
      
      descriptor.value = function(...args) {
        const permissionManager = new PermissionManager()
        const checkResult = permissionManager.checkPermission(permission)
        
        if (checkResult.hasPermission) {
          return originalMethod.apply(this, args)
        } else {
          permissionManager.showPermissionDenied(permission, options)
          return false
        }
      }
      
      return descriptor
    }
  }
}

// 创建单例
const permissionManager = new PermissionManager()

export default permissionManager

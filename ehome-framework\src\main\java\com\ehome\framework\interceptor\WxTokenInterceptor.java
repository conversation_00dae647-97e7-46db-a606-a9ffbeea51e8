package com.ehome.framework.interceptor;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.SecurityUtils;
import com.ehome.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class WxTokenInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(WxTokenInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                throw new ServiceException("缺少或格式错误的token");
            }

            // 验证token有效性
            LoginUser loginUser = SecurityUtils.getLoginUser(token);
            if (loginUser == null) {
                throw new ServiceException("token无效");
            }

            // 检查是否启用自动刷新功能
            if (SecurityUtils.isAutoRefreshEnabled() && SecurityUtils.needsRefresh(token)) {
                try {
                    String newToken = SecurityUtils.refreshToken(token);
                    if (newToken != null) {
                        // 在响应头中返回新token，前端可以自动更新
                        response.setHeader("New-Token", "Bearer " + newToken);
                        response.setHeader("Token-Refreshed", "true");

                        // 更新用户最后登录时间和IP
                        try {
                            Object wxUserService = SpringUtils.getBean("wxUserServiceImpl");
                            if (wxUserService != null) {
                                String ip = IpUtils.getIpAddr(request);
                                // 使用反射调用updateUserLoginInfo方法
                                wxUserService.getClass().getMethod("updateUserLoginInfo", Long.class, String.class)
                                    .invoke(wxUserService, loginUser.getUserId(), ip);
                                logger.debug("自动刷新token时更新用户登录信息成功，用户ID: {}, IP: {}", loginUser.getUserId(), ip);
                            }
                        } catch (Exception updateEx) {
                            logger.warn("自动刷新token时更新用户登录信息失败，用户ID: {}, 错误: {}",
                                       loginUser.getUserId(), updateEx.getMessage());
                        }

                        long remainingTime = SecurityUtils.getRemainingTime(newToken);
                        logger.info("Token已自动刷新成功，用户ID: {}, 新token剩余有效期: {}ms, 原token剩余: {}ms",
                                   loginUser.getUserId(), remainingTime, SecurityUtils.getRemainingTime(token));
                    } else {
                        logger.warn("Token刷新失败，用户ID: {}, 原token剩余有效期: {}ms",
                                   loginUser.getUserId(), SecurityUtils.getRemainingTime(token));
                    }
                } catch (Exception e) {
                    logger.error("Token自动刷新异常，用户ID: {}, 错误: {}",
                                loginUser.getUserId(), e.getMessage(), e);
                }
            } else {
                // 记录token状态信息，便于调试
                long remainingTime = SecurityUtils.getRemainingTime(token);
                if (remainingTime > 0) {
                    logger.debug("Token状态正常，用户ID: {}, 剩余有效期: {}ms, 自动刷新: {}",
                                loginUser.getUserId(), remainingTime, SecurityUtils.isAutoRefreshEnabled());
                }
            }

            // 将 loginUser 放入 request attribute
            request.setAttribute("currentUser", loginUser);
            return true;

        } catch (ServiceException ex) {
            logger.warn("Token校验失败:{}, {}",token, ex.getMessage());
            response.setStatus(401);
            response.setContentType("application/json;charset=UTF-8");
            // 使用具体的错误消息，支持强制下线等不同场景
            String errorMsg = ex.getMessage();
            if (errorMsg == null || errorMsg.trim().isEmpty()) {
                errorMsg = "未登录或token失效";
            }
            response.getWriter().write("{\"code\":401,\"msg\":\"" + errorMsg + "\"}");
            return false;
        } catch (Exception e) {
            logger.error("Token校验异常: {},{}", e.getMessage(),token, e);
            response.setStatus(401);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"msg\":\"token校验异常\"}");
            return false;
        }
    }
}
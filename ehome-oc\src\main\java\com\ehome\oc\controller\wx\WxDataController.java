package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.config.ServerConfig;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.entity.SysDictData;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.service.ICommunityConfigService;
import com.ehome.oc.service.INoticeTypeService;
import com.ehome.oc.service.IWechatAuthService;
import com.ehome.oc.service.IWxUserService;
import com.ehome.system.service.ISysDictTypeService;
import com.ehome.system.service.ISysNoticeCommentService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/wx/data")
public class WxDataController extends BaseWxController {
    @Autowired
    private IWechatAuthService wechatAuthService;

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private ISysNoticeCommentService commentService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private ICommunityConfigService communityConfigService;

    @Autowired
    private INoticeTypeService noticeTypeService;

    /**
     * 获取数据字典
     */
    @PostMapping("/getDictData")
    public AjaxResult getDictData() {
        try {
            JSONObject params = getParams();
            String dictType = params.getString("dictType");

            if (StringUtils.isEmpty(dictType)) {
                return AjaxResult.error("字典类型不能为空");
            }

            List<SysDictData> dictDataList = dictTypeService.selectDictDataByType(dictType);

            List<Map<String, Object>> result = new ArrayList<>();
            for (SysDictData dictData : dictDataList) {
                Map<String, Object> item = new HashMap<>();
                item.put("dictValue", dictData.getDictValue());
                item.put("dictLabel", dictData.getDictLabel());
                item.put("dictSort", dictData.getDictSort());
                item.put("listClass", dictData.getListClass());
                item.put("cssClass", dictData.getCssClass());
                result.add(item);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取数据字典失败: " + e.getMessage(), e);
            return AjaxResult.error("获取数据字典失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告类型（优先从小区配置读取，如果没有则返回默认类型）
     */
    @PostMapping("/getNoticeTypes")
    public AjaxResult getNoticeTypes() {
        try {
            // 获取当前用户的小区ID
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String communityId = currentUser.getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                return AjaxResult.error("用户未绑定小区");
            }

            // 使用NoticeTypeService获取公告类型
            List<Map<String, Object>> result = noticeTypeService.getNoticeTypes(communityId);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取公告类型失败: " + e.getMessage(), e);
            return AjaxResult.error("获取公告类型失败: " + e.getMessage());
        }
    }

    @RequestMapping("/menuContent")
    public AjaxResult menuContent() {
        JSONObject params = getParams();
        String id = params.getString("id");
        Record record =  Db.findFirst("select nav_id,nav_name,icon_name,is_default,content,pdf_file,pdf_file_id,pdf_file_list,nav_type,url,miniprogram_config,tap_name,source,update_time from eh_wx_nav where status = 0 and nav_id = ?",id);
        if(record == null){
            return error("菜单内容不存在");
        }
        Map<String,Object> map = new HashMap<>();
        map.put("nav_id", record.get("nav_id"));
        map.put("nav_name", record.getStr("nav_name"));
        map.put("icon_name", record.getStr("icon_name"));
        map.put("update_time", record.getStr("update_time"));
        map.put("pdf_file_list", record.getStr("pdf_file_list"));
        map.put("pdf_file", record.getStr("pdf_file"));
        String pdfFileId =  record.getStr("pdf_file_id");
        map.put("pdf_file_id",pdfFileId);
        if(StringUtils.isNotEmpty(pdfFileId)){
            map.put("pdf_file_path",Db.queryStr("select access_url from eh_file_info where file_id = ? and status = 0", pdfFileId));
        }else{
            map.put("pdf_file_path", "");
        }

        map.put("is_default", record.getInt("is_default"));
        map.put("nav_type", record.getStr("nav_type") != null ? record.getStr("nav_type") : "text");
        String content = record.getStr("content") != null ? record.getStr("content") : "";
        map.put("content", content);
        map.put("url", record.getStr("url") != null ? record.getStr("url") : "");
        map.put("miniprogram_config", record.getStr("miniprogram_config") != null ? record.getStr("miniprogram_config") : "");
        map.put("tap_name", record.getStr("tap_name") != null ? record.getStr("tap_name") : "");
        map.put("source", record.getStr("source") != null ? record.getStr("source") : "");
        return success(map);
    }

    /**
     * 获取PDF文件列表
     */
    @PostMapping("/getPdfFileList")
    public AjaxResult getPdfFileList() {
        JSONObject params = getParams();
        String navId = params.getString("nav_id");

        if (StringUtils.isEmpty(navId)) {
            return AjaxResult.error("菜单ID不能为空");
        }

        Record navInfo = Db.findFirst("SELECT pdf_file_list FROM eh_wx_nav WHERE nav_id = ?", navId);
        if (navInfo == null) {
            return AjaxResult.error("菜单不存在");
        }

        String pdfFileList = navInfo.getStr("pdf_file_list");
        List<Map<String, Object>> fileList = new ArrayList<>();

        if (StringUtils.isNotEmpty(pdfFileList)) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(pdfFileList);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject fileObj = jsonArray.getJSONObject(i);
                    Map<String, Object> fileMap = new HashMap<>();
                    String fileId = fileObj.getString("id");
                    fileMap.put("id", fileId);
                    fileMap.put("file_id", fileId);
                    fileMap.put("original_name", fileObj.getString("name"));
                    fileMap.put("url", fileObj.getString("url"));
                    fileMap.put("access_url", "/api/wx/file/download/" + fileId);
                    fileMap.put("file_type", fileObj.getString("type"));
                    fileList.add(fileMap);
                }
            } catch (Exception e) {
                logger.error("解析PDF文件列表失败: " + e.getMessage(),e);
            }
        }
        return AjaxResult.success(fileList);
    }




    /**
     * 获取物业服务数据（支持一二级菜单结构）
     */
    @RequestMapping("/getPropertyServices")
    public AjaxResult getPropertyServices() {
        try {
            // 获取所有菜单数据
            List<Map<String,Object>> allMenus = getPropertyServiceMenuData();

            // 递归构建菜单树（从根节点开始）
            List<Map<String,Object>> menuTree = buildPropertyServiceMenuTree(allMenus, 0);

            // 构建返回数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("categories", menuTree);

            return success(result);
        } catch (Exception e) {
            logger.error("获取物业服务数据失败: " + e.getMessage(), e);
            return error("获取物业服务数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取物业服务菜单数据
     */
    private List<Map<String,Object>> getPropertyServiceMenuData() {
        String sql = "select nav_id,nav_name,nav_type,content,pdf_file_list,icon_name,sort,update_time,parent_id from eh_wx_nav where status = 0 and community_id = ? and source = 'infoNav' order by parent_id, sort";
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(getCurrentUser().getCommunityId());

        List<Record> list = Db.find(sql, sqlParams.toArray());
        List<Map<String,Object>> listMap = new LinkedList<>();

        for(Record record : list){
            Map<String,Object> menuItem = record.toMap();
            // 确保字段类型正确
            menuItem.put("nav_id", record.get("nav_id"));
            menuItem.put("nav_name", record.getStr("nav_name"));
            menuItem.put("nav_type", record.getStr("nav_type") != null ? record.getStr("nav_type") : "text");
            menuItem.put("content", record.getStr("content"));
            menuItem.put("pdf_file_list", record.getStr("pdf_file_list"));
            menuItem.put("icon_name", record.getStr("icon_name"));
            menuItem.put("sort", record.getInt("sort"));
            menuItem.put("update_time", record.getStr("update_time"));
            menuItem.put("parent_id", record.getInt("parent_id"));
            listMap.add(menuItem);
        }
        return listMap;
    }

    /**
     * 递归构建物业服务菜单树
     */
    private List<Map<String,Object>> buildPropertyServiceMenuTree(List<Map<String,Object>> allMenus, int parentId) {
        List<Map<String,Object>> result = new ArrayList<>();

        for (Map<String,Object> menu : allMenus) {
            Integer menuParentId = (Integer) menu.get("parent_id");
            if (menuParentId != null && menuParentId == parentId) {
                // 递归获取子菜单
                List<Map<String,Object>> children = buildPropertyServiceMenuTree(allMenus, (Integer) menu.get("nav_id"));

                // 处理菜单内容
                Map<String, Object> processedMenu = processPropertyServiceMenu(menu, children);
                result.add(processedMenu);
            }
        }

        return result;
    }

    /**
     * 处理物业服务菜单内容
     */
    private Map<String, Object> processPropertyServiceMenu(Map<String,Object> menu, List<Map<String,Object>> children) {
        Map<String, Object> processedMenu = new HashMap<>();

        // 基本信息
        processedMenu.put("id", menu.get("nav_id"));
        processedMenu.put("text", menu.get("nav_name"));
        processedMenu.put("nav_type", menu.get("nav_type"));
        processedMenu.put("icon_name", menu.get("icon_name"));
        processedMenu.put("sort", menu.get("sort"));
        processedMenu.put("update_time", menu.get("update_time"));
        processedMenu.put("hasChildren", !children.isEmpty());

        if (!children.isEmpty()) {
            // 有子菜单的情况
            processedMenu.put("children", children);
            processedMenu.put("content", new ArrayList<>()); // 父菜单本身没有内容
        } else {
            // 叶子节点，处理具体内容
            List<Map<String, Object>> content = new ArrayList<>();
            String navType = (String) menu.get("nav_type");

            if ("pdf".equals(navType)) {
                // PDF类型处理
                String pdfFileList = (String) menu.get("pdf_file_list");
                if (StringUtils.isNotEmpty(pdfFileList)) {
                    try {
                        JSONArray pdfArray = JSONArray.parseArray(pdfFileList);
                        for (int i = 0; i < pdfArray.size(); i++) {
                            JSONObject pdfObj = pdfArray.getJSONObject(i);
                            Map<String, Object> pdfItem = new HashMap<>();
                            pdfItem.put("id", menu.get("nav_id") + "_" + i);
                            pdfItem.put("fileId", pdfObj.getString("id"));
                            pdfItem.put("text", pdfObj.getString("name"));
                            pdfItem.put("pdf_file", pdfObj.getString("name"));
                            pdfItem.put("pdf_file_path", pdfObj.getString("url"));
                            pdfItem.put("update_time", menu.get("update_time"));
                            content.add(pdfItem);
                        }
                    } catch (Exception e) {
                        logger.warn("解析PDF文件列表失败: " + e.getMessage());
                    }
                }
            } else {
                // 文本类型处理
                Map<String, Object> textItem = new HashMap<>();
                textItem.put("id", menu.get("nav_id"));
                textItem.put("text", menu.get("nav_name"));
                textItem.put("content", menu.get("content"));
                textItem.put("update_time", menu.get("update_time"));
                content.add(textItem);
            }

            processedMenu.put("content", content);
            processedMenu.put("children", new ArrayList<>());
        }

        return processedMenu;
    }

    public AjaxResult likeNotice(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String communityId = currentUser.getCommunityId();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 检查是否已经点赞
            Record existingLike = Db.findFirst(
                "SELECT * FROM sys_notice_like WHERE notice_id = ? AND user_id = ? AND status = '0'",
                noticeId, userId
            );

            boolean isLiked = false;
            int likeCount = 0;

            if (existingLike != null) {
                // 已点赞，取消点赞
                Db.update("UPDATE sys_notice_like SET status = '1', update_time = NOW() WHERE notice_id = ? AND user_id = ?",
                    noticeId, userId);

                // 减少点赞数
                Db.update("UPDATE sys_notice SET like_count = GREATEST(like_count - 1, 0) WHERE notice_id = ?", noticeId);
                isLiked = false;
            } else {
                // 未点赞，添加点赞记录
                Db.update(
                    "INSERT INTO sys_notice_like (notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, status, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, '0', NOW()) " +
                    "ON DUPLICATE KEY UPDATE status = '0', update_time = NOW()",
                    noticeId, userId, currentUser.getUsername(), "wx_user", communityId, ownerId, houseId, houseName
                );

                // 增加点赞数
                Db.update("UPDATE sys_notice SET like_count = like_count + 1 WHERE notice_id = ?", noticeId);
                isLiked = true;
            }

            // 获取最新的点赞数
            likeCount = Db.queryInt("SELECT like_count FROM sys_notice WHERE notice_id = ?", noticeId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("点赞操作失败: " + e.getMessage(), e);
            return AjaxResult.error("点赞操作失败: " + e.getMessage());
        }
    }

    /**
     * 公告分享统计
     */
    @PostMapping("/notice/{noticeId}/share")
    public AjaxResult shareNotice(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String communityId = currentUser.getCommunityId();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 记录分享行为
            Db.update(
                "INSERT INTO sys_notice_share (notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                noticeId, userId, currentUser.getUsername(), "wx_user", communityId, ownerId, houseId, houseName
            );

            // 增加分享数
            Db.update("UPDATE sys_notice SET share_count = share_count + 1 WHERE notice_id = ?", noticeId);

            // 获取最新的分享数
            int shareCount = Db.queryInt("SELECT share_count FROM sys_notice WHERE notice_id = ?", noticeId);

            Map<String, Object> result = new HashMap<>();
            result.put("shareCount", shareCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("分享统计失败: " + e.getMessage(), e);
            return AjaxResult.error("分享统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告统计信息（点赞数、分享数、用户是否已点赞）
     */
    @GetMapping("/notice/{noticeId}/stats")
    public AjaxResult getNoticeStats(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            String userId = currentUser != null ? String.valueOf(currentUser.getUserId()) : null;

            // 获取公告统计信息
            Record noticeStats = Db.findFirst(
                "SELECT like_count, share_count FROM sys_notice WHERE notice_id = ?", noticeId
            );

            if (noticeStats == null) {
                return AjaxResult.error("公告不存在");
            }

            boolean isLiked = false;
            if (userId != null) {
                // 检查用户是否已点赞
                Record likeRecord = Db.findFirst(
                    "SELECT * FROM sys_notice_like WHERE notice_id = ? AND user_id = ? AND status = '0'",
                    noticeId, userId
                );
                isLiked = likeRecord != null;
            }

            Map<String, Object> result = new HashMap<>();
            result.put("likeCount", noticeStats.getInt("like_count"));
            result.put("shareCount", noticeStats.getInt("share_count"));
            result.put("isLiked", isLiked);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取公告统计信息失败: " + e.getMessage(), e);
            return AjaxResult.error("获取公告统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 评论点赞/取消点赞
     */
    @PostMapping("/comment/{commentId}/like")
    public AjaxResult likeComment(@PathVariable("commentId") Long commentId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 检查是否已经点赞
            Record existingLike = Db.findFirst(
                "SELECT * FROM sys_comment_like WHERE comment_id = ? AND user_id = ? AND status = '0'",
                commentId, userId
            );

            boolean isLiked = false;
            int likeCount = 0;

            if (existingLike != null) {
                // 已点赞，取消点赞
                Db.update("UPDATE sys_comment_like SET status = '1', update_time = NOW() WHERE comment_id = ? AND user_id = ?",
                    commentId, userId);

                // 减少点赞数
                Db.update("UPDATE sys_notice_comment SET like_count = GREATEST(like_count - 1, 0) WHERE comment_id = ?", commentId);
                isLiked = false;
            } else {
                // 未点赞，添加点赞记录
                Db.update(
                    "INSERT INTO sys_comment_like (comment_id, user_id, user_name, user_type, owner_id, house_id, house_name, status, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, '0', NOW()) " +
                    "ON DUPLICATE KEY UPDATE status = '0', update_time = NOW()",
                    commentId, userId, currentUser.getUsername(), "wx_user", ownerId, houseId, houseName
                );

                // 增加点赞数
                Db.update("UPDATE sys_notice_comment SET like_count = like_count + 1 WHERE comment_id = ?", commentId);
                isLiked = true;
            }

            // 获取最新的点赞数
            likeCount = Db.queryInt("SELECT like_count FROM sys_notice_comment WHERE comment_id = ?", commentId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("评论点赞操作失败: " + e.getMessage(), e);
            return AjaxResult.error("评论点赞操作失败: " + e.getMessage());
        }
    }

}


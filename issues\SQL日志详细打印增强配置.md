# SQL日志详细打印增强配置

## 任务背景
用户要求详细打印SQL日志，包括参数、执行时间等信息，参考JFinal社区分享：
- https://jfinal.com/share/324
- https://www.jfinal.com/share/492

## 需求分析
需要增强现有的Druid SQL日志配置，实现：
1. **完整SQL语句** - 包含实际参数值的可执行SQL
2. **执行时间统计** - 精确到毫秒的SQL执行时间
3. **参数值记录** - SQL中的参数绑定值
4. **连接信息** - 数据库连接和事务状态
5. **慢SQL标记** - 超过阈值的SQL自动标记

## 实施方案
采用增强现有Druid配置的方案，而不是集成log4jdbc，原因：
1. 与现有架构兼容性更好
2. 不需要修改数据源URL和驱动配置
3. 可以利用现有的日志分级和文件轮转
4. Druid功能更强大，包含监控统计

## 修改内容

### 1. 增强ActiveRecordPluginConfig.java
**文件：** `ehome-jfinal/src/main/java/com/ehome/jfinal/config/ActiveRecordPluginConfig.java`

简化并优化了Slf4jLogFilter配置，只保留核心有效的配置项：
- ✅ 启用可执行SQL日志（包含参数值和执行时间）
- ✅ 配置基础错误日志记录
- ✅ 启用连接回滚日志

关键配置项：
```java
// 核心功能：启用可执行SQL日志（包含参数值和执行时间）
slf4jLogFilter.setStatementExecutableSqlLogEnable(true);

// 基础日志配置
slf4jLogFilter.setConnectionLogErrorEnabled(true);
slf4jLogFilter.setStatementLogErrorEnabled(true);
slf4jLogFilter.setResultSetLogErrorEnabled(true);
slf4jLogFilter.setConnectionRollbackAfterLogEnabled(true);
```

### 2. 优化logback-spring.xml配置
**文件：** `ehome-web/src/main/resources/logback-spring.xml`

增强了Druid SQL日志的Logger配置：
- ✅ 添加Statement日志到控制台输出（便于开发调试）
- ✅ 增加Connection日志记录
- ✅ 配置ResultSet日志（WARN级别）
- ✅ 确保所有SQL日志统一输出到sql.log文件

关键配置：
```xml
<!-- Statement日志：记录SQL执行、参数、执行时间等 -->
<logger name="druid.sql.Statement" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
    <appender-ref ref="console"/>
</logger>

<!-- Connection日志：记录数据库连接事件 -->
<logger name="druid.sql.Connection" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>
```

### 3. 增强application-druid.yml配置
**文件：** `ehome-web/src/main/resources/application-druid.yml`

简化了Druid的slf4j过滤器配置，只保留核心有效的选项：
- ✅ 启用可执行SQL日志记录（包含参数值）
- ✅ 启用基础SQL语句日志
- ✅ 启用连接日志
- ✅ 禁用结果集日志（减少日志量）

关键配置：
```yaml
slf4j:
    enabled: true
    # 核心功能：记录可执行SQL（包含参数值）
    statement-executable-sql-log-enable: true
    # 记录所有SQL语句
    statement-log-enabled: true
    # 记录连接和关闭事件
    connection-log-enabled: true
    # 记录结果集日志
    result-set-log-enabled: false
```

## 预期日志输出效果

### Druid SQL日志示例
```
14:30:27.123 [http-nio-8066-exec-5] DEBUG druid.sql.Statement - {conn-10001, pstmt-20001} executed. SELECT * FROM eh_community WHERE oc_name LIKE ? ['测试%'] cost 23 ms.

14:30:27.456 [http-nio-8066-exec-6] DEBUG druid.sql.Statement - {conn-10002, pstmt-20002} Parameters: [OC001, 1]

14:30:27.789 [http-nio-8066-exec-7] DEBUG druid.sql.Statement - {conn-10003, pstmt-20003} executed. INSERT INTO eh_owner (community_id, owner_name, tel) VALUES (?, ?, ?) ['OC001', '张三', '13800138000'] cost 15 ms.

14:30:28.123 [http-nio-8066-exec-8] WARN druid.sql.Statement - {conn-10004, pstmt-20004} slow sql 1100 ms. SELECT COUNT(*) FROM eh_owner o LEFT JOIN eh_community c ON o.community_id = c.oc_id WHERE c.status = ? [1]
```

### 连接日志示例
```
14:30:26.890 [http-nio-8066-exec-5] DEBUG druid.sql.Connection - {conn-10001} pool-connect

14:30:27.890 [http-nio-8066-exec-5] DEBUG druid.sql.Connection - {conn-10001} rollback
```

## 功能特性

### 1. 完整SQL记录
- ✅ 显示完整的可执行SQL语句
- ✅ 参数值已替换到SQL中，可直接复制执行
- ✅ 支持各种SQL类型：SELECT、INSERT、UPDATE、DELETE

### 2. 执行时间统计
- ✅ 精确到毫秒的SQL执行时间
- ✅ 慢SQL自动标记（>1000ms显示为WARN级别）
- ✅ 执行时间显示在日志末尾：`cost 23 ms.`

### 3. 参数值记录
- ✅ 显示SQL参数的实际值
- ✅ 参数值格式：`['参数1', '参数2']`
- ✅ 支持各种数据类型的参数

### 4. 连接信息
- ✅ 显示连接ID：`{conn-10001, pstmt-20001}`
- ✅ 记录连接池连接和释放事件
- ✅ 记录事务提交和回滚事件

### 5. 日志分级
- ✅ **DEBUG**: 正常SQL执行记录
- ✅ **WARN**: 慢SQL警告（>1000ms）
- ✅ **ERROR**: SQL执行异常

## 使用说明

### 1. 重启应用
配置修改后需要重启应用才能生效。

### 2. 查看日志
- **控制台**: 可以看到Statement级别的SQL日志
- **文件**: 所有SQL日志统一输出到 `${log.path}/sql.log`

### 3. 日志级别控制
可以通过修改logback-spring.xml中的日志级别来控制输出详细程度：
- `debug`: 显示所有SQL执行详情
- `info`: 只显示重要的SQL信息
- `warn`: 只显示慢SQL和异常

### 4. 性能监控
- 关注WARN级别的慢SQL日志，进行性能优化
- 监控ERROR级别的SQL异常，及时处理问题
- 通过执行时间统计识别性能瓶颈

## 与现有功能的协同

### 1. MyBatis SQL日志
- 现有的MyBatis拦截器继续工作
- Druid日志提供数据源层面的补充信息
- 两者结合提供完整的SQL执行链路

### 2. JFinal SQL日志
- 现有的JFinal增强功能继续工作
- Druid日志提供底层数据源的执行信息
- 统一输出到同一个sql.log文件

### 3. 日志文件轮转
- 继续使用现有的日志轮转配置
- 最大100MB，保留5个历史文件
- 异步日志处理，不影响应用性能

## 故障排除

### 1. 如果看不到SQL日志
检查以下配置：
- logback-spring.xml中druid.sql.Statement的日志级别是否为debug
- application-druid.yml中statement-executable-sql-log-enable是否为true
- ActiveRecordPluginConfig.java中setStatementExecutableSqlLogEnable是否为true

### 2. 如果日志过多
可以调整以下配置：
- 将statement-parameter-set-log-enabled设为false
- 将connection相关日志设为false
- 调整日志级别为info或warn

### 3. 如果性能受影响
- 确认使用了异步日志appender
- 考虑在生产环境关闭部分详细日志
- 监控日志文件大小和轮转情况

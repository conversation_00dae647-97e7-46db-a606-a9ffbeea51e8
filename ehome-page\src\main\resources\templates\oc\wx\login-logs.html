<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('登录日志')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 hidden">
                <h4>登录日志 - 用户ID: <span th:text="${userId}"></span></h4>
            </div>
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" name="userId" th:value="${userId}"/>
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>登录时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="beginTime"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchTable()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetForm()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>



            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var ctx = /*[[@{/}]]*/ '';
        var prefix = ctx + "oc/wxuser";
        var userId = /*[[${userId}]]*/ '';

        $(function() {
            var options = {
                url: prefix + "/loginLogs",
                queryParams: function(params) {
                    var search = $.table.queryParams(params);
                    search.userId = userId;
                    return search;
                },
                modalName: "登录日志",
                columns: [{
                    field: 'log_id',
                    title: '日志ID',
                    visible: false
                },
                {
                    field: 'login_date',
                    title: '登录时间',
                    sortable: true,
                    width: '180px'
                },
                {
                    field: 'login_ip',
                    title: '登录IP',
                    width: '120px',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'mobile',
                    title: '手机号码',
                    width: '120px',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'user_agent',
                    title: '用户代理',
                    formatter: function(value, row, index) {
                        if (!value) return '-';
                        
                        // 解析用户代理信息
                        var userAgent = value;
                        var browser = '未知浏览器';
                        var os = '未知系统';
                        
                        // 检测浏览器
                        if (userAgent.indexOf('MicroMessenger') > -1) {
                            browser = '微信浏览器';
                        } else if (userAgent.indexOf('Chrome') > -1) {
                            browser = 'Chrome';
                        } else if (userAgent.indexOf('Firefox') > -1) {
                            browser = 'Firefox';
                        } else if (userAgent.indexOf('Safari') > -1) {
                            browser = 'Safari';
                        } else if (userAgent.indexOf('Edge') > -1) {
                            browser = 'Edge';
                        }
                        
                        // 检测操作系统
                        if (userAgent.indexOf('Windows') > -1) {
                            os = 'Windows';
                        } else if (userAgent.indexOf('Mac') > -1) {
                            os = 'macOS';
                        } else if (userAgent.indexOf('Linux') > -1) {
                            os = 'Linux';
                        } else if (userAgent.indexOf('Android') > -1) {
                            os = 'Android';
                        } else if (userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
                            os = 'iOS';
                        }
                        
                        var result = '<div>';
                        result += '<div><strong>浏览器:</strong> ' + browser + '</div>';
                        result += '<div><strong>系统:</strong> ' + os + '</div>';
                        result += '<div class="text-muted" style="font-size: 11px; margin-top: 5px;" title="' + userAgent + '">';
                        result += userAgent.length > 50 ? userAgent.substring(0, 50) + '...' : userAgent;
                        result += '</div>';
                        result += '</div>';
                        
                        return result;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '100px',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showDetail(\'' + row.log_id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 搜索功能
        function searchTable() {
            $.table.search();
        }

        // 重置功能
        function resetForm() {
            $.form.reset();
        }

        // 显示详细信息
        function showDetail(logId) {
            // 从表格中获取当前行数据
            var tableData = $("#bootstrap-table").bootstrapTable('getData');
            var logData = null;
            for (var i = 0; i < tableData.length; i++) {
                if (tableData[i].log_id == logId) {
                    logData = tableData[i];
                    break;
                }
            }
            
            if (!logData) {
                $.modal.alertError("未找到日志数据");
                return;
            }
            
            var content = '<div class="row">';
            content += '<div class="col-sm-12">';
            content += '<table class="table table-bordered">';
            content += '<tr><td width="120px"><strong>日志ID:</strong></td><td>' + (logData.log_id || '-') + '</td></tr>';
            content += '<tr><td><strong>用户ID:</strong></td><td>' + (logData.user_id || '-') + '</td></tr>';
            content += '<tr><td><strong>手机号码:</strong></td><td>' + (logData.mobile || '-') + '</td></tr>';
            content += '<tr><td><strong>登录IP:</strong></td><td>' + (logData.login_ip || '-') + '</td></tr>';
            content += '<tr><td><strong>登录时间:</strong></td><td>' + (logData.login_date || '-') + '</td></tr>';
            content += '<tr><td><strong>用户代理:</strong></td><td style="word-break: break-all;">' + (logData.user_agent || '-') + '</td></tr>';
            content += '</table>';
            content += '</div></div>';
            
            layer.open({
                type: 1,
                area: ['700px', '400px'],
                fix: false,
                maxmin: true,
                shade: 0.3,
                title: "登录日志详情",
                content: content
            });
        }

        // 时间选择器
        laydate.render({
            elem: '#startTime',
            type: 'datetime'
        });
        laydate.render({
            elem: '#endTime',
            type: 'datetime'
        });

    </script>
</body>
</html>

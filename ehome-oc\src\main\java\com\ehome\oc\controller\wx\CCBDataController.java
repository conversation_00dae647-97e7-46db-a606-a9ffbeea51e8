package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.text.DecimalFormat;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 转入转出查询
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/wx/ccb")
public class CCBDataController extends BaseWxController {

    @GetMapping("/yearData")
    public AjaxResult yearData(){
        String lastYear = String.valueOf(java.time.LocalDate.now().getYear() - 1);
        String yearId = DateUtils.dateTimeNow(DateUtils.YYYY);
        EasySQL sql = new EasySQL();
        sql.append("SELECT SUBSTRING(tran_month,1,4) AS year,tran_month,");
        sql.append("SUM(CASE WHEN direction = 'in' THEN amt ELSE 0 END) AS income,");
        sql.append("SUM(CASE WHEN direction = 'out' THEN amt ELSE 0 END) AS expense");
        sql.append("FROM eh_tran_record WHERE 1=1");
        sql.append(getCurrentUser().getCommunityId(),"AND community_id = ?");
        sql.append("AND status = 'published'");
        sql.append(lastYear, "AND SUBSTRING(tran_month,1,4) >= ?");
        sql.append("GROUP BY tran_month");
        sql.append("order by tran_month desc");

        List<Record>  list = Db.find(sql.getSQL(),sql.getParams());
        //{year:{list:[],income:0,expend:0}}}
        JSONObject result = new JSONObject();
        result.put("updateTime",  DateUtils.dateTimeNow());
        result.put("dataSource", "建设银行");
        result.put("accountNumber", "--");
        result.put("balance", "--");

        Record bankInfo = Db.findFirst("select * from eh_pms_bank_account where community_id = ?",getCurrentUser().getCommunityId());
        if(bankInfo!=null){
            result.put("balance", bankInfo.getStr("balance"));
            result.put("updateTime",bankInfo.getStr("balance_last_time"));
            result.put("accountNumber",bankInfo.getStr("acc_no"));
        }

        Set<String> years = new HashSet<String>();
        if (list != null){
            JSONArray yearList = new JSONArray();
            for (Record record : list){
                String year = record.getStr("year");
                years.add(year);
            }
            for (String _year : years){
                yearList.add(_year);
            }
            result.put("years", yearList);
            for (String _year : years){
                JSONObject yearInfo = new JSONObject();
                JSONArray  months = new JSONArray();
                BigDecimal income = BigDecimal.ZERO;
                BigDecimal expend = BigDecimal.ZERO;
                for (Record record : list){
                    JSONObject row = new JSONObject();
                    String year = record.getStr("year");
                    if(year.equals(_year)){
                        row.put("tran_month",record.getStr("tran_month"));
                        row.put("year",year);
                        row.put("monthStr",convertToChineseMonth(record.getStr("tran_month")));
                        row.put("tran_month",record.getStr("tran_month"));
                        BigDecimal monthIncome = record.getBigDecimal("income");
                        BigDecimal monthExpense = record.getBigDecimal("expense");
                        row.put("income", formatAmount(monthIncome));
                        row.put("expend", formatAmount(monthExpense));
                        income = income.add(monthIncome == null ? BigDecimal.ZERO : monthIncome);
                        expend = expend.add(monthExpense == null ? BigDecimal.ZERO : monthExpense);
                        months.add(row);
                    }
                }
                yearInfo.put("yearIncome", formatAmount(income));
                yearInfo.put("yearExpense", formatAmount(expend));
                yearInfo.put("currentYear",_year);
                yearInfo.put("list",months);
                result.put(_year,yearInfo);
            }
        }
        return AjaxResult.success("成功",result);
    }

    @GetMapping("/getMonthData")
    public AjaxResult getMonthData(){
        EasySQL sql = new EasySQL();
        String date = getRequest().getParameter("date");
        if(StringUtils.isEmpty(date)){
            date = DateUtils.dateTimeNow(DateUtils.YYYY_MM);
        }
        date = date.replace("-","");
        sql.append("select * from eh_tran_record where 1=1");
        sql.append("published","and status = ?");
        sql.append(getCurrentUser().getCommunityId(),"and community_id = ?");
        sql.append(date,"and tran_month = ?");
        sql.append("order by tran_datetime desc");
        List<Record> list = Db.find(sql.getSQL(),sql.getParams());
        JSONArray array = new JSONArray();
        BigDecimal monthIncome = BigDecimal.ZERO;
        BigDecimal monthExpense = BigDecimal.ZERO;
        BigDecimal balance = null;
        for(Record record : list){
            JSONObject row = new JSONObject();
            row.put("id",record.get("trck_no"));
            row.put("title",record.get("message"));
            row.put("date",record.get("tran_date"));
            String direction = record.get("direction");
            row.put("type",direction);
            BigDecimal amt = record.getBigDecimal("amt");
            row.put("amount", formatAmount(amt));
            if(balance==null){
                balance = record.getBigDecimal("amt_all");
            }
            if("in".equals(direction)){
                 monthIncome = monthIncome.add(amt == null ? BigDecimal.ZERO : amt);
            }else if("out".equals(direction)){
                monthExpense = monthExpense.add(amt == null ? BigDecimal.ZERO : amt);
            }
            array.add(row);
        }
        JSONObject result = new JSONObject();
        result.put("list",array);
        result.put("monthIncome", formatAmount(monthIncome));
        result.put("monthExpense", formatAmount(monthExpense));
        result.put("balance",balance==null?formatAmount(BigDecimal.ZERO):formatAmount(balance));
        return AjaxResult.success("成功",result);
    }

    @GetMapping("/detail")
    public AjaxResult detail(){
        String id = getRequest().getParameter("id");
        Record record = Db.findFirst("select * from eh_tran_record where trck_no = ?",id);
        if(record == null){
            return AjaxResult.error("记录不存在");
        }
        JSONObject result = new JSONObject();
        result.put("id",record.get("trck_no"));
        result.put("title",record.get("message"));
        result.put("date",record.get("tran_date"));
        result.put("type",record.get("direction"));
        result.put("amount",record.get("amt"));
        result.put("remark",record.get("remark"));
        result.put("operator",record.get("acct_name"));
        result.put("categoryName",record.get("account_type_name"));

        JSONArray attachments = new JSONArray();
        //id name url
        result.put("attachments",attachments);

        return AjaxResult.success("成功",result);
    }

    private String convertToChineseMonth(String yearMonth) {
        if (yearMonth == null || yearMonth.length() != 6) {
            throw new IllegalArgumentException("格式必须是yyyyMM，例如202501");
        }
        String monthStr = yearMonth.substring(4, 6); // 提取 MM
        int month = Integer.parseInt(monthStr);      // 去掉前导0
        return month + "月";
    }

    /**
     * 金额格式化：四舍五入保留两位小数，千分位分隔符
     */
    private static String formatAmount(BigDecimal amount) {
        if (amount == null) return "0.00";
        BigDecimal scaled = amount.setScale(2, RoundingMode.HALF_UP);
        DecimalFormat df = new DecimalFormat("#,##0.00");
        return df.format(scaled);
    }

}

package com.ehome.oc.service;

import com.ehome.common.domain.CommunityConfig;

import java.util.Map;

/**
 * 小区配置服务接口
 * 
 * <AUTHOR>
 */
public interface ICommunityConfigService {
    
    /**
     * 初始化所有小区配置到缓存
     */
    void initAllConfigs();
    
    /**
     * 获取小区配置
     * @param ocId 小区ID
     * @return 小区配置
     */
    CommunityConfig getConfig(String ocId);
    
    /**
     * 更新单个配置
     * @param ocId 小区ID
     * @param key 配置键
     * @param value 配置值
     * @return 是否更新成功
     */
    boolean updateConfig(String ocId, String key, Object value);
    
    /**
     * 批量更新配置
     * @param ocId 小区ID
     * @param configMap 配置Map
     * @return 是否更新成功
     */
    boolean updateConfigs(String ocId, Map<String, Object> configMap);
    
    /**
     * 重载指定小区配置
     * @param ocId 小区ID
     */
    void reloadConfig(String ocId);
    
    /**
     * 重载所有小区配置
     */
    void reloadAllConfigs();
}

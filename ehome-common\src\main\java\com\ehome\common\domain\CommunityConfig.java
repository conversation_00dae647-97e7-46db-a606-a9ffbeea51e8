package com.ehome.common.domain;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.StringUtils;

/**
 * 小区配置实体类
 * 继承JSONObject，统一管理基础字段和扩展配置
 *
 * <AUTHOR>
 */
public class CommunityConfig extends JSONObject {

    private static final long serialVersionUID = 1L;

    // ========== 构造方法 ==========
    public CommunityConfig() {
        super();
    }

    public CommunityConfig(JSONObject jsonObject) {
        super(jsonObject);
    }
    
    // ========== 便捷方法 ==========

    /**
     * 设置扩展配置JSON字符串并解析到当前对象
     * @param extJson JSON字符串
     */
    public void setExtJson(String extJson) {
        if (StringUtils.isNotEmpty(extJson)) {
            try {
                JSONObject extObject = JSONObject.parseObject(extJson);
                // 将扩展配置合并到当前对象
                this.putAll(extObject);
            } catch (Exception e) {
                // 解析失败时忽略
            }
        }
    }

    /**
     * 设置小区扩展信息JSON字符串并解析到当前对象
     * @param infoJson JSON字符串
     */
    public void setInfoJson(String infoJson) {
        if (StringUtils.isNotEmpty(infoJson)) {
            try {
                JSONObject infoObject = JSONObject.parseObject(infoJson);
                // 将小区扩展信息合并到当前对象
                this.putAll(infoObject);
            } catch (Exception e) {
                // 解析失败时忽略
            }
        }
    }

    /**
     * 获取小区ID
     */
    public String getOcId() {
        return this.getString("oc_id");
    }

    /**
     * 设置小区ID
     */
    public void setOcId(String ocId) {
        this.put("oc_id", ocId);
    }

    /**
     * 获取小区名称
     */
    public String getOcName() {
        return this.getString("oc_name");
    }

    /**
     * 设置小区名称
     */
    public void setOcName(String ocName) {
        this.put("oc_name", ocName);
    }

    /**
     * 获取配置值（带默认值）
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public Object getConfigValue(String key, Object defaultValue) {
        Object value = this.get(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 获取配置字符串值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 字符串值
     */
    public String getConfigString(String key, String defaultValue) {
        Object value = this.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取配置整数值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 整数值
     */
    public Integer getConfigInteger(String key, Integer defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            if (value instanceof Integer) {
                return (Integer) value;
            }
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 获取配置布尔值
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 布尔值
     */
    public Boolean getConfigBoolean(String key, Boolean defaultValue) {
        Object value = this.get(key);
        if (value == null) {
            return defaultValue;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        String strValue = value.toString().toLowerCase();
        if ("true".equals(strValue) || "1".equals(strValue)) {
            return true;
        } else if ("false".equals(strValue) || "0".equals(strValue)) {
            return false;
        }

        return defaultValue;
    }

    /**
     * 设置配置值
     * @param key 配置键
     * @param value 配置值
     */
    public void setConfigValue(String key, Object value) {
        this.put(key, value);
    }

    /**
     * 合并配置
     * @param configMap 配置Map
     */
    public void mergeConfig(java.util.Map<String, Object> configMap) {
        if (configMap != null && !configMap.isEmpty()) {
            this.putAll(configMap);
        }
    }

    /**
     * 检查是否包含指定配置
     * @param key 配置键
     * @return 是否包含
     */
    public boolean hasConfig(String key) {
        return this.containsKey(key);
    }
}

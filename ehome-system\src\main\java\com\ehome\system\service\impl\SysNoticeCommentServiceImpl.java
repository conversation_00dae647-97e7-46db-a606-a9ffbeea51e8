package com.ehome.system.service.impl;

import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.SensitiveWordFilter;
import com.ehome.system.domain.SysNoticeComment;
import com.ehome.system.mapper.SysNoticeCommentMapper;
import com.ehome.system.mapper.SysNoticeMapper;
import com.ehome.system.service.ISysNoticeCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 通知公告评论Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysNoticeCommentServiceImpl implements ISysNoticeCommentService 
{
    @Autowired
    private SysNoticeCommentMapper sysNoticeCommentMapper;

    @Autowired
    private SysNoticeMapper sysNoticeMapper;

    /**
     * 查询通知公告评论
     * 
     * @param commentId 通知公告评论主键
     * @return 通知公告评论
     */
    @Override
    public SysNoticeComment selectSysNoticeCommentByCommentId(Long commentId)
    {
        return sysNoticeCommentMapper.selectSysNoticeCommentByCommentId(commentId);
    }

    /**
     * 查询通知公告评论列表
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 通知公告评论
     */
    @Override
    public List<SysNoticeComment> selectSysNoticeCommentList(SysNoticeComment sysNoticeComment)
    {
        return sysNoticeCommentMapper.selectSysNoticeCommentList(sysNoticeComment);
    }

    /**
     * 查询指定公告的评论列表（树形结构）
     * 
     * @param noticeId 公告ID
     * @return 评论树形列表
     */
    @Override
    public List<SysNoticeComment> selectCommentTreeByNoticeId(Long noticeId)
    {
        List<SysNoticeComment> allComments = sysNoticeCommentMapper.selectCommentsByNoticeId(noticeId);
        return buildCommentTree(allComments);
    }

    /**
     * 查询指定公告的顶级评论列表（分页）
     * 
     * @param noticeId 公告ID
     * @return 顶级评论列表
     */
    @Override
    public List<SysNoticeComment> selectTopCommentsByNoticeId(Long noticeId)
    {
        return sysNoticeCommentMapper.selectTopCommentsByNoticeId(noticeId);
    }

    /**
     * 查询指定评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    @Override
    public List<SysNoticeComment> selectRepliesByParentId(Long parentId)
    {
        return sysNoticeCommentMapper.selectRepliesByParentId(parentId);
    }

    /**
     * 查询指定评论的回复列表（包含用户自己的待审核回复）
     *
     * @param parentId 父评论ID
     * @param userId 用户ID
     * @return 回复列表
     */
    @Override
    public List<SysNoticeComment> selectRepliesByParentIdWithUser(Long parentId, String userId)
    {
        return sysNoticeCommentMapper.selectRepliesByParentIdWithUser(parentId, userId);
    }

    /**
     * 统计指定公告的评论数量
     * 
     * @param noticeId 公告ID
     * @return 评论数量
     */
    @Override
    public int countCommentsByNoticeId(Long noticeId)
    {
        return sysNoticeCommentMapper.countCommentsByNoticeId(noticeId);
    }

    /**
     * 新增通知公告评论
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 结果
     */
    @Override
    public int insertSysNoticeComment(SysNoticeComment sysNoticeComment)
    {
        sysNoticeComment.setCreateTime(DateUtils.getNowDate());
        int result = sysNoticeCommentMapper.insertSysNoticeComment(sysNoticeComment);

        // 更新主表评论次数
        if (result > 0 && sysNoticeComment.getNoticeId() != null) {
            sysNoticeMapper.updateNoticeCommentCount(sysNoticeComment.getNoticeId(), 1);
        }

        return result;
    }

    /**
     * 修改通知公告评论
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 结果
     */
    @Override
    public int updateSysNoticeComment(SysNoticeComment sysNoticeComment)
    {
        sysNoticeComment.setUpdateTime(DateUtils.getNowDate());
        return sysNoticeCommentMapper.updateSysNoticeComment(sysNoticeComment);
    }

    /**
     * 批量删除通知公告评论
     * 
     * @param commentIds 需要删除的通知公告评论主键
     * @return 结果
     */
    @Override
    public int deleteSysNoticeCommentByCommentIds(Long[] commentIds)
    {
        return sysNoticeCommentMapper.deleteSysNoticeCommentByCommentIds(commentIds);
    }

    /**
     * 删除通知公告评论信息
     * 
     * @param commentId 通知公告评论主键
     * @return 结果
     */
    @Override
    public int deleteSysNoticeCommentByCommentId(Long commentId)
    {
        // 先获取评论信息
        SysNoticeComment comment = sysNoticeCommentMapper.selectSysNoticeCommentByCommentId(commentId);
        int result = sysNoticeCommentMapper.deleteSysNoticeCommentByCommentId(commentId);

        // 更新主表评论次数
        if (result > 0 && comment != null && comment.getNoticeId() != null) {
            sysNoticeMapper.updateNoticeCommentCount(comment.getNoticeId(), -1);
        }

        return result;
    }

    /**
     * 软删除评论（更新状态为删除）
     * 
     * @param commentId 评论ID
     * @return 结果
     */
    @Override
    public int softDeleteComment(Long commentId)
    {
        return sysNoticeCommentMapper.softDeleteComment(commentId);
    }

    /**
     * 审核评论
     * 
     * @param commentId 评论ID
     * @param status 状态（0正常 1删除 2审核中）
     * @return 结果
     */
    @Override
    public int auditComment(Long commentId, String status)
    {
        return sysNoticeCommentMapper.auditComment(commentId, status);
    }

    /**
     * 发表评论
     *
     * @param noticeId 公告ID
     * @param parentId 父评论ID（可为空）
     * @param userId 用户ID
     * @param userName 用户名
     * @param userType 用户类型
     * @param content 评论内容
     * @param pmsId 物业ID
     * @param communityId 社区ID
     * @return 结果
     */
    @Override
    public int postComment(Long noticeId, Long parentId, String userId, String userName,
                          String userType, String content, String pmsId, String communityId)
    {
        return postComment(noticeId, parentId, userId, userName, userType, content, pmsId, communityId, null, null, null);
    }

    /**
     * 发表评论（包含房屋信息）
     *
     * @param noticeId 公告ID
     * @param parentId 父评论ID（可为空）
     * @param userId 用户ID
     * @param userName 用户名
     * @param userType 用户类型
     * @param content 评论内容
     * @param pmsId 物业ID
     * @param communityId 社区ID
     * @param ownerId 业主ID
     * @param houseId 房屋ID
     * @param houseName 房屋名称
     * @return 结果
     */
    public int postComment(Long noticeId, Long parentId, String userId, String userName,
                          String userType, String content, String pmsId, String communityId,
                          String ownerId, String houseId, String houseName)
    {
        // 敏感词过滤
        if (SensitiveWordFilter.containsSensitiveWord(content)) {
            content = SensitiveWordFilter.filterSensitiveWord(content);
        }

        SysNoticeComment comment = new SysNoticeComment();
        comment.setNoticeId(noticeId);
        comment.setParentId(parentId);
        comment.setUserId(userId);
        comment.setUserName(userName);
        comment.setUserType(userType);
        comment.setContent(content);
        comment.setStatus("2"); // 默认审核中状态
        comment.setPmsId(pmsId);
        comment.setCommunityId(communityId);
        comment.setOwnerId(ownerId);
        comment.setHouseId(houseId);
        comment.setHouseName(houseName);
        comment.setCreateBy(userName);

        return insertSysNoticeComment(comment);
    }

    /**
     * 查询指定小区所有公告的评论列表（包含公告标题）
     *
     * @param comment 评论信息（包含小区ID等查询条件）
     * @return 评论集合
     */
    @Override
    public List<SysNoticeComment> selectAllCommentsByCommunity(SysNoticeComment comment)
    {
        return sysNoticeCommentMapper.selectAllCommentsByCommunity(comment);
    }

    /**
     * 构建评论树形结构
     *
     * @param comments 评论列表
     * @return 树形结构评论列表
     */
    private List<SysNoticeComment> buildCommentTree(List<SysNoticeComment> comments)
    {
        List<SysNoticeComment> topComments = new ArrayList<>();
        Map<Long, List<SysNoticeComment>> childrenMap = comments.stream()
            .filter(comment -> comment.getParentId() != null)
            .collect(Collectors.groupingBy(SysNoticeComment::getParentId));

        for (SysNoticeComment comment : comments)
        {
            if (comment.getParentId() == null)
            {
                comment.setChildren(childrenMap.get(comment.getCommentId()));
                topComments.add(comment);
            }
        }

        return topComments;
    }
}

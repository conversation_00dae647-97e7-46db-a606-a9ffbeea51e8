import fileAccessManager from '../../utils/fileAccessManager.js'

const app = getApp();

Page({
  data: {
    detail: null,
    loading: true,
    timeDiff: ''
  },

  onLoad(options) {
    const { id } = options;
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    this.getDetail(id);
  },

  // 获取投诉建议详情
  async getDetail(id) {
    this.setData({ loading: true });
    try {
      const res = await app.request({
        url: '/api/wx/complaint/detail',
        method: 'POST',
        data: { id }
      });

      if (res.code === 0 && res.data) {
        const detail = this.processDetailData(res.data);
        const timeDiff = this.calculateTimeDiff(detail.create_time);
        this.setData({
          detail,
          timeDiff,
          loading: false
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: '投诉建议详情'
        });
      } else {
        throw new Error(res.msg || '获取详情失败');
      }
    } catch (error) {
      console.error('获取投诉建议详情失败:', error);
      wx.showToast({
        title: '获取详情失败',
        icon: 'none'
      });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 处理详情数据
  processDetailData(data) {
    let imageUrls = [];

    try {
      if (data.media_urls) {
        const parsed = JSON.parse(data.media_urls);
        // 直接提取图片URL
        imageUrls = parsed.map(media => media.url || media);
      }
    } catch (e) {
      console.warn('解析media_urls失败:', e);
      imageUrls = [];
    }

    return {
      ...data,
      imageUrls,
      statusText: this.getStatusText(data.status),
      statusColor: this.getStatusColor(data.status)
    };
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      '0': '待处理',
      '1': '处理中',
      '2': '已完成'
    };
    return statusMap[status] || '待处理';
  },

  // 获取状态颜色
  getStatusColor(status) {
    const colorMap = {
      '0': '#ff4d4f',  // 红色
      '1': '#faad14',  // 橙色
      '2': '#52c41a'   // 绿色
    };
    return colorMap[status] || '#ff4d4f';
  },

  // 计算时间差
  calculateTimeDiff(createTime) {
    if (!createTime) return '';
    
    try {
      const now = new Date();
      const create = new Date(createTime);
      const diff = now - create;
      
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (minutes < 60) {
        return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 30) {
        return `${days}天前`;
      } else {
        return '';
      }
    } catch (e) {
      return '';
    }
  },



  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    const { detail } = this.data;

    // 直接使用图片URL数组进行预览
    if (detail.imageUrls && detail.imageUrls.length > 0) {
      wx.previewImage({
        current: url,
        urls: detail.imageUrls
      });
    }
  },

  // 下载文件
  async onDownloadFile(e) {
    const { url, name, fileId, id } = e.currentTarget.dataset;

    try {
      wx.showLoading({ title: '下载中...' });

      // 优先使用fileId，然后是id，最后降级使用url
      const actualFileId = fileId || id;

      // 使用新的智能预览方法，基于文件ID
      await fileAccessManager.previewFile(actualFileId, url, name);

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('文件下载/打开失败:', error);
      wx.showToast({
        title: '下载失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  }
});

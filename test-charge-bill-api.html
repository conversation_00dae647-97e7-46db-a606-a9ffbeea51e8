<!DOCTYPE html>
<html>
<head>
    <title>测试缴费账单API</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>缴费账单API测试</h1>
    
    <div>
        <h3>1. 测试获取楼栋列表</h3>
        <button onclick="testGetBuildingList()">获取楼栋列表</button>
        <div id="buildingResult" class="result"></div>
    </div>
    
    <div>
        <h3>2. 测试获取房屋列表</h3>
        <input type="text" id="buildingId" placeholder="输入楼栋ID" value="56071">
        <button onclick="testGetBuildingHouseList()">获取房屋列表</button>
        <div id="houseResult" class="result"></div>
    </div>
    
    <div>
        <h3>3. 测试获取统计信息</h3>
        <button onclick="testGetChargeStatistics()">获取统计信息</button>
        <div id="statisticsResult" class="result"></div>
    </div>

    <script>
        // 模拟token（实际使用中需要真实的token）
        const mockToken = 'Bearer test-token';
        
        async function makeRequest(url, data = {}) {
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': mockToken
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }
        
        async function testGetBuildingList() {
            const result = await makeRequest('/api/wx/charge/bill/getBuildingList');
            document.getElementById('buildingResult').innerHTML = 
                '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
        
        async function testGetBuildingHouseList() {
            const buildingId = document.getElementById('buildingId').value;
            if (!buildingId) {
                alert('请输入楼栋ID');
                return;
            }
            
            const result = await makeRequest('/api/wx/charge/bill/getBuildingHouseList', {
                buildingId: buildingId
            });
            document.getElementById('houseResult').innerHTML = 
                '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
        
        async function testGetChargeStatistics() {
            const result = await makeRequest('/api/wx/charge/bill/getChargeStatistics');
            document.getElementById('statisticsResult').innerHTML = 
                '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
    </script>
</body>
</html>

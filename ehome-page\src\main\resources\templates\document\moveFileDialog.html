<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('移动文件')" />
    <th:block th:include="include :: ztree-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-move-file">
            <input type="hidden" name="fileIds" th:value="${fileIds}"/>
            <div class="form-group">    
                <label class="col-sm-3 control-label">选择目标文件夹：</label>
                <div class="col-sm-8">
                    <div style="border: 1px solid #ddd; height: 300px; overflow-y: auto; padding: 10px;">
                        <ul id="folderTree" class="ztree"></ul>
                    </div>
                    <input type="hidden" name="targetFolderId" id="targetFolderId"/>
                    <span class="help-block m-b-none">
                        <i class="fa fa-info-circle"></i> 请选择要移动到的目标文件夹，选择"根目录"表示移动到根目录下
                    </span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">当前选择：</label>
                <div class="col-sm-8">
                    <input type="text" id="selectedPath" class="form-control" value="根目录" readonly style="background-color: #f5f5f5;"/>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js" />
    <script type="text/javascript">
        var prefix = ctx + "document";
        var selectedFolderId = "";
        var selectedFolderName = "根目录";
        
        $(function() {
            loadFolderTree();
        });
        
        function loadFolderTree() {
            $.ajax({
                url: prefix + "/folderTree",
                type: "POST",
                success: function(res) {
                    if (res.code == 0) {
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true,
                                selectedMulti: false
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    selectedFolderId = treeNode.id;
                                    selectedFolderName = treeNode.name;
                                    $("#targetFolderId").val(selectedFolderId);
                                    $("#selectedPath").val(getFolderPath(treeNode));
                                }
                            }
                        };
                        
                        // 添加根目录节点
                        var treeData = [{
                            id: "",
                            pId: null,
                            name: "根目录",
                            type: "folder",
                            open: true
                        }];

                        if (res.data && res.data.length > 0) {
                            // 设置所有节点默认展开
                            res.data.forEach(function(node) {
                                node.open = true;
                            });
                            treeData = treeData.concat(res.data);
                        }

                        var treeObj = $.fn.zTree.init($("#folderTree"), setting, treeData);
                        // 展开所有节点
                        treeObj.expandAll(true);

                        // 默认选中根目录
                        var rootNode = treeObj.getNodeByParam("id", "");
                        if (rootNode) {
                            treeObj.selectNode(rootNode);
                        }
                    } else {
                        $.modal.alertError("加载文件夹树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载文件夹树失败：" + error);
                }
            });
        }
        
        // 获取文件夹路径
        function getFolderPath(treeNode) {
            if (!treeNode || treeNode.id === "") {
                return "根目录";
            }
            
            var path = treeNode.name;
            var parent = treeNode.getParentNode();
            while (parent && parent.id !== "") {
                path = parent.name + " / " + path;
                parent = parent.getParentNode();
            }
            return "根目录 / " + path;
        }
        
        function submitHandler() {
            var fileIds = $("input[name='fileIds']").val();
            var targetFolderId = $("#targetFolderId").val();
            
            if (!fileIds) {
                $.modal.alertWarning("没有选择要移动的文件");
                return;
            }
            
            $.ajax({
                url: prefix + "/moveFile",
                type: "POST",
                data: {
                    fileIds: fileIds,
                    targetFolderId: targetFolderId
                },
                success: function(res) {
                    if (res.code == 0) {
                        $.modal.msgSuccess("文件移动成功");
                        $.modal.closeAll();
                        // 通知父页面刷新
                        if (window.parent && window.parent.$.table) {
                            window.parent.$.table.refresh();
                        }
                    } else {
                        $.modal.alertError(res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("移动文件失败：" + error);
                }
            });
        }
    </script>
</body>
</html>

Page({
  data: {
    url: '',
    title: '',
    isTemp: false
  },

  onLoad(options) {
    const { url, title, isTemp } = options
    if (!url) {
      wx.showModal({
        title: '提示',
        content: 'URL参数无效',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
      return
    }

    this.setData({
      url: decodeURIComponent(url),
      title: title ? decodeURIComponent(title) : 'H5页面',
      isTemp: isTemp === 'true'
    })

    // 设置页面标题
    if (this.data.title) {
      wx.setNavigationBarTitle({
        title: this.data.title.length > 10
          ? this.data.title.substring(0, 10) + '...'
          : this.data.title
      })
    }
  },

  onUnload() {
    // 如果是临时文件，页面卸载时清理
    if (this.data.isTemp && this.data.url.startsWith('file://')) {
      const filePath = this.data.url.replace('file://', '')
      const fs = wx.getFileSystemManager()
      try {
        fs.unlinkSync(filePath)
        console.log('临时文件已清理:', filePath)
      } catch (error) {
        console.warn('清理临时文件失败:', error)
      }
    }
  },

  onWebViewMessage(e) {
    console.log('WebView消息:', e.detail.data)
  },

  onWebViewError(e) {
    console.error('WebView加载错误:', e.detail)
    const url = this.data.url

    wx.showModal({
      title: '加载失败',
      content: '页面加载失败，可能是域名未配置。是否复制链接到剪贴板？',
      confirmText: '复制链接',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 复制链接到剪贴板
          wx.setClipboardData({
            data: url,
            success: () => {
              wx.showToast({
                title: '链接已复制',
                icon: 'success'
              })
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            },
            fail: () => {
              wx.showModal({
                title: '复制失败',
                content: `请手动复制以下链接：\n${url}`,
                showCancel: false,
                confirmText: '知道了',
                success: () => {
                  wx.navigateBack()
                }
              })
            }
          })
        } else {
          wx.navigateBack()
        }
      }
    })
  }
})

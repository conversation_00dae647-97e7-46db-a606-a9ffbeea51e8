-- 为eh_wx_user表添加最后登录时间字段
-- 如果字段已存在则跳过，避免重复执行错误

-- 检查字段是否存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'eh_wx_user' 
         AND COLUMN_NAME = 'login_date') = 0,
        'ALTER TABLE eh_wx_user ADD COLUMN login_date datetime DEFAULT NULL COMMENT ''最后登录时间''',
        'SELECT ''字段login_date已存在，跳过添加'' as message'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查字段是否存在，如果不存在则添加login_ip字段
SET @sql2 = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'eh_wx_user' 
         AND COLUMN_NAME = 'login_ip') = 0,
        'ALTER TABLE eh_wx_user ADD COLUMN login_ip varchar(128) DEFAULT NULL COMMENT ''最后登录IP''',
        'SELECT ''字段login_ip已存在，跳过添加'' as message'
    )
);

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

package com.ehome.oc.controller.wxconfig;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务电话信息Controller
 */
@Controller
@RequestMapping("/oc/serviceTel")
public class ServiceTelController extends BaseController {

    private static final String PREFIX = "oc/serviceTel";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @GetMapping("/add")
    public String add(@RequestParam(required = false) String parentId, ModelMap mmap) {
        mmap.put("parentId", parentId);
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{serviceTelId}")
    public String edit(@PathVariable("serviceTelId") String serviceTelId, ModelMap mmap) {
        Record serviceTel = Db.findFirst("select * from eh_service_tel where service_tel_id = ?", serviceTelId);
        Map<String, Object> serviceTelMap = serviceTel.toMap();

        // 检查是否有子项
        int childCount = Db.queryInt("SELECT COUNT(*) FROM eh_service_tel WHERE parent_id = ?", serviceTelId);
        serviceTelMap.put("hasChildren", childCount > 0);

        mmap.put("serviceTel", serviceTelMap);
        return PREFIX + "/edit";
    }

    @GetMapping("/tree")
    public String tree(ModelMap mmap) {
        return PREFIX + "/tree";
    }

    @GetMapping("/vant-icons")
    public String vantIcons() {
        return PREFIX + "/vant-icons";
    }

    @PostMapping("/list")
    @ResponseBody
    public List<Map<String, Object>> list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        List<Record> records = Db.find("select *" + sql.toFullSql());

        // 转换为Map列表，TreeTable组件会自动构建树形关系
        List<Map<String, Object>> result = new ArrayList<>();

        // 统计每个服务电话的子项数量
        Map<Integer, Integer> childCountMap = new HashMap<>();
        for (Record record : records) {
            Integer parentId = record.getInt("parent_id");
            if (parentId != null && parentId != 0) {
                childCountMap.put(parentId, childCountMap.getOrDefault(parentId, 0) + 1);
            }
        }

        for (Record record : records) {
            Map<String, Object> map = record.toMap();
            Integer serviceTelId = record.getInt("service_tel_id");
            // 添加是否有子项的标识
            map.put("hasChildren", childCountMap.getOrDefault(serviceTelId, 0) > 0);
            result.add(map);
        }
        return result;
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        Integer serviceTelId = params.getInteger("service_tel_id");
        if (serviceTelId == null) {
            return AjaxResult.error("服务电话ID不能为空");
        }
        Record serviceTel = Db.findFirst("select * from eh_service_tel where service_tel_id = ?", serviceTelId);
        return AjaxResult.success(null, serviceTel.toMap());
    }

    @Log(title = "新增服务电话", businessType = BusinessType.INSERT)
    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        JSONObject params = getParams();
        Record serviceTel = new Record();
        serviceTel.setColumns(params);
        serviceTel.remove("service_tel_id");
        // service_tel_id 现在是自动递增，不需要手动设置

        // 处理parent_id
        Integer parentId = params.getInteger("parent_id");
        if (parentId != null && parentId != 0) {
            serviceTel.set("parent_id", parentId);

            // 验证层级（不能超过二级）
            AjaxResult levelCheck = validateLevel(parentId);
            if (!levelCheck.isSuccess()) {
                return levelCheck;
            }
        } else {
            serviceTel.set("parent_id", 0);
        }

        // 自动获取最大排序值+1（按社区和父级分组）
        Integer maxSort = Db.queryInt("SELECT IFNULL(MAX(sort_order), 0) FROM eh_service_tel WHERE community_id = ? AND parent_id = ?",
            getSysUser().getCommunityId(), serviceTel.getStr("parent_id"));
        serviceTel.set("sort_order", maxSort + 1);

        setCreateAndUpdateInfo(serviceTel);
        Db.save("eh_service_tel","service_tel_id",serviceTel);
        return AjaxResult.success();
    }

    @Log(title = "修改服务电话", businessType = BusinessType.UPDATE)
    @PostMapping("/editSave")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record serviceTel = new Record();
        serviceTel.setColumns(params);

        // 处理parent_id
        Integer parentId = params.getInteger("parent_id");
        if (parentId != null && parentId != 0) {
            Integer currentServiceTelId = params.getInteger("service_tel_id");
            serviceTel.set("parent_id", parentId);

            // 验证层级（不能超过二级）
            AjaxResult levelCheck = validateLevel(parentId);
            if (!levelCheck.isSuccess()) {
                return levelCheck;
            }

            // 验证不能将服务电话设置为自己的子项
            if (currentServiceTelId.equals(parentId)) {
                return AjaxResult.error("不能将服务电话设置为自己的子项");
            }
        } else {
            serviceTel.set("parent_id", 0);
        }

        setUpdateInfo(serviceTel);
        return toAjax(Db.update("eh_service_tel","service_tel_id",serviceTel));
    }

    @Log(title = "删除服务电话", businessType = BusinessType.DELETE)
    @RequestMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Integer serviceTelId = Integer.valueOf(id);
            // 检查是否有子项
            int childCount = Db.queryInt("SELECT COUNT(*) FROM eh_service_tel WHERE parent_id = ?", serviceTelId);
            if (childCount > 0) {
                Record serviceTel = Db.findFirst("SELECT service_name FROM eh_service_tel WHERE service_tel_id = ?", serviceTelId);
                String serviceName = serviceTel != null ? serviceTel.getStr("service_name") : "未知";
                return AjaxResult.error("服务电话[" + serviceName + "]存在子项，不能删除");
            }
            Db.deleteById("eh_service_tel","service_tel_id",serviceTelId);
        }
        return success();
    }

    @PostMapping("/checkTelNumber")
    @ResponseBody
    public boolean checkTelNumber() {
        JSONObject params = getParams();
        String telNumber = params.getString("tel_number");
        String communityId = getSysUser().getCommunityId();
        String serviceTelId = params.getString("service_tel_id");
        if (StringUtils.isEmpty(telNumber) || StringUtils.isEmpty(communityId)) {
            return false;
        }
        EasySQL sql = new EasySQL();
        sql.append("select * from eh_service_tel where 1=1");
        sql.append(telNumber, "and tel_number = ?");
        sql.append(serviceTelId, "and service_tel_id != ?");
        sql.append(communityId, "and community_id = ?");
        Record serviceTel = Db.findFirst(sql.getSQL(), sql.getParams());
        return serviceTel == null;
    }

    @Log(title = "批量更新服务电话排序", businessType = BusinessType.UPDATE)
    @PostMapping("/updateBatchSort")
    @ResponseBody
    public AjaxResult updateBatchSort() {
        JSONObject params = getParams();
        String serviceTelIds = params.getString("serviceTelIds");
        String sortOrders = params.getString("sortOrders");

        if (StringUtils.isEmpty(serviceTelIds) || StringUtils.isEmpty(sortOrders)) {
            return AjaxResult.error("参数不能为空");
        }

        String[] idArray = serviceTelIds.split(",");
        String[] sortArray = sortOrders.split(",");

        if (idArray.length != sortArray.length) {
            return AjaxResult.error("参数长度不匹配");
        }

        try {
            for (int i = 0; i < idArray.length; i++) {
                String serviceTelId = idArray[i].trim();
                String sortOrder = sortArray[i].trim();

                if (StringUtils.isNotEmpty(serviceTelId) && StringUtils.isNotEmpty(sortOrder)) {
                    Record serviceTel = new Record();
                    serviceTel.set("service_tel_id", serviceTelId);
                    serviceTel.set("sort_order", Integer.parseInt(sortOrder));
                    setUpdateInfo(serviceTel);
                    Db.update("eh_service_tel", "service_tel_id", serviceTel);
                }
            }
            return AjaxResult.success("排序更新成功");
        } catch (Exception e) {
            logger.error("批量更新排序失败: " + e.getMessage(), e);
            return AjaxResult.error("批量更新排序失败: " + e.getMessage());
        }
    }

    /**
     * 获取顶级分类列表（用于导入时选择）
     */
    @GetMapping("/getTopCategories")
    @ResponseBody
    public AjaxResult getTopCategories() {
        JSONObject params = getParams();
        String telType = params.getString("tel_type");

        EasySQL sql = new EasySQL();
        sql.append("from eh_service_tel t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.append("and t1.parent_id = '0'"); // 只返回顶级分类
        sql.append("and t1.status = '0'"); // 只返回启用的分类
        sql.append(telType, "and t1.tel_type = ?");
        sql.append("order by t1.sort_order, t1.create_time desc");

        List<Record> records = Db.find("select service_tel_id, service_name" + sql.toFullSql());

        List<Map<String, Object>> result = new ArrayList<>();
        for (Record record : records) {
            Map<String, Object> map = new HashMap<>();
            map.put("service_tel_id", record.getStr("service_tel_id"));
            map.put("service_name", record.getStr("service_name"));
            result.add(map);
        }
        return AjaxResult.success(result);
    }

    @Log(title = "批量导入服务电话", businessType = BusinessType.INSERT)
    @PostMapping("/batchImport")
    @ResponseBody
    public AjaxResult batchImport(@RequestBody Map<String, Object> requestData) {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> importData = (List<Map<String, String>>) requestData.get("importData");
        String parentId = (String) requestData.get("parentId");

        if (importData == null || importData.isEmpty()) {
            return AjaxResult.error("导入数据不能为空");
        }

        if (StringUtils.isEmpty(parentId)) {
            return AjaxResult.error("请选择要导入到的分类");
        }

        try {
            String communityId = getSysUser().getCommunityId();
            String loginName = getSysUser().getLoginName();
            String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);

            // 获取当前最大排序值（按父级分组）
            Integer maxSort = Db.queryInt("SELECT IFNULL(MAX(sort_order), 0) FROM eh_service_tel WHERE community_id = ? AND parent_id = ?", communityId, parentId);

            int successCount = 0;
            int skipCount = 0;

            for (Map<String, String> item : importData) {
                String serviceName = item.get("serviceName");
                String telNumber = item.get("telNumber");

                if (StringUtils.isEmpty(serviceName) || StringUtils.isEmpty(telNumber)) {
                    skipCount++;
                    continue;
                }

                // 检查电话号码是否已存在
                Record existingRecord = Db.findFirst("SELECT * FROM eh_service_tel WHERE tel_number = ? AND community_id = ?", telNumber, communityId);
                if (existingRecord != null) {
                    skipCount++;
                    continue;
                }

                // 创建新记录
                Record serviceTel = new Record();
                serviceTel.set("parent_id", parentId); // 导入到指定的父级分类下
                serviceTel.set("community_id", communityId);
                serviceTel.set("tel_number", telNumber);
                serviceTel.set("tel_type", "external"); // 批量导入默认为外部号码
                serviceTel.set("service_name", serviceName);
                serviceTel.set("company_name", "");
                serviceTel.set("icon_url", "");
                serviceTel.set("sort_order", ++maxSort);
                serviceTel.set("status", "0");
                serviceTel.set("create_time", now);
                serviceTel.set("update_time", now);
                serviceTel.set("create_by", loginName);
                serviceTel.set("update_by", loginName);
                serviceTel.set("remark", "批量导入");

                Db.save("eh_service_tel", "service_tel_id", serviceTel);
                successCount++;
            }

            String message = String.format("导入完成！成功导入 %d 条，跳过 %d 条（重复或格式错误）", successCount, skipCount);
            return AjaxResult.success(message);

        } catch (Exception e) {
            logger.error("批量导入服务电话失败: " + e.getMessage(), e);
            return AjaxResult.error("批量导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务电话树数据（用于父级选择）
     */
    @GetMapping("/serviceTelTreeData")
    @ResponseBody
    public List<Map<String, Object>> serviceTelTreeData() {
        JSONObject params = getParams();
        String telType = params.getString("tel_type");

        EasySQL sql = new EasySQL();
        sql.append("from eh_service_tel t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.append("and t1.parent_id = '0'"); // 只返回一级分类，用于父级选择
        sql.append(telType, "and t1.tel_type = ?"); // 根据号码类型过滤
        sql.append("order by t1.sort_order, t1.create_time desc");

        List<Record> records = Db.find("select service_tel_id, service_name, parent_id" + sql.toFullSql());

        List<Map<String, Object>> result = new ArrayList<>();
        for (Record record : records) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", record.getInt("service_tel_id"));
            map.put("name", record.getStr("service_name"));
            map.put("pId", record.getInt("parent_id"));
            result.add(map);
        }
        return result;
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_service_tel t1");
        sql.append("where 1=1");

        sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?");
        sql.appendLike(params.getString("tel_number"), "and t1.tel_number like ?");
        sql.appendLike(params.getString("service_name"), "and t1.service_name like ?");
        sql.appendLike(params.getString("company_name"), "and t1.company_name like ?");
        sql.append(params.getString("status"), "and t1.status = ?");
        sql.append(params.getString("tel_type"), "and t1.tel_type = ?","internal");//默认内部号码

        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and t1.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and t1.create_time <= ?");

        sql.append("order by t1.parent_id, t1.sort_order asc, t1.create_time desc");
        return sql;
    }

    /**
     * 验证层级（不能超过二级）
     */
    private AjaxResult validateLevel(Integer parentId) {
        if (parentId == null || parentId == 0) {
            return AjaxResult.success();
        }

        // 检查父级的parent_id，如果不为0，说明已经是二级了，不能再添加子级
        Record parent = Db.findFirst("SELECT parent_id FROM eh_service_tel WHERE service_tel_id = ?", parentId);
        if (parent != null && parent.getInt("parent_id") != 0) {
            return AjaxResult.error("不能超过二级目录");
        }

        return AjaxResult.success();
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
        record.set("community_id", getSysUser().getCommunityId());
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }
}

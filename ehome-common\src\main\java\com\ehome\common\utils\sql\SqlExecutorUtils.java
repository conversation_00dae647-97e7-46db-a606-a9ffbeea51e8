package com.ehome.common.utils.sql;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SQL执行工具类
 * 提供批量SQL执行、事务管理等功能
 * 
 * <AUTHOR>
 */
public class SqlExecutorUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlExecutorUtils.class);
    
    /**
     * SQL执行结果
     */
    public static class ExecutionResult {
        private boolean success;
        private int totalStatements;
        private int executedStatements;
        private List<String> errors;
        private String message;
        
        public ExecutionResult() {
            this.errors = new ArrayList<>();
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getTotalStatements() { return totalStatements; }
        public void setTotalStatements(int totalStatements) { this.totalStatements = totalStatements; }
        
        public int getExecutedStatements() { return executedStatements; }
        public void setExecutedStatements(int executedStatements) { this.executedStatements = executedStatements; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public void addError(String error) {
            this.errors.add(error);
        }
    }
    
    /**
     * 执行SQL模板
     * 
     * @param templateName 模板文件名
     * @param params 参数映射
     * @return 执行结果
     */
    public static ExecutionResult executeTemplate(String templateName, Map<String, Object> params) {
        ExecutionResult result = new ExecutionResult();
        
        try {
            // 解析SQL模板
            List<String> sqlList = SqlTemplateUtils.parseSqlTemplate(templateName, params);
            result.setTotalStatements(sqlList.size());
            
            if (sqlList.isEmpty()) {
                result.setSuccess(true);
                result.setMessage("模板文件为空，无需执行");
                return result;
            }
            
            // 在事务中执行所有SQL
            boolean success = Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {
                    int executed = 0;
                    
                    for (String sql : sqlList) {
                        try {
                            logger.debug("执行SQL: {}", sql);
                            int affectedRows = Db.update(sql);
                            executed++;
                            logger.debug("SQL执行成功，影响行数: {}", affectedRows);
                        } catch (Exception e) {
                            String error = String.format("SQL执行失败: %s, 错误: %s", sql, e.getMessage());
                            logger.error(error, e);
                            result.addError(error);
                            return false; // 回滚事务
                        }
                    }
                    
                    result.setExecutedStatements(executed);
                    return true;
                }
            });
            
            result.setSuccess(success);
            if (success) {
                result.setMessage(String.format("成功执行%d条SQL语句", result.getExecutedStatements()));
                logger.info("SQL模板执行成功: {}, 执行{}条语句", templateName, result.getExecutedStatements());
            } else {
                result.setMessage("SQL执行失败，事务已回滚");
                logger.error("SQL模板执行失败: {}", templateName);
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("模板解析或执行异常: " + e.getMessage());
            result.addError(e.getMessage());
            logger.error("SQL模板执行异常: {}", templateName, e);
        }
        
        return result;
    }
    
    /**
     * 批量执行多个SQL语句（在同一事务中）
     * 
     * @param sqlList SQL语句列表
     * @return 执行结果
     */
    public static ExecutionResult executeBatch(List<String> sqlList) {
        ExecutionResult result = new ExecutionResult();
        result.setTotalStatements(sqlList.size());
        
        if (sqlList.isEmpty()) {
            result.setSuccess(true);
            result.setMessage("SQL列表为空，无需执行");
            return result;
        }
        
        try {
            boolean success = Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {
                    int executed = 0;
                    
                    for (String sql : sqlList) {
                        try {
                            logger.debug("执行SQL: {}", sql);
                            int affectedRows = Db.update(sql);
                            executed++;
                            logger.debug("SQL执行成功，影响行数: {}", affectedRows);
                        } catch (Exception e) {
                            String error = String.format("SQL执行失败: %s, 错误: %s", sql, e.getMessage());
                            logger.error(error, e);
                            result.addError(error);
                            return false;
                        }
                    }
                    
                    result.setExecutedStatements(executed);
                    return true;
                }
            });
            
            result.setSuccess(success);
            if (success) {
                result.setMessage(String.format("成功执行%d条SQL语句", result.getExecutedStatements()));
                logger.info("批量SQL执行成功，执行{}条语句", result.getExecutedStatements());
            } else {
                result.setMessage("SQL执行失败，事务已回滚");
                logger.error("批量SQL执行失败");
            }
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("SQL执行异常: " + e.getMessage());
            result.addError(e.getMessage());
            logger.error("批量SQL执行异常", e);
        }
        
        return result;
    }
    
    /**
     * 执行单条SQL语句
     * 
     * @param sql SQL语句
     * @return 执行结果
     */
    public static ExecutionResult executeSingle(String sql) {
        ExecutionResult result = new ExecutionResult();
        result.setTotalStatements(1);
        
        try {
            logger.debug("执行SQL: {}", sql);
            int affectedRows = Db.update(sql);
            result.setExecutedStatements(1);
            result.setSuccess(true);
            result.setMessage(String.format("SQL执行成功，影响行数: %d", affectedRows));
            logger.info("SQL执行成功，影响行数: {}", affectedRows);
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("SQL执行失败: " + e.getMessage());
            result.addError(e.getMessage());
            logger.error("SQL执行失败: {}", sql, e);
        }
        
        return result;
    }
    
    /**
     * 检查数据是否已存在（避免重复初始化）
     * 
     * @param checkSql 检查SQL（应返回数量）
     * @return 数据是否存在
     */
    public static boolean dataExists(String checkSql) {
        try {
            Long count = Db.queryLong(checkSql);
            return count != null && count > 0;
        } catch (Exception e) {
            logger.error("检查数据存在性失败: {}", checkSql, e);
            return false;
        }
    }
    
    /**
     * 检查表是否存在
     * 
     * @param tableName 表名
     * @return 表是否存在
     */
    public static boolean tableExists(String tableName) {
        String checkSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '" + tableName + "'";
        return dataExists(checkSql);
    }
}

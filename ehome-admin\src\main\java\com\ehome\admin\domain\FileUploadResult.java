package com.ehome.admin.domain;

import com.ehome.common.json.JSONObject;
import com.ehome.common.utils.DateUtils;

/**
 * 文件上传结果
 * 
 * <AUTHOR>
 */
public class FileUploadResult {

    private String fileId;
    /** 文件名 */
    private String fileName;

    private String businessType;

    private String businessId;
    

    /** 文件绝对路径 */
    private String absolutePath;

    /** OSS访问URL */
    private String ossUrl;

    private String accessUrl;
    
    /** OSS存储键名 */
    private String ossKey;
    
    /** 存储类型 */
    private String storageType;
    
    /** 原始文件名 */
    private String originalFilename;
    
    /** 文件大小 */
    private long fileSize;
    
    /** 文件类型 */
    private String fileType;
    
    /** MIME类型 */
    private String mimeType;

    private String folderId;

    private String communityId;
    
    public FileUploadResult() {
    }
    
    public FileUploadResult(String fileName, String accessUrl, String absolutePath, String ossUrl, String ossKey,
                           String storageType, String originalFilename, long fileSize,
                           String fileType, String mimeType) {
        this.fileName = fileName;
        this.accessUrl = accessUrl;
        this.absolutePath = absolutePath;
        this.ossUrl = ossUrl;
        this.ossKey = ossKey;
        this.storageType = storageType;
        this.originalFilename = originalFilename;
        this.fileSize = fileSize;
        this.fileType = fileType;
        this.mimeType = mimeType;
    }
    
    /**
     * 获取最终访问URL（优先OSS URL）
     */
    public String getFinalUrl() {
        return ossUrl != null ? ossUrl : accessUrl;
    }
    
    // Getters and Setters
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    

    public String getAbsolutePath() {
        return absolutePath;
    }

    public void setAbsolutePath(String absolutePath) {
        this.absolutePath = absolutePath;
    }

    public String getOssUrl() {
        return ossUrl;
    }
    
    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }
    
    public String getOssKey() {
        return ossKey;
    }
    
    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }
    
    public String getStorageType() {
        return storageType;
    }
    
    public void setStorageType(String storageType) {
        this.storageType = storageType;
    }
    
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public String getMimeType() {
        return mimeType;
    }
    
    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getBusinessType() {
        return businessType;
    }
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessId() {
        return businessId;
    }
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public String getFileId() {
        return fileId;
    }
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFolderId() {
        return folderId;
    }

    public void setFolderId(String folderId) {
        this.folderId = folderId;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getAccessUrl() {
        return accessUrl;
    }

    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }

    public JSONObject getJson() {
        JSONObject json = new JSONObject();
        json.put("accessUrl", accessUrl);
        json.put("absolutePath", absolutePath);
        json.put("ossUrl", ossUrl);
        json.put("ossKey", ossKey);
        json.put("storageType", storageType);
        json.put("originalFilename", originalFilename);
        json.put("fileName", originalFilename);
        json.put("fileSize", fileSize);
        json.put("fileType", fileType);
        json.put("mimeType", mimeType);
        json.put("businessType", businessType);
        json.put("source", businessType);
        json.put("businessId", businessId);
        json.put("fileId", fileId);
        json.put("id", fileId);
        json.put("finalUrl", getFinalUrl());
        json.put("fileUrl", getFinalUrl());
        json.put("url", accessUrl);
        json.put("folderId", folderId);
        json.put("communityId", communityId);
        json.put("uploadTime", DateUtils.getTime());
        return json;
    }
}


package com.ehome.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云OSS配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {
    
    /** 是否启用OSS */
    private boolean enabled = false;
    
    /** 访问端点 */
    private String endpoint;
    
    /** 访问密钥ID */
    private String accessKeyId;
    
    /** 访问密钥Secret */
    private String accessKeySecret;
    
    /** 私有存储空间名称 */
    private String bucketName;

    /** 公共读存储空间名称 */
    private String publicBucketName;

    /** 自定义域名 */
    private String domain;

    /** 公共读存储空间自定义域名 */
    private String publicDomain;
    
    /** 文件路径前缀 */
    private String pathPrefix = "uploads";

    /** 预签名URL过期时间（秒），默认7天 */
    private long urlExpiration = 7 * 24 * 60 * 60;
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getEndpoint() {
        return endpoint;
    }
    
    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }
    
    public String getAccessKeyId() {
        return accessKeyId;
    }
    
    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }
    
    public String getAccessKeySecret() {
        return accessKeySecret;
    }
    
    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
    
    public String getBucketName() {
        return bucketName;
    }
    
    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getPublicBucketName() {
        return publicBucketName;
    }

    public void setPublicBucketName(String publicBucketName) {
        this.publicBucketName = publicBucketName;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getPublicDomain() {
        return publicDomain;
    }

    public void setPublicDomain(String publicDomain) {
        this.publicDomain = publicDomain;
    }
    
    public String getPathPrefix() {
        return pathPrefix;
    }
    
    public void setPathPrefix(String pathPrefix) {
        this.pathPrefix = pathPrefix;
    }

    public long getUrlExpiration() {
        return urlExpiration;
    }

    public void setUrlExpiration(long urlExpiration) {
        this.urlExpiration = urlExpiration;
    }
    
    /**
     * 获取完整的访问域名（私有Bucket）
     */
    public String getFullDomain() {
        return getFullDomain(false);
    }

    /**
     * 获取完整的访问域名
     *
     * @param isPublic 是否为公共Bucket
     */
    public String getFullDomain(boolean isPublic) {
        if (isPublic) {
            // 公共Bucket域名
            if (publicDomain != null && !publicDomain.isEmpty()) {
                return publicDomain.startsWith("http") ? publicDomain : "https://" + publicDomain;
            }
            String bucket = publicBucketName != null ? publicBucketName : bucketName;
            return "https://" + bucket + "." + endpoint;
        } else {
            // 私有Bucket域名
            if (domain != null && !domain.isEmpty()) {
                return domain.startsWith("http") ? domain : "https://" + domain;
            }
            return "https://" + bucketName + "." + endpoint;
        }
    }
    
    /**
     * 验证配置是否完整
     */
    public boolean isConfigValid() {
        return enabled &&
               endpoint != null && !endpoint.isEmpty() &&
               accessKeyId != null && !accessKeyId.isEmpty() &&
               accessKeySecret != null && !accessKeySecret.isEmpty() &&
               bucketName != null && !bucketName.isEmpty();
    }

    /**
     * 验证公共Bucket配置是否完整
     */
    public boolean isPublicBucketConfigValid() {
        return isConfigValid() &&
               publicBucketName != null && !publicBucketName.isEmpty();
    }
}

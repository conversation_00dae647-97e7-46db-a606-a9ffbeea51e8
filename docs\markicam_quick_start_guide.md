# Markicam API集成快速开始指南

## 概述

本指南将帮助您快速部署和使用Markicam API数据同步系统。

## 前置条件

1. ✅ 已部署ehome系统
2. ✅ 已添加OkHttp3依赖到ehome-oc模块
3. ✅ 拥有Markicam平台的组织ID和API密钥

## 快速开始

### 步骤1：初始化数据库

数据库表已预先创建，包含以下表：
- `eh_markicam_config` - API配置表
- `eh_markicam_team` - 团队信息表
- `eh_markicam_moment` - 照片视频表
- `eh_markicam_member` - 成员表
- `eh_markicam_illegal_park` - 违规车辆表
- `eh_markicam_sync_log` - 同步日志表

### 步骤2：配置API信息

1. 访问配置管理页面：`http://your-domain/oc/markicam/config`
2. 点击"新增配置"
3. 填写以下信息：
   - **组织ID**: 您的Markicam组织ID
   - **API密钥**: 从Markicam平台申请的API密钥
   - **API基础URL**: `https://open-api.markiapp.com`（默认）
   - **同步间隔**: 3600秒（默认）
   - **状态**: 启用
4. 点击"保存"
5. 点击"测试连接"验证配置

### 步骤3：开始同步数据

#### 手动同步
1. 访问主页面：`http://your-domain/oc/markicam/index`
2. 使用快速同步按钮：
   - 照片视频：同步照片和视频数据
   - 团队成员：同步成员信息
   - 违规车辆：同步违规停车记录
3. 或点击"同步所有数据"一次性同步所有类型

#### 查看同步结果
- **照片视频**: `/oc/markicam/moment`
- **团队成员**: `/oc/markicam/member`
- **违规车辆**: `/oc/markicam/illegal`
- **同步日志**: `/oc/markicam/log`

## 功能说明

### 主要功能模块

1. **配置管理**
   - API配置的增删改查
   - 连接测试和状态监控
   - 多社区配置支持

2. **数据同步**
   - 照片视频数据同步
   - 团队成员信息同步
   - 违规车辆记录同步
   - 批量同步和增量同步

3. **数据查看**
   - 分页列表展示
   - 条件搜索过滤
   - 详情查看和预览
   - 数据导出功能

4. **监控管理**
   - 同步状态监控
   - 数据统计报表
   - 同步日志查看
   - 错误信息追踪

### API接口说明

系统会调用以下Markicam API接口：

| 接口路径 | 功能 | 对应数据表 |
|----------|------|------------|
| `/marki/moment` | 获取照片视频 | eh_markicam_moment |
| `/marki/team/mem` | 获取团队成员 | eh_markicam_member |
| `/marki/illegal_park` | 获取违规车辆 | eh_markicam_illegal_park |

## 故障排除

### 常见问题

1. **数据库表不存在**
   - 错误信息：`Table 'eh_markicam_xxx' doesn't exist`
   - 解决方案：联系管理员确认数据库表已正确创建

2. **API连接失败**
   - 错误信息：`连接测试失败`
   - 检查项：
     - 组织ID和API密钥是否正确
     - 网络连接是否正常
     - API基础URL是否正确

3. **同步数据为空**
   - 可能原因：
     - 时间范围内没有数据
     - 团队ID不存在
     - API权限不足
   - 解决方案：检查API参数和权限

4. **空指针异常**
   - 错误信息：`NullPointerException`
   - 可能原因：API返回数据格式变化
   - 解决方案：检查日志中的API响应格式

### 日志查看

1. **应用日志**
   - 位置：`logs/ehome.log`
   - 包含：同步过程、错误信息、API调用详情

2. **SQL日志**
   - 位置：`logs/sql.log`
   - 包含：数据库操作、执行时间、参数信息

3. **同步日志**
   - 位置：数据库表 `eh_markicam_sync_log`
   - 包含：同步状态、数量、耗时、错误信息

## 性能优化

### 同步策略

1. **增量同步**
   - 使用时间范围参数避免重复同步
   - 记录最后同步时间

2. **分页处理**
   - 大数据量时分页获取
   - 避免内存溢出

3. **错误重试**
   - 网络异常时自动重试
   - 记录失败原因便于排查

### 数据库优化

1. **索引优化**
   - 为常用查询字段添加索引
   - 定期分析查询性能

2. **数据清理**
   - 定期清理旧的同步日志
   - 归档历史数据

## 扩展功能

### 定时同步

可以配置定时任务实现自动同步：

```java
@Scheduled(fixedRate = 3600000) // 每小时执行一次
public void autoSync() {
    // 获取所有启用的配置
    // 执行数据同步
}
```

### 消息通知

同步失败时发送通知：

```java
// 邮件通知
// 短信通知
// 钉钉/企微通知
```

### 数据分析

基于同步的数据进行分析：

```java
// 违规趋势分析
// 巡检效率统计
// 团队活跃度分析
```

## 技术支持

如果遇到问题，请：

1. 查看应用日志和SQL日志
2. 检查同步日志表中的错误信息
3. 验证API配置和网络连接
4. 确认数据库表结构完整

## 更新日志

### v1.0.0 (2025-01-08)
- ✅ 完整的数据库设计
- ✅ OkHttp3集成
- ✅ 前端管理界面
- ✅ 分页查询优化
- ✅ 数据库初始化工具

---

**注意**: 请确保API密钥的安全性，不要在日志中记录敏感信息。

import { getUpdateManager } from '../../utils/updateManager.js'

// 调试状态存储键名
const DEBUG_STATUS_KEY = 'debug_enabled_status'

Page({
  data: {
    updateStatus: '点击检查',
    showUpdateCheck: false, // 是否显示检查更新菜单
    debugEnabled: false, // 调试状态
    debugText: '打开调试' // 调试按钮文本
  },

  onLoad() {
    // 检查更新功能支持
    this.checkUpdateSupport()
    // 初始化调试状态
    this.initDebugStatus()

  },

  // 初始化调试状态
  initDebugStatus() {
    try {
      // 从本地存储读取调试状态
      const debugEnabled = wx.getStorageSync(DEBUG_STATUS_KEY) || false

      this.setData({
        debugEnabled: debugEnabled,
        debugText: debugEnabled ? '关闭调试' : '打开调试'
      })

      console.log('[More] 初始化调试状态:', debugEnabled)
    } catch (error) {
      console.error('[More] 读取调试状态失败:', error)
      // 默认状态
      this.setData({
        debugEnabled: false,
        debugText: '打开调试'
      })
    }
  },

  // 网络诊断
  goToNetworkDiagnosis() {
    wx.navigateTo({
      url: '/pages/network/index'
    })
  },

  // 系统权限设置
  goToSystemSettings() {
    wx.openSetting({
      success(res) {
        console.log('用户已进入设置页', res.authSetting);
        // res.authSetting 包含用户当前授权状态
        // 例如：res.authSetting['scope.userLocation'] 表示位置权限是否开启
      },
      fail(err) {
        console.error('跳转失败', err);
        wx.showToast({
          title: '打开设置失败',
          icon: 'none'
        });
      }
    });
  },

  // 检查是否支持更新功能
  checkUpdateSupport() {
    try {
      const updateManager = getUpdateManager()
      const status = updateManager.getStatus()

      this.setData({
        showUpdateCheck: status.isSupported
      })

      console.log('[More] 更新检查支持状态:', status.isSupported)
    } catch (error) {
      console.error('[More] 检查更新支持失败:', error)
      this.setData({
        showUpdateCheck: false
      })
    }
  },

  // 检查更新
  checkForUpdate() {
    try {
      const updateManager = getUpdateManager()
      const status = updateManager.getStatus()

      if (!status.isSupported) {
        wx.showToast({
          title: '当前微信版本不支持检查更新',
          icon: 'none',
          duration: 3000
        })
        return
      }

      this.setData({ updateStatus: '检查中...' })

      // 调用检查更新
      updateManager.checkForUpdate()

      // 2秒后重置状态文本
      setTimeout(() => {
        this.setData({ updateStatus: '点击检查' })
      }, 2000)

    } catch (error) {
      console.error('[More] 检查更新失败:', error)
      this.setData({ updateStatus: '检查失败' })

      setTimeout(() => {
        this.setData({ updateStatus: '点击检查' })
      }, 2000)
    }
  },

  // 切换调试模式
  toggleDebug() {
    const newDebugState = !this.data.debugEnabled

    wx.setEnableDebug({
      enableDebug: newDebugState,
      success: () => {
        // 保存调试状态到本地存储
        try {
          wx.setStorageSync(DEBUG_STATUS_KEY, newDebugState)
        } catch (error) {
          console.error('[More] 保存调试状态失败:', error)
        }

        this.setData({
          debugEnabled: newDebugState,
          debugText: newDebugState ? '关闭调试' : '打开调试'
        })

        wx.showToast({
          title: newDebugState ? '调试已打开' : '调试已关闭',
          icon: 'success',
          duration: 2000
        })

        console.log('[More] 调试模式已', newDebugState ? '打开' : '关闭')
      },
      fail: (err) => {
        console.error('[More] 设置调试模式失败:', err)
        wx.showToast({
          title: '设置失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 重启小程序
  restartMiniProgram() {
    wx.showModal({
      title: '重启小程序',
      content: '确定要重启小程序吗？',
      confirmText: '重启',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          console.log('[More] 用户确认重启小程序')

          // 检查API支持
          if (typeof wx.restartMiniProgram === 'function') {
            wx.restartMiniProgram({
              path: '/pages/index/index', // 重启到首页
              success: () => {
                console.log('[More] 小程序重启成功')
              },
              fail: (err) => {
                console.error('[More] 小程序重启失败:', err)
                wx.showToast({
                  title: '重启失败',
                  icon: 'none',
                  duration: 2000
                })
              }
            })
          } else {
            // API不支持时的提示
            wx.showToast({
              title: '当前微信版本不支持重启功能',
              icon: 'none',
              duration: 3000
            })
            console.warn('[More] wx.restartMiniProgram API不支持')
          }
        } else {
          console.log('[More] 用户取消重启小程序')
        }
      }
    })
  }
})

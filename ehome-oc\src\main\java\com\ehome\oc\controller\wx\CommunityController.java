package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.oc.service.ICommunityService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/wx/community")
public class CommunityController extends BaseWxController {

    @Autowired
    private ICommunityService communityService;

    @GetMapping("/list")
    public AjaxResult list() {
        try {
            return AjaxResult.success(communityService.selectCommunityList());
        } catch (Exception e) {
            return AjaxResult.error("获取小区列表失败: " + e.getMessage());
        }
    }

    @RequestMapping("/ocInfo")
    public AjaxResult ocInfo(){
        Record record =  Db.findFirst("select * from eh_community where oc_id = ?",getCurrentUser().getCommunityId());
        return AjaxResult.success(record.toMap());
    }
} 
-- 创建菜单点击日志表
DROP TABLE IF EXISTS `sys_menu_click_log`;
CREATE TABLE `sys_menu_click_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `menu_id` varchar(32) NOT NULL COMMENT '菜单ID',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_type` varchar(20) DEFAULT '' COMMENT '菜单类型(text/pdf/url/page/miniprogram)',
  `user_id` varchar(32) DEFAULT NULL COMMENT '用户ID（系统用户）',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户名称',
  `first_click_time` datetime DEFAULT NULL COMMENT '首次点击时间',
  `last_click_time` datetime NOT NULL COMMENT '最后点击时间',
  `click_date` date NOT NULL COMMENT '点击日期（用于每天只记录一次）',
  `click_count` int DEFAULT 1 COMMENT '当天点击次数',
  `ip_address` varchar(128) DEFAULT '' COMMENT 'IP地址',
  `owner_id` varchar(32) DEFAULT '' COMMENT '业主ID',
  `house_id` varchar(32) DEFAULT '' COMMENT '房屋ID',
  `house_name` varchar(200) DEFAULT '' COMMENT '房屋名称',
  `community_id` varchar(32) DEFAULT '' COMMENT '小区ID',
  `source` varchar(20) DEFAULT '' COMMENT '来源页面(index/nav/mine)',
  PRIMARY KEY (`log_id`) USING BTREE,
  UNIQUE KEY `uk_menu_user_date` (`menu_id`, `user_id`, `click_date`) USING BTREE,
  INDEX `idx_menu_id` (`menu_id`) USING BTREE,
  INDEX `idx_user_id` (`user_id`) USING BTREE,
  INDEX `idx_click_date` (`click_date`) USING BTREE,
  INDEX `idx_community_id` (`community_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单点击日志表' ROW_FORMAT = Dynamic;

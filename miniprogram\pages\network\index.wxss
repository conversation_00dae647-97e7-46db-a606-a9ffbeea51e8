/* pages/network/index.wxss */
page {
  background: #f7f8fa;
  height: 100vh;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 提示区域 */
.tip-section {
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #856404;
  line-height: 1.5;
}

/* 分区标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 30rpx 0 20rpx 0;
}

/* 信息区域 */
.info-section {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 网络测速区域 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 20rpx;
}

.speed-item {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.speed-item:last-child {
  border-bottom: none;
}

.speed-url {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-family: monospace;
}

.speed-result {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-text {
  font-size: 26rpx;
  margin-right: 20rpx;
}

.time-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
  text-align: right;
  margin-right: 20rpx;
}

/* 按钮区域 */
.button-container {
  margin-top: 40rpx;
  padding: 0 20rpx 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 重置缓存按钮特殊样式 */
.button-container .van-button--warning {
  background: linear-gradient(135deg, #ff6900, #ff8f00);
  border: none;
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 0, 0.3);
}

/* 重置说明 */
.reset-tip {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #fff3e0;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff6900;
}

.reset-tip-text {
  font-size: 24rpx;
  color: #e65100;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .speed-result {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .time-text {
    text-align: left;
    margin-right: 0;
    margin-top: 8rpx;
  }
}

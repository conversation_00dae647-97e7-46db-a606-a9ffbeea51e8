/* pages/serviceTel/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom:20rpx;
}

/* 搜索容器 */
.search-container {
  background-color: #fff;
  padding: 0rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
  padding: 80rpx 40rpx 40rpx;
}

.retry-btn {
  margin-top: 20rpx;
}

/* 标签卡内容 */
.tab-content {
  min-height: calc(100vh - 88rpx);
}

/* 联系人列表 */
.contacts-list {
  padding: 0 0 40rpx 0;
}

.contact-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #646566;
  padding: 24rpx 32rpx 16rpx;
  background-color: #f7f8fa;
}

/* 联系人项目 */
.contact-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s ease;
}

/* 移除整个item的点击效果，只保留中间信息区域的点击效果 */

.contact-item:last-child {
  border-bottom: none;
}

/* 联系人头像 */
.contact-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

/* 头像文字 */
.avatar-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}

/* 联系人信息 */
.contact-info {
  flex: 1;
  min-width: 0;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.contact-info:active {
  background-color: #f2f3f5;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #323233;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.contact-company {
  font-size: 26rpx;
  color: #646566;
  line-height: 1.4;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.contact-phone {
  font-size: 28rpx;
  color: #1989fa;
  line-height: 1.4;
  font-weight: 500;
}

/* 联系人操作按钮 */
.contact-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.call-btn {
  background-color: #e8f3ff;
}

.call-btn:active {
  background-color: #d1e9ff;
}

.more-btn {
  background-color: #f7f8fa;
}

.more-btn:active {
  background-color: #ebedf0;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .contact-item {
    padding: 20rpx 24rpx;
  }

  .contact-avatar {
    width: 72rpx;
    height: 72rpx;
    margin-right: 20rpx;
  }

  .contact-name {
    font-size: 30rpx;
  }

  .contact-company {
    font-size: 24rpx;
  }

  .contact-phone {
    font-size: 26rpx;
  }

  .action-btn {
    width: 56rpx;
    height: 56rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }

  .section-title {
    color: #969799;
    background-color: #1a1a1a;
  }

  .contact-item {
    background: #2a2a2a;
    border-bottom-color: #333;
  }

  .contact-item:active {
    background-color: #333;
  }

  .contact-name {
    color: #e5e5e5;
  }

  .contact-company {
    color: #969799;
  }

  .call-btn {
    background-color: #1a3a5c;
  }

  .call-btn:active {
    background-color: #0f2a47;
  }

  .more-btn {
    background-color: #333;
  }

  .more-btn:active {
    background-color: #444;
  }
}

package com.ehome.common.utils.sql;

import com.ehome.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL模板工具类
 * 用于读取和解析SQL模板文件，支持参数替换和注释过滤
 * 
 * <AUTHOR>
 */
public class SqlTemplateUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlTemplateUtils.class);
    
    /**
     * SQL模板文件根目录
     */
    private static final String TEMPLATE_ROOT = "sql-templates/";
    
    /**
     * 参数替换模式 {参数名}
     */
    private static final Pattern PARAM_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    
    /**
     * 读取SQL模板文件并解析为SQL语句列表
     * 
     * @param templateName 模板文件名（不含路径）
     * @param params 参数映射
     * @return SQL语句列表
     */
    public static List<String> parseSqlTemplate(String templateName, Map<String, Object> params) {
        try {
            String templateContent = readTemplateFile(templateName);
            if (StringUtils.isEmpty(templateContent)) {
                logger.warn("SQL模板文件为空: {}", templateName);
                return new ArrayList<>();
            }
            
            // 替换参数
            String processedContent = replaceParameters(templateContent, params);
            
            // 移除注释
            String cleanContent = removeComments(processedContent);
            
            // 分割SQL语句
            List<String> sqlList = splitSqlStatements(cleanContent);
            
            logger.info("成功解析SQL模板: {}, 共{}条SQL语句", templateName, sqlList.size());
            return sqlList;
            
        } catch (Exception e) {
            logger.error("解析SQL模板失败: {}", templateName, e);
            throw new RuntimeException("解析SQL模板失败: " + templateName, e);
        }
    }
    
    /**
     * 读取模板文件内容
     * 
     * @param templateName 模板文件名
     * @return 文件内容
     */
    private static String readTemplateFile(String templateName) throws IOException {
        String templatePath = TEMPLATE_ROOT + templateName;
        ClassPathResource resource = new ClassPathResource(templatePath);
        
        if (!resource.exists()) {
            throw new IOException("SQL模板文件不存在: " + templatePath);
        }
        
        StringBuilder content = new StringBuilder();
        try (InputStream inputStream = resource.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 替换模板参数
     * 
     * @param content 模板内容
     * @param params 参数映射
     * @return 替换后的内容
     */
    private static String replaceParameters(String content, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return content;
        }
        
        Matcher matcher = PARAM_PATTERN.matcher(content);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = params.get(paramName);
            
            if (paramValue != null) {
                // 转义单引号，防止SQL注入
                String value = paramValue.toString().replace("'", "''");
                matcher.appendReplacement(result, value);
                logger.debug("替换参数: {} -> {}", paramName, value);
            } else {
                logger.warn("未找到参数值: {}", paramName);
                // 保持原样
                matcher.appendReplacement(result, matcher.group(0));
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 移除SQL注释
     * 支持单行注释 -- 和块注释
     * @param content SQL内容
     * 示例： select * from user where id = 1;
     */
    private static String removeComments(String content) {
        StringBuilder result = new StringBuilder();
        String[] lines = content.split("\n");
        boolean inBlockComment = false;
        
        for (String line : lines) {
            StringBuilder processedLine = new StringBuilder();
            char[] chars = line.toCharArray();
            
            for (int i = 0; i < chars.length; i++) {
                char c = chars[i];
                
                if (!inBlockComment) {
                    // 检查块注释开始
                    if (c == '/' && i + 1 < chars.length && chars[i + 1] == '*') {
                        inBlockComment = true;
                        i++; // 跳过下一个字符
                        continue;
                    }
                    
                    // 检查单行注释
                    if (c == '-' && i + 1 < chars.length && chars[i + 1] == '-') {
                        // 单行注释，忽略本行剩余内容
                        break;
                    }
                    
                    processedLine.append(c);
                } else {
                    // 在块注释中，检查注释结束
                    if (c == '*' && i + 1 < chars.length && chars[i + 1] == '/') {
                        inBlockComment = false;
                        i++; // 跳过下一个字符
                    }
                }
            }
            
            // 如果不在块注释中，添加处理后的行
            if (!inBlockComment || processedLine.length() > 0) {
                String trimmedLine = processedLine.toString().trim();
                if (!trimmedLine.isEmpty()) {
                    result.append(trimmedLine).append("\n");
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * 分割SQL语句
     * 按分号分割，忽略空语句
     * 
     * @param content SQL内容
     * @return SQL语句列表
     */
    private static List<String> splitSqlStatements(String content) {
        List<String> sqlList = new ArrayList<>();
        String[] statements = content.split(";");
        
        for (String statement : statements) {
            String trimmed = statement.trim();
            if (!trimmed.isEmpty()) {
                sqlList.add(trimmed);
            }
        }
        
        return sqlList;
    }
    
    /**
     * 检查模板文件是否存在
     * 
     * @param templateName 模板文件名
     * @return 是否存在
     */
    public static boolean templateExists(String templateName) {
        String templatePath = TEMPLATE_ROOT + templateName;
        ClassPathResource resource = new ClassPathResource(templatePath);
        return resource.exists();
    }
    
    /**
     * 获取所有可用的模板文件名
     * 
     * @return 模板文件名列表
     */
    public static List<String> getAvailableTemplates() {
        // 这里可以扩展为扫描目录下的所有.sql文件
        List<String> templates = new ArrayList<>();
        templates.add("init_service_tel.sql");
        // 可以添加更多模板
        return templates;
    }
}

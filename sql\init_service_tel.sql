-- 初始化小区服务电话数据
-- 使用方法：将 {community_id} 替换为实际的小区ID

-- 外部号码分类
-- 1. 保险服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '保险服务', '', 1, '0', NOW());

-- 获取保险服务分类ID（需要手动替换为实际ID）
-- 保险服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '95518', 'external', '中国人保车险', '中国人民保险集团股份有限公司', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '95500', 'external', '中国太平洋车险', '中国太平洋保险（集团）股份有限公司', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '95519', 'external', '中国人寿保险', '中国人寿保险股份有限公司', 3, '0', NOW());

-- 2. 紧急服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '紧急服务', '', 2, '0', NOW());

-- 紧急服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '110', 'external', '报警电话', '公安部门', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '119', 'external', '火警电话', '消防部门', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '120', 'external', '急救电话', '医疗急救', 3, '0', NOW());

-- 3. 公共服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '公共服务', '', 3, '0', NOW());

-- 公共服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '95598', 'external', '供电服务', '国家电网', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96777', 'external', '燃气服务', '燃气公司', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96116', 'external', '供水服务', '自来水公司', 3, '0', NOW());

-- 内部号码分类
-- 1. 物业服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '物业服务', '', 1, '0', NOW());

-- 物业服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8001', 'internal', '物业客服', '物业管理处', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8002', 'internal', '物业经理', '物业管理处', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8003', 'internal', '收费处', '物业管理处', 3, '0', NOW());

-- 2. 安保服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '安保服务', '', 2, '0', NOW());

-- 安保服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8010', 'internal', '门岗值班', '安保部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8011', 'internal', '巡逻队', '安保部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8012', 'internal', '监控中心', '安保部', 3, '0', NOW());

-- 3. 维修服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '维修服务', '', 3, '0', NOW());

-- 维修服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES 
(LAST_INSERT_ID(), '{community_id}', '8020', 'internal', '水电维修', '维修部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8021', 'internal', '电梯维修', '维修部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8022', 'internal', '绿化养护', '维修部', 3, '0', NOW());

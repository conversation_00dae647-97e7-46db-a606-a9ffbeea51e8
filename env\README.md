# 环境配置文件目录

此目录包含不同环境的配置文件，用于部署脚本读取环境变量。

**注意**: 配置文件已移动到 `env` 目录中，部署脚本会自动从脚本所在目录的 `env` 子目录读取配置。

## 文件说明

### Linux/Mac 环境配置文件
- `env-dev.sh` - 开发环境配置
- `env-test.sh` - 测试环境配置  
- `env-prod.sh` - 生产环境配置

### Windows 环境配置文件
- `env-dev.bat` - 开发环境配置
- `env-test.bat` - 测试环境配置
- `env-prod.bat` - 生产环境配置

## 生成配置文件

### Linux/Mac
```bash
# 生成开发环境配置
./deploy-jar.sh config dev

# 生成测试环境配置
./deploy-jar.sh config test

# 生成生产环境配置
./deploy-jar.sh config prod
```

### Windows
```cmd
# 方法1：使用脚本生成（推荐Linux环境）
deploy-jar.bat config dev
deploy-jar.bat config test
deploy-jar.bat config prod

# 方法2：手动创建（推荐Windows环境）
# 复制模板文件
copy env-template.bat env-dev.bat
copy env-template.bat env-test.bat
copy env-template.bat env-prod.bat

# 然后编辑对应的配置文件
notepad env-prod.bat
```

## 配置项说明

### 数据库配置
- `DB_HOST` - 数据库主机地址
- `DB_PORT` - 数据库端口
- `DB_USERNAME` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `DB_NAME` - 数据库名称（自动根据环境设置）

### 微信小程序配置
- `WECHAT_DEV_APPID` / `WECHAT_DEV_SECRET` - 开发环境
- `WECHAT_TEST_APPID` / `WECHAT_TEST_SECRET` - 测试环境
- `WECHAT_PROD_APPID` / `WECHAT_PROD_SECRET` - 生产环境

### Token配置
- `TOKEN_SECRET` - JWT Token密钥

## 重要说明

### IDEA开发环境
- **IDEA中运行开发环境不受这些配置文件影响**
- IDEA使用 `application-dev.yml` 中的默认配置
- 这些配置文件仅用于部署脚本启动应用

### 安全注意事项
- ⚠️ **不要将配置文件提交到版本控制系统**
- ⚠️ **生产环境必须修改默认密码和密钥**
- ⚠️ **定期更换敏感信息**
- ⚠️ **限制配置文件的访问权限**

### 配置文件优先级
1. 环境配置文件（此目录中的文件）
2. 应用配置文件（application-*.yml）
3. 默认配置

## 示例配置

### 开发环境示例 (env-dev.sh)
```bash
#!/bin/bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USERNAME=root
export DB_PASSWORD=your_dev_password
export WECHAT_DEV_APPID=your_dev_appid
export WECHAT_DEV_SECRET=your_dev_secret
export TOKEN_SECRET=your_dev_token_secret
```

### 生产环境示例 (env-prod.sh)
```bash
#!/bin/bash
export DB_HOST=prod.database.com
export DB_PORT=3306
export DB_USERNAME=ehome_prod
export DB_PASSWORD=your_strong_prod_password
export WECHAT_PROD_APPID=your_prod_appid
export WECHAT_PROD_SECRET=your_prod_secret
export TOKEN_SECRET=your_complex_prod_token_secret
```

## 故障排除

### 配置文件不生效
1. 检查文件路径是否正确
2. 确认文件有执行权限（Linux/Mac）
3. 检查环境变量语法是否正确

### IDEA开发环境问题
- IDEA开发环境使用yml配置文件，不读取此目录的配置
- 如需修改开发环境配置，请编辑 `application-dev.yml`

### 权限问题
```bash
# Linux/Mac 设置配置文件权限
chmod 600 config/env-*.sh
chmod 600 config/env-*.bat
```

// 配置管理器 - 统一管理小程序配置
import ConfigParser from './configParser.js'
import SecureLogger from './secureLogger.js'

class ConfigManager {
  constructor() {
    this.config = null
    this.listeners = new Map() // 配置变更监听器
    this.cache = new Map() // 配置缓存
    this.defaultConfig = this.getDefaultConfig()
  }

  /**
   * 获取默认配置
   * @returns {Object} 默认配置对象
   */
  getDefaultConfig() {
    return {
      // 小程序首页配置
      miniprogram_title: '',
      miniprogram_subtitle: '',
      enable_miniprogram_title: '0',

      // 功能控制配置
      enable_miniprogram_mark: '0',
      enable_screenshot: '1',
      enable_comment: '1', // 全局评论开关

      // 布局配置
      menu_rows: '4',
      show_big_card: '1',

      // 分享配置
      enable_share: '1',
      share_title: '智慧社区服务',
      share_desc: '一键触达生活所需，物业服务就在掌心',
      share_image: '/static/images/share-cover.png',
      enable_share_statistics: '1',
      show_sharer_info: '1',

      // 版本更新配置
      enable_auto_update: '1',
      force_update_mode: '0',
      update_check_interval: '86400000' // 24小时，毫秒
    }
  }

  /**
   * 初始化配置
   * @param {Object} communityInfo 社区信息
   */
  init(communityInfo) {
    try {
      // 解析小配置（extJson）
      const parsedExtConfig = ConfigParser.parseExtJson(communityInfo)
      // 解析小区扩展信息（infoJson）
      const parsedInfoConfig = ConfigParser.parseInfoJson(communityInfo)

      // 合并所有配置
      this.config = {
        ...this.defaultConfig,
        ...parsedExtConfig,
        ...parsedInfoConfig
      }

      // 清除缓存
      this.cache.clear()

      SecureLogger.log('ConfigManager', '配置初始化完成', {
        hasConfig: !!this.config,
        configKeys: Object.keys(this.config || {}),
        hasExtConfig: !!parsedExtConfig,
        hasInfoConfig: !!parsedInfoConfig
      })

      // 通知配置变更
      this.notifyConfigChange('init', this.config)
    } catch (error) {
      SecureLogger.error('ConfigManager', '配置初始化失败', error)
      this.config = { ...this.defaultConfig }
    }
  }

  /**
   * 更新配置
   * @param {Object} communityInfo 社区信息
   * @returns {boolean} 是否有配置变更
   */
  update(communityInfo) {
    try {
      const oldConfig = this.config ? { ...this.config } : null

      // 解析小配置（extJson）
      const parsedExtConfig = ConfigParser.parseExtJson(communityInfo)
      // 解析小区扩展信息（infoJson）
      const parsedInfoConfig = ConfigParser.parseInfoJson(communityInfo)

      // 合并所有配置
      const newConfig = {
        ...this.defaultConfig,
        ...parsedExtConfig,
        ...parsedInfoConfig
      }

      // 检查配置是否有变更
      const hasChanged = !oldConfig || JSON.stringify(oldConfig) !== JSON.stringify(newConfig)

      if (hasChanged) {
        this.config = newConfig
        this.cache.clear() // 清除缓存

        SecureLogger.log('ConfigManager', '配置已更新', {
          hasChanged,
          configKeys: Object.keys(newConfig),
          hasExtConfig: !!parsedExtConfig,
          hasInfoConfig: !!parsedInfoConfig
        })

        // 通知配置变更
        this.notifyConfigChange('update', newConfig, oldConfig)
      }

      return hasChanged
    } catch (error) {
      SecureLogger.error('ConfigManager', '配置更新失败', error)
      return false
    }
  }

  /**
   * 获取配置值
   * @param {string} key 配置键
   * @param {*} defaultValue 默认值
   * @returns {*} 配置值
   */
  get(key, defaultValue = null) {
    if (!this.config) {
      return defaultValue !== null ? defaultValue : this.defaultConfig[key]
    }

    // 检查缓存
    const cacheKey = `get_${key}`
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const value = this.config[key]
    const result = value !== undefined ? value : (defaultValue !== null ? defaultValue : this.defaultConfig[key])
    
    // 缓存结果
    this.cache.set(cacheKey, result)
    
    return result
  }

  /**
   * 获取所有配置
   * @returns {Object} 配置对象
   */
  getAll() {
    if (!this.config) {
      return { ...this.defaultConfig }
    }

    // 检查缓存
    const cacheKey = 'get_all'
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const result = { ...this.config }
    this.cache.set(cacheKey, result)
    
    return result
  }

  /**
   * 获取分类配置
   * @param {string} category 配置分类
   * @returns {Object} 分类配置对象
   */
  getCategory(category) {
    const cacheKey = `category_${category}`
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const allConfig = this.getAll()
    let result = {}

    switch (category) {
      case 'ui':
        result = {
          miniprogram_title: allConfig.miniprogram_title,
          miniprogram_subtitle: allConfig.miniprogram_subtitle,
          enable_miniprogram_title: allConfig.enable_miniprogram_title,
          menu_rows: allConfig.menu_rows,
          show_big_card: allConfig.show_big_card
        }
        break
      case 'feature':
        result = {
          enable_miniprogram_mark: allConfig.enable_miniprogram_mark,
          enable_screenshot: allConfig.enable_screenshot
        }
        break
      case 'share':
        result = {
          enable_share: allConfig.enable_share,
          share_title: allConfig.share_title,
          share_desc: allConfig.share_desc,
          share_image: allConfig.share_image,
          enable_share_statistics: allConfig.enable_share_statistics,
          show_sharer_info: allConfig.show_sharer_info
        }
        break
      default:
        result = allConfig
    }

    this.cache.set(cacheKey, result)
    return result
  }

  /**
   * 检查配置是否启用
   * @param {string} key 配置键
   * @returns {boolean} 是否启用
   */
  isEnabled(key) {
    const value = this.get(key, '0')
    return value === '1' || value === 'true' || value === true
  }

  /**
   * 获取数值配置
   * @param {string} key 配置键
   * @param {number} defaultValue 默认值
   * @returns {number} 数值
   */
  getNumber(key, defaultValue = 0) {
    const value = this.get(key, defaultValue.toString())
    const num = parseInt(value)
    return isNaN(num) ? defaultValue : num
  }

  /**
   * 添加配置变更监听器
   * @param {string} id 监听器ID
   * @param {Function} callback 回调函数
   */
  addListener(id, callback) {
    if (typeof callback !== 'function') {
      SecureLogger.warn('ConfigManager', '监听器回调必须是函数', { id })
      return
    }
    
    this.listeners.set(id, callback)
    SecureLogger.log('ConfigManager', '添加配置监听器', { id })
  }

  /**
   * 移除配置变更监听器
   * @param {string} id 监听器ID
   */
  removeListener(id) {
    const removed = this.listeners.delete(id)
    if (removed) {
      SecureLogger.log('ConfigManager', '移除配置监听器', { id })
    }
  }

  /**
   * 通知配置变更
   * @param {string} type 变更类型
   * @param {Object} newConfig 新配置
   * @param {Object} oldConfig 旧配置
   */
  notifyConfigChange(type, newConfig, oldConfig = null) {
    this.listeners.forEach((callback, id) => {
      try {
        callback({
          type,
          newConfig,
          oldConfig,
          timestamp: Date.now()
        })
      } catch (error) {
        SecureLogger.error('ConfigManager', '配置监听器执行失败', { id, error })
      }
    })
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear()
    SecureLogger.log('ConfigManager', '配置缓存已清除')
  }

  /**
   * 获取配置状态信息
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      hasConfig: !!this.config,
      configCount: this.config ? Object.keys(this.config).length : 0,
      cacheSize: this.cache.size,
      listenerCount: this.listeners.size
    }
  }
}

// 创建单例
let configManager = null

export function getConfigManager() {
  if (!configManager) {
    configManager = new ConfigManager()
  }
  return configManager
}

export default getConfigManager

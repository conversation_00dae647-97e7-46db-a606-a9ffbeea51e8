.detail-page {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: 150rpx; /* 为底部预留空间 */
}

.loading, .error {
  text-align: center;
  color: #999;
  margin-top: 200rpx;
  font-size: 28rpx;
}

.detail-content {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 标题区域 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.address {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.time-diff {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.status-tag {
  font-size: 28rpx;
  font-weight: 500;
}

/* 信息区域 */
.info-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 16rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4rpx;
  width: 6rpx;
  height: 32rpx;
  background: #1890ff;
  border-radius: 3rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.info-value.content-value {
  line-height: 1.6;
  word-wrap: break-word;
}

.no-feedback {
  color: #999;
  font-style: italic;
}

/* 内容区域 */
.content-section {
  padding: 32rpx;
  background: #f8f9fa;
  margin: 16rpx 32rpx;
  border-radius: 12rpx;
}

.content-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

/* 图片展示区域 */
.images-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 16rpx;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 附件展示区域 */
.attachments-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 16rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx;
  border: 1rpx solid #e9ecef;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 8rpx;
  margin-right: 16rpx;
  font-size: 32rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  font-size: 24rpx;
  color: #999;
}

.download-icon {
  font-size: 28rpx;
  color: #1890ff;
}

.attachment-info {
  flex: 1;
  min-width: 0;
}

.attachment-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-size {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 暂无处理结果 */
.no-reply-section {
  padding: 32rpx;
}

.no-reply-content {
  background: #fafafa;
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
}

.no-reply-text {
  font-size: 28rpx;
  color: #999;
}

/* 处理结果独立内容块 */
.result-content {
  background: #fff;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.result-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-section:last-child {
  border-bottom: none;
}

.section-title-top {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: flex-start;
}

.section-title-top::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4rpx;
  width: 6rpx;
  height: 32rpx;
  background: #1890ff;
  border-radius: 3rpx;
}

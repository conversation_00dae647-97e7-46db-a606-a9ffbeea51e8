<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('同步日志管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>同步类型：</label>
                                <select name="sync_type">
                                    <option value="">所有</option>
                                    <option value="moment">照片视频</option>
                                    <option value="member">团队成员</option>
                                    <option value="illegal_park">违规车辆</option>
                                    <option value="team">团队信息</option>
                                </select>
                            </li>
                            <li>
                                <label>同步状态：</label>
                                <select name="sync_status">
                                    <option value="">所有</option>
                                    <option value="0">失败</option>
                                    <option value="1">成功</option>
                                    <option value="2">进行中</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>同步时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="start_time"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="end_time"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-danger multiple disabled" onclick="clearLogs()" shiro:hasPermission="oc:markicam:clear">
                    <i class="fa fa-trash"></i> 清理日志
                </a>
                <a class="btn btn-info" onclick="refreshLogs()" shiro:hasPermission="oc:markicam:view">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-warning" onclick="exportLogs()" shiro:hasPermission="oc:markicam:export">
                    <i class="fa fa-download"></i> 导出日志
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 错误详情模态框 -->
    <div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-labelledby="errorModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="errorModalLabel">错误详情</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="errorContent">
                    <!-- 错误内容将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";
        
        $(function() {
            var options = {
                url: prefix + "/log/list",
                createUrl: prefix + "/log/add",
                updateUrl: prefix + "/log/edit/{id}",
                removeUrl: prefix + "/log/remove",
                exportUrl: prefix + "/log/export",
                modalName: "同步日志",
                sortName: "created_at",
                sortOrder: "desc",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'sync_type',
                    title: '同步类型',
                    width: 100,
                    formatter: function(value, row, index) {
                        var typeMap = {
                            'moment': '<span class="badge badge-primary">照片视频</span>',
                            'member': '<span class="badge badge-info">团队成员</span>',
                            'illegal_park': '<span class="badge badge-warning">违规车辆</span>',
                            'team': '<span class="badge badge-success">团队信息</span>'
                        };
                        return typeMap[value] || value;
                    }
                },
                {
                    field: 'sync_status',
                    title: '同步状态',
                    align: 'center',
                    width: 80,
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="badge badge-success">成功</span>';
                        } else if (value == 0) {
                            return '<span class="badge badge-danger">失败</span>';
                        } else if (value == 2) {
                            return '<span class="badge badge-warning">进行中</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'sync_count',
                    title: '同步数量',
                    width: 80,
                    align: 'center'
                },
                {
                    field: 'duration',
                    title: '耗时(秒)',
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value && value > 0) {
                            return value + 's';
                        }
                        return '-';
                    }
                },
                {
                    field: 'start_time',
                    title: '开始时间',
                    width: 150
                },
                {
                    field: 'end_time',
                    title: '结束时间',
                    width: 150
                },
                {
                    field: 'error_msg',
                    title: '错误信息',
                    width: 200,
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return '<span title="' + value + '">' + value.substring(0, 50) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (row.sync_status == 0 && row.error_msg) {
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="viewError(\'' + row.id + '\')"><i class="fa fa-exclamation-triangle"></i>错误</a>');
                        }
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join(' ');
                    }
                }]
            };
            $.table.init(options);
            
            // 初始化时间选择器
            laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
            
            // 自动刷新（每30秒）
            setInterval(function() {
                $.table.refresh();
            }, 30000);
        });

        function refreshLogs() {
            $.table.refresh();
        }

        function clearLogs() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择要清理的日志");
                return;
            }
            
            $.modal.confirm("确定要清理选中的" + rows.length + "条日志吗？", function() {
                $.post(prefix + "/log/clear", { "ids": rows.join(",") }, function(result) {
                    if (result.code == 0) {
                        $.modal.alertSuccess("清理成功");
                        $.table.refresh();
                    } else {
                        $.modal.alertError("清理失败：" + result.msg);
                    }
                });
            });
        }

        function exportLogs() {
            $.modal.confirm("确定要导出同步日志吗？", function() {
                var formData = $("#formId").serialize();
                window.location.href = prefix + "/log/export?" + formData;
            });
        }

        function viewError(id) {
            $.get(prefix + "/log/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="alert alert-danger">';
                    content += '<h5><i class="fa fa-exclamation-triangle"></i> 同步失败</h5>';
                    content += '<p><strong>同步类型:</strong> ' + data.sync_type + '</p>';
                    content += '<p><strong>开始时间:</strong> ' + (data.start_time || '-') + '</p>';
                    content += '<p><strong>结束时间:</strong> ' + (data.end_time || '-') + '</p>';
                    content += '<p><strong>耗时:</strong> ' + (data.duration || 0) + '秒</p>';
                    content += '</div>';
                    
                    if (data.error_msg) {
                        content += '<h6>错误详情:</h6>';
                        content += '<pre style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; max-height: 300px; overflow-y: auto;">';
                        content += data.error_msg;
                        content += '</pre>';
                    }
                    
                    $('#errorContent').html(content);
                    $('#errorModal').modal('show');
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function viewDetail(id) {
            $.get(prefix + "/log/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>同步类型:</strong> ' + data.sync_type + '</p>';
                    
                    var statusText = '';
                    var statusClass = '';
                    if (data.sync_status == 1) {
                        statusText = '成功';
                        statusClass = 'success';
                    } else if (data.sync_status == 0) {
                        statusText = '失败';
                        statusClass = 'danger';
                    } else if (data.sync_status == 2) {
                        statusText = '进行中';
                        statusClass = 'warning';
                    }
                    content += '<p><strong>同步状态:</strong> <span class="badge badge-' + statusClass + '">' + statusText + '</span></p>';
                    content += '<p><strong>同步数量:</strong> ' + (data.sync_count || 0) + '</p>';
                    content += '<p><strong>耗时:</strong> ' + (data.duration || 0) + '秒</p>';
                    content += '</div>';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>开始时间:</strong> ' + (data.start_time || '-') + '</p>';
                    content += '<p><strong>结束时间:</strong> ' + (data.end_time || '-') + '</p>';
                    content += '<p><strong>创建时间:</strong> ' + (data.created_at || '-') + '</p>';
                    content += '</div>';
                    content += '</div>';
                    
                    if (data.error_msg) {
                        content += '<hr><h6>错误信息:</h6>';
                        content += '<div class="alert alert-danger">' + data.error_msg + '</div>';
                    }
                    
                    $.modal.open("同步日志详情", content);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    </script>
</body>
</html>

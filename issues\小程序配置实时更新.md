# 小程序配置实时更新功能

## 问题描述
用户在后台管理页面修改小程序配置后，小程序端无法及时获取到最新的配置，需要重新进入小程序才能看到更新。

## 解决方案
通过在小程序的生命周期中检查配置更新时间，实现配置的自动刷新。

## 修改内容

### 1. 后端修改
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxIndexController.java`
- 在`status`接口返回的`communityInfo`中添加`updateTime`字段
- 用于小程序端比较配置是否有更新

```java
// 添加配置更新时间，用于小程序端检查配置是否有更新
communityInfo.put("updateTime", communityRecord.get("update_time"));
```

### 2. 小程序StateManager修改
**文件**: `miniprogram/utils/stateManager.js`
- 添加`checkConfigUpdate`方法：检查配置是否需要更新
- 添加`refreshConfig`方法：刷新配置信息

主要功能：
- 比较本地存储的配置更新时间和服务器返回的更新时间
- 如果服务器时间更新，返回true表示需要更新配置
- 提供配置刷新方法重新获取最新配置

### 3. 小程序App.js修改
**文件**: `miniprogram/app.js`
- 在`handleAppShow`方法中添加配置检查
- 添加`checkConfigUpdate`方法：在小程序显示时检查配置更新

主要功能：
- 小程序每次显示时都会检查配置是否有更新
- 如果检测到配置更新，自动刷新配置并通知当前页面

### 4. 首页修改
**文件**: `miniprogram/pages/index/index.js`
- 在认证状态检查时添加配置更新检查
- 添加`onConfigUpdate`回调方法：处理配置更新后的页面刷新

主要功能：
- 在调用status接口时检查配置更新
- 配置更新后重新设置菜单行数、水印等

## 工作流程

1. **配置修改**：用户在后台管理页面修改配置
2. **后端处理**：
   - 更新数据库中的`ext_json`字段
   - 自动更新`update_time`字段
   - 清除后端缓存
3. **小程序检测**：
   - 小程序显示时调用`checkConfigUpdate`
   - 通过`status`接口获取最新的`updateTime`
   - 比较本地和服务器的更新时间
4. **配置更新**：
   - 如果检测到更新，自动刷新配置
   - 更新StateManager中的配置信息
   - 通知相关页面刷新显示

## 优势

1. **自动化**：用户无需手动操作，配置自动更新
2. **实时性**：小程序每次显示都会检查配置更新
3. **高效性**：利用现有的status接口，不增加额外的网络请求
4. **兼容性**：不破坏现有的缓存机制和业务逻辑

## 测试方法

1. 在后台管理页面修改小程序配置（如标题、菜单行数等）
2. 保存配置
3. 切换到小程序（如果已打开则先切到后台再切回来）
4. 观察配置是否自动更新生效

## 注意事项

- 配置检查在小程序显示时进行，不会频繁请求服务器
- 如果配置检查失败，不会影响小程序的正常使用
- 配置更新是静默进行的，用户感知不到更新过程

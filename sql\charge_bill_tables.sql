-- 收费账单系统数据库表设计

-- 1. 收费标准表版本化改造
ALTER TABLE `eh_charge_standard`
ADD COLUMN `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
ADD COLUMN `parent_id` bigint(20) DEFAULT NULL COMMENT '父级ID（原始记录ID）',
ADD COLUMN `is_current` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否当前版本(1:是,0:否)',
ADD INDEX `idx_parent_version` (`parent_id`, `version`),
ADD INDEX `idx_current` (`is_current`);

-- 2. 收费账单主表
DROP TABLE IF EXISTS `eh_charge_bill`;
CREATE TABLE `eh_charge_bill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `community_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小区ID',
  `asset_type` tinyint(4) NOT NULL COMMENT '资产类型(1:房屋,2:车位,3:商铺)',
  `asset_id` bigint(20) NOT NULL COMMENT '资产ID',
  `asset_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资产名称',
  `charge_standard_id` bigint(20) NOT NULL COMMENT '收费标准ID',
  `charge_standard_version` int(11) NOT NULL DEFAULT 1 COMMENT '收费标准版本号',
  `charge_binding_id` bigint(20) NOT NULL COMMENT '收费绑定ID',
  `charge_item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收费项目名称',
  `charge_item_type` tinyint(4) NOT NULL COMMENT '收费类型(1:周期性收费,2:走表收费,3:临时性收费)',
  
  -- 账期信息
  `start_time` bigint(20) NOT NULL COMMENT '账期开始时间(时间戳)',
  `end_time` bigint(20) NOT NULL COMMENT '账期结束时间(时间戳)',
  `in_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账期月份(YYYY-MM)',
  `bill_type` tinyint(4) NOT NULL DEFAULT 3 COMMENT '账单类型(1:手工账单,2:补缴账单,3:系统生成)',
  
  -- 金额信息
  `amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '应收金额(分)',
  `bill_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '账单金额(分)',
  `discount_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '优惠金额(分)',
  `late_money_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '违约金金额(分)',
  `deposit_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '押金金额(分)',
  
  -- 支付信息
  `pay_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '支付状态(0:未缴,1:已缴,2:部分缴费)',
  `pay_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '支付方式(0:未支付,1:现金,2:微信,3:支付宝,4:银行转账)',
  `pay_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '支付时间(时间戳)',
  `second_pay_channel` tinyint(4) NOT NULL DEFAULT 0 COMMENT '第二支付渠道',
  `second_pay_amount` bigint(20) NOT NULL DEFAULT 0 COMMENT '第二支付金额(分)',
  
  -- 其他信息
  `can_revoke` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否可撤销(0:否,1:是)',
  `bad_bill_state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '坏账状态(0:正常,1:坏账)',
  `is_bad_bill` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否坏账',
  `has_split` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否拆分账单',
  `split_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '拆分说明',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  `deal_log_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '处理日志ID',
  `receipt_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '收据ID',
  
  -- 审计字段
  `create_time` bigint(20) NOT NULL COMMENT '创建时间(时间戳)',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `update_time` bigint(20) DEFAULT NULL COMMENT '更新时间(时间戳)',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新人',
  `last_op_time` datetime DEFAULT NULL COMMENT '最后操作时间',
  
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_community_asset` (`community_id`,`asset_type`,`asset_id`) USING BTREE,
  KEY `idx_charge_standard` (`charge_standard_id`) USING BTREE,
  KEY `idx_charge_binding` (`charge_binding_id`) USING BTREE,
  KEY `idx_in_month` (`in_month`) USING BTREE,
  KEY `idx_pay_status` (`pay_status`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_standard_version` (`charge_standard_id`, `charge_standard_version`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='收费账单表' ROW_FORMAT=Dynamic;

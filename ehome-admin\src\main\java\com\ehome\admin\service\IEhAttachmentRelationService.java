package com.ehome.admin.service;

import com.jfinal.plugin.activerecord.Record;

import java.util.List;

/**
 * 附件关联服务接口
 * 
 * <AUTHOR>
 */
public interface IEhAttachmentRelationService {
    
    /**
     * 根据业务类型和业务ID查询附件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 附件列表
     */
    List<Record> getAttachmentsByBusiness(String businessType, String businessId);
    
    /**
     * 保存业务附件关联
     *
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param fileIds 文件ID数组
     * @param createBy 创建者
     * @param communityId 小区ID
     * @return 是否成功
     */
    boolean saveBusinessAttachments(String businessType, String businessId, String[] fileIds, String createBy, String communityId);
    
    /**
     * 删除业务附件关联
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 是否成功
     */
    boolean deleteBusinessAttachments(String businessType, String businessId);
    
    /**
     * 删除单个附件关联
     * 
     * @param relationId 关联ID
     * @return 是否成功
     */
    boolean deleteAttachmentRelation(String relationId);
    
    /**
     * 更新附件排序
     * 
     * @param relationId 关联ID
     * @param sortOrder 排序号
     * @return 是否成功
     */
    boolean updateAttachmentSort(String relationId, Integer sortOrder);
    
    /**
     * 获取附件详细信息（包含文件信息）
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 附件详细信息列表
     */
    List<Record> getAttachmentDetails(String businessType, String businessId);
}

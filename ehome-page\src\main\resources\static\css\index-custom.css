.skin-blue .navbar-default .nav>li.selected>a, .skin-blue .navbar-default .nav>li.selected>a:focus{
    border-radius: 6px;
    background: #2e5bff;
}

.search-collapse, .select-table{
    border-radius: 3px;
}

#side-menu .nav-label{
    font-size: 14px;
    font-weight: 600;
}

#side-menu>li>a{
    padding: 14px 20px 10px 14px!important;
}

#side-menu .nav>li{
    margin: 4px 0px;
}

#side-menu .fa{
    font-size: 16px;
    color: rgba(0,0,0,.45);
    margin-right: 6px;
}
#side-menu .active.selected .fa {
    color: #ffffff;
}


.nav-second-level li, .nav-third-level li{
    padding-top: 2px;
    padding-bottom: 2px;
}

.nav-second-level li a{
    height: 40px;
    line-height: 26px;
}

.menuItem{
    font-size: 14px;
}


#side-menu{
    margin: 10px;
}

.skin-blue .nav:not(.navbar-toolbar)>li.active{
    border-left: none;
}
<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小程序用户管理')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>手机号码：</label>
                                <input type="text" name="mobile" placeholder="请输入手机号码"/>
                            </li>
                            <li>
                                <label>用户状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="0">正常</option>
                                    <option value="1">停用</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="beginTime"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="endTime"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-info" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/wxuser";

        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "小程序用户",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'user_id',
                    title: '用户ID',
                    visible: false
                },
                {
                    field: 'avatar_url',
                    title: '头像',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<img src="' + value + '" style="width: 40px; height: 40px; border-radius: 50%;" onerror="this.src=\'/img/profile.jpg\'">';
                        }
                        return '<img src="/img/profile.jpg" style="width: 40px; height: 40px; border-radius: 50%;">';
                    }
                },
                {
                    field: 'nick_name',
                    title: '用户昵称',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'mobile',
                    title: '手机号码',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },

                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '0') {
                            return '<span class="label label-success">正常</span>';
                        } else {
                            return '<span class="label label-danger">停用</span>';
                        }
                    }
                },
                {
                    field: 'owner_name',
                    title: '绑定业主',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'owner_mobile',
                    title: '业主手机号',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'house_room',
                    title: '绑定房屋',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'login_ip',
                    title: '最后登录IP',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'login_date',
                    title: '最后登录时间',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                },
                {
                    field: 'create_time',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showLoginLogs(\'' + row.user_id + '\', \'' + (row.nick_name || '未知用户') + '\')"><i class="fa fa-list"></i>登录日志</a> ');
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="showUserDetail(\'' + row.user_id + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        
                        if (row.status == '0') {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="changeStatus(\'' + row.user_id + '\', \'1\')"><i class="fa fa-ban"></i>停用</a>');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="changeStatus(\'' + row.user_id + '\', \'0\')"><i class="fa fa-check"></i>启用</a>');
                        }
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 显示登录日志
        function showLoginLogs(userId, nickName) {
            var url = prefix + "/loginLogs/" + userId;
            var title = "登录日志 - " + nickName;
            top.layer.open({
                type: 2,
                area: ['80%', '100%'],
                fix: false,
                offset:'r',
                maxmin: true,
                scrollbar: false,
                shade: 0.3,
                title: title,
                content: url,
                success: function(layero, index) {

                }
            });
        }

        // 显示用户详情
        function showUserDetail(userId) {
            $.post(prefix + "/detail", {userId: userId}, function(result) {
                if (result.code == 0) {
                    var user = result.data;
                    var content = '<div class="row" style="margin:10px;">';
                    content += '<div class="col-sm-12">';
                    content += '<table class="table table-bordered">';
                    content += '<tr><td width="120px"><strong>用户ID:</strong></td><td>' + (user.user_id || '-') + '</td></tr>';
                    content += '<tr><td><strong>OpenID:</strong></td><td>' + (user.openid || '-') + '</td></tr>';
                    content += '<tr><td><strong>昵称:</strong></td><td>' + (user.nick_name || '-') + '</td></tr>';
                    content += '<tr><td><strong>手机号:</strong></td><td>' + (user.mobile || '-') + '</td></tr>';
                    content += '<tr><td><strong>状态:</strong></td><td>' + getStatusText(user.status) + '</td></tr>';
                    content += '<tr><td><strong>绑定业主:</strong></td><td>' + (user.owner_name || '-') + '</td></tr>';
                    content += '<tr><td><strong>业主手机号:</strong></td><td>' + (user.owner_mobile || '-') + '</td></tr>';
                    content += '<tr><td><strong>绑定房屋:</strong></td><td>' + (user.house_room || '-') + '</td></tr>';
                    content += '<tr><td><strong>最后登录IP:</strong></td><td>' + (user.login_ip || '-') + '</td></tr>';
                    content += '<tr><td><strong>最后登录时间:</strong></td><td>' + (user.login_date || '-') + '</td></tr>';
                    content += '<tr><td><strong>创建时间:</strong></td><td>' + (user.create_time || '-') + '</td></tr>';
                    content += '<tr><td><strong>更新时间:</strong></td><td>' + (user.update_time || '-') + '</td></tr>';
                    content += '</table>';
                    content += '</div></div>';
                    
                    layer.open({
                        type: 1,
                        area: ['600px', '500px'],
                        fix: false,
                        maxmin: true,
                        shade: 0.3,
                        title: "用户详情 - " + (user.nick_name || '未知用户'),
                        content: content
                    });
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        // 更改用户状态
        function changeStatus(userId, status) {
            var statusText = status == '0' ? '启用' : '停用';
            $.modal.confirm("确认要" + statusText + "该用户吗？", function() {
                $.post(prefix + "/changeStatus", {userId: userId, status: status}, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(statusText + "成功");
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 获取状态文本
        function getStatusText(status) {
            return status == '0' ? '正常' : '停用';
        }

        // 时间选择器
        laydate.render({
            elem: '#startTime'
        });
        laydate.render({
            elem: '#endTime'
        });

    </script>
</body>
</html>

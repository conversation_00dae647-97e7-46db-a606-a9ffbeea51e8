# 房屋类型数据字典使用说明

## 概述
本次更新将房屋管理页面的房屋类型选项从硬编码改为使用数据字典动态加载，提高了系统的可维护性和灵活性。

## 数据字典内容

### 房屋类型列表
| ID | 名称 | 值 | 说明 |
|---|---|---|---|
| 1 | 住宅 | 1 | 普通住宅类型 |
| 2 | 公寓 | 2 | 公寓类型 |
| 3 | 商铺 | 3 | 商业店铺 |
| 4 | 办公 | 4 | 办公场所 |
| 5 | 仓库 | 5 | 仓储用房 |
| 6 | 其他 | 6 | 其他类型 |
| 7 | 厂房 | 7 | 工业厂房 |
| 8 | 多层住宅 | 1217 | 多层住宅建筑 |
| 9 | 联排别墅 | 1216 | 联排别墅类型 |
| 10 | 叠加洋房 | 1213 | 叠加洋房类型 |

## 部署步骤

### 1. 执行数据库初始化脚本
```sql
-- 执行房屋类型字典初始化
source init_house_types.sql;
```

### 2. 重启应用服务
重启ehome-page服务以确保数据字典缓存更新。

### 3. 验证功能
- 访问房屋管理页面
- 点击"新增房屋"或"编辑房屋"
- 确认房屋类型下拉框显示所有字典选项

## 技术实现

### 前端页面修改
- **编辑页面**: `ehome-page/src/main/resources/templates/oc/house/edit.html`
- **添加页面**: `ehome-page/src/main/resources/templates/oc/house/add.html`

### 实现方式
使用Thymeleaf模板引擎的字典工具类：
```html
<select name="house_type" class="form-control" th:with="type=${@dict.getType('house_type')}">
    <option value="">请选择房屋类型</option>
    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
</select>
```

## 管理维护

### 添加新的房屋类型
1. 登录系统管理后台
2. 进入"系统管理" -> "字典管理"
3. 找到"房屋类型"字典类型
4. 添加新的字典数据项

### 修改现有房屋类型
1. 在字典管理中找到对应的房屋类型
2. 修改字典标签或字典值
3. 保存后系统会自动更新缓存

## 注意事项

1. **数据一致性**: 修改字典值时需要考虑已有房屋数据的兼容性
2. **缓存更新**: 修改字典后可能需要清理系统缓存
3. **权限控制**: 字典管理需要相应的系统权限
4. **备份数据**: 修改前建议备份相关数据

## 优势

1. **灵活性**: 可以通过后台管理界面动态调整房屋类型
2. **一致性**: 所有使用房屋类型的地方都从统一的数据字典获取
3. **可维护性**: 避免了硬编码，降低了维护成本
4. **扩展性**: 便于后续添加新的房屋类型

## 回滚方案

如需回滚到硬编码方式，可以：
1. 恢复页面文件的原始版本
2. 删除数据字典相关数据（可选）

```sql
-- 删除房屋类型字典数据（可选）
DELETE FROM sys_dict_data WHERE dict_type = 'house_type';
DELETE FROM sys_dict_type WHERE dict_type = 'house_type';
```

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('Markicam配置管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>状态：</label>
                                <select name="is_enabled" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="add()" shiro:hasPermission="oc:markicam:add">
                    <i class="fa fa-plus"></i> 新增配置
                </a>
                <a class="btn btn-primary btn-edit disabled" onclick="edit()" shiro:hasPermission="oc:markicam:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="remove()" shiro:hasPermission="oc:markicam:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info" onclick="testConnection()" shiro:hasPermission="oc:markicam:test">
                    <i class="fa fa-plug"></i> 测试连接
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 配置表单模态框 -->
    <div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-labelledby="configModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="configModalLabel">Markicam配置</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <input type="hidden" id="configId" name="id">
                        <div class="form-group">
                            <label for="orgId">组织ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="orgId" name="org_id" required>
                            <small class="form-text text-muted">Markicam平台的组织ID</small>
                        </div>
                        <div class="form-group">
                            <label for="apiKey">API密钥 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="apiKey" name="api_key" required>
                            <small class="form-text text-muted">从Markicam平台申请的API密钥</small>
                        </div>
                        <div class="form-group">
                            <label for="baseUrl">API基础URL</label>
                            <input type="text" class="form-control" id="baseUrl" name="base_url" value="https://open-api.markiapp.com">
                            <small class="form-text text-muted">Markicam API的基础URL</small>
                        </div>
                        <div class="form-group">
                            <label for="syncInterval">同步间隔(秒)</label>
                            <input type="number" class="form-control" id="syncInterval" name="sync_interval" value="3600" min="60">
                            <small class="form-text text-muted">自动同步的时间间隔，最小60秒</small>
                        </div>
                        <div class="form-group">
                            <label for="isEnabled">状态</label>
                            <select class="form-control" id="isEnabled" name="is_enabled">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";
        
        $(function() {
            var options = {
                url: prefix + "/config/list",
                createUrl: prefix + "/config/add",
                updateUrl: prefix + "/config/edit/{id}",
                removeUrl: prefix + "/config/remove",
                exportUrl: prefix + "/config/export",
                modalName: "Markicam配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'org_id',
                    title: '组织ID'
                },
                {
                    field: 'api_key',
                    title: 'API密钥',
                    formatter: function(value, row, index) {
                        if (value && value.length > 8) {
                            return value.substring(0, 8) + '****';
                        }
                        return value;
                    }
                },
                {
                    field: 'base_url',
                    title: 'API基础URL'
                },
                {
                    field: 'sync_interval',
                    title: '同步间隔(秒)'
                },
                {
                    field: 'is_enabled',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="badge badge-success">启用</span>';
                        } else {
                            return '<span class="badge badge-secondary">禁用</span>';
                        }
                    }
                },
                {
                    field: 'last_sync_time',
                    title: '最后同步时间'
                },
                {
                    field: 'created_at',
                    title: '创建时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="testSingleConnection(\'' + row.id + '\')"><i class="fa fa-plug"></i>测试</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function add() {
            $('#configModalLabel').text('新增Markicam配置');
            $('#configForm')[0].reset();
            $('#configId').val('');
            $('#configModal').modal('show');
        }

        function edit(id) {
            if (!id) {
                var rows = $.table.selectColumns("id");
                if (rows.length == 0) {
                    $.modal.alertWarning("请选择一条记录");
                    return;
                }
                id = rows[0];
            }
            
            // 获取配置详情
            $.get(prefix + "/config/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    $('#configModalLabel').text('编辑Markicam配置');
                    $('#configId').val(data.id);
                    $('#orgId').val(data.org_id);
                    $('#apiKey').val(data.api_key);
                    $('#baseUrl').val(data.base_url);
                    $('#syncInterval').val(data.sync_interval);
                    $('#isEnabled').val(data.is_enabled);
                    $('#configModal').modal('show');
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function saveConfig() {
            var formData = $('#configForm').serialize();
            $.post(prefix + "/config/save", formData, function(result) {
                if (result.code == 0) {
                    $.modal.alertSuccess("保存成功");
                    $('#configModal').modal('hide');
                    $.table.refresh();
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function remove(id) {
            var ids = id ? [id] : $.table.selectColumns("id");
            if (ids.length == 0) {
                $.modal.alertWarning("请选择要删除的数据");
                return;
            }
            $.modal.confirm("确定删除选中的" + ids.length + "条数据吗？", function() {
                $.post(prefix + "/config/remove", { "ids": ids.join(",") }, function(result) {
                    if (result.code == 0) {
                        $.modal.alertSuccess("删除成功");
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        function testConnection() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择一条配置记录");
                return;
            }
            testSingleConnection(rows[0]);
        }

        function testSingleConnection(id) {
            $.modal.loading("正在测试连接...");
            $.post(prefix + "/test/connection", { "config_id": id }, function(result) {
                $.modal.closeLoading();
                if (result.code == 0) {
                    $.modal.alertSuccess("连接测试成功");
                } else {
                    $.modal.alertError("连接测试失败：" + result.msg);
                }
            }).fail(function() {
                $.modal.closeLoading();
                $.modal.alertError("连接测试失败");
            });
        }
    </script>
</body>
</html>

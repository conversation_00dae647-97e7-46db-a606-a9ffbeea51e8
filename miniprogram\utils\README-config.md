# 配置管理系统使用指南

## 概述

本项目采用独立的配置管理系统，通过 `ConfigManager` 和 `ConfigHelper` 提供统一、规范且方便维护的配置访问接口。

## 核心组件

### 1. ConfigManager (配置管理器)
- **文件**: `utils/configManager.js`
- **职责**: 配置的解析、缓存、更新和监听
- **特性**: 单例模式、配置缓存、变更通知

### 2. ConfigHelper (配置访问工具)
- **文件**: `utils/configHelper.js`
- **职责**: 提供便捷的配置访问接口
- **特性**: 分类访问、响应式绑定、页面混入

### 3. ConfigParser (配置解析器)
- **文件**: `utils/configParser.js`
- **职责**: extJson配置的解析和标准化
- **特性**: 类型安全、默认值处理

## 使用方法

### 基础配置访问

```javascript
import { getConfigValue, isConfigEnabled, getConfigNumber } from '../../utils/configHelper.js'

// 获取配置值
const title = getConfigValue('miniprogram_title', '默认标题')

// 检查功能是否启用
const isScreenshotEnabled = isConfigEnabled('enable_screenshot')

// 获取数值配置
const menuRows = getConfigNumber('menu_rows', 4)
```

### 分类配置访问

```javascript
import { UIConfig, FeatureConfig, ShareConfig } from '../../utils/configHelper.js'

// UI配置
const title = UIConfig.getTitle()
const subtitle = UIConfig.getSubtitle()
const menuRows = UIConfig.getMenuRows()
const isBigCardEnabled = UIConfig.isBigCardEnabled()

// 功能配置
const isMarkEnabled = FeatureConfig.isMarkEnabled()
const isScreenshotEnabled = FeatureConfig.isScreenshotEnabled()

// 分享配置
const shareConfig = ShareConfig.getShareConfig()
const isShareEnabled = ShareConfig.isEnabled()
```

### 配置监听

#### 方法1: 页面混入
```javascript
import { ConfigListenerMixin } from '../../utils/configHelper.js'

Page({
  // 混入配置监听能力
  ...ConfigListenerMixin,

  // 配置变更回调
  onConfigChange(event) {
    console.log('配置已更新:', event.newConfig)
    // 根据配置变更更新页面
    this.updatePageByConfig(event.newConfig)
  }
})
```

#### 方法2: 响应式绑定
```javascript
import { bindConfigToData, bindMultipleConfigToData } from '../../utils/configHelper.js'

Page({
  onLoad() {
    // 单个配置绑定
    bindConfigToData(this, 'miniprogram_title', 'pageTitle')
    
    // 批量配置绑定
    bindMultipleConfigToData(this, {
      'menu_rows': 'menuRows',
      'show_big_card': 'showBigCard',
      'enable_screenshot': 'enableScreenshot'
    })
  }
})
```

#### 方法3: 手动监听
```javascript
import { getConfigManager } from '../../utils/configManager.js'

Page({
  onLoad() {
    const configManager = getConfigManager()
    
    // 添加监听器
    configManager.addListener('my-page', (event) => {
      console.log('配置变更:', event)
      this.handleConfigChange(event)
    })
  },

  onUnload() {
    const configManager = getConfigManager()
    // 移除监听器
    configManager.removeListener('my-page')
  }
})
```

## 配置分类

### UI配置 (ui)
- `miniprogram_title`: 小程序标题
- `miniprogram_subtitle`: 小程序副标题
- `enable_miniprogram_title`: 是否显示标题
- `menu_rows`: 菜单行数
- `show_big_card`: 是否显示大卡片

### 功能配置 (feature)
- `enable_miniprogram_mark`: 是否启用小程序标记
- `enable_screenshot`: 是否允许截屏

### 分享配置 (share)
- `enable_share`: 是否启用分享
- `share_title`: 分享标题
- `share_desc`: 分享描述
- `share_image`: 分享图片
- `enable_share_statistics`: 是否启用分享统计
- `show_sharer_info`: 是否显示分享者信息

## 最佳实践

### 1. 统一访问方式
```javascript
// ✅ 推荐：使用ConfigHelper
import { UIConfig } from '../../utils/configHelper.js'
const menuRows = UIConfig.getMenuRows()

// ❌ 避免：直接访问globalData
const menuRows = getApp().globalData.extConfig?.menu_rows || 4
```

### 2. 配置缓存
```javascript
// ✅ 推荐：利用内置缓存
const config = getAllConfig() // 自动缓存

// ❌ 避免：重复解析
const config1 = ConfigParser.parseExtJson(communityInfo)
const config2 = ConfigParser.parseExtJson(communityInfo) // 重复解析
```

### 3. 类型安全
```javascript
// ✅ 推荐：使用类型安全的方法
const menuRows = getConfigNumber('menu_rows', 4) // 确保返回数字
const isEnabled = isConfigEnabled('enable_share') // 确保返回布尔值

// ❌ 避免：直接使用原始值
const menuRows = parseInt(getConfigValue('menu_rows')) || 4
```

### 4. 配置监听
```javascript
// ✅ 推荐：及时清理监听器
Page({
  onLoad() {
    configManager.addListener('page-id', this.handleConfigChange)
  },
  onUnload() {
    configManager.removeListener('page-id') // 防止内存泄漏
  }
})
```

## 扩展配置

### 添加新配置项

1. **更新ConfigParser**:
```javascript
// utils/configParser.js
static normalizeExtConfig(extConfig) {
  return {
    // 现有配置...
    
    // 新增配置
    new_feature_enabled: this.safeGetString(extConfig.new_feature_enabled, '0'),
  }
}
```

2. **更新ConfigManager默认值**:
```javascript
// utils/configManager.js
getDefaultConfig() {
  return {
    // 现有配置...
    
    // 新增配置
    new_feature_enabled: '0',
  }
}
```

3. **添加便捷访问方法**:
```javascript
// utils/configHelper.js
export const FeatureConfig = {
  // 现有方法...
  
  // 新增方法
  isNewFeatureEnabled() {
    return isConfigEnabled('new_feature_enabled')
  }
}
```

## 故障排除

### 常见问题

1. **配置未生效**
   - 检查配置是否正确解析
   - 确认配置管理器已初始化
   - 验证配置键名是否正确

2. **内存泄漏**
   - 确保页面卸载时移除配置监听器
   - 避免重复添加相同ID的监听器

3. **配置缓存问题**
   - 配置更新后缓存会自动清除
   - 可手动调用 `configManager.clearCache()`

### 调试工具

```javascript
import { getConfigManager } from '../../utils/configManager.js'

const configManager = getConfigManager()

// 查看配置状态
console.log('配置状态:', configManager.getStatus())

// 查看所有配置
console.log('所有配置:', configManager.getAll())

// 清除缓存
configManager.clearCache()
```

## 迁移指南

### 从旧方式迁移

```javascript
// 旧方式
const app = getApp()
const extConfig = app.globalData.extConfig || {}
const menuRows = parseInt(extConfig.menu_rows) || 4

// 新方式
import { UIConfig } from '../../utils/configHelper.js'
const menuRows = UIConfig.getMenuRows()
```

这样的迁移可以获得：
- 更好的类型安全
- 自动缓存优化
- 统一的访问接口
- 配置变更监听能力

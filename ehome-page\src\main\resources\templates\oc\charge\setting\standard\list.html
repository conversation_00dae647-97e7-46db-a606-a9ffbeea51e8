<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收费标准列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <!-- 搜索区域 -->
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>收费名称：</label>
                                <input type="text" name="feeName"/>
                            </li>
                            <li>
                                <label>收费类型：</label>
                                <select name="feeType">
                                    <option value="">所有</option>
                                    <option value="1">周期性收费</option>
                                    <option value="2">走表收费</option>
                                    <option value="3">临时性收费</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                                    <i class="fa fa-search"></i>&nbsp;搜索
                                </a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                    <i class="fa fa-refresh"></i>&nbsp;重置
                                </a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <!-- 工具栏 -->
            <div class="btn-group-sm" id="toolbar" role="group">
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fa fa-plus"></i> 添加收费标准 <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a href="javascript:void(0)" onclick="addChargeStandard('周期性收费',1)">周期性收费</a></li>
                        <li><a href="javascript:void(0)" onclick="addChargeStandard('走表收费',2)">走表收费</a></li>
                        <li><a href="javascript:void(0)" onclick="addChargeStandard('临时性收费',3)">临时性收费</a></li>
                    </ul>
                </div>
                <a class="btn btn-primary single disabled" onclick="editSelectedChargeStandard()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                    <i class="fa fa-remove"></i> 删除
                </a>
            </div>

            <!-- 表格 -->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/charge/setting/standard";
        
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                modalName: "收费标准",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'name',
                    title: '收费名称'
                },
                {
                    field: 'charge_type',
                    title: '收费类型',
                    formatter: function(value, row, index) {
                        var chargeType = parseInt(value);
                        if (chargeType === 1) return '周期性收费';
                        if (chargeType === 2) return '走表收费';
                        if (chargeType === 3) return '临时性收费';
                        return '未知类型';
                    }
                },
                {
                    field: 'unit',
                    title: '计费精度',
                    formatter: function(value, row) {
                        var precisionText = '';
                        var unitValue = parseInt(value);
                        switch(unitValue) {
                            case 1: precisionText = '元（不保留小数）'; break;
                            case 2: precisionText = '角（保留一位小数）'; break;
                            case 3: precisionText = '分（保留两位小数）'; break;
                            default: return '-';
                        }

                        var roundText = '';
                        var roundValue = parseInt(row.round_type);
                        switch(roundValue) {
                            case 0: roundText = ',四舍五入'; break;
                            case 1: roundText = ',抹零'; break;
                            case 2: roundText = ',向上取整'; break;
                        }

                        return precisionText + roundText;
                    }
                },
                {
                    field: 'period_type',
                    title: '收费方式',
                    formatter: function(value, row) {
                        var periodValue = parseInt(value);
                        var result = '';

                        switch(periodValue) {
                            case 101:
                                result = '按月生成账单（推荐）';
                                break;
                            default:
                                return '-';
                        }

                        // 添加不足一月处理方式
                        if (row.incomplete_month_handling) {
                            var incompleteText = '';
                            var incompleteValue = parseInt(row.incomplete_month_handling);
                            switch(incompleteValue) {
                                case 1: incompleteText = '按天收费'; break;
                                case 2: incompleteText = '按月收费'; break;
                                case 3: incompleteText = '不收费'; break;
                            }
                            if (incompleteText) {
                                result += '<br/>不足一月按' + incompleteText;
                            }
                        }

                        return result;
                    }
                },
                {
                    field: 'count_type',
                    title: '金额计算方式',
                    formatter: function(value, row) {
                        var countValue = parseInt(value);

                        if (countValue === 200) {
                            // 固定金额
                            var amount = row.fixed_amount || '0';
                            return '固定金额: ' + amount + '元';
                        } else if (countValue === 100) {
                            // 判断是否为走表收费
                            if (row.charge_type === 2) {
                                // 走表收费：单价*使用量
                                var result = '单价*使用量';

                                // 获取单价
                                var price = '';
                                if (row.count_info) {
                                    try {
                                        var countInfo = JSON.parse(row.count_info);
                                        if (countInfo.meter_price && parseFloat(countInfo.meter_price) > 0) {
                                            price = countInfo.meter_price;
                                        }
                                    } catch (e) {
                                        // JSON解析失败，忽略
                                    }
                                }

                                if (price) {
                                    result += ':<br/>' + price + '元/度';
                                }

                                return result;
                            } else {
                                // 周期性收费和临时性收费：单价*计量方式
                                var result = '单价*计量方式';

                                // 获取计量方式
                                var measurementType = '';
                                if (row.count_info) {
                                    try {
                                        var countInfo = JSON.parse(row.count_info);
                                        var areaType = parseInt(countInfo.area_type);
                                        switch(areaType) {
                                            case 1: measurementType = '建筑面积'; break;
                                            case 2: measurementType = '使用面积'; break;
                                        }
                                    } catch (e) {
                                        // JSON解析失败，忽略
                                    }
                                }

                                // 获取单价
                                var price = '';
                                if (row.count_info) {
                                    try {
                                        var countInfo = JSON.parse(row.count_info);
                                        if (countInfo.price && parseFloat(countInfo.price) > 0) {
                                            price = countInfo.price;
                                        }
                                    } catch (e) {
                                        // JSON解析失败，忽略
                                    }
                                }

                                if (measurementType && price) {
                                    result += ':<br/>' + measurementType + '*' + price;
                                }

                                return result;
                            }
                        }

                        return '-';
                    }
                },

                {
                    field: 'accounting_period_day',
                    title: '账单生成日期',
                    formatter: function(value) {
                        return value ? '每月' + value + '号' : '-';
                    }
                },
                {
                    field: 'late_money_type',
                    title: '违约金',
                    formatter: function(value) {
                        var lateValue = parseInt(value);
                        switch(lateValue) {
                            case 0: return '无';
                            case 1: return '计算违约金';
                            default: return '-';
                        }
                    }
                },
                {
                    field: 'related_asset_count',
                    title: '关联资产数',
                    formatter: function(value, row, index) {
                        var count = value || 0;
                        if (count > 0) {
                            return '<a href="javascript:void(0)" onclick="viewBindings(' + row.id + ')" style="color: #007bff;">' + count + '</a>';
                        } else {
                            return count;
                        }
                    }
                },
                {
                    field: 'is_active',
                    title: '状态',
                    formatter: function(value) {
                        if (value) {
                            return '<span class="label label-success">启用</span>';
                        } else {
                            return '<span class="label label-danger">禁用</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var chargeTypeName = getChargeTypeName(row.charge_type);
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editChargeStandard(\'' + row.id + '\', \'' + chargeTypeName + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 添加收费标准函数
        function addChargeStandard(chargeTypeName,chargeType) {
            var url = prefix + "/add?chargeType=" + chargeType+"&chargeTypeName="+chargeTypeName;
            $.modal.openFull("添加收费标准（" + chargeTypeName + "）", url);
        }

        // 获取收费类型名称
        function getChargeTypeName(chargeTypeCode) {
            if (chargeTypeCode === 1 || chargeTypeCode === '1') return '周期性收费';
            if (chargeTypeCode === 2 || chargeTypeCode === '2') return '走表收费';
            if (chargeTypeCode === 3 || chargeTypeCode === '3') return '临时性收费';
            return '周期性收费';
        }

        // 编辑收费标准函数
        function editChargeStandard(id, chargeType) {
            var url = prefix + "/edit/" + id;
            $.modal.openFull("编辑收费标准（" + chargeType + "）", url);
        }

        // 工具栏编辑选中的收费标准
        function editSelectedChargeStandard() {
            var rows = $("#bootstrap-table").bootstrapTable('getSelections');
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if (rows.length > 1) {
                $.modal.alertWarning("只能选择一条记录");
                return;
            }
            var row = rows[0];
            editChargeStandard(row.id, getChargeTypeName(row.charge_type));
        }

        // 查看收费绑定
        function viewBindings(chargeStandardId) {
            var url = ctx + "oc/charge/setting/binding/mgr?chargeStandardId=" + chargeStandardId;
            $.modal.openTab("收费绑定管理", url);
        }
    </script>
</body>
</html> 
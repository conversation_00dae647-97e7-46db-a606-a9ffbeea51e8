package com.ehome.system.mapper;

import com.ehome.system.domain.SysNoticeComment;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 通知公告评论Mapper接口
 * 
 * <AUTHOR>
 */
public interface SysNoticeCommentMapper 
{
    /**
     * 查询通知公告评论
     * 
     * @param commentId 通知公告评论主键
     * @return 通知公告评论
     */
    public SysNoticeComment selectSysNoticeCommentByCommentId(Long commentId);

    /**
     * 查询通知公告评论列表
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 通知公告评论集合
     */
    public List<SysNoticeComment> selectSysNoticeCommentList(SysNoticeComment sysNoticeComment);

    /**
     * 查询指定公告的评论列表（包含回复）
     * 
     * @param noticeId 公告ID
     * @return 评论列表
     */
    public List<SysNoticeComment> selectCommentsByNoticeId(Long noticeId);

    /**
     * 查询指定公告的顶级评论列表
     * 
     * @param noticeId 公告ID
     * @return 顶级评论列表
     */
    public List<SysNoticeComment> selectTopCommentsByNoticeId(Long noticeId);

    /**
     * 查询指定评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    public List<SysNoticeComment> selectRepliesByParentId(Long parentId);

    /**
     * 查询指定评论的回复列表（包含用户自己的待审核回复）
     *
     * @param parentId 父评论ID
     * @param userId 用户ID
     * @return 回复列表
     */
    public List<SysNoticeComment> selectRepliesByParentIdWithUser(@Param("parentId") Long parentId, @Param("userId") String userId);

    /**
     * 统计指定公告的评论数量
     * 
     * @param noticeId 公告ID
     * @return 评论数量
     */
    public int countCommentsByNoticeId(Long noticeId);

    /**
     * 统计指定评论的回复数量
     * 
     * @param parentId 父评论ID
     * @return 回复数量
     */
    public int countRepliesByParentId(Long parentId);

    /**
     * 新增通知公告评论
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 结果
     */
    public int insertSysNoticeComment(SysNoticeComment sysNoticeComment);

    /**
     * 修改通知公告评论
     * 
     * @param sysNoticeComment 通知公告评论
     * @return 结果
     */
    public int updateSysNoticeComment(SysNoticeComment sysNoticeComment);

    /**
     * 删除通知公告评论
     * 
     * @param commentId 通知公告评论主键
     * @return 结果
     */
    public int deleteSysNoticeCommentByCommentId(Long commentId);

    /**
     * 批量删除通知公告评论
     * 
     * @param commentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysNoticeCommentByCommentIds(Long[] commentIds);

    /**
     * 软删除评论（更新状态为删除）
     * 
     * @param commentId 评论ID
     * @return 结果
     */
    public int softDeleteComment(Long commentId);

    /**
     * 审核评论（更新状态）
     *
     * @param commentId 评论ID
     * @param status 状态
     * @return 结果
     */
    public int auditComment(@Param("commentId") Long commentId, @Param("status") String status);

    /**
     * 查询指定小区所有公告的评论列表（包含公告标题）
     *
     * @param comment 评论信息（包含小区ID等查询条件）
     * @return 评论集合
     */
    public List<SysNoticeComment> selectAllCommentsByCommunity(SysNoticeComment comment);
}

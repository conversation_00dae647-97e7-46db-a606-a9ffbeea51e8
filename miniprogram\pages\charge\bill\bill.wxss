.container {
  padding: 0 0 120rpx 0;
  background: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部整体区域 */
.header-wrapper {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #69c0ff 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  position: relative;
  z-index: 100;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 26rpx;
  position: relative;
  z-index: 2;
}

.location-wrapper {
  display: flex;
  align-items: center;
}

.navbar-location {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* Tab容器 */
.tab-container {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 筛选条件 */
.filter-container {
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

/* 内容区域 */
.content-area {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 统计信息卡片 */
.statistics-card {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stat-value {
  font-size: 28rpx;
  font-weight: 500;
}

.stat-value.arrear {
  color: #ff4757;
}

/* 楼栋列表 */
.building-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.building-item {
  border-bottom: 1rpx solid #f0f0f0;
}

.building-item:last-child {
  border-bottom: none;
}

/* 房屋列表 */
.house-list {
  padding: 20rpx;
  background: #fafafa;
}

.house-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: space-between;
}

.house-card {
  width: calc(50% - 50rpx);
  background: #fff;
  border-radius: 12rpx;
  padding: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  border: 2rpx solid transparent;
  min-height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.house-card.arrear {
  justify-content: space-between;
  border-color: #ff4757;
  background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
}

.house-card.normal {
  border-color: #e8e8e8;
  background: #fff;
}

/* 正常缴费房屋样式 */
.house-content-normal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.house-unit-normal {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.house-icon-normal {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
}

.unit-text-normal {
  font-size: 28rpx;
  font-weight: normal;
  color: #333;
  text-align: center;
}

/* 欠费房屋样式 */
.house-content-arrear {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  gap: 8rpx;
}

.house-unit-arrear {
  text-align: center;
}

.house-amount-arrear {
  text-align: center;
}

.unit-text-arrear {
  font-size: 28rpx;
  font-weight: normal;
  color: #ff4757;
  text-align: center;
}

.amount-text-arrear {
  font-size: 26rpx;
  font-weight: normal;
  color: #ff4757;
  text-align: center;
}

/* 加载和空状态 */
.house-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120rpx;
  background: #fafafa;
}

.house-empty {
  padding: 40rpx;
  background: #fafafa;
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 车位内容（预留） */
.parking-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.coming-soon {
  text-align: center;
}

/* Vant组件样式覆盖 */
.van-collapse-item__content {
  padding: 0 !important;
}

.van-dropdown-menu {
  box-shadow: none;
}

.van-dropdown-menu__bar {
  height: 88rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.08);
}

.van-tabs__line {
  background-color: #1890ff;
}

.van-tab--active {
  color: #1890ff;
}

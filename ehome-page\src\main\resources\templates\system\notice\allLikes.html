<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小区公告点赞记录')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="like-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                住户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                公告标题：<input type="text" name="noticeTitle"/>
                            </li>
                            <li>
                                来源：<select name="userType">
                                    <option value="">所有</option>
                                    <option value="wx_user">微信用户</option>
                                    <option value="sys_user">系统用户</option>
                                </select>
                            </li>
                            <li>
                                点赞状态：<select name="status">
                                    <option value="">所有</option>
                                    <option value="0">有效</option>
                                    <option value="1">已取消</option>
                                </select>
                            </li>
                            <li>
                                点赞时间：<input type="text" class="time-input" name="likeTime"/>
                            </li>
                            <li>
                                房屋名称：<input type="text" name="houseName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-info" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice/allLikes";

        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "点赞记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'likeId',
                    title: '序号'
                },
                {
                    field: 'noticeTitle',
                    title: '公告标题',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '未知公告';
                    }
                },
                {
                    field: 'userName',
                    title: '点赞住户',
                    formatter: function(value, row, index) {
                        var badge = row.userType === 'sys_user' ? 
                            '<span class="badge badge-danger">管理员</span>' : 
                            '<span class="badge badge-success">用户</span>';
                        return value + ' ' + badge;
                    }
                },
                {
                    field: 'houseName',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="badge badge-info">' + value + '</span>';
                        }
                        return '<span class="text-muted">未绑定</span>';
                    }
                },
                {
                    field: 'status',
                    title: '点赞状态',
                    formatter: function(value, row, index) {
                        if (value === '0') {
                            return '<span class="badge badge-success">有效</span>';
                        } else if (value === '1') {
                            return '<span class="badge badge-warning">已取消</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'likeTime',
                    title: '点赞时间',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewLike(\'' + row.likeId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewNotice(\'' + row.noticeId + '\')"><i class="fa fa-file-text"></i>查看公告</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 查看点赞详情
        function viewLike(likeId) {
            // 从表格中获取点赞数据
            var table = $("#bootstrap-table");
            var data = table.bootstrapTable('getData');
            var like = null;

            for (var i = 0; i < data.length; i++) {
                if (data[i].likeId == likeId) {
                    like = data[i];
                    break;
                }
            }

            if (!like) {
                $.modal.msgError("点赞数据不存在");
                return;
            }

            var statusText = like.status === '0' ? 
                '<span class="badge badge-success">有效</span>' : 
                '<span class="badge badge-warning">已取消</span>';

            var userTypeText = like.userType === 'sys_user' ?
                '<span class="badge badge-danger">管理员</span>' :
                '<span class="badge badge-success">用户</span>';

            var likeTime = $.common.dateFormat(like.likeTime, 'yyyy-MM-dd HH:mm:ss');

            var content = '<div class="row">' +
                '<div class="col-sm-12">' +
                '<table class="table table-bordered">' +
                '<tr><td width="120px"><strong>点赞ID</strong></td><td>' + like.likeId + '</td></tr>' +
                '<tr><td><strong>所属公告</strong></td><td>' + (like.noticeTitle || '未知公告') + '</td></tr>' +
                '<tr><td><strong>点赞用户</strong></td><td>' + like.userName + ' ' + userTypeText + '</td></tr>' +
                '<tr><td><strong>点赞状态</strong></td><td>' + statusText + '</td></tr>' +
                '<tr><td><strong>点赞时间</strong></td><td>' + likeTime + '</td></tr>' +
                '</table>' +
                '</div>' +
                '</div>';

            // 使用自定义模态框
            var modalHtml = '<div class="modal fade" id="likeDetailModal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog modal-lg" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<h4 class="modal-title">点赞详情</h4>' +
                '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
                '</div>' +
                '<div class="modal-body">' + content + '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除已存在的模态框
            $('#likeDetailModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalHtml);
            $('#likeDetailModal').modal('show');
        }

        // 查看公告详情
        function viewNotice(noticeId) {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/view/" + noticeId;
            $.modal.openTab("公告详情", url);
        }

        // 导出数据
        function exportData() {
            $.modal.confirm("确定要导出点赞记录数据吗？", function() {
                $.post(prefix + "/export", $("#like-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

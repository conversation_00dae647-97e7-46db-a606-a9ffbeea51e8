-- 初始化微信小程序导航数据
-- 使用方法：将 {community_id} 替换为实际的小区ID

-- 清理可能存在的测试数据（可选）
-- DELETE FROM eh_wx_nav WHERE community_id = '{community_id}';

-- 1. 首页导航菜单
INSERT INTO eh_wx_nav (community_id, nav_name, icon_name, tap_name, nav_type, source, is_default, sort, create_time, status)
VALUES 
('{community_id}', '小区信息', 'home-o', 'goToOcInfo', 'page', 'indexNav', 1, 1, NOW(), '0'),
('{community_id}', '物业服务', 'service-o', 'goToService', 'page', 'indexNav', 1, 2, NOW(), '0'),
('{community_id}', '便民电话', 'phone-o', 'goToServiceTel', 'page', 'indexNav', 1, 3, NOW(), '0'),
('{community_id}', '公告通知', 'bell-o', 'goToNotice', 'page', 'indexNav', 1, 4, NOW(), '0'),
('{community_id}', '在线报修', 'tool-o', 'goToRepair', 'page', 'indexNav', 1, 5, NOW(), '0'),
('{community_id}', '投诉建议', 'chat-o', 'goToComplaint', 'page', 'indexNav', 1, 6, NOW(), '0'),
('{community_id}', '缴费查询', 'bill-o', 'goToPayment', 'page', 'indexNav', 1, 7, NOW(), '0'),
('{community_id}', '更多服务', 'apps-o', 'goToMore', 'page', 'indexNav', 1, 8, NOW(), '0');

-- 2. 顶部菜单（top_show = 1）
INSERT INTO eh_wx_nav (community_id, nav_name, icon_name, tap_name, nav_type, source, is_default, top_show, sort, create_time, status)
VALUES 
('{community_id}', '小区公告', 'volume-o', 'goToNotice', 'page', 'topNav', 0, 1, 1, NOW(), '0'),
('{community_id}', '物业缴费', 'gold-coin-o', 'goToPayment', 'page', 'topNav', 0, 1, 2, NOW(), '0');

-- 3. 服务中心菜单
INSERT INTO eh_wx_nav (community_id, nav_name, icon_name, tap_name, nav_type, source, is_default, sort, create_time, status)
VALUES 
('{community_id}', '在线报修', 'tool-o', 'goToRepair', 'page', 'serviceNav', 0, 1, NOW(), '0'),
('{community_id}', '投诉建议', 'chat-o', 'goToComplaint', 'page', 'serviceNav', 0, 2, NOW(), '0'),
('{community_id}', '访客登记', 'friends-o', 'goToVisitor', 'page', 'serviceNav', 0, 3, NOW(), '0'),
('{community_id}', '快递代收', 'shopping-cart-o', 'goToExpress', 'page', 'serviceNav', 0, 4, NOW(), '0'),
('{community_id}', '车位管理', 'location-o', 'goToParking', 'page', 'serviceNav', 0, 5, NOW(), '0'),
('{community_id}', '社区活动', 'calendar-o', 'goToActivity', 'page', 'serviceNav', 0, 6, NOW(), '0');

-- 4. 信息导航菜单
INSERT INTO eh_wx_nav (community_id, nav_name, icon_name, tap_name, nav_type, source, is_default, sort, create_time, status)
VALUES 
('{community_id}', '小区介绍', 'home-o', 'goToOcIntro', 'content', 'infoNav', 0, 1, NOW(), '0'),
('{community_id}', '物业介绍', 'manager-o', 'goToPropertyIntro', 'content', 'infoNav', 0, 2, NOW(), '0'),
('{community_id}', '服务指南', 'guide-o', 'goToServiceGuide', 'content', 'infoNav', 0, 3, NOW(), '0'),
('{community_id}', '收费标准', 'bill-o', 'goToFeeStandard', 'content', 'infoNav', 0, 4, NOW(), '0'),
('{community_id}', '联系我们', 'phone-o', 'goToContact', 'content', 'infoNav', 0, 5, NOW(), '0');

-- 5. 外部链接示例（如果需要）
/*
INSERT INTO eh_wx_nav (community_id, nav_name, icon_name, tap_name, nav_type, source, is_default, sort, create_time, status, nav_url)
VALUES 
('{community_id}', '天气预报', 'sun-o', '', 'url', 'indexNav', 0, 9, NOW(), '0', 'https://weather.com'),
('{community_id}', '周边商家', 'shop-o', '', 'url', 'indexNav', 0, 10, NOW(), '0', 'https://map.baidu.com');
*/

const app = getApp()
import { getStateManager } from '../../utils/stateManager.js'

Page({
  data: {
    houseId: '',
    houseInfo: null,
    communityName: '', // 小区名称
    relType: '2', // 默认家庭成员
    invitePhone: '', // 接收人手机号
    formattedPhone: '', // 格式化后的手机号显示
    remark: '',
    creating: false,
    shareInfo: null,
    showShareModal: false,
    canCreate: false,
    phoneError: '' // 手机号错误提示
  },

  onLoad(options) {
    // 获取小区信息
    this.loadCommunityInfo()

    if (options.houseId) {
      this.setData({ houseId: options.houseId })
      this.loadHouseInfo()
      this.checkCanCreate() // 初始检查
    } else {
      wx.showToast({
        title: '房屋信息错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载小区信息
  loadCommunityInfo() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()
      let communityInfo = state.communityInfo

      // 如果状态管理器中没有，则从本地存储获取
      if (!communityInfo) {
        communityInfo = wx.getStorageSync('communityInfo') || {}
      }

      this.setData({
        communityName: communityInfo.communityName || '智慧小区服务'
      })
    } catch (error) {
      console.error('获取小区信息失败:', error)
      this.setData({
        communityName: '智慧小区服务'
      })
    }
  },

  // 加载房屋信息
  async loadHouseInfo() {
    try {
      const res = await app.request({
        url: '/api/wx/house/detail',
        method: 'GET',
        data: { houseId: this.data.houseId }
      })

      if (res.code === 0) {
        const houseInfo = res.data
        // 构建房屋名称
        const houseName = this.buildHouseName(houseInfo)

        this.setData({
          houseInfo: {
            ...houseInfo,
            houseName: houseName,
            address: houseInfo.address || ''
          }
        })
      } else {
        throw new Error(res.msg || '获取房屋信息失败')
      }
    } catch (error) {
      console.error('获取房屋信息失败:', error)
      wx.showToast({
        title: error.message || '获取房屋信息失败',
        icon: 'none'
      })
    }
  },

  // 手机号输入
  onPhoneInput(e) {
    const value = e.detail.value || e.detail || ''
    // 只保留数字
    const phone = value.replace(/\D/g, '')
    // 格式化显示（添加空格）
    const formatted = this.formatPhoneDisplay(phone)
    // 验证手机号
    const error = this.validatePhone(phone)

    this.setData({
      invitePhone: phone,
      formattedPhone: formatted,
      phoneError: error
    })
    this.checkCanCreate()
  },

  // 格式化手机号显示
  formatPhoneDisplay(phone) {
    if (!phone) return ''
    if (phone.length <= 3) return phone
    if (phone.length <= 7) return `${phone.slice(0, 3)} ${phone.slice(3)}`
    return `${phone.slice(0, 3)} ${phone.slice(3, 7)} ${phone.slice(7, 11)}`
  },

  // 验证手机号
  validatePhone(phone) {
    if (!phone) return ''
    if (phone.length < 11) return '手机号长度不足'
    if (!/^1[3-9]\d{9}$/.test(phone)) return '手机号格式不正确'
    return ''
  },

  // 选择关系类型
  selectRelType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({ relType: type })
  },

  // 关系类型变化（保留兼容性）
  onRelTypeChange(e) {
    this.setData({ relType: e.detail })
    this.checkCanCreate()
  },

  // 备注输入
  onRemarkInput(e) {
    const value = e.detail.value || e.detail || ''
    this.setData({ remark: value })
  },

  // 检查是否可以创建邀请
  checkCanCreate() {
    const { invitePhone, phoneError } = this.data
    const canCreate = invitePhone && invitePhone.length === 11 && !phoneError
    this.setData({ canCreate })
  },

  // 创建邀请
  async createInvite() {
    if (this.data.creating || !this.data.canCreate) return

    try {
      this.setData({ creating: true })

      const res = await app.request({
        url: '/api/wx/house/card-invite/create',
        method: 'POST',
        data: {
          houseId: this.data.houseId,
          relType: parseInt(this.data.relType),
          invitePhone: this.data.invitePhone,
          remark: this.data.remark
        }
      })

      if (res.code === 0) {
        const shareData = res.data
        // 格式化有效期时间
        if (shareData.expireTime) {
          shareData.expireTimeFormatted = this.formatExpireTime(shareData.expireTime)
        }

        // 分享路径已包含所有必要信息，无需额外添加参数

        this.setData({
          shareInfo: shareData,
          showShareModal: true
        })
      } else {
        throw new Error(res.msg || '创建邀请失败')
      }
    } catch (error) {
      console.error('创建邀请失败:', error)
      wx.showToast({
        title: error.message || '创建邀请失败',
        icon: 'none'
      })
    } finally {
      this.setData({ creating: false })
    }
  },

  // 分享邀请
  onShareAppMessage() {
    const { shareInfo } = this.data
    
    if (!shareInfo) {
      wx.showToast({
        title: '请先创建邀请',
        icon: 'none'
      })
      return {}
    }

    return {
      title: shareInfo.shareTitle,
      path: shareInfo.sharePath,
      imageUrl: '/static/images/invite-cover.png'
    }
  },

  // 复制邀请链接
  copyInviteLink() {
    const { shareInfo, communityName } = this.data

    if (!shareInfo) {
      wx.showToast({
        title: '请先创建邀请',
        icon: 'none'
      })
      return
    }

    const appName = communityName || '智慧小区服务'
    const inviteText = `${shareInfo.shareTitle}\n\n点击链接加入房屋：\n小程序://${appName}${shareInfo.sharePath || ''}`

    wx.setClipboardData({
      data: inviteText,
      success: () => {
        wx.showToast({
          title: '邀请信息已复制',
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 关闭分享弹窗
  closeShareModal() {
    this.setData({ showShareModal: false })
    // 返回上一页
    wx.navigateBack()
  },

  // 格式化有效期时间
  formatExpireTime(expireTime) {
    if (!expireTime) return '24小时'

    const date = new Date(expireTime)
    const now = new Date()

    // 如果是今天，显示时间
    if (date.toDateString() === now.toDateString()) {
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      return `今天 ${hour}:${minute}`
    }

    // 如果是明天，显示明天+时间
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    if (date.toDateString() === tomorrow.toDateString()) {
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      return `明天 ${hour}:${minute}`
    }

    // 其他情况显示完整日期时间
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hour}:${minute}`
  },

  // 获取关系类型名称
  getRelTypeName(relType) {
    switch (relType) {
      case '1': return '业主'
      case '2': return '家庭成员'
      case '3': return '租户'
      default: return '未知'
    }
  },

  // 格式化过期时间
  formatExpireTime(time) {
    if (!time) return ''

    const date = new Date(time)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')

    return `${month}-${day} ${hour}:${minute}`
  },

  // 构建房屋名称
  buildHouseName(houseInfo) {
    if (!houseInfo) return ''

    let name = ''
    if (houseInfo.building_name) {
      name += houseInfo.building_name
    }
    if (houseInfo.unit_name) {
      if (name) name += '/'
      name += houseInfo.unit_name
    }
    if (houseInfo.room) {
      if (name) name += '/'
      name += houseInfo.room
    }

    return name
  }
})

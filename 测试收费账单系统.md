# 收费账单系统测试指南

## 测试前准备

### 1. 确保数据库表已创建
执行以下SQL创建账单表：
```sql
-- 执行 sql/charge_bill_tables.sql 中的建表语句
```

### 2. 检查基础数据
确保系统中有以下基础数据：
- 收费标准数据（eh_charge_standard表）
- 房屋信息数据（eh_house_info表）
- 收费绑定数据（eh_charge_binding表）

## 测试步骤

### 步骤1：访问账单管理页面
1. 登录系统
2. 访问：`/oc/charge/manage/bill/mgr`
3. 查看账单列表页面是否正常显示

### 步骤2：更新收费绑定时间（测试用）
1. 点击页面上的"更新绑定时间(测试用)"按钮
2. 确认执行，系统会将收费绑定的next_bill_time设置为昨天
3. 查看返回结果，确认更新了多少个绑定记录

### 步骤3：批量生成账单
1. 点击"批量生成账单"按钮
2. 确认执行，系统会自动生成账单
3. 查看生成结果，确认生成了多少条账单

### 步骤4：查看生成的账单
1. 刷新账单列表页面
2. 查看新生成的账单数据
3. 验证账单字段是否正确：
   - 资产名称
   - 收费项目
   - 账期月份
   - 应收金额
   - 支付状态等

### 步骤5：查看统计信息
1. 点击"统计信息"按钮
2. 查看账单统计数据：
   - 总账单数
   - 未缴费账单数
   - 已缴费账单数
   - 总应收金额等

## 测试用SQL语句

### 查看收费绑定数据
```sql
SELECT 
    id,
    asset_name,
    charge_standard_id,
    FROM_UNIXTIME(start_time) as start_time_str,
    FROM_UNIXTIME(next_bill_time) as next_bill_time_str,
    is_active
FROM eh_charge_binding 
WHERE is_active = 1
LIMIT 10;
```

### 查看生成的账单
```sql
SELECT 
    id,
    asset_name,
    charge_item_name,
    in_month,
    amount/100 as amount_yuan,
    pay_status,
    FROM_UNIXTIME(create_time) as create_time_str
FROM eh_charge_bill 
ORDER BY create_time DESC
LIMIT 10;
```

### 手动更新绑定时间（如果按钮不工作）
```sql
UPDATE eh_charge_binding 
SET next_bill_time = UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY))
WHERE is_active = 1 
AND (next_bill_time IS NULL OR next_bill_time = 0 OR next_bill_time > UNIX_TIMESTAMP(NOW()));
```

### 创建测试收费绑定数据（如果没有数据）
```sql
-- 假设有房屋数据和收费标准数据
INSERT INTO eh_charge_binding (
    asset_type,
    asset_id,
    asset_name,
    charge_standard_id,
    start_time,
    end_time,
    period_num,
    natural_period,
    next_bill_time,
    community_id,
    is_active,
    create_time,
    create_by
) VALUES (
    1, -- 房屋类型
    1001, -- 房屋ID（需要实际存在的房屋ID）
    '测试房屋1号', -- 房屋名称
    1, -- 收费标准ID（需要实际存在的收费标准ID）
    UNIX_TIMESTAMP('2024-01-01'), -- 开始时间
    UNIX_TIMESTAMP('2025-12-31'), -- 结束时间
    1, -- 周期数（月）
    0, -- 自然周期
    UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)), -- 下次账单时间（昨天）
    'test_community', -- 小区ID
    1, -- 启用状态
    UNIX_TIMESTAMP(NOW()), -- 创建时间
    'SYSTEM' -- 创建人
);
```

## 预期结果

### 成功的测试结果应该包括：
1. ✅ 页面正常显示，包含所有字段列
2. ✅ 更新绑定时间成功，返回更新数量
3. ✅ 批量生成账单成功，返回生成数量
4. ✅ 账单列表显示新生成的账单
5. ✅ 统计信息正确显示各项数据
6. ✅ 账单详情页面可以正常查看

### 常见问题排查：
1. **没有收费绑定数据**：检查eh_charge_binding表是否有数据
2. **绑定时间未到期**：使用"更新绑定时间"功能或手动执行SQL
3. **收费标准不存在**：检查eh_charge_standard表数据
4. **权限问题**：确保用户有相应的操作权限
5. **数据库连接问题**：检查数据库配置和连接状态

## 功能验证清单

- [ ] 账单列表页面显示正常
- [ ] 多条件筛选功能正常
- [ ] 批量生成账单功能正常
- [ ] 手动创建账单功能正常
- [ ] 账单详情查看功能正常
- [ ] 统计信息功能正常
- [ ] 账单删除功能正常（仅未缴费账单）
- [ ] 分页功能正常
- [ ] 排序功能正常

完成以上测试后，收费账单系统应该可以正常使用了。

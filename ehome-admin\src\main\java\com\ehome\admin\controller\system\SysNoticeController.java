package com.ehome.admin.controller.system;

import com.ehome.admin.service.IEhAttachmentRelationService;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.PageDomain;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.core.page.TableSupport;
import com.ehome.common.core.text.Convert;
import com.ehome.common.domain.CommunityConfig;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.poi.ExcelUtil;
import com.ehome.oc.service.ICommunityConfigService;
import com.ehome.oc.service.INoticeTypeService;
import com.ehome.oc.service.WxMessageService;
import com.ehome.system.domain.*;
import com.ehome.system.service.*;
import com.ehome.system.domain.SysMenuClickLogExport;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公告 信息操作处理
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController
{
    private String prefix = "system/notice";

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private ISysNoticeReadLogService readLogService;

    @Autowired
    private IEhAttachmentRelationService attachmentRelationService;

    @Autowired
    private ISysNoticeCommentService commentService;

    @Autowired
    private ISysNoticeLikeService likeService;

    @Autowired
    private ISysNoticeShareService shareService;

    @Autowired
    private WxMessageService wxMessageService;

    @Autowired
    private INoticeTypeService noticeTypeService;

    @Autowired
    private ICommunityConfigService communityConfigService;

    @RequiresPermissions("system:notice:view")
    @GetMapping()
    public String notice(ModelMap mmap)
    {
        mmap.put("noticeTypes", getNoticeTypes());
        return prefix + "/notice";
    }

    /**
     * 查询公告列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysNotice notice)
    {
        startPage();
        notice.setCommunityId(getSysUser().getCommunityId());
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 新增公告
     */
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        mmap.put("noticeTypes", getNoticeTypes());
        mmap.put("signatureConfig", getSignatureConfig());
        return prefix + "/add";
    }

    /**
     * 新增保存公告
     */
    @RequiresPermissions("system:notice:add")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysNotice notice)
    {
        notice.setCreateBy(getLoginName());
        notice.setPmsId(getSysUser().getPmsId());
        notice.setCommunityId(getSysUser().getCommunityId());

        int result = noticeService.insertNotice(notice);
        if (result > 0) {
            // 保存附件关联
            String attachmentIds = getRequest().getParameter("attachmentIds");
            if (attachmentIds != null && !attachmentIds.trim().isEmpty()) {
                String[] fileIds = attachmentIds.split(",");
                attachmentRelationService.saveBusinessAttachments(
                    "notice",
                    notice.getNoticeId().toString(),
                    fileIds,
                    getLoginName(),
                    getSysUser().getCommunityId()
                );
            }

            // 发送通知消息（确保ID不为空）
            if (notice.getNoticeId() != null) {
                sendNoticeMessage(notice);
            } else {
                logger.warn("通知ID为空，无法发送消息: title={}", notice.getNoticeTitle());
            }
        }

        return toAjax(result);
    }

    /**
     * 修改公告
     */
    @RequiresPermissions("system:notice:edit")
    @GetMapping("/edit/{noticeId}")
    public String edit(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        mmap.put("notice", noticeService.selectNoticeById(noticeId));
        mmap.put("noticeTypes", getNoticeTypes());
        mmap.put("signatureConfig", getSignatureConfig());
        return prefix + "/edit";
    }

    /**
     * 修改保存公告
     */
    @RequiresPermissions("system:notice:edit")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysNotice notice)
    {
        notice.setUpdateBy(getLoginName());
        int result = noticeService.updateNotice(notice);

        if (result > 0) {
            // 更新附件关联
            String attachmentIds = getRequest().getParameter("attachmentIds");
            String[] fileIds = null;
            if (attachmentIds != null && !attachmentIds.trim().isEmpty()) {
                fileIds = attachmentIds.split(",");
            }
            attachmentRelationService.saveBusinessAttachments(
                "notice",
                notice.getNoticeId().toString(),
                fileIds,
                getLoginName(),
                getSysUser().getCommunityId()
            );
        }

        return toAjax(result);
    }

    /**
     * 查询公告详细
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/view/{noticeId}")
    public String view(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        // 查看公告
        SysNotice notice = noticeService.selectNoticeById(noticeId);
        mmap.put("notice", notice);
        return prefix + "/view";
    }

    /**
     * 删除公告
     */
    @RequiresPermissions("system:notice:remove")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        // 删除公告附件关联
        if (ids != null && !ids.trim().isEmpty()) {
            String[] noticeIds = ids.split(",");
            for (String noticeId : noticeIds) {
                attachmentRelationService.deleteBusinessAttachments("notice", noticeId);
            }
        }

        return toAjax(noticeService.deleteNoticeByIds(ids));
    }

    /**
     * 查看公告阅读记录
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/readLog/{noticeId}")
    public String readLog(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        SysNotice notice = noticeService.selectNoticeById(noticeId);
        mmap.put("notice", notice);
        return prefix + "/readLog";
    }

    /**
     * 获取公告附件列表
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/attachments/{noticeId}")
    @ResponseBody
    public AjaxResult getAttachments(@PathVariable("noticeId") Long noticeId)
    {
        try {
            List<Record> attachmentRecords = attachmentRelationService.getAttachmentDetails("notice", noticeId.toString());
            List<Map<String, Object>> attachments = new ArrayList<>();
            if (attachmentRecords != null) {
                for (Record record : attachmentRecords) {
                    attachments.add(record.toMap());
                }
            }
            return AjaxResult.success(attachments);
        } catch (Exception e) {
            return AjaxResult.error("获取附件列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询公告阅读记录列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/readLog/list")
    @ResponseBody
    public TableDataInfo readLogList(SysNoticeReadLog readLog)
    {
        startPage();
        List<SysNoticeReadLog> list = readLogService.selectReadLogList(readLog);
        return getDataTable(list);
    }

    /**
     * 查看公告评论
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/comments/{noticeId}")
    public String comments(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        logger.info("访问评论管理页面，noticeId: {}", noticeId);

        if (noticeId == null || noticeId <= 0) {
            logger.warn("无效的公告ID: {}", noticeId);
            mmap.put("notice", null);
            mmap.put("noticeId", 0);
            mmap.put("error", "无效的公告ID: " + noticeId);
            return prefix + "/comments";
        }

        SysNotice notice = noticeService.selectNoticeById(noticeId);
        if (notice == null) {
            logger.warn("公告不存在，noticeId: {}", noticeId);
            mmap.put("notice", null);
            mmap.put("noticeId", noticeId);
            mmap.put("error", "公告不存在，ID: " + noticeId);
            return prefix + "/comments";
        }

        logger.info("成功获取公告信息: {}", notice.getNoticeTitle());
        mmap.put("notice", notice);
        mmap.put("noticeId", noticeId); // 直接传递noticeId
        return prefix + "/comments";
    }

    /**
     * 查询公告评论列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/comments/list")
    @ResponseBody
    public TableDataInfo commentsList(SysNoticeComment comment)
    {
        startPage();
        List<SysNoticeComment> list = commentService.selectSysNoticeCommentList(comment);
        return getDataTable(list);
    }

    /**
     * 获取指定公告的评论树形结构
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/comments/tree/{noticeId}")
    @ResponseBody
    public AjaxResult getCommentTree(@PathVariable("noticeId") Long noticeId)
    {
        List<SysNoticeComment> comments = commentService.selectCommentTreeByNoticeId(noticeId);
        return AjaxResult.success(comments);
    }

    /**
     * 审核评论
     */
    @RequiresPermissions("system:notice:edit")
    @Log(title = "评论审核", businessType = BusinessType.UPDATE)
    @PostMapping("/comments/audit")
    @ResponseBody
    public AjaxResult auditComment(Long commentId, String status)
    {
        return toAjax(commentService.auditComment(commentId, status));
    }

    /**
     * 删除评论
     */
    @RequiresPermissions("system:notice:remove")
    @Log(title = "删除评论", businessType = BusinessType.DELETE)
    @PostMapping("/comments/remove")
    @ResponseBody
    public AjaxResult removeComment(String ids)
    {
        return toAjax(commentService.deleteSysNoticeCommentByCommentIds(Convert.toLongArray(ids)));
    }

    /**
     * 软删除评论
     */
    @RequiresPermissions("system:notice:edit")
    @Log(title = "软删除评论", businessType = BusinessType.UPDATE)
    @PostMapping("/comments/softDelete")
    @ResponseBody
    public AjaxResult softDeleteComment(Long commentId)
    {
        return toAjax(commentService.softDeleteComment(commentId));
    }

    /**
     * 统计公告评论数量
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/comments/count/{noticeId}")
    @ResponseBody
    public AjaxResult getCommentCount(@PathVariable("noticeId") Long noticeId)
    {
        int count = commentService.countCommentsByNoticeId(noticeId);
        return AjaxResult.success(count);
    }

    /**
     * 查看整个小区所有公告的阅读记录
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/allReadLogs")
    public String allReadLogs()
    {
        return prefix + "/allReadLogs";
    }

    /**
     * 查询整个小区所有公告的阅读记录列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/allReadLogs/list")
    @ResponseBody
    public TableDataInfo allReadLogsList(SysNoticeReadLog readLog)
    {
        startPage();
        // 设置当前用户的小区ID，查询该小区所有公告的阅读记录
        readLog.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeReadLog> list = readLogService.selectAllReadLogsByCommunity(readLog);
        return getDataTable(list);
    }

    /**
     * 查看整个小区所有公告的评论管理
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/allComments")
    public String allComments()
    {
        return prefix + "/allComments";
    }

    /**
     * 查询整个小区所有公告的评论列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/allComments/list")
    @ResponseBody
    public TableDataInfo allCommentsList(SysNoticeComment comment)
    {
        startPage();
        // 设置当前用户的小区ID，查询该小区所有公告的评论
        comment.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeComment> list = commentService.selectAllCommentsByCommunity(comment);
        return getDataTable(list);
    }

    /**
     * 查看整个小区所有公告的点赞记录
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/allLikes")
    public String allLikes()
    {
        return prefix + "/allLikes";
    }

    /**
     * 查看单个公告的点赞记录
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/likes/{noticeId}")
    public String likes(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        SysNotice notice = noticeService.selectNoticeById(noticeId);
        mmap.put("notice", notice);
        return prefix + "/likes";
    }

    /**
     * 查询整个小区所有公告的点赞记录列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/allLikes/list")
    @ResponseBody
    public TableDataInfo allLikesList(SysNoticeLike like)
    {
        startPage();
        // 设置当前用户的小区ID，查询该小区所有公告的点赞记录
        like.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeLike> list = likeService.selectAllLikesByCommunity(like);
        return getDataTable(list);
    }

    /**
     * 查询单个公告的点赞记录列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/likes/list")
    @ResponseBody
    public TableDataInfo likesList(SysNoticeLike like)
    {
        startPage();
        // 设置当前用户的小区ID
        like.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeLike> list = likeService.selectAllLikesByCommunity(like);
        return getDataTable(list);
    }

    /**
     * 查看整个小区所有公告的分享记录
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/allShares")
    public String allShares()
    {
        return prefix + "/allShares";
    }

    /**
     * 查询整个小区所有公告的分享记录列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/allShares/list")
    @ResponseBody
    public TableDataInfo allSharesList(SysNoticeShare share)
    {
        startPage();
        // 设置当前用户的小区ID，查询该小区所有公告的分享记录
        share.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeShare> list = shareService.selectAllSharesByCommunity(share);
        return getDataTable(list);
    }

    /**
     * 导出整个小区所有公告的阅读记录
     */
    @RequiresPermissions("system:notice:list")
    @Log(title = "阅读记录", businessType = BusinessType.EXPORT)
    @PostMapping("/allReadLogs/export")
    @ResponseBody
    public AjaxResult exportAllReadLogs(SysNoticeReadLog readLog)
    {
        readLog.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeReadLog> list = readLogService.selectAllReadLogsByCommunity(readLog);
        ExcelUtil<SysNoticeReadLog> util = new ExcelUtil<SysNoticeReadLog>(SysNoticeReadLog.class);
        return util.exportExcel(list, "小区公告阅读记录");
    }

    /**
     * 导出整个小区所有公告的评论记录
     */
    @RequiresPermissions("system:notice:list")
    @Log(title = "评论记录", businessType = BusinessType.EXPORT)
    @PostMapping("/allComments/export")
    @ResponseBody
    public AjaxResult exportAllComments(SysNoticeComment comment)
    {
        comment.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeComment> list = commentService.selectAllCommentsByCommunity(comment);
        ExcelUtil<SysNoticeComment> util = new ExcelUtil<SysNoticeComment>(SysNoticeComment.class);
        return util.exportExcel(list, "小区公告评论记录");
    }

    /**
     * 导出整个小区所有公告的点赞记录
     */
    @RequiresPermissions("system:notice:list")
    @Log(title = "点赞记录", businessType = BusinessType.EXPORT)
    @PostMapping("/allLikes/export")
    @ResponseBody
    public AjaxResult exportAllLikes(SysNoticeLike like)
    {
        like.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeLike> list = likeService.selectAllLikesByCommunity(like);
        ExcelUtil<SysNoticeLike> util = new ExcelUtil<SysNoticeLike>(SysNoticeLike.class);
        return util.exportExcel(list, "小区公告点赞记录");
    }

    /**
     * 导出整个小区所有公告的分享记录
     */
    @RequiresPermissions("system:notice:list")
    @Log(title = "分享记录", businessType = BusinessType.EXPORT)
    @PostMapping("/allShares/export")
    @ResponseBody
    public AjaxResult exportAllShares(SysNoticeShare share)
    {
        share.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeShare> list = shareService.selectAllSharesByCommunity(share);
        ExcelUtil<SysNoticeShare> util = new ExcelUtil<SysNoticeShare>(SysNoticeShare.class);
        return util.exportExcel(list, "小区公告分享记录");
    }

    /**
     * 发送通知消息
     */
    private void sendNoticeMessage(SysNotice notice) {
        try {
            // 检查通知对象和ID
            if (notice == null || notice.getNoticeId() == null) {
                logger.warn("通知对象或ID为空，无法发送消息");
                return;
            }

            // 查询社区内所有已订阅的用户
            String communityId = notice.getCommunityId();
            if (StringUtils.isEmpty(communityId)) {
                logger.warn("社区ID为空，无法发送消息: noticeId={}", notice.getNoticeId());
                return;
            }

            // 查询已订阅物业通知的用户（优化查询，直接使用订阅记录表的社区ID）
            String sql = "SELECT DISTINCT s.openid, s.owner_id, s.wx_user_id " +
                        "FROM eh_wx_subscribe_record s " +
                        "WHERE s.template_type = 'property_notice' " +
                        "AND s.status = 1 " +
                        "AND s.community_id = ? " +
                        "AND (s.expire_time IS NULL OR s.expire_time > NOW())";

            List<Record> subscribedUsers = com.jfinal.plugin.activerecord.Db.find(sql, communityId);

            if (subscribedUsers == null || subscribedUsers.isEmpty()) {
                logger.info("没有找到已订阅的用户，社区ID: {}", communityId);
                return;
            }

            // 构建消息内容
            String content = notice.getNoticeTitle();
            String publishTime = DateUtils.dateTimeNow();
            String messageType = getNoticeTypeText(notice.getNoticeType());
            String publisher = notice.getCreateBy();
            String noticeTime = DateUtils.dateTimeNow();

            // 批量发送消息
            int successCount = 0;
            for (Record userRecord : subscribedUsers) {
                String openId = userRecord.getStr("openid");
                if (StringUtils.isNotEmpty(openId)) {
                    boolean success = wxMessageService.sendPropertyNotice(
                        openId, content, publishTime, messageType, publisher, noticeTime,
                        notice.getNoticeId().toString()
                    );
                    if (success) {
                        successCount++;
                    }
                }
            }

            logger.info("通知消息发送完成: 总用户数={}, 成功发送={}, 通知ID={}",
                       subscribedUsers.size(), successCount, notice.getNoticeId());

        } catch (Exception e) {
            logger.error("发送通知消息异常: noticeId={}, error={}",
                        notice != null && notice.getNoticeId() != null ? notice.getNoticeId() : "unknown",
                        e.getMessage(), e);
        }
    }

    /**
     * 获取通知类型文本
     */
    private String getNoticeTypeText(String noticeType) {
        // 这里可以根据实际的字典配置来转换
        switch (noticeType) {
            case "1":
                return "通知";
            case "2":
                return "公告";
            default:
                return "消息";
        }
    }

    /**
     * 跳转到分享统计页面
     */
    @RequiresPermissions("system:notice:view")
    @GetMapping("/shareStats")
    public String shareStats()
    {
        return prefix + "/shareStats";
    }

    /**
     * 跳转到单个公告分享统计页面
     */
    @RequiresPermissions("system:notice:view")
    @GetMapping("/shareStats/{noticeId}")
    public String shareStatsDetail(@PathVariable("noticeId") Long noticeId, ModelMap mmap)
    {
        SysNotice notice = noticeService.selectNoticeById(noticeId);
        mmap.put("notice", notice);
        mmap.put("noticeId", noticeId);
        return prefix + "/shareStatsDetail";
    }

    /**
     * 获取分享统计数据API
     */
    @RequiresPermissions("system:notice:view")
    @GetMapping("/shareStats/data/{noticeId}")
    @ResponseBody
    public AjaxResult getShareStatsData(@PathVariable("noticeId") Long noticeId)
    {
        try {
            // 使用JFinal Db直接查询数据库
            // 获取分享总数
            int shareCount = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share WHERE notice_id = ?", noticeId);

            // 获取总访问数
            int totalVisits = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share_visit WHERE notice_id = ?", noticeId);

            // 获取独立访问者数
            int uniqueVisitors = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(DISTINCT COALESCE(visitor_id, visit_ip)) FROM sys_notice_share_visit WHERE notice_id = ?",
                noticeId);

            // 获取新访问者数
            int newVisitors = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share_visit WHERE notice_id = ? AND is_new_visitor = 1",
                noticeId);

            // 获取最近的分享记录
            List<com.jfinal.plugin.activerecord.Record> recentShares = com.jfinal.plugin.activerecord.Db.find(
                "SELECT s.user_name, s.create_time, s.visit_count, s.last_visit_time " +
                "FROM sys_notice_share s WHERE s.notice_id = ? ORDER BY s.create_time DESC LIMIT 10",
                noticeId);

            // 获取最近的访问记录
            List<com.jfinal.plugin.activerecord.Record> recentVisits = com.jfinal.plugin.activerecord.Db.find(
                "SELECT v.visitor_name, v.visit_time, v.is_new_visitor " +
                "FROM sys_notice_share_visit v WHERE v.notice_id = ? ORDER BY v.visit_time DESC LIMIT 10",
                noticeId);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("shareCount", shareCount);
            result.put("totalVisits", totalVisits);
            result.put("uniqueVisitors", uniqueVisitors);
            result.put("newVisitors", newVisitors);
            result.put("recentShares", recordToMap(recentShares));
            result.put("recentVisits", recordToMap(recentVisits));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取分享统计失败: " + e.getMessage(), e);
            return AjaxResult.error("获取分享统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享统计汇总数据
     */
    @RequiresPermissions("system:notice:view")
    @GetMapping("/shareStats/summary")
    @ResponseBody
    public AjaxResult getShareStatsSummary()
    {
        try {
            String communityId = getSysUser().getCommunityId();

            // 获取总分享数
            int totalShares = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share s " +
                "JOIN sys_notice n ON s.notice_id = n.notice_id " +
                "WHERE n.community_id = ?", communityId);

            // 获取总访问数
            int totalVisits = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share_visit v " +
                "JOIN sys_notice n ON v.notice_id = n.notice_id " +
                "WHERE n.community_id = ?", communityId);

            // 获取独立访问者数
            int uniqueVisitors = com.jfinal.plugin.activerecord.Db.queryInt(
                "SELECT COUNT(DISTINCT COALESCE(v.visitor_id, v.visit_ip)) FROM sys_notice_share_visit v " +
                "JOIN sys_notice n ON v.notice_id = n.notice_id " +
                "WHERE n.community_id = ?", communityId);

            // 获取热门公告排行
            List<com.jfinal.plugin.activerecord.Record> hotNotices = com.jfinal.plugin.activerecord.Db.find(
                "SELECT n.notice_id, n.notice_title as title, " +
                "COUNT(DISTINCT s.share_id) as shareCount, " +
                "COUNT(DISTINCT v.visit_id) as visitCount " +
                "FROM sys_notice n " +
                "LEFT JOIN sys_notice_share s ON n.notice_id = s.notice_id " +
                "LEFT JOIN sys_notice_share_visit v ON n.notice_id = v.notice_id " +
                "WHERE n.community_id = ? " +
                "GROUP BY n.notice_id, n.notice_title " +
                "HAVING shareCount > 0 " +
                "ORDER BY shareCount DESC, visitCount DESC " +
                "LIMIT 10", communityId);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("totalShares", totalShares);
            result.put("totalVisits", totalVisits);
            result.put("uniqueVisitors", uniqueVisitors);
            result.put("hotNotices", recordToMap(hotNotices));

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取分享统计汇总失败: " + e.getMessage(), e);
            return AjaxResult.error("获取分享统计汇总失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享趋势数据
     */
    @RequiresPermissions("system:notice:view")
    @GetMapping("/shareStats/trend")
    @ResponseBody
    public AjaxResult getShareTrend(@RequestParam(value = "days", defaultValue = "7") Integer days)
    {
        try {
            String communityId = getSysUser().getCommunityId();

            // 获取最近N天的分享趋势
            List<com.jfinal.plugin.activerecord.Record> shareTrend = com.jfinal.plugin.activerecord.Db.find(
                "SELECT DATE(s.create_time) as date, COUNT(*) as shareCount " +
                "FROM sys_notice_share s " +
                "JOIN sys_notice n ON s.notice_id = n.notice_id " +
                "WHERE n.community_id = ? AND s.create_time >= DATE_SUB(CURDATE(), INTERVAL ? DAY) " +
                "GROUP BY DATE(s.create_time) " +
                "ORDER BY date", communityId, days);

            // 获取最近N天的访问趋势
            List<com.jfinal.plugin.activerecord.Record> visitTrend = com.jfinal.plugin.activerecord.Db.find(
                "SELECT DATE(v.visit_time) as date, COUNT(*) as visitCount " +
                "FROM sys_notice_share_visit v " +
                "JOIN sys_notice n ON v.notice_id = n.notice_id " +
                "WHERE n.community_id = ? AND v.visit_time >= DATE_SUB(CURDATE(), INTERVAL ? DAY) " +
                "GROUP BY DATE(v.visit_time) " +
                "ORDER BY date", communityId, days);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("shareTrend", recordToMap(shareTrend));
            result.put("visitTrend", recordToMap(visitTrend));
            result.put("days", days);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取分享趋势失败: " + e.getMessage(), e);
            return AjaxResult.error("获取分享趋势失败: " + e.getMessage());
        }
    }

    /**
     * 同步公告统计数据
     */
    @RequiresPermissions("system:notice:edit")
    @PostMapping("/syncStats")
    @ResponseBody
    public AjaxResult syncNoticeStats()
    {
        try {
            String communityId = getSysUser().getCommunityId();

            // 同步点赞统计
            int likeUpdated = com.jfinal.plugin.activerecord.Db.update(
                "UPDATE sys_notice n " +
                "SET like_count = (" +
                "    SELECT COUNT(*) " +
                "    FROM sys_notice_like l " +
                "    WHERE l.notice_id = n.notice_id AND l.status = '0'" +
                ") " +
                "WHERE n.community_id = ?", communityId);

            // 同步分享统计
            int shareUpdated = com.jfinal.plugin.activerecord.Db.update(
                "UPDATE sys_notice n " +
                "SET share_count = (" +
                "    SELECT COUNT(*) " +
                "    FROM sys_notice_share s " +
                "    WHERE s.notice_id = n.notice_id" +
                ") " +
                "WHERE n.community_id = ?", communityId);

            // 同步评论统计
            int commentUpdated = com.jfinal.plugin.activerecord.Db.update(
                "UPDATE sys_notice n " +
                "SET comment_count = (" +
                "    SELECT COUNT(*) " +
                "    FROM sys_notice_comment c " +
                "    WHERE c.notice_id = n.notice_id AND c.status = '0'" +
                ") " +
                "WHERE n.community_id = ?", communityId);

            // 同步阅读统计
            int readUpdated = com.jfinal.plugin.activerecord.Db.update(
                "UPDATE sys_notice n " +
                "SET read_count = (" +
                "    SELECT COUNT(DISTINCT r.user_id) " +
                "    FROM sys_notice_read_log r " +
                "    WHERE r.notice_id = n.notice_id" +
                ") " +
                "WHERE n.community_id = ?", communityId);

            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("likeUpdated", likeUpdated);
            result.put("shareUpdated", shareUpdated);
            result.put("commentUpdated", commentUpdated);
            result.put("readUpdated", readUpdated);

            return AjaxResult.success("统计数据同步完成", result);
        } catch (Exception e) {
            logger.error("同步统计数据失败: " + e.getMessage(), e);
            return AjaxResult.error("同步统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 导出单个公告的点赞记录
     */
    @RequiresPermissions("system:notice:export")
    @Log(title = "公告点赞记录", businessType = BusinessType.EXPORT)
    @PostMapping("/likes/export")
    @ResponseBody
    public AjaxResult exportLikes(SysNoticeLike like)
    {
        // 设置当前用户的小区ID
        like.setCommunityId(getSysUser().getCommunityId());
        List<SysNoticeLike> list = likeService.selectAllLikesByCommunity(like);
        ExcelUtil<SysNoticeLike> util = new ExcelUtil<SysNoticeLike>(SysNoticeLike.class);
        return util.exportExcel(list, "公告点赞记录");
    }

    /**
     * 获取公告类型（优先从小区配置读取，如果没有则返回默认类型）
     */
    private List<Map<String, Object>> getNoticeTypes() {
        String communityId = getSysUser().getCommunityId();
        return noticeTypeService.getNoticeTypes(communityId);
    }

    /**
     * 获取署名配置
     */
    private Map<String, Object> getSignatureConfig() {
        Map<String, Object> config = new HashMap<>();
        try {
            String communityId = getSysUser().getCommunityId();
            if (StringUtils.isNotEmpty(communityId)) {
                CommunityConfig communityConfig = communityConfigService.getConfig(communityId);
                if (communityConfig != null) {
                    // 获取默认署名
                    String defaultSignature = communityConfig.getString("notice_default_signature");
                    config.put("defaultSignature", defaultSignature != null ? defaultSignature : "");

                    // 获取强制署名设置
                    String forceSignature = communityConfig.getString("notice_force_signature");
                    config.put("forceSignature", "1".equals(forceSignature));
                } else {
                    config.put("defaultSignature", "");
                    config.put("forceSignature", false);
                }
            } else {
                config.put("defaultSignature", "");
                config.put("forceSignature", false);
            }
        } catch (Exception e) {
            logger.warn("获取署名配置失败: " + e.getMessage());
            config.put("defaultSignature", "");
            config.put("forceSignature", false);
        }
        return config;
    }

    /**
     * 查看菜单点击日志管理页面
     */
    @RequiresPermissions("system:notice:list")
    @GetMapping("/allMenuClickLogs")
    public String allMenuClickLogs()
    {
        return prefix + "/allMenuClickLogs";
    }

    /**
     * 查询菜单点击日志列表
     */
    @RequiresPermissions("system:notice:list")
    @PostMapping("/allMenuClickLogs/list")
    @ResponseBody
    public TableDataInfo allMenuClickLogsList()
    {
        try {
            startPage();

        // 获取查询参数
        String userName = getRequest().getParameter("userName");
        String menuName = getRequest().getParameter("menuName");
        String menuType = getRequest().getParameter("menuType");
        String source = getRequest().getParameter("source");
        String clickTime = getRequest().getParameter("clickTime");
        String houseName = getRequest().getParameter("houseName");

        // 构建WHERE条件SQL
        StringBuilder whereClause = new StringBuilder();
        whereClause.append("FROM sys_menu_click_log WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        // 按当前用户小区ID过滤
        String communityId = getSysUser().getCommunityId();
        if (StringUtils.isNotEmpty(communityId)) {
            whereClause.append("AND community_id = ? ");
            params.add(communityId);
        }

        // 添加查询条件
        if (StringUtils.isNotEmpty(userName)) {
            whereClause.append("AND user_name LIKE ? ");
            params.add("%" + userName + "%");
        }

        if (StringUtils.isNotEmpty(menuName)) {
            whereClause.append("AND menu_name LIKE ? ");
            params.add("%" + menuName + "%");
        }

        if (StringUtils.isNotEmpty(menuType)) {
            whereClause.append("AND menu_type = ? ");
            params.add(menuType);
        }

        if (StringUtils.isNotEmpty(source)) {
            whereClause.append("AND source = ? ");
            params.add(source);
        }

        if (StringUtils.isNotEmpty(clickTime)) {
            whereClause.append("AND DATE_FORMAT(click_date, '%Y-%m-%d') = ? ");
            params.add(clickTime);
        }

        if (StringUtils.isNotEmpty(houseName)) {
            whereClause.append("AND house_name LIKE ? ");
            params.add("%" + houseName + "%");
        }

        whereClause.append("ORDER BY last_click_time DESC");

        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        // 执行分页查询 - JFinal分页方法：第三个参数是SELECT子句，第四个参数是FROM子句
        Page<Record> page = Db.paginate(pageNum, pageSize,
            "SELECT log_id, menu_id, menu_name, menu_type, user_id, user_name, " +
            "first_click_time, last_click_time, click_date, click_count, " +
            "ip_address, owner_id, house_id, house_name, community_id, source",
            whereClause.toString(), params.toArray());

            return getDataTable(page);
        } catch (Exception e) {
            logger.error("查询菜单点击日志失败", e);
            return getDataTable(new ArrayList<>());
        }
    }

    /**
     * 导出菜单点击日志
     */
    @RequiresPermissions("system:notice:list")
    @Log(title = "菜单点击日志", businessType = BusinessType.EXPORT)
    @PostMapping("/allMenuClickLogs/export")
    @ResponseBody
    public AjaxResult exportMenuClickLogs()
    {
        // 构建基础查询SQL
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT log_id, menu_id, menu_name, menu_type, user_id, user_name, ");
        sql.append("first_click_time, last_click_time, click_date, click_count, ");
        sql.append("ip_address, owner_id, house_id, house_name, community_id, source ");
        sql.append("FROM sys_menu_click_log WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        // 按当前用户小区ID过滤
        String communityId = getSysUser().getCommunityId();
        if (StringUtils.isNotEmpty(communityId)) {
            sql.append("AND community_id = ? ");
            params.add(communityId);
        }

        sql.append("ORDER BY last_click_time DESC");

        List<Record> recordList = Db.find(sql.toString(), params.toArray());

        // 将Record转换为导出实体类
        List<SysMenuClickLogExport> list = new ArrayList<>();
        for (Record record : recordList) {
            SysMenuClickLogExport exportObj = new SysMenuClickLogExport();
            exportObj.setMenuName(record.getStr("menu_name"));
            exportObj.setMenuType(record.getStr("menu_type"));
            exportObj.setUserName(record.getStr("user_name"));
            exportObj.setFirstClickTime(record.getDate("first_click_time"));
            exportObj.setLastClickTime(record.getDate("last_click_time"));
            exportObj.setClickCount(record.getInt("click_count"));
            exportObj.setSource(record.getStr("source"));
            exportObj.setHouseName(record.getStr("house_name"));
            exportObj.setIpAddress(record.getStr("ip_address"));
            list.add(exportObj);
        }

        ExcelUtil<SysMenuClickLogExport> util = new ExcelUtil<SysMenuClickLogExport>(SysMenuClickLogExport.class);
        return util.exportExcel(list, "菜单点击日志");
    }

}

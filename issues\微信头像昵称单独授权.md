# 微信头像昵称单独授权功能实现

## 任务背景
用户要求在个人信息页面中，头像和昵称需要单独请求微信授权确认才可以获取到。添加箭头符号，点击触发获取微信授权，一般会出现"用微信头像"、"用微信昵称"的选项。

## 实现方案
使用微信小程序新版授权API：
- 头像：使用 `button` 组件的 `open-type="chooseAvatar"`
- 昵称：使用 `input` 组件的 `type="nickname"`

## 修改文件

### 1. miniprogram/pages/profile/index.wxml
- 为头像、昵称、姓名项添加箭头图标和点击事件
- 添加头像选择弹窗（使用chooseAvatar授权）
- 添加昵称编辑弹窗（使用nickname输入框）
- 添加姓名编辑弹窗

### 2. miniprogram/pages/profile/index.js
- 添加弹窗状态管理
- 实现头像选择逻辑（onChooseAvatar）
- 实现昵称编辑逻辑
- 实现姓名编辑逻辑
- 调用后端更新API（/api/wx/auth/update）
- 更新本地存储和页面数据

### 3. miniprogram/pages/profile/index.wxss
- 添加箭头图标样式
- 添加授权按钮样式
- 添加输入框和弹窗按钮样式

## 功能特点
1. **符合微信规范**：使用最新的微信授权API
2. **用户体验好**：点击即可触发授权，操作简单
3. **数据同步**：更新后同步到本地存储和后端
4. **错误处理**：完善的错误提示和加载状态

## 技术要点
- 使用 `open-type="chooseAvatar"` 触发头像选择
- 使用 `type="nickname"` 输入框支持微信昵称输入
- 调用后端 `/api/wx/auth/update` 接口更新用户信息
- 更新本地 `wxUserInfo` 存储保持数据一致性

## 问题修复
**问题**：用户反馈获取的头像地址是临时地址（`http://tmp/...`），而不是正式的HTTP图片地址。

**原因**：微信小程序的 `chooseAvatar` API 返回的是临时文件路径，需要上传到服务器获得正式的图片URL。

**解决方案**：
1. 添加 `uploadAvatarFile` 方法，将临时头像文件上传到服务器
2. 使用 `wx.uploadFile` API 上传文件到 `/api/wx/bx/upload` 接口
3. 获得正式的HTTP图片URL后，再调用用户信息更新接口
4. 确保URL是完整的HTTP地址格式

## UI和交互优化（2024-12-29）

### 优化内容
1. **箭头符号改为Vant图标**
   - 使用 `van-icon` 组件替换文本箭头
   - 统一图标风格和大小

2. **昵称直接编辑**
   - 移除昵称弹窗，改为点击直接编辑
   - 使用 `type="nickname"` 输入框支持微信昵称
   - 失去焦点或确认时自动提交

3. **头像布局优化**
   - 头像显示在最左边
   - 右边显示"更新头像"文字和箭头图标
   - 点击整个区域触发头像选择

4. **手机号微信授权**
   - 点击"更换"或"去绑定"触发微信手机号授权
   - 使用 `open-type="getPhoneNumber"` 获取手机号
   - 调用后端 `/api/wx/auth/bindPhone` 接口更新

### 技术实现
- 昵称编辑状态管理：`isEditingNickname`
- 手机号授权弹窗：`showPhonePopup`
- Vant图标：`van-icon` 组件
- 微信授权：`chooseAvatar` 和 `getPhoneNumber`

## 测试建议
1. 测试头像选择功能是否正常触发微信授权
2. 验证头像上传后获得的是正式HTTP地址而非临时地址
3. 测试昵称直接编辑功能，包括微信昵称输入特性
4. 测试手机号微信授权功能
5. 验证数据更新是否同步到后端和本地存储
6. 测试错误情况的处理和提示
7. 验证UI布局和图标显示是否正确

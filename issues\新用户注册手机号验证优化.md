# 新用户注册手机号验证优化

## 任务概述
修改微信登录逻辑，确保新用户注册时必须提供手机号授权码，并在 `wxLogin` 方法执行前解密手机号，直接在注册时设置用户手机号。

## 问题分析
1. 原有逻辑在 `wxLogin` 后才处理手机号绑定，导致新用户可以在没有手机号的情况下完成注册
2. 手机号解密在登录后进行，无法在用户创建时直接设置手机号
3. 缺少对新用户的强制手机号验证

## 解决方案

### 1. 修改 WxLoginDTO 类
**文件：** `ehome-oc/src/main/java/com/ehome/oc/domain/dto/WxLoginDTO.java`
- ✅ 添加 `phoneNumber` 字段
- ✅ 添加对应的 getter/setter 方法

### 2. 修改 WxUserServiceImpl.wxLogin 方法
**文件：** `ehome-oc/src/main/java/com/ehome/oc/service/impl/WxUserServiceImpl.java`
- ✅ 在创建新用户时，检查 `loginDTO` 中是否包含 `phoneNumber`
- ✅ 如果包含手机号，直接设置到新用户对象中
- ✅ 添加相应的日志记录

### 3. 修改 WechatAuthController.oneClickLogin 方法
**文件：** `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WechatAuthController.java`
- ✅ 添加 `userExists` 参数接收，用于判断是否为新用户
- ✅ 新用户注册时强制要求提供 `phoneCode`
- ✅ 在调用 `wxLogin` 前解密手机号并设置到 `loginDTO` 中
- ✅ 修改老用户的手机号绑定逻辑，只对老用户进行后续绑定
- ✅ 优化日志记录，区分新用户和老用户

### 4. 修改前端登录逻辑
**文件：** `miniprogram/pages/login/index.js`
- ✅ 修改 `performLogin` 方法签名，添加 `userExists` 参数
- ✅ 在老用户登录时传递 `userExists=true`
- ✅ 在新用户注册时将 `userExists=false` 保存到 `pendingLoginData`
- ✅ 在新用户手机号授权后传递 `userExists=false` 到后台
- ✅ 在登录数据中添加 `userExists` 字段并发送到后台

## 技术要点

### 新用户注册流程
1. 前端调用 `checkUser` 接口获取 `userExists` 标识
2. 前端将 `userExists` 传递给 `oneClickLogin` 接口
3. 后端检查如果是新用户且没有 `phoneCode`，直接返回错误
4. 如果是新用户且有 `phoneCode`，先解密手机号
5. 将解密后的手机号设置到 `loginDTO` 中
6. 调用 `wxLogin` 方法，新用户创建时直接设置手机号

### 老用户登录流程
1. 保持原有逻辑不变
2. 如果老用户提供了 `phoneCode` 且未绑定手机号，进行自动绑定

### 错误处理
- 新用户未提供手机号授权码：返回 "新用户注册需要手机号授权"
- 手机号解密失败：返回具体的解密错误信息
- 获取手机号为空：返回 "获取手机号失败"

## 预期效果
1. 新用户必须提供手机号授权才能完成注册
2. 新用户注册时直接设置手机号，无需后续绑定步骤
3. 老用户登录流程保持不变，可选择性绑定手机号
4. 提高用户注册的完整性和数据质量

## 前端配合
- ✅ 前端已修改，在调用 `oneClickLogin` 时传递 `userExists` 参数
- ✅ `userExists` 参数从 `checkUser` 接口的返回结果中获取
- ✅ 老用户和新用户都正确传递 `userExists` 标识到后台

## 完整流程
### 老用户登录流程
1. 调用 `checkUser` 接口，返回 `userExists=true`
2. 调用 `oneClickLogin` 接口，传递 `userExists=true`
3. 后台识别为老用户，正常登录
4. 如果提供了 `phoneCode` 且未绑定手机号，进行自动绑定

### 新用户注册流程
1. 调用 `checkUser` 接口，返回 `userExists=false`
2. 前端引导用户进行用户信息和手机号授权
3. 用户完成手机号授权后，调用 `oneClickLogin` 接口，传递 `userExists=false` 和 `phoneCode`
4. 后台识别为新用户，先解密手机号
5. 将手机号设置到 `loginDTO` 中，调用 `wxLogin` 创建用户
6. 新用户创建时直接设置手机号，完成注册

package com.ehome.common.utils.cache;

import com.ehome.common.domain.CommunityConfig;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 小区配置缓存工具类
 * 提供小区配置的内存缓存管理，支持ext_json解析和配置热更新
 *
 * 注意：此类提供缓存管理功能，数据加载需要在具体的业务模块中实现
 *
 * <AUTHOR>
 */
@Component
public class CommunityConfigCache {
    
    private static final Logger logger = LoggerFactory.getLogger(CommunityConfigCache.class);
    
    /** 缓存键前缀 */
    private static final String CACHE_KEY_PREFIX = "community_config_";
    
    /** 默认缓存时间（小时） */
    private static final long DEFAULT_CACHE_HOURS = 24;
    
    @Autowired
    private ConcurrentHashMapCache cache;
    
    private static CommunityConfigCache instance;
    
    @PostConstruct
    public void init() {
        instance = this;
        logger.info("CommunityConfigCache initialized");
    }
    
    /**
     * 获取工具类实例
     * @return CommunityConfigCache实例
     */
    public static CommunityConfigCache getInstance() {
        if (instance == null) {
            instance = SpringUtils.getBean(CommunityConfigCache.class);
        }
        return instance;
    }
    
    /**
     * 添加配置到缓存
     * @param config 小区配置
     */
    public static void putConfig(CommunityConfig config) {
        if (config == null || StringUtils.isEmpty(config.getOcId())) {
            return;
        }

        try {
            CommunityConfigCache cacheInstance = getInstance();
            String cacheKey = CACHE_KEY_PREFIX + config.getOcId();
            cacheInstance.cache.putWithTTL(cacheKey, config, DEFAULT_CACHE_HOURS, TimeUnit.HOURS);
            logger.debug("添加小区配置到缓存，ocId: {}", config.getOcId());

        } catch (Exception e) {
            logger.error("添加小区配置到缓存失败，ocId: {}, 错误: {}",
                config.getOcId(), e.getMessage());
        }
    }

    /**
     * 批量添加配置到缓存
     * @param configs 小区配置列表
     */
    public static void putConfigs(java.util.List<CommunityConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return;
        }

        try {
            CommunityConfigCache cacheInstance = getInstance();
            int successCount = 0;

            for (CommunityConfig config : configs) {
                if (config != null && StringUtils.isNotEmpty(config.getOcId())) {
                    String cacheKey = CACHE_KEY_PREFIX + config.getOcId();
                    cacheInstance.cache.putWithTTL(cacheKey, config, DEFAULT_CACHE_HOURS, TimeUnit.HOURS);
                    successCount++;
                }
            }

            logger.info("批量添加小区配置到缓存完成，成功: {}, 总数: {}", successCount, configs.size());

        } catch (Exception e) {
            logger.error("批量添加小区配置到缓存失败", e);
        }
    }

    /**
     * 获取小区配置
     * @param ocId 小区ID
     * @return 小区配置，如果不存在则返回null
     */
    public static CommunityConfig getConfig(String ocId) {
        if (StringUtils.isEmpty(ocId)) {
            return null;
        }

        try {
            CommunityConfigCache cacheInstance = getInstance();
            String cacheKey = CACHE_KEY_PREFIX + ocId;

            CommunityConfig config = cacheInstance.cache.get(cacheKey, CommunityConfig.class);
            if (config != null) {
                logger.debug("从缓存获取小区配置成功，ocId: {}", ocId);
                return config;
            }

            // 缓存未命中，返回null
            logger.debug("缓存中未找到小区配置，ocId: {}", ocId);
            return null;

        } catch (Exception e) {
            logger.error("获取小区配置失败，ocId: {}, 错误: {}", ocId, e.getMessage());
            return null;
        }
    }


    
    /**
     * 获取配置值
     * @param ocId 小区ID
     * @param key 配置键
     * @return 配置值
     */
    public static Object getConfigValue(String ocId, String key) {
        return getConfigValue(ocId, key, null);
    }
    
    /**
     * 获取配置值（带默认值）
     * @param ocId 小区ID
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    public static Object getConfigValue(String ocId, String key, Object defaultValue) {
        CommunityConfig config = getConfig(ocId);
        if (config == null) {
            return defaultValue;
        }
        
        // 直接从JSONObject中获取值
        return config.getConfigValue(key, defaultValue);
    }
    
    /**
     * 获取配置字符串值
     * @param ocId 小区ID
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 字符串值
     */
    public static String getConfigString(String ocId, String key, String defaultValue) {
        Object value = getConfigValue(ocId, key, defaultValue);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * 获取配置整数值
     * @param ocId 小区ID
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 整数值
     */
    public static Integer getConfigInteger(String ocId, String key, Integer defaultValue) {
        CommunityConfig config = getConfig(ocId);
        if (config == null) {
            return defaultValue;
        }
        
        return config.getConfigInteger(key, defaultValue);
    }
    
    /**
     * 获取配置布尔值
     * @param ocId 小区ID
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 布尔值
     */
    public static Boolean getConfigBoolean(String ocId, String key, Boolean defaultValue) {
        CommunityConfig config = getConfig(ocId);
        if (config == null) {
            return defaultValue;
        }
        
        return config.getConfigBoolean(key, defaultValue);
    }
    
    /**
     * 更新缓存中的配置值
     * @param ocId 小区ID
     * @param key 配置键
     * @param value 配置值
     */
    public static void updateCacheConfig(String ocId, String key, Object value) {
        CommunityConfig config = getConfig(ocId);
        if (config != null) {
            config.setConfigValue(key, value);
            putConfig(config);
            logger.debug("更新缓存配置成功，ocId: {}, key: {}", ocId, key);
        }
    }

    /**
     * 批量更新缓存中的配置
     * @param ocId 小区ID
     * @param configMap 配置Map
     */
    public static void updateCacheConfigs(String ocId, Map<String, Object> configMap) {
        if (StringUtils.isEmpty(ocId) || configMap == null || configMap.isEmpty()) {
            return;
        }

        CommunityConfig config = getConfig(ocId);
        if (config != null) {
            config.mergeConfig(configMap);
            putConfig(config);
            logger.debug("批量更新缓存配置成功，ocId: {}, 配置数量: {}", ocId, configMap.size());
        }
    }
    
    /**
     * 重载指定小区配置（清除缓存）
     * @param ocId 小区ID
     */
    public static void reload(String ocId) {
        if (StringUtils.isEmpty(ocId)) {
            return;
        }

        try {
            CommunityConfigCache cacheInstance = getInstance();
            String cacheKey = CACHE_KEY_PREFIX + ocId;
            cacheInstance.cache.remove(cacheKey);
            logger.debug("清除小区配置缓存，ocId: {}", ocId);

        } catch (Exception e) {
            logger.error("重载小区配置失败，ocId: {}, 错误: {}", ocId, e.getMessage());
        }
    }

    /**
     * 重载所有小区配置（清除所有缓存）
     */
    public static void reloadAll() {
        try {
            CommunityConfigCache cacheInstance = getInstance();
            cacheInstance.cache.clear();
            logger.info("清除所有小区配置缓存完成");

        } catch (Exception e) {
            logger.error("重载所有小区配置失败", e);
        }
    }
    
    /**
     * 清除指定小区配置缓存
     * @param ocId 小区ID
     */
    public static void removeCache(String ocId) {
        if (StringUtils.isNotEmpty(ocId)) {
            try {
                CommunityConfigCache cacheInstance = getInstance();
                String cacheKey = CACHE_KEY_PREFIX + ocId;
                cacheInstance.cache.remove(cacheKey);
                logger.debug("清除小区配置缓存，ocId: {}", ocId);
                
            } catch (Exception e) {
                logger.error("清除小区配置缓存失败，ocId: {}, 错误: {}", ocId, e.getMessage());
            }
        }
    }
    

}

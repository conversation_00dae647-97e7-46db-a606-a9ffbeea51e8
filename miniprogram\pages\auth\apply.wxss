/* 页面容器 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 温馨提示 */
.tips {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.tips-text {
  color: #856404;
  font-size: 28rpx;
  line-height: 1.5;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 级联选择器弹窗 */
.cascader-popup {
  height: 60vh;
}

/* 表单字段 */
.form-field {
  margin-bottom: 0;
}

.form-field .van-field__control {
  font-size: 30rpx;
}



/* 认证身份选择 */
.rel-type-section {
  margin-top: 20rpx;
}

.rel-type-title {
  padding: 30rpx 30rpx 20rpx;
  font-size: 30rpx;
  color: #333333;
  font-weight: 600;
}

/* 提交按钮 */
.submit-section {
  padding: 40rpx 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.submit-btn.disabled {
  background-color: #e9ecef !important;
  color: #6c757d !important;
  border-color: #e9ecef !important;
}

/* 选择器弹窗样式 */
.picker-popup {
  height: 50vh;
}

/* 备注字段样式 */
.form-field .van-field__control--textarea {
  min-height: 80rpx;
  max-height: 200rpx;
}

/* 新字段样式优化 */
.form-field .van-field__label {
  min-width: 140rpx;
}

.form-field .van-field__control {
  color: #333;
}

/* 必填标识 */
.required {
  color: #ff4444;
  margin-left: 4rpx;
}

/* 表单验证错误 */
.field-error {
  border-color: #ff4444 !important;
}

.error-message {
  color: #ff4444;
  font-size: 24rpx;
  margin-top: 8rpx;
  padding-left: 30rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #999999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .section-title {
    font-size: 30rpx;
    padding: 25rpx 25rpx 15rpx;
  }
  
  .house-selector,
  .id-type-selector,
  .rel-type-selector {
    padding: 25rpx;
  }
}

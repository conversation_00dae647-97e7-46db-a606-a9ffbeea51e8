// Token管理工具
import { STORAGE_KEYS } from '../constants/index.js'

class TokenManager {
  constructor() {
    this.refreshing = false // 防止并发刷新
    this.refreshPromise = null // 刷新Promise，用于等待刷新完成
    this.checkTimer = null // 定时检查器
    this.retryCount = 0 // 重试计数
    this.maxRetries = 3 // 最大重试次数
    this.isChecking = false // 防止并发检查
    this.checkInterval = 5 * 60 * 1000 // 5分钟检查一次
  }

  /**
   * 获取当前token
   */
  getToken() {
    return wx.getStorageSync(STORAGE_KEYS.TOKEN)
  }

  /**
   * 设置新token
   */
  setToken(token) {
    if (token) {
      // 在存储前检查token完整性
      const integrity = this.checkTokenIntegrity(token)
      if (!integrity.isValid) {
        console.warn('[TokenManager] Token完整性检查失败:', integrity.message)
        console.warn('[TokenManager] Token内容:', token)
      }
      wx.setStorageSync(STORAGE_KEYS.TOKEN, token)
    }
  }

  /**
   * 检查token完整性
   * @param {string} token JWT token
   * @returns {Object} 完整性检查结果
   */
  checkTokenIntegrity(token) {
    if (!token || typeof token !== 'string') {
      return { isValid: false, message: 'Token为空或不是字符串' }
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return {
        isValid: false,
        message: `JWT格式不正确，应该有3个部分，实际有${parts.length}个`,
        parts: parts.length
      }
    }

    // 检查每个部分是否为有效的base64
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i]
      if (!part) {
        return {
          isValid: false,
          message: `第${i + 1}部分为空`,
          partIndex: i
        }
      }

      // 检查base64格式（允许URL安全的base64）
      const base64Regex = /^[A-Za-z0-9+/\-_]*={0,2}$/
      if (!base64Regex.test(part)) {
        return {
          isValid: false,
          message: `第${i + 1}部分不是有效的base64格式`,
          partIndex: i,
          partContent: part
        }
      }
    }

    return { isValid: true, message: 'Token格式正确' }
  }

  /**
   * 清除token
   */
  clearToken() {
    wx.removeStorageSync(STORAGE_KEYS.TOKEN)
  }

  /**
   * 检查响应头中是否有新token并自动更新
   * @param {Object} response 微信请求响应对象
   */
  checkAndUpdateToken(response) {
    if (response && response.header) {
      const newToken = response.header['New-Token'] || response.header['new-token']
      const tokenRefreshed = response.header['Token-Refreshed'] || response.header['token-refreshed']
      
      if (newToken && tokenRefreshed === 'true') {
        console.log('[TokenManager] 检测到新token，自动更新')
        this.setToken(newToken.replace('Bearer ', ''))
        
        // 触发token更新事件，通知其他组件
        this.notifyTokenUpdated(newToken)
        
        return true
      }
    }
    return false
  }

  /**
   * 手动刷新token
   * @returns {Promise<string>} 返回新token
   */
  async refreshToken() {
    // 防止并发刷新
    if (this.refreshing) {
      return this.refreshPromise
    }

    this.refreshing = true
    this.refreshPromise = this._doRefreshToken()

    try {
      const result = await this.refreshPromise
      return result
    } finally {
      this.refreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * 执行token刷新
   * @private
   */
  async _doRefreshToken() {
    try {
      const app = getApp()
      const currentToken = this.getToken()

      if (!currentToken) {
        throw new Error('没有可用的token')
      }

      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${app.globalData.baseUrl}/api/wx/auth/refreshToken`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${currentToken}`
          },
          success: resolve,
          fail: reject
        })
      })

      if (response.statusCode === 200 && response.data.code === 0) {
        const newToken = response.data.data.token
        this.setToken(newToken)
        this.notifyTokenUpdated(`Bearer ${newToken}`)

        // 重置重试计数
        this.retryCount = 0

        console.log('[TokenManager] Token刷新成功')
        return newToken
      } else {
        throw new Error(response.data.msg || 'Token刷新失败')
      }
    } catch (error) {
      console.error('[TokenManager] Token刷新失败:', error)

      // 永不放弃的重试逻辑
      if (this.isNetworkError(error)) {
        this.retryCount++
        console.log(`[TokenManager] 网络错误，将进行第${this.retryCount}次重试（永不放弃）`)

        // 延迟重试，但永不停止
        const delay = Math.min(2000 * this.retryCount, 30000) // 最大延迟30秒
        setTimeout(() => {
          this.refreshToken().catch(() => {
            // 继续重试，永不放弃
          })
        }, delay)

        throw error
      }

      // 非网络错误也不清除token，只记录错误
      console.error('[TokenManager] Token刷新遇到非网络错误，但保持登录状态:', error)

      // 延迟后再次尝试
      setTimeout(() => {
        console.log('[TokenManager] 延迟后重新尝试刷新token')
        this.refreshToken().catch(() => {
          // 继续重试
        })
      }, 10000) // 10秒后重试

      throw error
    }
  }

  /**
   * 通知token已更新
   * @param {string} newToken 新token
   */
  notifyTokenUpdated(newToken) {
    // 可以在这里发送自定义事件或调用回调函数
    const app = getApp()
    if (app && app.globalData) {
      app.globalData.lastTokenUpdate = Date.now()
    }

    // 触发全局事件
    wx.eventBus && wx.eventBus.emit('tokenUpdated', newToken)
  }

  /**
   * 检查token是否即将过期（前端简单检查）
   * 注意：这只是一个辅助检查，主要的过期检查在后端
   */
  isTokenNearExpiry() {
    try {
      const token = this.getToken()
      if (!token) return false // 没有token不算过期，避免误判

      // 检查token格式是否正确
      const parts = token.split('.')
      if (parts.length !== 3) {
        console.warn('[TokenManager] Token格式不正确，但保持登录状态')
        return false // 格式错误也不认为过期，让后端处理
      }

      // 使用安全的JWT解析方法
      const payload = this.safeParseJWTPayload(parts[1])
      if (!payload) {
        console.warn('[TokenManager] 无法解析token payload，保持登录状态')
        return false // 解析失败也不认为过期，让后端处理
      }

      // 检查是否有过期时间字段
      if (!payload.exp) {
        console.warn('[TokenManager] Token中没有过期时间字段，保持登录状态')
        return false // 没有过期时间也不认为过期
      }

      const exp = payload.exp * 1000 // 转换为毫秒
      const now = Date.now()
      const sevenDays = 7 * 24 * 60 * 60 * 1000 // 7天，与后端配置一致

      return (exp - now) < sevenDays
    } catch (error) {
      console.error('[TokenManager] 检查token过期时间失败，保持登录状态:', error)
      return false // 解析失败也不认为过期，保持登录状态
    }
  }

  /**
   * 获取token剩余有效期（毫秒）
   * @returns {number} 剩余有效期，-1表示token无效
   */
  getRemainingTime() {
    try {
      const token = this.getToken()
      if (!token) {
        console.log('[TokenManager] 没有token')
        return -1
      }

      // 检查token基本格式
      if (typeof token !== 'string') {
        console.warn('[TokenManager] Token不是字符串类型:', typeof token)
        return -1
      }

      const parts = token.split('.')
      if (parts.length !== 3) {
        console.warn('[TokenManager] Token格式不正确，部分数量:', parts.length)
        return -1
      }

      // 安全解析payload
      const payload = this.safeParseJWTPayload(parts[1])
      if (!payload) {
        console.warn('[TokenManager] 无法解析token payload')
        return -1
      }

      if (!payload.exp) {
        console.warn('[TokenManager] Token中没有过期时间字段')
        return -1
      }

      const exp = payload.exp * 1000
      const now = Date.now()
      const remaining = Math.max(0, exp - now)

      return remaining
    } catch (error) {
      console.error('[TokenManager] 获取token剩余时间失败:', error)
      return -1
    }
  }

  /**
   * 安全解析JWT payload
   * @param {string} payloadPart JWT的payload部分（base64编码）
   * @returns {Object|null} 解析后的payload对象，失败返回null
   */
  safeParseJWTPayload(payloadPart) {
    try {
      if (!payloadPart || typeof payloadPart !== 'string') {
        console.warn('[TokenManager] Payload部分无效:', payloadPart)
        return null
      }

      // 检查是否为有效的base64字符串（支持URL安全的base64）
      const base64Regex = /^[A-Za-z0-9+/\-_]*={0,2}$/
      if (!base64Regex.test(payloadPart)) {
        console.warn('[TokenManager] Payload不是有效的base64格式:', payloadPart)
        return null
      }

      // 尝试解码base64（处理URL安全的base64）
      let decodedString
      try {
        // 将URL安全的base64转换为标准base64
        let standardBase64 = payloadPart.replace(/-/g, '+').replace(/_/g, '/')

        // 添加必要的填充
        while (standardBase64.length % 4) {
          standardBase64 += '='
        }

        decodedString = atob(standardBase64)
      } catch (decodeError) {
        console.error('[TokenManager] Base64解码失败:', decodeError.message)
        console.error('[TokenManager] 原始payload:', payloadPart)
        return null
      }

      // 检查解码后的字符串是否为有效JSON
      if (!decodedString) {
        console.warn('[TokenManager] 解码后的字符串为空')
        return null
      }

      // 尝试解析JSON
      const payload = JSON.parse(decodedString)

      // 基本验证payload结构
      if (typeof payload !== 'object' || payload === null) {
        console.warn('[TokenManager] Payload不是有效的对象')
        return null
      }

      return payload
    } catch (error) {
      console.error('[TokenManager] 解析JWT payload失败:', error.message)
      return null
    }
  }

  /**
   * 判断是否为网络错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否为网络错误
   */
  isNetworkError(error) {
    if (!error) return false

    const networkErrorMessages = [
      'network error',
      'timeout',
      'request:fail',
      'network timeout',
      'connection failed',
      '网络错误',
      '连接失败',
      '请求超时'
    ]

    const errorMessage = (error.message || error.errMsg || '').toLowerCase()
    return networkErrorMessages.some(msg => errorMessage.includes(msg))
  }

  /**
   * 启动定时检查token状态
   */
  startTokenCheck() {
    this.stopTokenCheck() // 先停止之前的定时器

    console.log('[TokenManager] 启动token定时检查')

    // 立即进行一次检查，确保系统重启后token状态正常
    this.checkTokenStatus()

    this.checkTimer = setInterval(() => {
      this.checkTokenStatus()
    }, this.checkInterval)
  }

  /**
   * 停止定时检查
   */
  stopTokenCheck() {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
      console.log('[TokenManager] 停止token定时检查')
    }
  }

  /**
   * 检查token状态并在需要时刷新
   */
  async checkTokenStatus() {
    if (this.isChecking || this.refreshing) {
      return // 避免并发检查
    }

    this.isChecking = true

    try {
      const remainingTime = this.getRemainingTime()

      if (remainingTime === -1) {
        console.log('[TokenManager] Token无效，停止检查')
        this.stopTokenCheck()
        return
      }

      // 如果剩余时间少于7天，主动刷新
      const sevenDays = 7 * 24 * 60 * 60 * 1000
      if (remainingTime < sevenDays) {
        console.log(`[TokenManager] Token将在${Math.round(remainingTime / 1000 / 60 / 60 / 24)}天后过期，主动刷新`)
        await this.refreshToken()
      }
    } catch (error) {
      console.error('[TokenManager] 检查token状态失败:', error)
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 重置重试计数
   */
  resetRetryCount() {
    this.retryCount = 0
  }

  /**
   * 调试token信息（仅用于开发调试）
   * @returns {Object} token调试信息
   */
  debugTokenInfo() {
    try {
      const token = this.getToken()
      if (!token) {
        return { status: 'no_token', message: '没有token' }
      }

      const parts = token.split('.')
      if (parts.length !== 3) {
        return {
          status: 'invalid_format',
          message: `Token格式不正确，部分数量: ${parts.length}`,
          tokenLength: token.length,
          tokenPreview: token.substring(0, 100) + (token.length > 100 ? '...' : ''),
          parts: parts.map((part, index) => ({
            index,
            length: part.length,
            preview: part.substring(0, 50) + (part.length > 50 ? '...' : '')
          }))
        }
      }

      const payload = this.safeParseJWTPayload(parts[1])
      if (!payload) {
        return {
          status: 'parse_failed',
          message: 'Payload解析失败',
          payloadPart: parts[1], // 显示完整的payload部分
          payloadLength: parts[1].length,
          headerPart: parts[0],
          signaturePart: parts[2]
        }
      }

      const remaining = this.getRemainingTime()
      return {
        status: 'valid',
        message: 'Token有效',
        exp: payload.exp,
        iat: payload.iat,
        userId: payload.userId,
        remainingTime: remaining,
        remainingDays: Math.round(remaining / 1000 / 60 / 60 / 24),
        isNearExpiry: this.isTokenNearExpiry()
      }
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
        error: error.toString()
      }
    }
  }
}

// 创建单例
const tokenManager = new TokenManager()

export default tokenManager

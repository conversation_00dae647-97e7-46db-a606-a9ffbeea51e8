package com.ehome.oc.service.impl;

import com.ehome.oc.domain.Building;
import com.ehome.oc.mapper.BuildingMapper;
import com.ehome.oc.service.IBuildingService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 楼栋信息Service业务层处理
 */
@Service
public class BuildingServiceImpl implements IBuildingService {

    @Autowired
    private BuildingMapper buildingMapper;

    @Override
    public List<Map<String, Object>> getBuildingUnitTree(String communityId) {
        // 使用联表查询获取数据
        String sql = "SELECT t1.building_id,t1.unit_id,t1.`name` unit_name,t2.`name` building_name FROM eh_unit t1,eh_building t2 WHERE t1.building_id = t2.building_id AND t2.community_id = ?";
        List<Record> records = Db.find(sql, communityId);

        // 按楼栋ID分组
        Map<String, Map<String, Object>> buildingMap = new HashMap<>();
        
        for (Record record : records) {
            String buildingId = record.getStr("building_id");
            
            // 如果是新的楼栋，创建楼栋节点
            buildingMap.putIfAbsent(buildingId, new HashMap<String, Object>() {{
                put("buildingId", buildingId);
                put("unitId", record.getStr("unit_id"));
                put("unitName", record.getStr("unit_name"));
                put("buildingName", record.getStr("building_name"));
                put("children", new ArrayList<Map<String, Object>>());
            }});
            
            // 创建单元节点并添加到对应楼栋的children中
            Map<String, Object> unitNode = new HashMap<>();
            unitNode.put("unitId", record.getStr("unit_id"));
            unitNode.put("buildingId", buildingId);
            unitNode.put("unitName", record.getStr("unit_name"));
            unitNode.put("buildingName", record.getStr("building_name"));

            List<Map<String, Object>> children = (List<Map<String, Object>>) buildingMap.get(buildingId).get("children");
            children.add(unitNode);
        }
        return new ArrayList<>(buildingMap.values());
    }

    /**
     * 查询楼栋信息
     */
    @Override
    public Building selectBuildingById(Integer buildingId) {
        return buildingMapper.selectBuildingById(buildingId);
    }

    /**
     * 查询楼栋信息列表
     */
    @Override
    public List<Building> selectBuildingList(Building building) {
        return buildingMapper.selectBuildingList(building);
    }

    /**
     * 新增楼栋信息
     */
    @Override
    public int insertBuilding(Building building) {
        return buildingMapper.insertBuilding(building);
    }

    /**
     * 修改楼栋信息
     */
    @Override
    public int updateBuilding(Building building) {
        return buildingMapper.updateBuilding(building);
    }

    /**
     * 删除楼栋信息
     */
    @Override
    public int deleteBuildingById(Integer buildingId) {
        return buildingMapper.deleteBuildingById(buildingId);
    }

    /**
     * 批量删除楼栋信息
     */
    @Override
    public int deleteBuildingByIds(Integer[] buildingIds) {
        return buildingMapper.deleteBuildingByIds(buildingIds);
    }

    /**
     * 更新楼栋单元数
     */
    @Override
    public int updateBuildingTotalUnits(String buildingId) {
        return buildingMapper.updateBuildingTotalUnits(buildingId);
    }
} 
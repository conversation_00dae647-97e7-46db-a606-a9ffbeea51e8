package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序服务电话Controller
 */
@RestController
@RequestMapping("/api/wx/serviceTel")
public class WxServiceTelController extends BaseWxController {

    /**
     * 获取服务电话列表（按类型分类）
     */
    @GetMapping("/list")
    public AjaxResult getServiceTelList() {
        try {
            String communityId = getCurrentUser().getCommunityId();

            String sql = "select service_tel_id, parent_id, tel_number, tel_type, service_name, company_name, sort_order " +
                        "from eh_service_tel where status = '0' and community_id = ? " +
                        "order by tel_type, parent_id, sort_order asc, create_time desc";

            List<Record> list = Db.find(sql, communityId);

            // 按tel_type分类构建数据结构
            Map<String, Object> result = buildServiceTelByType(list);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取服务电话列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取服务电话列表失败: " + e.getMessage());
        }
    }

    /**
     * 按tel_type分类构建服务电话数据结构
     */
    private Map<String, Object> buildServiceTelByType(List<Record> records) {
        Map<String, Object> result = new HashMap<>();
        Map<String, List<Map<String, Object>>> internalData = new HashMap<>();
        Map<String, List<Map<String, Object>>> externalData = new HashMap<>();

        // 按tel_type分类处理数据
        for (Record record : records) {
            Map<String, Object> serviceTelMap = record.toMap();
            String telType = record.getStr("tel_type");
            Integer parentId = record.getInt("parent_id");

            Map<String, List<Map<String, Object>>> targetData = "internal".equals(telType) ? internalData : externalData;

            if (parentId == null || parentId == 0) {
                // 顶级分类
                targetData.computeIfAbsent("categories", k -> new ArrayList<>()).add(serviceTelMap);
            } else {
                // 子项，按parent_id分组
                targetData.computeIfAbsent(parentId.toString(), k -> new ArrayList<>()).add(serviceTelMap);
            }
        }

        // 构建内部号码树形结构
        List<Map<String, Object>> internalTree = buildTreeFromData(internalData);
        // 构建外部号码树形结构
        List<Map<String, Object>> externalTree = buildTreeFromData(externalData);

        result.put("internal", internalTree);
        result.put("external", externalTree);

        return result;
    }

    /**
     * 从分类数据构建树形结构
     */
    private List<Map<String, Object>> buildTreeFromData(Map<String, List<Map<String, Object>>> data) {
        List<Map<String, Object>> categories = data.get("categories");
        if (categories == null) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> validCategories = new ArrayList<>();

        // 为每个分类添加子项
        for (Map<String, Object> category : categories) {
            Integer categoryId = (Integer) category.get("service_tel_id");
            String categoryTelNumber = (String) category.get("tel_number");
            List<Map<String, Object>> children = data.get(categoryId.toString());

            boolean hasValidChildren = false;
            if (children != null && !children.isEmpty()) {
                // 过滤掉电话号码为空的子项（二级菜单必须有电话号码）
                List<Map<String, Object>> validChildren = new ArrayList<>();
                for (Map<String, Object> child : children) {
                    String telNumber = (String) child.get("tel_number");
                    if (telNumber != null && !telNumber.trim().isEmpty()) {
                        validChildren.add(child);
                    }
                }
                if (!validChildren.isEmpty()) {
                    category.put("children", validChildren);
                    hasValidChildren = true;
                }
            }

            // 只有满足以下条件之一的分类才显示：
            // 1. 分类本身有电话号码
            // 2. 分类有有效的子项
            boolean categoryHasTelNumber = categoryTelNumber != null && !categoryTelNumber.trim().isEmpty();
            if (categoryHasTelNumber || hasValidChildren) {
                validCategories.add(category);
            }
        }

        return validCategories;
    }

    /**
     * 构建服务电话树形结构（保留原方法以备用）
     */
    private List<Map<String, Object>> buildServiceTelTree(List<Record> records) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, List<Map<String, Object>>> childrenMap = new HashMap<>();

        // 转换Record为Map并按parent_id分组
        for (Record record : records) {
            Map<String, Object> serviceTelMap = record.toMap();
            String parentId = record.getStr("parent_id");

            if ("0".equals(parentId)) {
                // 顶级分类
                result.add(serviceTelMap);
            } else {
                // 子项
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(serviceTelMap);
            }
        }

        // 为每个父级添加children
        addChildrenToParents(result, childrenMap);

        return result;
    }

    /**
     * 递归为父级添加子项
     */
    private void addChildrenToParents(List<Map<String, Object>> parents, Map<String, List<Map<String, Object>>> childrenMap) {
        for (Map<String, Object> parent : parents) {
            String serviceTelId = (String) parent.get("service_tel_id");
            List<Map<String, Object>> children = childrenMap.get(serviceTelId);
            if (children != null && !children.isEmpty()) {
                parent.put("children", children);
                // 递归处理子项的子项（虽然目前限制为二级，但保持扩展性）
                addChildrenToParents(children, childrenMap);
            }
        }
    }

    /**
     * 记录拨打电话日志
     */
    @PostMapping("/logCall")
    public AjaxResult logCall(@RequestBody Map<String, String> params) {
        try {
            String telNumber = params.get("telNumber");
            String serviceName = params.get("serviceName");

            if (StringUtils.isEmpty(telNumber)) {
                return AjaxResult.error("电话号码不能为空");
            }
            if(getCurrentUser()!=null){
                // 获取当前用户信息
                String communityId = getCurrentUser().getCommunityId();
                String houseId = getCurrentUser().getHouseId();
                String houseName = getCurrentUser().getHouseName();
                String ownerName = getCurrentUser().getNickname();

                // 记录日志
                logger.info("用户拨打服务电话 - 社区ID: {}, 房屋ID: {}, 房屋名称: {}, 业主: {}, 服务名称: {}, 电话号码: {}",
                       communityId, houseId, houseName, ownerName, serviceName, telNumber);
            }
            return AjaxResult.success("日志记录成功");
        } catch (Exception e) {
            logger.error("记录拨打电话日志失败: " + e.getMessage(), e);
            return AjaxResult.error("日志记录失败");
        }
    }
}

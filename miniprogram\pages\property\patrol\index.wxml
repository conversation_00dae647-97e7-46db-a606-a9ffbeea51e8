<view class="patrol-container">
  <view class="header">
    <text class="title">{{title}}</text>
  </view>
  
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <view wx:else class="patrol-list">
    <view wx:for="{{patrolList}}" wx:key="id" class="patrol-item" bindtap="viewPatrolDetail" data-id="{{item.id}}">
      <view class="patrol-info">
        <view class="patrol-location">{{item.location}}</view>
        <view class="patrol-time">{{item.time}}</view>
      </view>
      <view class="patrol-status {{item.status === '正常' ? 'normal' : 'abnormal'}}">{{item.status}}</view>
    </view>
    
    <view wx:if="{{patrolList.length === 0}}" class="empty">
      <text>暂无巡检记录</text>
    </view>
  </view>
</view>

-- Markicam API数据同步表结构创建脚本
-- 创建时间: 2025-01-08
-- 说明: 用于同步Markicam开放平台的数据到本地数据库

-- 1. 创建配置表
DROP TABLE IF EXISTS `eh_markicam_config`;
CREATE TABLE `eh_markicam_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `org_id` varchar(50) NOT NULL COMMENT 'Markicam组织ID',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(255) DEFAULT 'https://open-api.markiapp.com' COMMENT 'API基础URL',
  `is_enabled` int(11) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `last_sync_time` varchar(19) DEFAULT '' COMMENT '最后同步时间',
  `sync_interval` int(11) DEFAULT 3600 COMMENT '同步间隔(秒)',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `is_deleted` int(11) DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否激活(0:未激活 1:激活)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_community_org` (`community_id`, `org_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam API配置表';

-- 2. 创建团队表
DROP TABLE IF EXISTS `eh_markicam_team`;
CREATE TABLE `eh_markicam_team` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `team_id` int(11) NOT NULL COMMENT 'Markicam团队ID',
  `team_name` varchar(100) DEFAULT '' COMMENT '团队名称',
  `team_desc` text COMMENT '团队描述',
  `member_count` int(11) DEFAULT 0 COMMENT '成员数量',
  `reg_member_count` int(11) DEFAULT 0 COMMENT '已注册成员数量',
  `unreg_member_count` int(11) DEFAULT 0 COMMENT '未注册成员数量',
  `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `is_deleted` int(11) DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否激活(0:未激活 1:激活)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_community_team` (`community_id`, `team_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam团队信息表';

-- 3. 创建照片视频表
DROP TABLE IF EXISTS `eh_markicam_moment`;
CREATE TABLE `eh_markicam_moment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `markicam_id` varchar(50) NOT NULL COMMENT 'Markicam照片/视频ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `team_id` int(11) NOT NULL COMMENT '团队ID',
  `url` text COMMENT '照片/视频链接',
  `moment_type` int(11) NOT NULL COMMENT '动态类型(1:照片 2:视频)',
  `content` text COMMENT '水印内容',
  `mark_name` varchar(255) DEFAULT '' COMMENT '水印名称',
  `lng` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `lat` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `post_time` int(11) NOT NULL COMMENT '上传时间戳',
  `post_time_str` varchar(19) DEFAULT '' COMMENT '上传时间字符串',
  `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `is_deleted` int(11) DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否激活(0:未激活 1:激活)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_markicam_id` (`markicam_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_moment_type` (`moment_type`),
  KEY `idx_post_time` (`post_time`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam照片视频表';

-- 4. 创建成员表
DROP TABLE IF EXISTS `eh_markicam_member`;
CREATE TABLE `eh_markicam_member` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `uid` int(11) NOT NULL COMMENT 'Markicam用户ID',
  `team_id` int(11) NOT NULL COMMENT '团队ID',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) DEFAULT '' COMMENT '电话',
  `join_time` int(11) NOT NULL COMMENT '加入团队时间戳',
  `join_time_str` varchar(19) DEFAULT '' COMMENT '加入时间字符串',
  `member_type` int(11) NOT NULL COMMENT '成员类型(1:已注册主管理员 2:已注册管理员 3:已注册普通成员 4:未注册普通成员 5:未注册管理员)',
  `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `is_deleted` int(11) DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否激活(0:未激活 1:激活)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_community_uid_team` (`community_id`, `uid`, `team_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_member_type` (`member_type`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam团队成员表';

-- 5. 创建违规车辆表
DROP TABLE IF EXISTS `eh_markicam_illegal_park`;
CREATE TABLE `eh_markicam_illegal_park` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `team_id` int(11) NOT NULL COMMENT '团队ID',
  `report_uid` int(11) NOT NULL COMMENT '上报人用户ID',
  `car_plate` varchar(20) DEFAULT '' COMMENT '违规车辆车牌号',
  `car_picture` text COMMENT '违规车辆照片链接',
  `report_time` int(11) NOT NULL COMMENT '上报时间戳',
  `report_time_str` varchar(19) DEFAULT '' COMMENT '上报时间字符串',
  `sync_time` varchar(19) DEFAULT '' COMMENT '同步时间',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  `updated_at` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  `is_deleted` int(11) DEFAULT 0 COMMENT '是否删除(0:未删除 1:已删除)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否激活(0:未激活 1:激活)',
  PRIMARY KEY (`id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_report_uid` (`report_uid`),
  KEY `idx_car_plate` (`car_plate`),
  KEY `idx_report_time` (`report_time`),
  KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam违规车辆表';

-- 6. 插入测试配置数据
INSERT INTO `eh_markicam_config` (
  `community_id`, `org_id`, `api_key`, `base_url`, `is_enabled`,
  `sync_interval`, `created_at`, `updated_at`, `created_by`, `updated_by`
) VALUES (
  'test_community', '12345', 'your_api_key_here', 'https://open-api.markiapp.com', 1,
  3600, '2025-01-08 10:00:00', '2025-01-08 10:00:00', 'system', 'system'
);

-- 7. 创建同步日志表
DROP TABLE IF EXISTS `eh_markicam_sync_log`;
CREATE TABLE `eh_markicam_sync_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `sync_type` varchar(20) NOT NULL COMMENT '同步类型(moment/member/illegal_park/team)',
  `sync_status` int(11) NOT NULL COMMENT '同步状态(0:失败 1:成功 2:进行中)',
  `sync_count` int(11) DEFAULT 0 COMMENT '同步数量',
  `error_msg` text COMMENT '错误信息',
  `start_time` varchar(19) DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(19) DEFAULT '' COMMENT '结束时间',
  `duration` int(11) DEFAULT 0 COMMENT '耗时(秒)',
  `created_at` varchar(19) DEFAULT '' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Markicam同步日志表';

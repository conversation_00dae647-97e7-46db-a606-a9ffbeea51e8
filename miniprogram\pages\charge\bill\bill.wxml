<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 顶部整体区域 -->
  <view class="header-wrapper">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
      <view class="navbar-content">
        <view class="location-wrapper">
          <text class="navbar-location">小区缴费情况</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Tab切换 -->
  <view class="tab-container">
    <van-tabs active="{{activeTab}}" bind:change="onTabChange" swipe-threshold="2">
      <van-tab title="房屋" name="house" />
      <van-tab title="车位" name="parking" />
    </van-tabs>
  </view>

  <!-- 筛选条件 -->
  <view class="filter-container">
    <van-dropdown-menu>
      <van-dropdown-item value="{{filterYear}}" options="{{yearOptions}}" bind:change="onYearChange" />
      <van-dropdown-item value="{{filterMonth}}" options="{{monthOptions}}" bind:change="onMonthChange" />
      <van-dropdown-item value="{{filterStatus}}" options="{{statusOptions}}" bind:change="onStatusChange" />
    </van-dropdown-menu>
  </view>

  <!-- 缴费情况明细 -->
  <view class="content-area">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading type="spinner" size="48rpx" color="#07c160">加载中...</van-loading>
    </view>

    <!-- 房屋缴费情况 -->
    <view wx:elif="{{activeTab === 'house'}}" class="house-content">
      <!-- 统计信息 -->
      <view class="statistics-card">
        <view class="stat-item">
          <text class="stat-label">缴费情况明细</text>
          <text class="stat-value arrear">待缴纳:{{totalArrearAmount}}元({{totalArrearHouses}}户)</text>
        </view>
      </view>

      <!-- 楼栋列表 -->
      <view wx:if="{{buildingList.length > 0}}" class="building-list">
        <van-collapse value="{{activeBuildingIds}}" bind:change="onBuildingChange">
          <van-collapse-item
            wx:for="{{buildingList}}"
            wx:key="building_id"
            name="{{item.building_id}}"
            title="{{item.building_name}}"
            class="building-item"
          >
            <!-- 房屋详情 -->
            <view wx:if="{{item.houseList && item.houseList.length > 0}}" class="house-list">
              <view class="house-grid">
                <view
                  wx:for="{{item.houseList}}"
                  wx:for-item="house"
                  wx:key="house_id"
                  class="house-card {{house.arrear_amount > 0 ? 'arrear' : 'normal'}}"
                >
                  <!-- 正常缴费房屋 -->
                  <view wx:if="{{house.arrear_amount == 0}}" class="house-content-normal">
                    <view class="house-unit-normal">
                      <text class="unit-text-normal">1单元/{{house.room}}</text>
                    </view>
                    <view class="house-icon-normal">
                      <van-icon name="good-job-o" size="32rpx" color="#ffd700" />
                    </view>
                  </view>

                  <!-- 欠费房屋 -->
                  <view wx:else class="house-content-arrear">
                    <view class="house-unit-arrear">
                      <text class="unit-text-arrear">1单元/{{house.room}}</text>
                    </view>
                    <view class="house-amount-arrear">
                      <text class="amount-text-arrear">欠费:{{house.arrear_amount}}元</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 加载房屋数据 -->
            <view wx:elif="{{item.loading}}" class="house-loading">
              <van-loading type="spinner" size="32rpx">加载房屋数据...</van-loading>
            </view>
            <!-- 无房屋数据 -->
            <view wx:else class="house-empty">
              <van-empty description="暂无房屋数据" />
            </view>
          </van-collapse-item>
        </van-collapse>
      </view>

      <!-- 空状态 -->
      <view wx:else class="empty-content">
        <van-empty description="暂无楼栋数据" />
      </view>
    </view>

    <!-- 车位缴费情况（预留） -->
    <view wx:elif="{{activeTab === 'parking'}}" class="parking-content">
      <view class="coming-soon">
        <van-empty description="车位缴费功能开发中..." />
      </view>
    </view>
  </view>
</view>

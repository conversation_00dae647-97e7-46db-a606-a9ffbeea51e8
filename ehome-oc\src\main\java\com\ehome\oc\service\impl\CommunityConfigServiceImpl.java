package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.domain.CommunityConfig;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.cache.CommunityConfigCache;
import com.ehome.jfinal.model.OcInfoModel;
import com.ehome.oc.service.ICommunityConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 小区配置服务实现类
 * 负责小区配置的数据库操作和缓存管理
 * 
 * <AUTHOR>
 */
@Service
public class CommunityConfigServiceImpl implements ICommunityConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(CommunityConfigServiceImpl.class);
    

    
    @Override
    public void initAllConfigs() {
        try {
            logger.info("开始初始化所有小区配置到缓存...");
            
            List<OcInfoModel> communities = OcInfoModel.dao.find(
                "SELECT * FROM eh_community WHERE oc_state = 0"
            );
            
            List<CommunityConfig> configs = new ArrayList<>();
            for (OcInfoModel model : communities) {
                try {
                    CommunityConfig config = convertToConfig(model);
                    configs.add(config);
                } catch (Exception e) {
                    logger.error("转换小区配置失败，ocId: {}, 错误: {}", 
                        model.getStr("oc_id"), e.getMessage());
                }
            }
            
            // 批量添加到缓存
            CommunityConfigCache.putConfigs(configs);
            logger.info("小区配置初始化完成，成功加载 {} 个小区配置", configs.size());
            
        } catch (Exception e) {
            logger.error("初始化小区配置失败", e);
        }
    }
    
    @Override
    public CommunityConfig getConfig(String ocId) {
        if (StringUtils.isEmpty(ocId)) {
            return null;
        }
        
        // 先从缓存获取
        CommunityConfig config = CommunityConfigCache.getConfig(ocId);
        if (config != null) {
            return config;
        }
        
        // 缓存未命中，从数据库加载
        return loadConfigFromDatabase(ocId);
    }
    
    @Override
    public boolean updateConfig(String ocId, String key, Object value) {
        if (StringUtils.isEmpty(ocId) || StringUtils.isEmpty(key)) {
            return false;
        }
        
        try {
            // 先查询现有数据
            OcInfoModel model = OcInfoModel.dao.findById(ocId);
            if (model == null) {
                logger.warn("小区不存在，无法更新配置，ocId: {}", ocId);
                return false;
            }
            
            // 获取现有的ext_json数据
            JSONObject existingExtData = new JSONObject();
            String extJson = model.getStr("ext_json");
            if (StringUtils.isNotEmpty(extJson)) {
                try {
                    existingExtData = JSONObject.parseObject(extJson);
                } catch (Exception e) {
                    logger.warn("解析现有ext_json失败，使用空对象，ocId: {}", ocId);
                    existingExtData = new JSONObject();
                }
            }
            
            // 设置新配置
            existingExtData.put(key, value);
            
            // 更新数据库
            model.set("ext_json", existingExtData.toJSONString());
            model.set("update_time", DateUtils.dateTimeNow());
            
            boolean success = model.update();
            if (success) {
                // 更新缓存
                CommunityConfigCache.updateCacheConfig(ocId, key, value);
                logger.info("更新小区配置成功，ocId: {}, key: {}", ocId, key);
            } else {
                logger.error("更新小区配置失败，数据库更新失败，ocId: {}", ocId);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("更新小区配置失败，ocId: {}, key: {}, 错误: {}", ocId, key, e.getMessage());
            return false;
        }
    }
    
    @Override
    public boolean updateConfigs(String ocId, Map<String, Object> configMap) {
        if (StringUtils.isEmpty(ocId) || configMap == null || configMap.isEmpty()) {
            return false;
        }
        
        try {
            // 先查询现有数据
            OcInfoModel model = OcInfoModel.dao.findById(ocId);
            if (model == null) {
                logger.warn("小区不存在，无法更新配置，ocId: {}", ocId);
                return false;
            }
            
            // 获取现有的ext_json数据
            JSONObject existingExtData = new JSONObject();
            String extJson = model.getStr("ext_json");
            if (StringUtils.isNotEmpty(extJson)) {
                try {
                    existingExtData = JSONObject.parseObject(extJson);
                } catch (Exception e) {
                    logger.warn("解析现有ext_json失败，使用空对象，ocId: {}", ocId);
                    existingExtData = new JSONObject();
                }
            }
            
            // 合并新配置
            existingExtData.putAll(configMap);
            
            // 更新数据库
            model.set("ext_json", existingExtData.toJSONString());
            model.set("update_time", DateUtils.dateTimeNow());
            
            boolean success = model.update();
            if (success) {
                // 更新缓存
                CommunityConfigCache.updateCacheConfigs(ocId, configMap);
                logger.info("批量更新小区配置成功，ocId: {}, 配置数量: {}", ocId, configMap.size());
            } else {
                logger.error("批量更新小区配置失败，数据库更新失败，ocId: {}", ocId);
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("批量更新小区配置失败，ocId: {}, 错误: {}", ocId, e.getMessage());
            return false;
        }
    }
    
    @Override
    public void reloadConfig(String ocId) {
        if (StringUtils.isEmpty(ocId)) {
            return;
        }
        
        // 清除缓存
        CommunityConfigCache.reload(ocId);
        
        // 重新加载到缓存
        loadConfigFromDatabase(ocId);
    }
    
    @Override
    public void reloadAllConfigs() {
        // 清除所有缓存
        CommunityConfigCache.reloadAll();
        
        // 重新初始化
        initAllConfigs();
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 从数据库加载配置
     */
    private CommunityConfig loadConfigFromDatabase(String ocId) {
        try {
            OcInfoModel model = OcInfoModel.dao.findById(ocId);
            if (model == null) {
                logger.warn("小区不存在，ocId: {}", ocId);
                return null;
            }
            
            CommunityConfig config = convertToConfig(model);
            
            // 加载到缓存
            CommunityConfigCache.putConfig(config);
            
            logger.debug("从数据库加载小区配置成功，ocId: {}", ocId);
            return config;
            
        } catch (Exception e) {
            logger.error("从数据库加载小区配置失败，ocId: {}, 错误: {}", ocId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 将OcInfoModel转换为CommunityConfig
     */
    private CommunityConfig convertToConfig(OcInfoModel model) {
        CommunityConfig config = new CommunityConfig();

        // 设置基础字段
        config.put("oc_id", model.getStr("oc_id"));
        config.put("oc_code", model.getStr("oc_code"));
        config.put("oc_name", model.getStr("oc_name"));
        config.put("oc_address", model.getStr("oc_address"));
        config.put("oc_link", model.getStr("oc_link"));
        config.put("oc_state", model.getInt("oc_state"));
        config.put("create_time", model.getStr("create_time"));
        config.put("create_by", model.getStr("create_by"));
        config.put("update_time", model.getStr("update_time"));
        config.put("update_by", model.getStr("update_by"));
        config.put("owner_count", model.getInt("owner_count"));
        config.put("building_num", model.getInt("building_num"));
        config.put("community_name", model.getStr("community_name"));
        config.put("oc_area", model.getStr("oc_area"));
        config.put("pms_id", model.getStr("pms_id"));
        config.put("pms_name", model.getStr("pms_name"));
        config.put("manager_id", model.getStr("manager_id"));
        config.put("service_phone", model.getStr("service_phone"));

        // 设置扩展配置（小配置）
        String extJson = model.getStr("ext_json");
        config.setExtJson(extJson);

        // 设置小区扩展信息
        String infoJson = model.getStr("info_json");
        config.setInfoJson(infoJson);

        return config;
    }
}

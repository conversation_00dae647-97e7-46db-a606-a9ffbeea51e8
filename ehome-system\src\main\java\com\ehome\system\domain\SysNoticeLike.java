package com.ehome.system.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 公告点赞记录表 sys_notice_like
 * 
 * <AUTHOR>
 */
public class SysNoticeLike extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 点赞ID */
    @Excel(name = "序号")
    private Long likeId;

    /** 公告ID */
    private Long noticeId;

    /** 用户ID */
    private String userId;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    /** 用户类型（wx_user/sys_user） */
    @Excel(name = "用户类型", readConverterExp = "wx_user=微信用户,sys_user=系统用户")
    private String userType;

    /** 关联小区ID */
    private String communityId;

    /** 业主ID */
    private String ownerId;

    /** 房屋ID */
    private String houseId;

    /** 房屋名称 */
    @Excel(name = "房屋名称")
    private String houseName;

    /** 状态（0有效 1已取消） */
    @Excel(name = "点赞状态", readConverterExp = "0=有效,1=已取消")
    private String status;

    /** 点赞时间 */
    @Excel(name = "点赞时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date likeTime;

    /** 公告标题（用于显示） */
    @Excel(name = "公告标题", width = 50)
    private String noticeTitle;

    public Long getLikeId() {
        return likeId;
    }

    public void setLikeId(Long likeId) {
        this.likeId = likeId;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLikeTime() {
        return likeTime;
    }

    public void setLikeTime(Date likeTime) {
        this.likeTime = likeTime;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("likeId", getLikeId())
            .append("noticeId", getNoticeId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("userType", getUserType())
            .append("communityId", getCommunityId())
            .append("status", getStatus())
            .append("likeTime", getLikeTime())
            .append("noticeTitle", getNoticeTitle())
            .toString();
    }
}

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('管理车辆绑定')" />
    <th:block th:include="include :: select2-css" />
    <style>
        .binding-section {
            margin-bottom: 25px;
            background: white;
            border-radius: 3px;
            padding: 20px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .binding-section:hover {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .binding-section h5 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #333;
            font-weight: 600;
            font-size: 18px;
            display: flex;
            align-items: center;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
        }

        .binding-section h5 i {
            margin-right: 10px;
            font-size: 20px;
        }

        /* 不同绑定类型的主题色 */
        .binding-section:nth-child(2) h5 i { color: #007bff; } /* 住户 */
        .binding-section:nth-child(3) h5 i { color: #28a745; } /* 房屋 */
        .binding-section:nth-child(4) h5 i { color: #fd7e14; } /* 车位 */
        .binding-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .binding-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }

        .binding-item:last-child {
            margin-bottom: 0;
        }

        .binding-info {
            flex: 1;
        }

        .binding-info strong {
            font-size: 16px;
            color: #333;
        }

        .binding-actions {
            margin-left: 15px;
        }

        .binding-actions .btn {
            margin-left: 5px;
        }

        .add-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 2px dashed #dee2e6;
        }

        .add-section:hover {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .binding-add-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .binding-select {
            flex: 1;
            min-width: 200px;
            height: 34px;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 14px;
        }

        .binding-select:focus {
            border-color: #007bff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .binding-add-btn {
            white-space: nowrap;
            flex-shrink: 0;
        }
        .vehicle-title {
            padding: 15px 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .vehicle-title h2 {
            color: #333;
            margin: 0;
            font-weight: 600;
            font-size: 16px;
            text-align: left;
        }

        .vehicle-title .vehicle-icon {
            font-size: 16px;
            margin-right: 8px;
            color: #666;
        }
        .no-data {
            text-align: center;
            color: #999;
            padding: 20px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!-- 车辆标题 -->
        <div class="vehicle-title">
            <h2><i class="fa fa-automobile vehicle-icon"></i><span th:text="${vehicle.plate_no}"></span></h2>
        </div>

        <!-- 已绑定住户 -->
        <div class="binding-section">
            <h5><i class="fa fa-user"></i> 已绑定住户</h5>
            <div id="ownerList">
                <div class="no-data">暂无绑定住户</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="ownerSelect" class="binding-select">
                        <option value="">请选择住户</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addOwnerBinding()">
                        <i class="fa fa-plus"></i> 绑定住户
                    </button>
                </div>
            </div>
        </div>

        <!-- 已绑定房屋 -->
        <div class="binding-section">
            <h5><i class="fa fa-home"></i> 已绑定房屋</h5>
            <div id="houseList">
                <div class="no-data">暂无绑定房屋</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="houseSelect" class="binding-select">
                        <option value="">请选择房屋</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addHouseBinding()">
                        <i class="fa fa-plus"></i> 绑定房屋
                    </button>
                </div>
            </div>
        </div>

        <!-- 已绑定车位 -->
        <div class="binding-section">
            <h5><i class="fa fa-square"></i> 已绑定车位</h5>
            <div id="parkingList">
                <div class="no-data">暂无绑定车位</div>
            </div>
            <div class="add-section">
                <div class="binding-add-row">
                    <select id="parkingSelect" class="binding-select">
                        <option value="">请选择车位</option>
                    </select>
                    <button type="button" class="btn btn-primary binding-add-btn" onclick="addParkingBinding()">
                        <i class="fa fa-plus"></i> 绑定车位
                    </button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var vehicleId = /*[[${vehicleId}]]*/ '';
        var prefix = ctx + "oc/vehicle";

        $(function() {
            loadBindingData();
            initSelects();
        });

        function loadBindingData() {
            loadOwnerBindings();
            loadHouseBindings();
            loadParkingBindings();
        }

        function initSelects() {
            // 初始化住户选择
            $('#ownerSelect').select2({
                placeholder: '请选择住户',
                allowClear: true,
                ajax: {
                    url: prefix + '/ownerList',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            owner_name: params.term,
                            pageNum: params.page || 1,
                            pageSize: 20
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.rows.map(function(item) {
                                return {
                                    id: item.owner_id,
                                    text: item.owner_name + ' (' + item.mobile + ')'
                                };
                            }),
                            pagination: {
                                more: (params.page * 20) < data.total
                            }
                        };
                    }
                }
            });

            // 初始化房屋选择
            $('#houseSelect').select2({
                placeholder: '请选择房屋',
                allowClear: true,
                ajax: {
                    url: prefix + '/getAvailableHousesForVehicle',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            search: params.term,
                            vehicleId: vehicleId,
                            page: params.page || 1
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.data || [],
                            pagination: {
                                more: (params.page * 20) < (data.total || 0)
                            }
                        };
                    }
                }
            });

            // 初始化车位选择
            $('#parkingSelect').select2({
                placeholder: '请选择车位',
                allowClear: true,
                ajax: {
                    url: prefix + '/parkingSpaceList',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            parking_no: params.term,
                            pageNum: params.page || 1,
                            pageSize: 20
                        };
                    },
                    processResults: function (data, params) {
                        params.page = params.page || 1;
                        return {
                            results: data.rows.map(function(item) {
                                return {
                                    id: item.parking_id,
                                    text: item.parking_no + ' (' + getParkingStatusText(item.parking_status) + ')'
                                };
                            }),
                            pagination: {
                                more: (params.page * 20) < data.total
                            }
                        };
                    }
                }
            });
        }

        function getParkingStatusText(status) {
            switch(status) {
                case 1: return '出售';
                case 2: return '出租';
                case 3: return '自用';
                default: return '未知';
            }
        }

        function loadOwnerBindings() {
            $.ajax({
                url: prefix + "/ownerBindings",
                type: "GET",
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderOwnerBindings(res.data);
                    }
                }
            });
        }

        function loadHouseBindings() {
            $.ajax({
                url: prefix + "/houseBindings", 
                type: "GET",
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderHouseBindings(res.data);
                    }
                }
            });
        }

        function loadParkingBindings() {
            $.ajax({
                url: prefix + "/parkingBindings",
                type: "GET", 
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderParkingBindings(res.data);
                    }
                }
            });
        }

        function renderOwnerBindings(data) {
            var html = '';
            if (data && data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    var owner = data[i];
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + owner.owner_name + '</strong>';
                    html += '<span class="text-muted"> (' + owner.mobile + ')</span>';
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<a href="javascript:void(0)" onclick="removeOwnerBinding(\'' + owner.rel_id + '\', \'' + owner.owner_name + '\')" class="btn btn-xs btn-danger">解绑</a>';
                    html += '</div>';
                    html += '</div>';
                }
            } else {
                html = '<div class="no-data">暂无绑定住户</div>';
            }
            $('#ownerList').html(html);
        }

        function renderHouseBindings(data) {
            var html = '';
            if (data && data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    var house = data[i];
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + house.house_name + '</strong>';
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<a href="javascript:void(0)" onclick="removeHouseBinding(\'' + house.rel_id + '\', \'' + house.house_name + '\')" class="btn btn-xs btn-danger">解绑</a>';
                    html += '</div>';
                    html += '</div>';
                }
            } else {
                html = '<div class="no-data">暂无绑定房屋</div>';
            }
            $('#houseList').html(html);
        }

        function renderParkingBindings(data) {
            var html = '';
            if (data && data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    var parking = data[i];
                    html += '<div class="binding-item">';
                    html += '<div class="binding-info">';
                    html += '<strong>' + parking.parking_no + '</strong>';
                    html += '<span class="text-muted"> (' + getParkingStatusText(parking.parking_status) + ')</span>';
                    html += '</div>';
                    html += '<div class="binding-actions">';
                    html += '<a href="javascript:void(0)" onclick="removeParkingBinding(\'' + parking.rel_id + '\', \'' + parking.parking_no + '\')" class="btn btn-xs btn-danger">解绑</a>';
                    html += '</div>';
                    html += '</div>';
                }
            } else {
                html = '<div class="no-data">暂无绑定车位</div>';
            }
            $('#parkingList').html(html);
        }

        // 添加住户绑定
        function addOwnerBinding() {
            var ownerId = $('#ownerSelect').val();
            if (!ownerId) {
                $.modal.alertWarning("请选择要绑定的住户");
                return;
            }

            $.ajax({
                url: prefix + "/bindOwner",
                type: "POST",
                data: {
                    vehicleId: vehicleId,
                    ownerId: ownerId
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#ownerSelect').val(null).trigger('change');
                        loadOwnerBindings();
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        // 添加房屋绑定
        function addHouseBinding() {
            var houseId = $('#houseSelect').val();
            if (!houseId) {
                $.modal.alertWarning("请选择要绑定的房屋");
                return;
            }

            $.ajax({
                url: prefix + "/bindHouse",
                type: "POST",
                data: {
                    vehicleId: vehicleId,
                    houseId: houseId
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#houseSelect').val(null).trigger('change');
                        loadHouseBindings();
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        // 添加车位绑定
        function addParkingBinding() {
            var parkingId = $('#parkingSelect').val();
            if (!parkingId) {
                $.modal.alertWarning("请选择要绑定的车位");
                return;
            }

            $.ajax({
                url: prefix + "/bindParkingSpace",
                type: "POST",
                data: {
                    vehicleId: vehicleId,
                    parkingId: parkingId
                },
                success: function(res) {
                    if (res.code === 0) {
                        $.modal.msgSuccess("绑定成功");
                        $('#parkingSelect').val(null).trigger('change');
                        loadParkingBindings();
                    } else {
                        $.modal.alertError("绑定失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("绑定失败");
                }
            });
        }

        // 解绑住户
        function removeOwnerBinding(relId, ownerName) {
            $.modal.confirm("确定要解绑住户【" + ownerName + "】吗？", function() {
                $.ajax({
                    url: prefix + "/removeOwnerBinding",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadOwnerBindings();
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        // 解绑房屋
        function removeHouseBinding(relId, houseName) {
            $.modal.confirm("确定要解绑房屋【" + houseName + "】吗？", function() {
                $.ajax({
                    url: prefix + "/removeHouseBinding",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadHouseBindings();
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }

        // 解绑车位
        function removeParkingBinding(relId, parkingNo) {
            $.modal.confirm("确定要解绑车位【" + parkingNo + "】吗？", function() {
                $.ajax({
                    url: prefix + "/removeParkingBinding",
                    type: "POST",
                    data: { relId: relId },
                    success: function(res) {
                        if (res.code === 0) {
                            $.modal.msgSuccess("解绑成功");
                            loadParkingBindings();
                        } else {
                            $.modal.alertError("解绑失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("解绑失败");
                    }
                });
            });
        }


    </script>
</body>
</html>

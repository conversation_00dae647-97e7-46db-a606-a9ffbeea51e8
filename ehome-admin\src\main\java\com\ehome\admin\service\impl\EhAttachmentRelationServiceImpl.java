package com.ehome.admin.service.impl;

import com.ehome.admin.service.IEhAttachmentRelationService;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 附件关联服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class EhAttachmentRelationServiceImpl implements IEhAttachmentRelationService {
    
    private static final Logger log = LoggerFactory.getLogger(EhAttachmentRelationServiceImpl.class);
    
    @Override
    public List<Record> getAttachmentsByBusiness(String businessType, String businessId) {
        String sql = "SELECT * FROM eh_attachment_relation WHERE business_type = ? AND business_id = ? AND status = '0' ORDER BY sort_order ASC, create_time ASC";
        return Db.find(sql, businessType, businessId);
    }
    
    @Override
    @Transactional
    public boolean saveBusinessAttachments(String businessType, String businessId, String[] fileIds, String createBy, String communityId) {
        try {
            // 先删除原有关联
            deleteBusinessAttachments(businessType, businessId);
            
            // 添加新的关联
            if (fileIds != null && fileIds.length > 0) {
                for (int i = 0; i < fileIds.length; i++) {
                    Record record = new Record();
                    record.set("relation_id", Seq.getId());
                    record.set("business_type", businessType);
                    record.set("business_id", businessId);
                    record.set("file_id", fileIds[i]);
                    record.set("sort_order", i + 1);
                    record.set("create_time", DateUtils.getNowDate());
                    record.set("create_by", createBy);
                    record.set("community_id", communityId);
                    record.set("status", "0");
                    
                    Db.save("eh_attachment_relation", "relation_id", record);

                    Db.update("update eh_file_info set business_id = ? where file_id = ?", businessId,fileIds[i]);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("保存业务附件关联失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean deleteBusinessAttachments(String businessType, String businessId) {
        try {
            String sql = "UPDATE eh_attachment_relation SET status = '1' WHERE business_type = ? AND business_id = ?";
            Db.update(sql, businessType, businessId);
            return true;
        } catch (Exception e) {
            log.error("删除业务附件关联失败", e);
            return false;
        }
    }
    
    @Override
    public boolean deleteAttachmentRelation(String relationId) {
        try {
            String sql = "UPDATE eh_attachment_relation SET status = '1' WHERE relation_id = ?";
            Db.update(sql, relationId);
            return true;
        } catch (Exception e) {
            log.error("删除附件关联失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateAttachmentSort(String relationId, Integer sortOrder) {
        try {
            String sql = "UPDATE eh_attachment_relation SET sort_order = ? WHERE relation_id = ?";
            Db.update(sql, sortOrder, relationId);
            return true;
        } catch (Exception e) {
            log.error("更新附件排序失败", e);
            return false;
        }
    }
    
    @Override
    public List<Record> getAttachmentDetails(String businessType, String businessId) {
        String sql = "SELECT ar.*, fi.original_name, fi.access_url, fi.file_size, fi.file_type, fi.mime_type " +
                    "FROM eh_attachment_relation ar " +
                    "LEFT JOIN eh_file_info fi ON ar.file_id = fi.file_id " +
                    "WHERE ar.business_type = ? AND ar.business_id = ? AND ar.status = '0' AND fi.status = '0' " +
                    "ORDER BY ar.sort_order ASC, ar.create_time ASC";
        return Db.find(sql, businessType, businessId);
    }
}

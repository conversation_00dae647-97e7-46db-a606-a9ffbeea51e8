# 房屋解除绑定功能实现

## 需求描述
在小程序房屋页面底部操作按钮区域添加"解除绑定"按钮，实现房屋和业主关系解除功能。

## 实现方案
基于现有管理端解绑逻辑，在微信端实现房屋解绑功能。

## 技术实现

### 1. 后端API实现
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/wx/HouseController.java`

新增 `/api/wx/house/unbind` 接口：
- 接收参数：`houseId`
- 验证用户权限和绑定关系
- 处理默认房屋逻辑
- 删除绑定关系并更新统计数据

**核心逻辑**：
1. 根据当前用户ownerId和houseId查找绑定关系
2. 检查是否为默认房屋，如有其他房屋则自动设置新的默认房屋
3. 删除eh_house_owner_rel表中的绑定记录
4. 更新房屋和业主的统计信息

### 2. 前端界面实现
**文件**: `miniprogram/pages/house/index.wxml`

在底部操作按钮区域添加解绑按钮：
```xml
<van-button
  type="danger"
  icon="delete-o"
  custom-class="bottom-btn unbind-btn"
  bindtap="unbindHouse"
  wx:if="{{houseList.length > 0}}"
>
  解除绑定
</van-button>
```

### 3. 前端逻辑实现
**文件**: `miniprogram/pages/house/index.js`

实现解绑交互流程：
1. `unbindHouse()` - 主入口，处理房屋选择
2. `confirmUnbindHouse()` - 确认弹窗，显示警告信息
3. `performUnbindHouse()` - 执行解绑API调用

**交互流程**：
- 单套房屋：直接确认解绑
- 多套房屋：显示选择列表
- 默认房屋：特殊警告提示
- 成功后刷新房屋列表和本地缓存

### 4. 样式优化
**文件**: `miniprogram/pages/house/index.wxss`

优化三个按钮的布局和样式：

**按钮布局优化**：
- 合适的按钮间距：`gap: 24rpx`
- 设置按钮最小/最大宽度：`min-width: 200rpx, max-width: 240rpx`
- 添加文字溢出处理：`white-space: nowrap, text-overflow: ellipsis`
- 底部固定显示：`position: fixed, bottom: 0`
- 添加顶部边框：`border-top: 1rpx solid #ebedf0`
- 设置层级：`z-index: 100`
- 容器底部留白：`padding-bottom: 140rpx`
- 单行排列：`flex-wrap: nowrap`

**响应式设计**：
- 中等屏幕：调整按钮尺寸和间距
- 小屏幕：改为垂直布局，避免挤压

**按钮文字优化**：
- "邀请住户" - 保持清晰描述
- "管理邀请" - 明确功能含义
- "解绑房屋" - 准确表达操作

**图标和尺寸优化**：
- 图标大小：`icon-size="16px"`
- 按钮高度：`88rpx`
- 文字大小：`26rpx`
- 按钮间距：`12rpx`

**解绑按钮样式**：
```css
.unbind-btn {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}
```

## 业务逻辑处理

### 默认房屋处理
- 如果解绑的是默认房屋且用户还有其他房屋，自动将最早的其他房屋设为默认
- 如果是最后一套房屋，清除默认房屋设置

### 数据一致性
- 更新 `eh_house_info.owner_count` 字段
- 更新 `eh_owner.house_count` 字段  
- 更新 `eh_owner.house_info` 字段
- 如果是默认房屋，更新 `eh_owner.house_id` 字段

### 权限验证
- 只能解绑当前用户自己的房屋
- 验证绑定关系确实存在

## 测试要点
1. 单套房屋解绑
2. 多套房屋选择解绑
3. 默认房屋解绑
4. 最后一套房屋解绑
5. 解绑后数据一致性检查
6. 权限验证测试

## 附加功能
### 住户信息显示
在房屋卡片中新增住户信息显示行：
- **前端界面**：在房屋地址下方添加"住户信息"行
- **后端数据**：在 `recordToObj` 方法中设置 `ownerStr` 字段
- **显示逻辑**：显示 `owner_str` 字段内容，无数据时显示"暂无住户"

## 完成状态
✅ 后端API实现
✅ 前端界面添加
✅ 前端逻辑实现
✅ 样式优化
✅ 住户信息显示

package com.ehome.oc.domain;

import lombok.Data;

@Data
public class Community {
    private String ocId;        // 小区唯一标识
    private String ocCode;      // 小区代码
    private String ocName;      // 小区名称
    private String ocAddress;   // 小区地址
    private String ocLink;      // 小区联系人信息
    private Integer ocState;    // 小区状态
    private String createTime;  // 创建时间
    private String createBy;    // 创建人
    private String updateTime;  // 修改时间
    private String updateBy;    // 修改人
    private Integer ownerCount; // 业主数量
    private Integer buildingNum;// 楼宇栋数
    private String communityName;// 所属社区
    private String ocArea;      // 小区面积
    private String pmsId;       // 物业ID
    private String pmsName;     // 物业名称
    private String managerId;   // 负责该小区的员工ID
} 
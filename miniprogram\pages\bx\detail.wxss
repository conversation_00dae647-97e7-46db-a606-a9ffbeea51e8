.detail-page {
  background: #f7f8fa;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: 150rpx; /* 为验收按钮预留更大空间 */
}

.loading, .error {
  text-align: center;
  color: #999;
  margin-top: 200rpx;
  font-size: 28rpx;
}

.detail-content {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 标题区域 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.location-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.address {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}

.time-diff {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.status-tag {
  font-size: 28rpx;
  font-weight: 500;
}

/* 类型徽章 */
.type-badge {
  padding: 12rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.badge-text {
  display: inline-block;
  padding: 6rpx 12rpx;
  border: 1rpx solid #1890ff;
  color: #1890ff;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  background: transparent;
}

/* 内容区域 */
.content-section {
  padding: 32rpx;
  background: #f8f9fa;
  margin: 16rpx 32rpx;
  border-radius: 12rpx;
}

.content-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.content-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
}

/* 图片区域 */
.images-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

.images-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.image {
  width: 100%;
  height: 100%;
}

/* 附件区域 */
.attachments-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.file-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6f7ff;
  border-radius: 8rpx;
  margin-right: 16rpx;
  font-size: 32rpx;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.file-size {
  font-size: 24rpx;
  color: #999;
}

.download-icon {
  font-size: 28rpx;
  color: #1890ff;
}

/* 基本信息 */
.info-section {
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.info-value.content-value {
  line-height: 1.6;
  word-wrap: break-word;
}

.info-value.no-feedback {
  color: #999;
  font-style: italic;
}

/* 验收状态样式 */
.info-value.ys-status-0 {
  color: #faad14;
}

.info-value.ys-status-1 {
  color: #52c41a;
}

.info-value.ys-status-2 {
  color: #ff4d4f;
}



/* 暂无处理结果 */
.no-reply-section {
  padding: 32rpx;
}

.no-reply-content {
  background: #fafafa;
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
}

.no-reply-text {
  font-size: 28rpx;
  color: #999;
}

/* 处理结果独立内容块 */
.result-content {
  background: #fff;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.result-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.result-section:last-child {
  border-bottom: none;
}

.section-title-top {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 16rpx;
  display: flex;
  align-items: flex-start;
}

.section-title-top::before {
  content: '';
  position: absolute;
  left: 0;
  top: 4rpx;
  width: 6rpx;
  height: 32rpx;
  background: #1890ff;
  border-radius: 3rpx;
}

/* 验收按钮 */
.acceptance-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 24rpx rgba(0, 0, 0, 0.08);
  border-top: 1rpx solid #f0f0f0;
  gap: 24rpx;
}

.accept-btn, .reject-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.accept-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
}

.reject-btn {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
}

.accept-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
}

.reject-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.4);
}

.accept-btn::before, .reject-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.accept-btn:active::before, .reject-btn:active::before {
  left: 100%;
}

// 配置系统测试文件
// 用于验证新的配置管理系统是否正常工作

import { getConfigManager } from '../utils/configManager.js'
import { 
  getConfigValue, 
  getAllConfig, 
  isConfigEnabled, 
  getConfigNumber,
  UIConfig, 
  FeatureConfig, 
  ShareConfig 
} from '../utils/configHelper.js'

/**
 * 测试配置管理器基本功能
 */
function testConfigManager() {
  console.log('=== 测试ConfigManager ===')
  
  const configManager = getConfigManager()
  
  // 测试初始化
  const mockCommunityInfo = {
    extJson: JSON.stringify({
      miniprogram_title: '测试小区',
      enable_screenshot: '0',
      menu_rows: '6',
      enable_share: '1'
    })
  }
  
  configManager.init(mockCommunityInfo)
  
  // 测试基本配置获取
  console.log('标题:', configManager.get('miniprogram_title'))
  console.log('截屏设置:', configManager.get('enable_screenshot'))
  console.log('菜单行数:', configManager.getNumber('menu_rows'))
  console.log('分享启用:', configManager.isEnabled('enable_share'))
  
  // 测试配置状态
  console.log('配置状态:', configManager.getStatus())
  
  console.log('ConfigManager测试完成 ✓')
}

/**
 * 测试配置助手功能
 */
function testConfigHelper() {
  console.log('\n=== 测试ConfigHelper ===')
  
  // 测试基础函数
  console.log('配置值获取:', getConfigValue('miniprogram_title', '默认标题'))
  console.log('配置启用检查:', isConfigEnabled('enable_share'))
  console.log('数值配置获取:', getConfigNumber('menu_rows', 4))
  
  // 测试分类配置
  console.log('\nUI配置:')
  console.log('- 标题:', UIConfig.getTitle())
  console.log('- 菜单行数:', UIConfig.getMenuRows())
  console.log('- 大卡片显示:', UIConfig.isBigCardEnabled())
  
  console.log('\n功能配置:')
  console.log('- 标记启用:', FeatureConfig.isMarkEnabled())
  console.log('- 截屏启用:', FeatureConfig.isScreenshotEnabled())
  
  console.log('\n分享配置:')
  console.log('- 分享启用:', ShareConfig.isEnabled())
  console.log('- 分享标题:', ShareConfig.getTitle())
  console.log('- 完整分享配置:', ShareConfig.getShareConfig())
  
  console.log('ConfigHelper测试完成 ✓')
}

/**
 * 测试配置监听功能
 */
function testConfigListener() {
  console.log('\n=== 测试配置监听 ===')
  
  const configManager = getConfigManager()
  
  // 添加监听器
  configManager.addListener('test-listener', (event) => {
    console.log('配置变更事件:', event.type)
    console.log('新配置键数量:', Object.keys(event.newConfig).length)
  })
  
  // 模拟配置更新
  const updatedCommunityInfo = {
    extJson: JSON.stringify({
      miniprogram_title: '更新后的小区',
      enable_screenshot: '1',
      menu_rows: '8',
      enable_share: '0'
    })
  }
  
  configManager.update(updatedCommunityInfo)
  
  // 验证更新后的配置
  console.log('更新后标题:', configManager.get('miniprogram_title'))
  console.log('更新后截屏设置:', configManager.get('enable_screenshot'))
  
  // 移除监听器
  configManager.removeListener('test-listener')
  
  console.log('配置监听测试完成 ✓')
}

/**
 * 测试配置缓存功能
 */
function testConfigCache() {
  console.log('\n=== 测试配置缓存 ===')
  
  const configManager = getConfigManager()
  
  // 多次获取相同配置，验证缓存
  const start = Date.now()
  for (let i = 0; i < 1000; i++) {
    configManager.get('miniprogram_title')
    configManager.getAll()
    configManager.getCategory('ui')
  }
  const end = Date.now()
  
  console.log(`1000次配置访问耗时: ${end - start}ms`)
  console.log('缓存大小:', configManager.getStatus().cacheSize)
  
  // 清除缓存
  configManager.clearCache()
  console.log('清除缓存后大小:', configManager.getStatus().cacheSize)
  
  console.log('配置缓存测试完成 ✓')
}

/**
 * 测试错误处理
 */
function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===')
  
  const configManager = getConfigManager()
  
  // 测试无效的extJson
  const invalidCommunityInfo = {
    extJson: 'invalid json'
  }
  
  try {
    configManager.init(invalidCommunityInfo)
    console.log('无效JSON处理正常 ✓')
  } catch (error) {
    console.error('无效JSON处理失败:', error)
  }
  
  // 测试不存在的配置键
  const nonExistentValue = configManager.get('non_existent_key', 'default')
  console.log('不存在的配置键返回默认值:', nonExistentValue)
  
  // 测试无效的监听器
  configManager.addListener('invalid-listener', 'not a function')
  
  console.log('错误处理测试完成 ✓')
}

/**
 * 运行所有测试
 */
export function runConfigSystemTests() {
  console.log('开始配置系统测试...\n')
  
  try {
    testConfigManager()
    testConfigHelper()
    testConfigListener()
    testConfigCache()
    testErrorHandling()
    
    console.log('\n🎉 所有配置系统测试通过！')
    return true
  } catch (error) {
    console.error('\n❌ 配置系统测试失败:', error)
    return false
  }
}

/**
 * 在页面中使用的简化测试
 */
export function quickConfigTest() {
  console.log('=== 快速配置测试 ===')
  
  // 测试基本配置访问
  console.log('UI配置 - 标题:', UIConfig.getTitle())
  console.log('功能配置 - 截屏:', FeatureConfig.isScreenshotEnabled())
  console.log('分享配置 - 启用:', ShareConfig.isEnabled())
  
  // 测试配置管理器状态
  const configManager = getConfigManager()
  console.log('配置管理器状态:', configManager.getStatus())
  
  console.log('快速配置测试完成 ✓')
}

// 如果在页面中直接运行
if (typeof Page !== 'undefined') {
  // 在页面加载时运行快速测试
  const originalOnLoad = Page.prototype.onLoad
  Page.prototype.onLoad = function(options) {
    // 延迟执行测试，确保配置已初始化
    setTimeout(() => {
      quickConfigTest()
    }, 1000)
    
    if (originalOnLoad) {
      originalOnLoad.call(this, options)
    }
  }
}

export default {
  runConfigSystemTests,
  quickConfigTest
}

.patrol-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #666;
}

.patrol-list {
  background: white;
  border-radius: 10rpx;
  overflow: hidden;
}

.patrol-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patrol-item:last-child {
  border-bottom: none;
}

.patrol-info {
  flex: 1;
}

.patrol-location {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.patrol-time {
  font-size: 24rpx;
  color: #999;
}

.patrol-status {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.normal {
  color: #52c41a;
  background: #f6ffed;
}

.abnormal {
  color: #f5222d;
  background: #fff1f0;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

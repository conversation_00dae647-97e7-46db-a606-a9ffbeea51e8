/**
 * 首页UI管理器
 * 统一管理首页的UI状态和显示控制
 */

/**
 * UI状态常量
 */
export const UI_STATES = {
  LOADING: 'loading',
  CONTENT_VISIBLE: 'content_visible',
  FUNCTION_CONTAINER_VISIBLE: 'function_container_visible'
}

/**
 * 首页UI管理器类
 */
class IndexUIManager {
  constructor(page) {
    this.page = page
    this.pendingUpdates = {}
    this.updateTimer = null
    this.isUpdating = false
    
    // UI状态
    this.uiState = {
      pageLoading: true,
      contentVisible: false,
      functionContainerVisible: false
    }
  }

  /**
   * 设置页面loading状态
   * @param {boolean} loading 是否loading
   */
  setPageLoading(loading) {
    this.uiState.pageLoading = loading
    this.scheduleUpdate({ pageLoading: loading })
  }

  /**
   * 设置内容可见状态
   * @param {boolean} visible 是否可见
   */
  setContentVisible(visible) {
    this.uiState.contentVisible = visible
    this.scheduleUpdate({ contentVisible: visible })
  }

  /**
   * 设置功能容器可见状态
   * @param {boolean} visible 是否可见
   */
  setFunctionContainerVisible(visible) {
    this.uiState.functionContainerVisible = visible
    this.scheduleUpdate({ functionContainerVisible: visible })
  }

  /**
   * 批量设置UI状态
   * @param {Object} states 状态对象
   */
  setUIStates(states) {
    Object.assign(this.uiState, states)
    this.scheduleUpdate(states)
  }

  /**
   * 获取当前UI状态
   * @returns {Object} UI状态
   */
  getUIStates() {
    return { ...this.uiState }
  }

  /**
   * 显示页面内容（快速显示）
   */
  showContent() {
    const updates = {
      pageLoading: false,
      contentVisible: true
    }
    
    Object.assign(this.uiState, updates)
    this.immediateUpdate(updates)
  }

  /**
   * 隐藏页面内容
   */
  hideContent() {
    const updates = {
      pageLoading: true,
      contentVisible: false
    }
    
    Object.assign(this.uiState, updates)
    this.immediateUpdate(updates)
  }

  /**
   * 强制显示内容（兜底保护）
   */
  forceShowContent() {
    const updates = {
      pageLoading: false,
      contentVisible: true,
      functionContainerVisible: true
    }
    
    Object.assign(this.uiState, updates)
    this.immediateUpdate(updates)
    
    console.log('[IndexUIManager] 强制显示页面内容')
  }

  /**
   * 初始化页面显示状态
   */
  initializeDisplay() {
    const updates = {
      pageLoading: true,
      contentVisible: false,
      functionContainerVisible: false
    }
    
    Object.assign(this.uiState, updates)
    this.immediateUpdate(updates)
  }

  /**
   * 完成页面初始化
   */
  completeInitialization() {
    const updates = {
      pageLoading: false,
      contentVisible: true,
      functionContainerVisible: true
    }
    
    Object.assign(this.uiState, updates)
    this.immediateUpdate(updates)
  }

  /**
   * 调度更新（批量更新优化）
   * @param {Object} updates 更新数据
   */
  scheduleUpdate(updates) {
    // 合并待更新的数据
    Object.assign(this.pendingUpdates, updates)
    
    // 如果已经有定时器，清除它
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
    }
    
    // 设置新的定时器，延迟执行更新
    this.updateTimer = setTimeout(() => {
      this.flushUpdates()
    }, 16) // 约一帧的时间
  }

  /**
   * 立即更新（用于关键UI状态）
   * @param {Object} updates 更新数据
   */
  immediateUpdate(updates) {
    if (this.isUpdating) {
      // 如果正在更新，合并到待更新数据中
      Object.assign(this.pendingUpdates, updates)
      return
    }
    
    this.performUpdate(updates)
  }

  /**
   * 刷新所有待更新的数据
   */
  flushUpdates() {
    if (Object.keys(this.pendingUpdates).length === 0) {
      return
    }
    
    const updates = { ...this.pendingUpdates }
    this.pendingUpdates = {}
    this.updateTimer = null
    
    this.performUpdate(updates)
  }

  /**
   * 执行实际的setData更新
   * @param {Object} updates 更新数据
   */
  performUpdate(updates) {
    if (!this.page || !updates || Object.keys(updates).length === 0) {
      return
    }
    
    this.isUpdating = true
    
    try {
      this.page.setData(updates)
      console.log('[IndexUIManager] UI状态已更新:', Object.keys(updates))
    } catch (error) {
      console.error('[IndexUIManager] UI更新失败:', error)
    } finally {
      this.isUpdating = false
    }
  }

  /**
   * 设置超时保护
   * @param {number} timeout 超时时间（毫秒）
   * @param {Function} callback 超时回调
   * @returns {number} 定时器ID
   */
  setTimeoutProtection(timeout, callback) {
    return setTimeout(() => {
      if (this.uiState.pageLoading) {
        console.warn('[IndexUIManager] 超时保护触发')
        callback && callback()
      }
    }, timeout)
  }

  /**
   * 清除超时保护
   * @param {number} timerId 定时器ID
   */
  clearTimeoutProtection(timerId) {
    if (timerId) {
      clearTimeout(timerId)
    }
  }

  /**
   * 创建带超时保护的初始化流程
   * @param {Function} initFunction 初始化函数
   * @param {number} timeout 超时时间
   * @returns {Promise} 初始化Promise
   */
  async createProtectedInitialization(initFunction, timeout = 3000) {
    // 设置初始状态
    this.initializeDisplay()
    
    try {
      // 创建超时保护
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('页面初始化超时')), timeout)
      )
      
      // 执行初始化
      const initPromise = initFunction()
      
      // 竞争执行
      await Promise.race([initPromise, timeoutPromise])
      
      // 成功完成初始化
      this.completeInitialization()
      
    } catch (error) {
      console.warn('[IndexUIManager] 初始化失败:', error.message || error)
      // 失败时强制显示内容
      this.forceShowContent()
    }
    
    // 兜底保护：无论如何，一定时间后强制显示
    setTimeout(() => {
      if (this.uiState.pageLoading) {
        console.warn('[IndexUIManager] 兜底保护：强制显示页面内容')
        this.forceShowContent()
      }
    }, timeout + 500)
  }

  /**
   * 重置UI状态
   */
  reset() {
    this.uiState = {
      pageLoading: true,
      contentVisible: false,
      functionContainerVisible: false
    }
    
    this.pendingUpdates = {}
    
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
      this.updateTimer = null
    }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
    }
    
    this.page = null
    this.pendingUpdates = {}
    this.updateTimer = null
  }
}

/**
 * 创建UI管理器实例
 * @param {Object} page 页面实例
 * @returns {IndexUIManager}
 */
export function createIndexUIManager(page) {
  return new IndexUIManager(page)
}

export default IndexUIManager

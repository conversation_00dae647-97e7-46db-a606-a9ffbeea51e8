<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('房屋详情')" />
    <style>
        .house-info-card {
            background: transparent;
            padding: 10px 0;
        }
        .layui-tab-content{
            padding-top: 0px!important;
        }

        .search-collapse, .select-table{
            box-shadow:none;
            padding: 0px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
        }
        .info-row-four {
            display: flex;
            margin-bottom: 0px;
            flex-wrap: wrap;
        }
        .info-item {
            flex: 1;
            min-width: 25%;
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            width: auto;
            font-weight: 500;
            color: #333;
            white-space: nowrap;
        }
        .info-value {
            flex: 1;
            color: #666;
        }
        .arrear-highlight {
            color: #f56c6c;
            font-weight: bold;
            font-size: 16px;
        }
        .normal-highlight {
            color: #67c23a;
            font-weight: bold;
            font-size: 16px;
        }
        .layui-tab-content {
            padding: 20px 0;
        }
        .bill-table {
            margin-top: 20px;
        }
        .checkout-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: right;
            background: #fff;
            border-top: 1px solid #e5e5e5;
            padding: 15px 20px;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        }
        .main-content {
            padding-bottom: 80px; /* 为固定底部留出空间 */
        }

        #houseInfoContent{
            background: rgba(0, 0, 0, .02);
            padding: 10px 20px;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content main-content">
        <input type="hidden" id="houseId" th:value="${houseId}" />
        <!-- 标签页 -->
        <div class="layui-tab layui-tab-brief" lay-filter="houseDetailTab">
            <ul class="layui-tab-title">
                <li class="layui-this">未缴账单</li>
                <li>已缴账单</li>
            </ul>
            
            <!-- 房屋基本信息卡片 -->
            <div class="house-info-card">
                <div id="houseInfoContent">
                    <!-- 房屋信息将在这里动态加载 -->
                </div>
            </div>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                        <div class="row">
                            <div class="col-sm-12">
                                <!-- 账单表格 -->
                                <div class="col-sm-12 select-table table-striped">
                                    <table id="billTable"></table>
                                </div>
                            </div>
                        </div>

                </div>
                
                <!-- 已缴账单标签页 -->
                <div class="layui-tab-item">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="btn-group-sm" role="group" style="margin-bottom: 15px;">
                                    <button class="btn btn-info" onclick="refreshPaidBills()">
                                        <i class="fa fa-refresh"></i> 刷新
                                    </button>
                                </div>

                                <!-- 已缴账单表格 -->
                                <div class="col-sm-12 select-table table-striped">
                                    <table id="paidBillTable"></table>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 固定底部操作栏 -->
    <div class="checkout-footer">
        <span id="paymentSummary" style="margin-left: 20px; font-weight: bold; color: #333;">
            应缴金额：¥0(违约金合计¥0，优惠合计¥0)
        </span>
        <button class="btn btn-sm btn-info ml-10" onclick="refreshBills()" style="margin-left: 20px;">刷新</button>
        <button class="btn btn-sm btn-success ml-10" onclick="batchPayment()" shiro:hasPermission="oc:charge:bill:payment">批量收款</button>
    </div>

    <!-- jsrender模板 -->
    <script id="houseInfoTemplate" type="text/x-jsrender">
        <div class="info-row-four">
            <div class="info-item">
                <div class="info-label">房屋：</div>
                <div class="info-value">{{:building_name || '-'}}/{{:unit_name || '-'}}/{{:room || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">当前绑定住户：</div>
                <div class="info-value">{{:owner_str || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">当前绑定车位：</div>
                <div class="info-value">{{:parking_str || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">当前绑定车辆：</div>
                <div class="info-value">
                    {{if vehicle_str}}
                        {{:vehicle_str}}
                    {{else}}
                        -
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="info-row-four">
            <div class="info-item">
                <div class="info-label">预存款总余额：</div>
                <div class="info-value">¥{{:prepaid_balance || '0'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">押金合计金额：</div>
                <div class="info-value">¥{{:deposit_amount || '0'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">使用/建筑面积：</div>
                <div class="info-value">{{:use_area || '0'}}m²/{{:total_area || '0'}}m²</div>
            </div>
            <div class="info-item">
                <div class="info-label">备注：</div>
                <div class="info-value">{{:remark || '-'}}</div>
            </div>
        </div>
        <div class="info-row-four">
            <div class="info-item">
                <div class="info-label">房屋类型：</div>
                <div class="info-value">{{:house_type_str || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">房屋状态</div>
                <div class="info-value">{{:house_status || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label">交房时间：</div>
                <div class="info-value">{{:delivery_time || '-'}}</div>
            </div>
            <div class="info-item">
                <div class="info-label"></div>
                <div class="info-value"></div>
            </div>
        </div>
    </script>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/charge/manage/checkout";
        var houseId = $("#houseId").val();

        $(function() {
            // 初始化layui
            layui.use(['element'], function(){
                var element = layui.element;
            });
            
            // 加载房屋详情
            loadHouseDetail();
            
            // 初始化账单表格
            initBillTable();
        });

        // 加载房屋详情
        function loadHouseDetail() {
            $.ajax({
                url: prefix + "/getHouseDetail",
                type: "POST",
                data: { houseId: houseId },
                success: function(res) {
                    if (res.code == 0) {
                        displayHouseInfo(res.data);
                    } else {
                        $.modal.alertError("加载房屋详情失败：" + res.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("加载房屋详情失败");
                }
            });
        }

        // 显示房屋信息
        function displayHouseInfo(house) {
            // 确保arrear_amount是数字类型
            house.arrear_amount = parseFloat(house.arrear_amount || 0);

            // 使用jsrender模板渲染房屋信息
            var template = $.templates("#houseInfoTemplate");
            var html = template.render(house);
            $("#houseInfoContent").html(html);
        }

        // 初始化账单表格
        function initBillTable() {
            $('#billTable').bootstrapTable({
                url: prefix + "/getHouseBills",
                method: 'post',
                contentType: "application/x-www-form-urlencoded",
                queryParams: function(params) {
                    return {
                        houseId: houseId,
                        pageNum: params.offset / params.limit + 1,
                        pageSize: params.limit
                    };
                },
                sidePagination: "server",
                pagination: true,
                pageSize: 10,
                pageList: [10, 20, 50],
                showToolbar: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                onLoadSuccess: function(data) {
                    // 计算应缴金额统计
                    calculatePaymentSummary();
                    // 默认选中所有复选框
                    $('#billTable').bootstrapTable('checkAll');
                },
                onCheck: function(row) {
                    calculatePaymentSummary();
                },
                onUncheck: function(row) {
                    calculatePaymentSummary();
                },
                onCheckAll: function(rows) {
                    calculatePaymentSummary();
                },
                onUncheckAll: function(rows) {
                    calculatePaymentSummary();
                },
                columns: [{
                    checkbox: true
                }, {
                    field: 'in_month',
                    title: '账单月份'
                }, {
                    field: 'charge_standard_name',
                    title: '收费标准'
                }, {
                    field: 'asset_type_name',
                    title: '资产类型',
                    formatter: function(value, row, index) {
                        return row.asset_type == 1 ? '房屋' : '其他';
                    }
                }, {
                    field: 'asset_name',
                    title: '资产名称',
                    formatter: function(value, row, index) {
                        return row.combina_name || '-';
                    }
                }, {
                    field: 'charge_period',
                    title: '计费开始/结束日期',
                    formatter: function(value, row, index) {
                        var startDate = formatTimestamp(row.start_time);
                        var endDate = formatTimestamp(row.end_time);
                        return startDate + '/' + endDate;
                    }
                }, {
                    field: 'amount',
                    title: '应收金额',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'discount_amount',
                    title: '优惠',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'late_money_amount',
                    title: '违约金',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'bill_amount',
                    title: '应缴费金额',
                    formatter: function(value, row, index) {
                        return '<strong style="color: #f56c6c;">¥' + (parseFloat(value || 0).toFixed(2)) + '</strong>';
                    }
                }, {
                    field: 'pay_type',
                    title: '支付方式',
                    formatter: function(value, row, index) {
                        if (value == 0) return '未支付';
                        if (value == 1) return '线下-现金';
                        if (value == 2) return '线下-微信';
                        if (value == 3) return '线下-支付宝';
                        if (value == 4) return '线下-银行转账';
                        return '未知';
                    }
                }, {
                    field: 'create_time',
                    title: '缴费时间',
                    formatter: function(value, row, index) {
                        return formatTimestamp(value);
                    }
                }, {
                    field: 'order_id',
                    title: '缴费ID',
                    formatter: function(value, row, index) {
                        return value || '-';
                    }
                }, {
                    field: 'remark',
                    title: '备注',
                    formatter: function(value, row, index) {
                        var remarkText = value || '-';
                        return remarkText + ' <a href="javascript:void(0)" onclick="editRemark(\'' + row.id + '\', \'' + (value || '') + '\')" title="修改备注"><i class="fa fa-edit text-primary"></i></a>';
                    }
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="voidBill(\'' + row.id + '\')"><i class="fa fa-ban"></i> 作废</a>');
                        return actions.join('');
                    }
                }]
            });
        }

        // 格式化时间戳为日期字符串
        function formatTimestamp(timestamp) {
            if (!timestamp || timestamp == 0 || timestamp == '') {
                return '';
            }

            var date;
            // 判断是时间戳还是日期字符串
            if (typeof timestamp === 'string' && timestamp.indexOf('-') > -1) {
                // 日期字符串格式 (YYYY-MM-DD HH:mm:ss)
                date = new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                // 数字类型，判断是秒级还是毫秒级时间戳
                if (timestamp > 1000000000000) {
                    // 毫秒级时间戳（13位数字）
                    date = new Date(timestamp);
                } else {
                    // 秒级时间戳（10位数字）
                    date = new Date(timestamp * 1000);
                }
            } else {
                // 其他格式，尝试直接转换
                date = new Date(timestamp);
            }

            // 检查日期是否有效
            if (isNaN(date.getTime())) {
                return '';
            }

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        // 计算应缴金额统计
        function calculatePaymentSummary() {
            var selectedRows = $('#billTable').bootstrapTable('getSelections');
            var totalAmount = 0;
            var totalDiscount = 0;
            var totalLateFee = 0;

            selectedRows.forEach(function(row) {
                totalAmount += parseFloat(row.bill_amount || 0);
                totalDiscount += parseFloat(row.discount_amount || 0);
                totalLateFee += parseFloat(row.late_money_amount || 0);
            });

            var summaryText = '应缴金额：¥' + totalAmount.toFixed(2) +
                             '(违约金合计¥' + totalLateFee.toFixed(2) +
                             '，优惠合计¥' + totalDiscount.toFixed(2) + ')';

            $('#paymentSummary').text(summaryText);
        }

        // 刷新账单列表
        function refreshBills() {
            $("#billTable").bootstrapTable('refresh');
        }

        // 刷新已缴账单列表
        function refreshPaidBills() {
            $("#paidBillTable").bootstrapTable('refresh');
        }

        // 修改备注
        function editRemark(billId, currentRemark) {
            layer.open({
                type: 1,
                title: '修改备注',
                content: '<div style="padding: 20px;"><textarea id="remarkInput" class="form-control" rows="4" placeholder="请输入备注信息">' + currentRemark + '</textarea></div>',
                area: ['500px', '300px'],
                btn: ['保存', '取消'],
                yes: function(index, layero) {
                    var newRemark = $("#remarkInput").val();

                    // 调用修改备注接口
                    $.ajax({
                        url: prefix + "/updateRemark",
                        type: "post",
                        data: {
                            billId: billId,
                            remark: newRemark
                        },
                        success: function(result) {
                            if (result.code == 0) {
                                layer.msg("备注修改成功", {icon: 1});
                                layer.close(index);
                                $("#billTable").bootstrapTable('refresh');
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg("修改备注失败", {icon: 2});
                        }
                    });
                },
                btn2: function(index, layero) {
                    layer.close(index);
                }
            });
        }

        // 作废账单
        function voidBill(billId) {
            layer.confirm('确认作废该账单吗？作废后不可恢复！', {
                btn: ['确认作废', '取消']
            }, function(index) {
                // 调用作废接口
                $.ajax({
                    url: prefix + "/voidBill",
                    type: "post",
                    data: {
                        billId: billId
                    },
                    success: function(result) {
                        if (result.code == 0) {
                            layer.msg("账单作废成功", {icon: 1});
                            layer.close(index);
                            $("#billTable").bootstrapTable('refresh');
                            calculatePaymentSummary();
                        } else {
                            layer.msg(result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg("作废账单失败", {icon: 2});
                    }
                });
            });
        }

        // 查看账单详情
        function viewBillDetail(billId) {
            layer.open({
                type: 1,
                title: '账单详情',
                content: '<div id="billDetailContent" style="padding: 20px;">加载中...</div>',
                area: ['800px', '600px'],
                btn: ['关闭'],
                success: function(layero, index) {
                    // 加载账单详情
                    $.ajax({
                        url: prefix + "/getBillDetail",
                        type: "post",
                        data: {
                            billId: billId
                        },
                        success: function(result) {
                            if (result.code == 0) {
                                var bill = result.data;
                                var html = '<div class="form-horizontal">' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">账单ID：</label><div class="col-sm-9"><p class="form-control-static">' + bill.id + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">账单月份：</label><div class="col-sm-9"><p class="form-control-static">' + bill.in_month + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">收费标准：</label><div class="col-sm-9"><p class="form-control-static">' + bill.charge_standard_name + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">资产名称：</label><div class="col-sm-9"><p class="form-control-static">' + bill.combina_name + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">应收金额：</label><div class="col-sm-9"><p class="form-control-static">¥' + parseFloat(bill.amount || 0).toFixed(2) + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">优惠金额：</label><div class="col-sm-9"><p class="form-control-static">¥' + parseFloat(bill.discount_amount || 0).toFixed(2) + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">违约金：</label><div class="col-sm-9"><p class="form-control-static">¥' + parseFloat(bill.late_money_amount || 0).toFixed(2) + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">应缴费金额：</label><div class="col-sm-9"><p class="form-control-static">¥' + parseFloat(bill.bill_amount || 0).toFixed(2) + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">支付状态：</label><div class="col-sm-9"><p class="form-control-static">' + (bill.pay_status == 1 ? '已缴费' : '未缴费') + '</p></div></div>' +
                                    '<div class="form-group"><label class="col-sm-3 control-label">备注：</label><div class="col-sm-9"><p class="form-control-static">' + (bill.remark || '-') + '</p></div></div>' +
                                    '</div>';
                                $("#billDetailContent").html(html);
                            } else {
                                $("#billDetailContent").html('<p class="text-center text-danger">加载失败：' + result.msg + '</p>');
                            }
                        },
                        error: function() {
                            $("#billDetailContent").html('<p class="text-center text-danger">加载失败</p>');
                        }
                    });
                }
            });
        }

        // 批量收款
        function batchPayment() {
            var rows = $("#billTable").bootstrapTable('getSelections');
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            var billIds = [];
            var totalAmount = 0;
            $.each(rows, function(index, row) {
                billIds.push(row.id);
                totalAmount += parseFloat(row.bill_amount || 0);
            });

            // 显示支付方式选择弹窗
            showPaymentMethodDialog(billIds, totalAmount, rows.length);
        }

        // 显示支付方式选择弹窗
        function showPaymentMethodDialog(billIds, totalAmount, billCount) {
            var html = '<div class="form-group">' +
                '<label class="col-sm-3 control-label">支付方式：</label>' +
                '<div class="col-sm-9">' +
                '<select id="paymentMethod" class="form-control">' +
                '<option value="">请选择支付方式</option>' +
                '<option value="1">线下-现金</option>' +
                '<option value="2">线下-微信</option>' +
                '<option value="3">线下-支付宝</option>' +
                '<option value="4">线下-银行转账</option>' +
                '</select>' +
                '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">收款金额：</label>' +
                '<div class="col-sm-9">' +
                '<input type="text" class="form-control" value="¥' + totalAmount.toFixed(2) + '" readonly>' +
                '</div>' +
                '</div>' +
                '<div class="form-group">' +
                '<label class="col-sm-3 control-label">账单数量：</label>' +
                '<div class="col-sm-9">' +
                '<input type="text" class="form-control" value="' + billCount + ' 条" readonly>' +
                '</div>' +
                '</div>';

            layer.open({
                type: 1,
                title: '批量收款',
                content: '<div class="form-horizontal" style="padding: 20px;">' + html + '</div>',
                area: ['600px', '400px'],
                btn: ['确认收款', '取消'],
                yes: function(index, layero) {
                    var payType = $("#paymentMethod").val();
                    if (!payType) {
                        layer.msg("请选择支付方式", {icon: 2});
                        return false;
                    }

                    // 调用批量收款接口
                    $.ajax({
                        url: prefix + "/batchPayment",
                        type: "post",
                        data: {
                            billIds: billIds.join(","),
                            payType: payType
                        },
                        success: function(result) {
                            if (result.code == 0) {
                                layer.msg(result.msg, {icon: 1});
                                layer.close(index);
                                $("#billTable").bootstrapTable('refresh');
                                calculatePaymentSummary();
                            } else {
                                layer.msg(result.msg, {icon: 2});
                            }
                        },
                        error: function() {
                            layer.msg("批量收款失败", {icon: 2});
                        }
                    });
                },
                btn2: function(index, layero) {
                    layer.close(index);
                }
            });
        }

        // 初始化已缴账单表格
        function initPaidBillTable() {
            $('#paidBillTable').bootstrapTable({
                url: prefix + "/getPaidHouseBills",
                method: 'post',
                contentType: "application/x-www-form-urlencoded",
                queryParams: function(params) {
                    return {
                        houseId: houseId,
                        pageNum: params.offset / params.limit + 1,
                        pageSize: params.limit
                    };
                },
                sidePagination: "server",
                pagination: true,
                pageSize: 10,
                pageList: [10, 20, 50],
                showToolbar: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                columns: [{
                    field: 'in_month',
                    title: '账单月份'
                }, {
                    field: 'charge_standard_name',
                    title: '收费标准'
                }, {
                    field: 'asset_type_name',
                    title: '资产类型',
                    formatter: function(value, row, index) {
                        return row.asset_type == 1 ? '房屋' : '其他';
                    }
                }, {
                    field: 'asset_name',
                    title: '资产名称',
                    formatter: function(value, row, index) {
                        return row.combina_name || '-';
                    }
                }, {
                    field: 'charge_period',
                    title: '计费开始/结束日期',
                    formatter: function(value, row, index) {
                        var startDate = formatTimestamp(row.start_time);
                        var endDate = formatTimestamp(row.end_time);
                        return startDate + '/' + endDate;
                    }
                }, {
                    field: 'amount',
                    title: '应收金额',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'discount_amount',
                    title: '优惠',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'late_money_amount',
                    title: '违约金',
                    formatter: function(value, row, index) {
                        return '¥' + (parseFloat(value || 0).toFixed(2));
                    }
                }, {
                    field: 'bill_amount',
                    title: '应缴费金额',
                    formatter: function(value, row, index) {
                        return '<strong style="color: #67c23a;">¥' + (parseFloat(value || 0).toFixed(2)) + '</strong>';
                    }
                }, {
                    field: 'pay_type',
                    title: '支付方式',
                    formatter: function(value, row, index) {
                        if (value == 0) return '未支付';
                        if (value == 1) return '线下-现金';
                        if (value == 2) return '线下-微信';
                        if (value == 3) return '线下-支付宝';
                        if (value == 4) return '线下-银行转账';
                        return '未知';
                    }
                }, {
                    field: 'pay_time',
                    title: '缴费时间',
                    formatter: function(value, row, index) {
                        return formatTimestamp(value);
                    }
                }, {
                    field: 'id',
                    title: '缴费ID'
                }, {
                    field: 'remark',
                    title: '备注',
                    formatter: function(value, row, index) {
                        var remarkText = value || '-';
                        return remarkText + ' <a href="javascript:void(0)" onclick="editPaidBillRemark(\'' + row.id + '\', \'' + (value || '') + '\')" title="修改备注"><i class="fa fa-edit text-primary"></i></a>';
                    }
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewBillDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i> 查看</a>');
                        return actions.join('');
                    }
                }]
            });
        }

        // 标签页切换事件
        $(document).ready(function() {
            layui.use('element', function(){
                var element = layui.element;

                // 监听标签页切换
                element.on('tab(houseDetailTab)', function(data){
                    if (data.index == 1) { // 已缴账单标签页
                        if (!$('#paidBillTable').hasClass('bootstrap-table')) {
                            initPaidBillTable();
                        }
                    }
                });
            });
        });

        // 修改已缴账单备注
        function editPaidBillRemark(billId, currentRemark) {
            editRemark(billId, currentRemark); // 复用未缴账单的备注修改功能
        }
    </script>



</body>
</html>

-- 收银台系统测试脚本
-- 执行时间：2025-01-27

-- 1. 检查是否已添加arrear_amount字段
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'eh_house_info' AND COLUMN_NAME = 'arrear_amount';

-- 2. 查看房屋表结构
DESCRIBE eh_house_info;

-- 3. 查看现有房屋数据（前10条）
SELECT house_id, combina_name, room, arrear_amount, owner_str 
FROM eh_house_info 
LIMIT 10;

-- 4. 查看现有账单数据（前10条）
SELECT id, asset_type, asset_id, asset_name, charge_item_name, bill_amount, pay_status, is_bad_bill
FROM eh_charge_bill 
WHERE asset_type = 1 
ORDER BY create_time DESC 
LIMIT 10;

-- 5. 统计未缴费账单
SELECT 
    COUNT(*) as unpaid_bill_count,
    SUM(bill_amount) as total_unpaid_amount
FROM eh_charge_bill 
WHERE asset_type = 1 AND pay_status = 0 AND is_bad_bill = 0;

-- 6. 查看有欠费的房屋（如果已初始化数据）
SELECT 
    house_id,
    combina_name,
    room,
    arrear_amount,
    (SELECT COUNT(*) FROM eh_charge_bill cb WHERE cb.asset_type = 1 AND cb.asset_id = h.house_id AND cb.pay_status = 0 AND cb.is_bad_bill = 0) as unpaid_bill_count
FROM eh_house_info h 
WHERE arrear_amount > 0 
ORDER BY arrear_amount DESC 
LIMIT 10;

-- 7. 验证欠费金额计算是否正确（对比房屋表和账单表）
SELECT 
    h.house_id,
    h.combina_name,
    h.room,
    h.arrear_amount as house_arrear_amount,
    COALESCE(SUM(cb.bill_amount), 0.00) as calculated_arrear_amount,
    CASE 
        WHEN h.arrear_amount = COALESCE(SUM(cb.bill_amount), 0.00) THEN '一致' 
        ELSE '不一致' 
    END as status
FROM eh_house_info h 
LEFT JOIN eh_charge_bill cb ON cb.asset_type = 1 AND cb.asset_id = h.house_id AND cb.pay_status = 0 AND cb.is_bad_bill = 0
GROUP BY h.house_id, h.combina_name, h.room, h.arrear_amount
HAVING h.arrear_amount > 0 OR calculated_arrear_amount > 0
ORDER BY h.arrear_amount DESC
LIMIT 10;

-- 8. 查看楼栋单元树数据
SELECT building_id, name as building_name 
FROM eh_building 
ORDER BY name 
LIMIT 5;

SELECT unit_id, building_id, name as unit_name 
FROM eh_unit 
ORDER BY building_id, name 
LIMIT 10;

-- 9. 测试收银台相关查询
-- 模拟获取单元下的房屋缴费情况
SELECT 
    h.house_id, 
    h.room, 
    h.combina_name, 
    h.house_status, 
    h.arrear_amount, 
    h.owner_str, 
    h.owner_count,
    CASE WHEN h.arrear_amount > 0 THEN 1 ELSE 0 END as is_arrearage 
FROM eh_house_info h 
WHERE h.unit_id = (SELECT unit_id FROM eh_unit LIMIT 1)
ORDER BY h.room;

-- 10. 模拟获取房屋的未缴账单列表
SELECT 
    cb.*, 
    cs.name as charge_standard_name 
FROM eh_charge_bill cb 
LEFT JOIN eh_charge_standard cs ON cb.charge_standard_id = cs.id 
WHERE cb.asset_type = 1 
    AND cb.asset_id = (SELECT house_id FROM eh_house_info WHERE arrear_amount > 0 LIMIT 1)
    AND cb.pay_status = 0 
    AND cb.is_bad_bill = 0 
ORDER BY cb.in_month DESC;

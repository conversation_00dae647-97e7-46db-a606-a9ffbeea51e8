# 物业服务二级菜单功能实现

## 需求描述
参考 `getAllMenus` 接口实现 `getPropertyServices` 的二级菜单功能：
- 如果只有一级菜单，保持现在的逻辑不变
- 如果有二级菜单，右侧显示Tab，第一个是"全部"，然后是多个子菜单Tab
- 点击Tab加载对应的内容

## 实现方案

### 1. 后端修改 (WxDataController.java)

#### 修改内容：
- 重构 `getPropertyServices` 方法，参考 `getAllMenus` 的实现
- 增加 `parent_id` 字段查询支持
- 新增辅助方法：
  - `getPropertyServiceMenuData()` - 获取菜单数据
  - `buildPropertyServiceMenuTree()` - 构建菜单树
  - `processPropertyServiceMenu()` - 处理菜单内容

#### 数据结构变化：
```json
{
  "categories": [
    {
      "id": 1,
      "text": "菜单名称",
      "nav_type": "text|pdf",
      "icon_name": "图标名称",
      "hasChildren": true|false,
      "children": [...],  // 子菜单数组
      "content": [...]    // 内容数组
    }
  ]
}
```

### 2. 小程序端修改

#### 页面数据 (index.js)
新增字段：
- `hasSecondLevel`: 是否有二级菜单
- `activeTabIndex`: 当前激活的Tab索引
- `tabList`: Tab列表数据
- `currentTabContent`: 当前Tab的内容

#### 新增方法：
- `initTabData(category)` - 初始化Tab数据
- `handleTabChange(e)` - Tab切换处理

#### 页面模板 (index.wxml)
- 增加Tab组件的条件渲染
- 保持一级菜单的原有逻辑
- 二级菜单使用Tab展示

#### 页面配置 (index.json)
新增组件：
- `van-tabs`
- `van-tab`

#### 样式 (index.wxss)
新增样式：
- `.tab-container` - Tab容器
- `.tab-content` - Tab内容区域

## 兼容性说明
- 完全兼容现有的一级菜单逻辑
- 二级菜单自动检测并启用Tab模式
- 数据结构向后兼容

## 测试要点
1. 一级菜单显示正常（sidebar模式）
2. 二级菜单显示Tab（不包含"全部"Tab，默认选中第一个子菜单）
3. Tab切换功能正常
4. PDF和文本内容显示正常
5. 空状态处理正确

## 实现细节

### 数据库结构
- 使用 `eh_wx_nav` 表的 `parent_id` 字段实现菜单层级
- `parent_id = 0` 表示一级菜单
- `parent_id > 0` 表示二级菜单，值为父菜单的 `nav_id`

### 关键修复
1. **Sidebar组件activeKey问题**：Vant Weapp的sidebar组件使用索引而不是ID作为activeKey
2. **Tab组件配置**：去掉"全部"Tab，默认选中第一个子菜单Tab
3. **数据结构兼容**：保持对一级菜单的完全兼容

### 当前测试数据
- "议事规则" (nav_id: 13) - 父菜单，包含5个子菜单
- "重大事项" (nav_id: 23) - 一级菜单
- "合同类" (nav_id: 22) - 一级菜单

## 相关文件
- `ehome-oc\src\main\java\com\ehome\oc\controller\wx\WxDataController.java`
- `miniprogram\pages\service\index.js`
- `miniprogram\pages\service\index.wxml`
- `miniprogram\pages\service\index.json`
- `miniprogram\pages\service\index.wxss`

## 功能状态
✅ 后端接口支持二级菜单数据结构
✅ 小程序端支持Tab组件显示
✅ 一级菜单兼容性保持
✅ 二级菜单Tab切换功能
✅ 去掉"全部"Tab，默认选中第一个子菜单

# PDF预览跨源URL处理

## 问题描述

在nav/view.html中，PDF预览功能存在跨源访问问题：

1. **直接跨源URL**：如果pdfUrl是跨源的，layer.open会因为跨源限制无法正常显示
2. **后台重定向问题**：即使初始URL看起来是同源的（如`/api/wx/file/download/123`），但后台可能会重定向到OSS等外部存储的跨源URL，导致iframe加载失败

需要智能判断URL类型，选择合适的打开方式。

## 解决方案

### 1. 修改nav/view.html中的PDF预览功能

**文件**: `ehome-page/src/main/resources/templates/oc/wx/config/nav/view.html`

**修改内容**:
- 添加了`isCrossOriginURL`函数来检测URL是否为跨源
- 修改`previewPdf`函数，根据URL是否同源选择不同的打开方式：
  - 同源URL：使用`layer.open`在弹窗中预览
  - 跨源URL：使用`window.open`在新窗口中打开

**实现逻辑**:
```javascript
// 检查URL是否为跨源URL
function isCrossOriginURL(url) {
    var location = window.location;
    var REGEXP_ORIGINS = /^(\w+:)\/\/([^:/?#]*):?(\d*)/i;
    var parts = url.match(REGEXP_ORIGINS);
    return parts !== null && (parts[1] !== location.protocol || parts[2] !== location.hostname || parts[3] !== location.port);
}

// PDF预览功能
function previewPdf(pdfUrl, pdfName) {
    if (isCrossOriginURL(pdfUrl)) {
        // 跨源URL使用window.open在新窗口打开
        window.open(pdfUrl, '_blank');
    } else {
        // 同源URL使用layer弹窗显示PDF预览
        top.layer.open({
            type: 2,
            title: '📄 ' + pdfName,
            content: pdfUrl,
            // ... 其他配置
        });
    }
}
```

### 2. 创建通用的跨源URL处理工具

**文件**: `ehome-page/src/main/resources/static/ruoyi/js/ry-ui.js`

**新增功能**:
- 在`$.modal`对象中添加了`isCrossOriginURL`函数用于检测跨源URL
- 添加了`isFileDownloadUrl`函数用于检测可能重定向的文件下载URL
- 添加了`openUrl`函数作为智能URL打开工具：
  - 自动检测URL是否跨源或为文件下载URL
  - 同源内容使用layer弹窗，跨源或文件下载使用新窗口
  - 提供统一的配置选项和错误处理

**使用方式**:
```javascript
// 智能打开URL
$.modal.openUrl(url, title, options);

// 检测是否跨源
if ($.modal.isCrossOriginURL(url)) {
    // 处理跨源情况
}
```

### 3. 优化现有文件预览功能

**文件**: `ehome-page/src/main/resources/static/js/fileAccessUtils.js`

**修改内容**:
- 更新`previewFile`函数使用新的`$.modal.openUrl`工具
- 添加了fileName参数用于显示更友好的标题
- 自动处理同源和跨源URL的不同预览方式

**优化后的使用**:
```javascript
// 预览文件，自动处理跨源问题
FileAccessUtils.previewFile(fileIdOrUrl, expiration, fileName);
```

### 4. 简化view.html中的实现

**最终实现**:
```javascript
// PDF预览功能 - 使用通用工具
function previewPdf(pdfUrl, pdfName) {
    if (!pdfUrl) {
        $.modal.alertWarning("PDF文件路径不存在");
        return;
    }

    // 使用通用的智能URL打开功能
    $.modal.openUrl(pdfUrl, '📄 ' + pdfName, {
        success: function(layero, index) {
            console.log('PDF预览打开成功:', pdfName);
        },
        error: function() {
            layer.close(index);
            $.modal.alertError("PDF文件加载失败，请检查文件是否存在");
        }
    });
}
```

## 技术要点

### 跨源检测原理

使用正则表达式`/^(\w+:)\/\/([^:/?#]*):?(\d*)/i`解析URL的协议、主机名和端口，与当前页面的location进行比较：
- 协议不同（http vs https）
- 主机名不同（不同域名）
- 端口不同（不同端口号）

任何一项不同都被认为是跨源URL。

### 处理策略

1. **同源内容URL**: 使用`layer.open` type: 2（iframe层）在弹窗中预览
   - 优点：用户体验好，不离开当前页面
   - 适用：确定的内部页面、同域名静态内容

2. **跨源URL**: 使用`window.open`在新窗口打开
   - 优点：避免跨源访问限制
   - 适用：外部链接、第三方文件服务

3. **文件下载URL**: 使用`window.open`在新窗口打开
   - 原因：这类URL通常会重定向到OSS或其他外部存储
   - 适用：`/download/`、`/api/wx/file/download/`等模式的URL

### 向后兼容

- 保留了原有的函数接口
- 现有调用代码无需修改
- 自动处理跨源问题，对调用者透明

## 测试要点

1. **同源PDF文件**：验证是否在弹窗中正常预览
2. **跨源PDF文件**：验证是否在新窗口中正常打开
3. **无效URL**：验证错误提示是否正常显示
4. **弹窗被阻止**：验证是否有相应的错误提示

## 影响范围

- ✅ nav/view.html - PDF预览功能优化
- ✅ ry-ui.js - 添加通用跨源处理工具
- ✅ fileAccessUtils.js - 文件预览功能优化
- ✅ FileController.java - 修复重定向URL中文字符编码问题
- ✅ WxFileController.java - 修复重定向URL中文字符编码问题
- 📝 其他可能使用layer.open预览外部文件的地方需要类似处理

## 关键改进：文件下载URL检测

### 问题背景
用户反馈指出了一个重要问题：后台重定向导致的跨源问题。即使前端访问的URL看起来是同源的（如`/api/wx/file/download/123`），但后台可能会重定向到OSS预签名URL等跨源地址。

### 解决方案
添加了`isFileDownloadUrl`函数，检测可能重定向的文件下载URL模式：
```javascript
isFileDownloadUrl: function(url) {
    var downloadPatterns = [
        '/download/',
        '/api/wx/file/download/',
        '/common/download/',
        '/file/download',
        '/download?',
        '/resource'
    ];
    return downloadPatterns.some(function(pattern) {
        return url.indexOf(pattern) !== -1;
    });
}
```

### 智能判断逻辑
```javascript
if ($.modal.isCrossOriginURL(url) || $.modal.isFileDownloadUrl(url)) {
    // 跨源URL或文件下载URL使用window.open
    window.open(url, '_blank');
} else {
    // 确定的同源内容URL使用layer弹窗
    top.layer.open({...});
}
```

## 重要修复：HTTP重定向中文字符编码

### 问题发现
在测试过程中发现了一个严重问题：
```
The HTTP response header [Location] with value [https://ehome.getxf.cn/profile/upload/2025/07/03/4、管理规约_20250703162844A004.pdf] has been removed from the response because it is invalid
java.lang.IllegalArgumentException: The Unicode character [、] at code point [12,289] cannot be encoded as it is outside the permitted range of 0 to 255
```

### 问题原因
HTTP响应头中的Location值包含了中文字符（、），这在HTTP头中是不被允许的，导致重定向失败。

### 解决方案
在FileController和WxFileController中添加了`encodeRedirectUrl`方法：

```java
private String encodeRedirectUrl(String url) {
    if (StringUtils.isEmpty(url)) {
        return url;
    }

    try {
        // 检查URL是否包含非ASCII字符
        if (!url.matches("^[\\x00-\\x7F]*$")) {
            // 对URL进行编码，但保留协议和域名部分
            if (url.startsWith("http://") || url.startsWith("https://")) {
                int pathStart = url.indexOf('/', url.indexOf("://") + 3);
                if (pathStart > 0) {
                    String baseUrl = url.substring(0, pathStart);
                    String path = url.substring(pathStart);
                    // 只对路径部分进行编码，保持URL结构
                    String[] pathParts = path.split("/");
                    StringBuilder encodedPath = new StringBuilder();
                    for (String part : pathParts) {
                        if (!part.isEmpty()) {
                            encodedPath.append("/").append(ServletUtils.urlEncode(part));
                        }
                    }
                    return baseUrl + encodedPath.toString();
                }
            }
            // 如果不是完整URL，直接编码
            return ServletUtils.urlEncode(url);
        }
        return url;
    } catch (Exception e) {
        log.warn("URL编码失败，使用原始URL: {} - {}", url, e.getMessage());
        return url;
    }
}
```

**编码逻辑说明**：
- 保留协议和域名部分不变（如`https://ehome.getxf.cn`）
- 只对路径部分按段进行编码，保持URL结构
- 例如：`/profile/upload/2025/07/03/4、管理规约.pdf` → `/profile/upload/2025/07/03/4%E3%80%81%E7%AE%A1%E7%90%86%E8%A7%84%E7%BA%A6.pdf`

### 修改范围
- 所有`response.sendRedirect(fileUrl)`调用都改为`response.sendRedirect(encodeRedirectUrl(fileUrl))`
- 确保包含中文字符的文件URL能够正确重定向

## 后续优化建议

1. 考虑为其他文件类型（如图片、文档）也添加类似的跨源处理
2. 可以考虑添加配置选项，让用户选择默认的打开方式
3. 对于某些已知的可信域名，可以考虑加入白名单仍使用弹窗预览
4. 可以考虑通过AJAX预检查URL是否会重定向，但这会增加复杂性
5. 建议在文件上传时就对文件名进行规范化处理，避免特殊字符

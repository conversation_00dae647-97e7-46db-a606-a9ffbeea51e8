<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小区公告阅读记录')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="readLog-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                住户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                公告标题：<input type="text" name="noticeTitle"/>
                            </li>
                            <li>
                                阅读时间：<input type="text" class="time-input" name="readTime"/>
                            </li>
                            <li>
                                房屋名称：<input type="text" name="houseName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-info" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice/allReadLogs";

        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "阅读记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'logId',
                    title: '序号'
                },
                {
                    field: 'noticeTitle',
                    title: '公告标题',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '未知公告';
                    }
                },
                {
                    field: 'userName',
                    title: '住户名称'
                },
                {
                    field: 'houseName',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="badge badge-info">' + value + '</span>';
                        }
                        return '<span class="text-muted">未绑定</span>';
                    }
                },
                {
                    field: 'readTime',
                    title: '阅读时间',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    field: 'ipAddress',
                    title: 'IP地址'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewNotice(\'' + row.noticeId + '\')"><i class="fa fa-eye"></i>查看公告</a>');
                        return actions.join(' ');
                    }
                }]
            };
            $.table.init(options);
        });

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 查看公告详情
        function viewNotice(noticeId) {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/view/" + noticeId;
            $.modal.openTab("公告详情", url);
        }

        // 导出数据
        function exportData() {
            $.modal.confirm("确定要导出阅读记录数据吗？", function() {
                $.post(prefix + "/export", $("#readLog-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

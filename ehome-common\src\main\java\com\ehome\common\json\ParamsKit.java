package com.ehome.common.json;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;

public class ParamsKit {
	
	public static JSONObject getJSONObject(JSONObject jsonObject,String modelName){
		if(StringUtils.isBlank(modelName)){
			return jsonObject;
		}else{
			JSONObject newJsonObject=new JSONObject();
			for(String key :jsonObject.keySet()){
				if(key.startsWith(modelName+".")){
					newJsonObject.put(key.replace(modelName+".", "").trim(), jsonObject.get(key));
				}
			}
			return newJsonObject;
		}
	}

	public static JSONObject getJSONObject(JSONObject params,String modelName,String split){
		if(params!=null){
			return getJSONObject(params.toJSONString(),modelName,split);
		}
		return new JSONObject();
	}

	public static JSONObject getJSONObject(String data,String modelName,String split){
		if(StringUtils.isBlank(data)){
			return  new JSONObject();
		}else{
			if(data.startsWith("[")){
				return new JSONObject();
			}
			try {
				JSONObject jsonObject =  JSONObject.parseObject(data);
				if(StringUtils.isBlank(modelName)){
					return jsonObject;
				}else{
					if(StringUtils.isBlank(split)){
						split=".";
					}
					JSONObject newJsonObject=new JSONObject();
					for(String key :jsonObject.keySet()){
						if(key.startsWith(modelName+split)){
							newJsonObject.put(key.replace(modelName+split, "").trim(), jsonObject.get(key));
						}	
					}
					return newJsonObject;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return new JSONObject();
	}
	
	/**
	 * 默认.分割  > modelName.colName
	 * @param data
	 * @param modelName
	 * @return
	 */
	public static JSONObject getJSONObject(String data,String modelName){
		return getJSONObject(data,modelName,null);
	}

	public static JSONObject getJSONObject(HttpServletRequest request){
		return getJSONObject(request,null);
	}
	
	public static JSONObject getJSONObject(HttpServletRequest request,String modelName){
		String contentType = request.getContentType();
		if(StringUtils.isNotBlank(contentType)&&contentType.indexOf("json")>-1) {
			 StringBuilder params = new StringBuilder();
             try {
                 String str;
                 BufferedReader br = request.getReader();
                 while ((str = br.readLine()) != null) {
                     params.append(str);
                 }
             } catch (IOException e) {
            	 e.printStackTrace();
             }
             if (params.length() > 0) {
                 JSONObject jsonObject = JSONObject.parseObject(params.toString());
				 return getJSONObject(jsonObject,modelName);
             }
		}else {
			JSONObject jsonObject = new JSONObject();
			Enumeration<String> parameterNames = request.getParameterNames();
			while (parameterNames.hasMoreElements()) {
				String paramName = parameterNames.nextElement();
				String[] paramValues = request.getParameterValues(paramName);

				if (paramValues != null) {
					if (paramValues.length == 1) {
						// 单值参数
						jsonObject.put(paramName, paramValues[0]);
					} else {
						// 多值参数
						jsonObject.put(paramName, paramValues);
					}
				}
			}
			return getJSONObject(jsonObject,modelName);
		}
		return new JSONObject();
	}
	
	
	public static JSONArray getJSONArray(HttpServletRequest request){
		String contentType = request.getContentType();
		if(StringUtils.isNotBlank(contentType)&&contentType.indexOf("json")>-1) {
			StringBuilder params = new StringBuilder();
			try {
				String str;
				BufferedReader br = request.getReader();
				while ((str = br.readLine()) != null) {
					params.append(str);
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
			if (params.length()>0) {
				JSONObject paramJsonObject = JSONObject.parseObject(params.toString());
				return paramJsonObject.getJSONArray("data");
			}
		}else {
			String data = request.getParameter("data");
			return JSONArray.parseArray(data);
		}
		return new JSONArray();
	}
	
}

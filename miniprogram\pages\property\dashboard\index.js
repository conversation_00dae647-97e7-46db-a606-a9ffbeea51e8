// 物业工作台页面
import { handlePropertyPageShow } from '../../../utils/pageUtils.js'

Page({
  data: {
    title: '工作台',
    stats: {
      pendingOrders: 0,
      todayPatrol: 0,
      monthlyRevenue: 0
    }
  },

  onLoad() {
    console.log('物业工作台页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, 0, this.loadDashboardData)
  },

  // 加载工作台数据
  loadDashboardData() {
    // TODO: 实现工作台数据加载
    console.log('加载工作台数据')
  },

  // 跳转到工单管理
  goToOrders() {
    wx.navigateTo({
      url: '/pages/property/orders/index'
    })
  },

  // 跳转到巡检记录
  goToPatrol() {
    wx.navigateTo({
      url: '/pages/property/patrol/index'
    })
  }
})

// 工单管理页面
import { handlePropertyPageShow } from '../../../utils/pageUtils.js'

Page({
  data: {
    title: '工单管理',
    orderList: [],
    loading: false
  },

  onLoad() {
    console.log('工单管理页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, 1, this.loadOrderList)
  },

  // 加载工单列表
  loadOrderList() {
    this.setData({ loading: true })
    
    // TODO: 实现工单列表加载
    setTimeout(() => {
      this.setData({ 
        loading: false,
        orderList: [
          { id: 1, title: '示例工单1', status: '待处理' },
          { id: 2, title: '示例工单2', status: '处理中' }
        ]
      })
    }, 1000)
  },

  // 查看工单详情
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id
    console.log('查看工单详情:', orderId)
    // TODO: 跳转到工单详情页面
  }
})

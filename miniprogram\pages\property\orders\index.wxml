<view class="orders-container">
  <view class="header">
    <text class="title">{{title}}</text>
  </view>
  
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <view wx:else class="order-list">
    <view wx:for="{{orderList}}" wx:key="id" class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
      <view class="order-title">{{item.title}}</view>
      <view class="order-status">{{item.status}}</view>
    </view>
    
    <view wx:if="{{orderList.length === 0}}" class="empty">
      <text>暂无工单</text>
    </view>
  </view>
</view>

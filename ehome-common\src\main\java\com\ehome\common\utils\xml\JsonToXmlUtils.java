package com.ehome.common.utils.xml;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.Map.Entry;
import java.util.Set;
/**
 * json转xml
 */
public class JsonToXmlUtils {
		public static String jsonToXmlStr(JSONObject jsonObject) throws Exception {
			Element ee = jsonToXmlObj(jsonObject);
			return ee.asXML();
		}
		
		public static Element jsonToXmlObj(JSONObject jsonObject) throws Exception {
			Document document = DocumentHelper.createDocument();
			Element root =  document.addElement("TX");
			Element ee = toXml(jsonObject, root, null);
			return ee;
		}
		
		@SuppressWarnings("unused")
		private static Element jsonToXml(JSONObject jsonObject, Element parentElement) throws Exception {
			Element ee = toXml(jsonObject, parentElement, null);
			return ee;
		}
		
		private static Element toXml(Object jsonElement, Element parentElement, String name) {
			if (jsonElement instanceof JSONArray) {
				JSONArray sonJsonArray = (JSONArray)jsonElement;
				for (int i = 0; i < sonJsonArray.size(); i++) {
					Object object=sonJsonArray.get(i);
					if(object instanceof JSONObject){
						JSONObject arrayElement = sonJsonArray.getJSONObject(i);
						toXml(arrayElement, parentElement, name);
					}
				}
			}else if (jsonElement instanceof JSONObject) {
				JSONObject sonJsonObject = (JSONObject) jsonElement;
				Element currentElement = null;
				if (name != null) {
					currentElement = parentElement.addElement(name);
				}
				Set<Entry<String, Object>> set = sonJsonObject.entrySet();
				for (Entry<String,  Object> s : set) {
					toXml(s.getValue(), currentElement != null ? currentElement : parentElement, s.getKey());
				}
			} else {
				//说明是一个键值对的key,可以作为节点插入了
				if(jsonElement!=null&& StringUtils.isNotBlank(jsonElement.toString())){
					addAttribute(parentElement, name, jsonElement.toString());
				}
			}
			return parentElement;
		}

		private static void addAttribute(Element element, String name, String value) {
			//增加子节点，并为子节点赋值
			Element el = element.addElement(name);
			el.addText(value);
		}

	}
	


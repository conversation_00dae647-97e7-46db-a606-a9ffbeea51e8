// 配置访问工具函数 - 提供便捷的配置访问接口
import { getConfigManager } from './configManager.js'

/**
 * 获取配置管理器实例
 * @returns {ConfigManager} 配置管理器实例
 */
export function getConfig() {
  return getConfigManager()
}

/**
 * 获取配置值
 * @param {string} key 配置键
 * @param {*} defaultValue 默认值
 * @returns {*} 配置值
 */
export function getConfigValue(key, defaultValue = null) {
  return getConfigManager().get(key, defaultValue)
}

/**
 * 获取所有配置
 * @returns {Object} 配置对象
 */
export function getAllConfig() {
  return getConfigManager().getAll()
}

/**
 * 获取分类配置
 * @param {string} category 配置分类 (ui/feature/share)
 * @returns {Object} 分类配置对象
 */
export function getCategoryConfig(category) {
  return getConfigManager().getCategory(category)
}

/**
 * 检查配置是否启用
 * @param {string} key 配置键
 * @returns {boolean} 是否启用
 */
export function isConfigEnabled(key) {
  return getConfigManager().isEnabled(key)
}

/**
 * 获取数值配置
 * @param {string} key 配置键
 * @param {number} defaultValue 默认值
 * @returns {number} 数值
 */
export function getConfigNumber(key, defaultValue = 0) {
  return getConfigManager().getNumber(key, defaultValue)
}

// UI配置相关的便捷函数
export const UIConfig = {
  /**
   * 获取小程序标题
   * @returns {string} 标题
   */
  getTitle() {
    return getConfigValue('miniprogram_title', '')
  },

  /**
   * 获取小程序副标题
   * @returns {string} 副标题
   */
  getSubtitle() {
    return getConfigValue('miniprogram_subtitle', '')
  },

  /**
   * 是否显示标题
   * @returns {boolean} 是否显示
   */
  isTitleEnabled() {
    return isConfigEnabled('enable_miniprogram_title')
  },

  /**
   * 获取菜单行数
   * @returns {number} 行数
   */
  getMenuRows() {
    return getConfigNumber('menu_rows', 4)
  },

  /**
   * 是否显示大卡片
   * @returns {boolean} 是否显示
   */
  isBigCardEnabled() {
    return isConfigEnabled('show_big_card')
  }
}

// 功能配置相关的便捷函数
export const FeatureConfig = {
  /**
   * 是否启用小程序标记
   * @returns {boolean} 是否启用
   */
  isMarkEnabled() {
    return isConfigEnabled('enable_miniprogram_mark')
  },

  /**
   * 是否允许截屏
   * @returns {boolean} 是否允许
   */
  isScreenshotEnabled() {
    return isConfigEnabled('enable_screenshot')
  },

  /**
   * 是否启用评论功能（全局开关）
   * @returns {boolean} 是否启用
   */
  isCommentEnabled() {
    return isConfigEnabled('enable_comment')
  }
}

// 分享配置相关的便捷函数
export const ShareConfig = {
  /**
   * 是否启用分享
   * @returns {boolean} 是否启用
   */
  isEnabled() {
    return isConfigEnabled('enable_share')
  },

  /**
   * 获取分享标题
   * @returns {string} 分享标题
   */
  getTitle() {
    return getConfigValue('share_title', '智慧社区服务')
  },

  /**
   * 获取分享描述
   * @returns {string} 分享描述
   */
  getDesc() {
    return getConfigValue('share_desc', '一键触达生活所需，物业服务就在掌心')
  },

  /**
   * 获取分享图片
   * @returns {string} 分享图片路径
   */
  getImage() {
    return getConfigValue('share_image', '/static/images/share-cover.png')
  },

  /**
   * 是否启用分享统计
   * @returns {boolean} 是否启用
   */
  isStatisticsEnabled() {
    return isConfigEnabled('enable_share_statistics')
  },

  /**
   * 是否显示分享者信息
   * @returns {boolean} 是否显示
   */
  isSharerInfoEnabled() {
    return isConfigEnabled('show_sharer_info')
  },

  /**
   * 获取完整的分享配置对象
   * @returns {Object} 分享配置
   */
  getShareConfig() {
    return {
      title: this.getTitle(),
      desc: this.getDesc(),
      imageUrl: this.getImage(),
      enabled: this.isEnabled(),
      statistics: this.isStatisticsEnabled(),
      sharerInfo: this.isSharerInfoEnabled()
    }
  }
}

/**
 * 页面配置监听器混入
 * 为页面提供配置变更监听能力
 */
export const ConfigListenerMixin = {
  /**
   * 页面加载时添加配置监听器
   */
  onLoad() {
    const configManager = getConfigManager()
    const pageRoute = this.route || 'unknown'
    
    // 添加配置监听器
    configManager.addListener(`page_${pageRoute}`, (event) => {
      if (typeof this.onConfigChange === 'function') {
        this.onConfigChange(event)
      }
    })
  },

  /**
   * 页面卸载时移除配置监听器
   */
  onUnload() {
    const configManager = getConfigManager()
    const pageRoute = this.route || 'unknown'
    
    // 移除配置监听器
    configManager.removeListener(`page_${pageRoute}`)
  }
}

/**
 * 响应式配置获取
 * 当配置变更时自动更新页面数据
 * @param {Object} page 页面实例
 * @param {string} key 配置键
 * @param {string} dataKey 页面数据键
 * @param {*} defaultValue 默认值
 */
export function bindConfigToData(page, key, dataKey, defaultValue = null) {
  const configManager = getConfigManager()
  
  // 初始设置
  const initialValue = configManager.get(key, defaultValue)
  page.setData({
    [dataKey]: initialValue
  })

  // 添加监听器
  const listenerId = `bind_${page.route || 'unknown'}_${key}`
  configManager.addListener(listenerId, (event) => {
    const newValue = event.newConfig[key]
    if (newValue !== undefined) {
      page.setData({
        [dataKey]: newValue
      })
    }
  })

  // 页面卸载时清理监听器
  const originalOnUnload = page.onUnload
  page.onUnload = function() {
    configManager.removeListener(listenerId)
    if (originalOnUnload) {
      originalOnUnload.call(this)
    }
  }
}

/**
 * 批量绑定配置到页面数据
 * @param {Object} page 页面实例
 * @param {Object} bindings 绑定配置 {configKey: dataKey}
 */
export function bindMultipleConfigToData(page, bindings) {
  Object.entries(bindings).forEach(([configKey, dataKey]) => {
    bindConfigToData(page, configKey, dataKey)
  })
}

export default {
  getConfig,
  getConfigValue,
  getAllConfig,
  getCategoryConfig,
  isConfigEnabled,
  getConfigNumber,
  UIConfig,
  FeatureConfig,
  ShareConfig,
  ConfigListenerMixin,
  bindConfigToData,
  bindMultipleConfigToData
}

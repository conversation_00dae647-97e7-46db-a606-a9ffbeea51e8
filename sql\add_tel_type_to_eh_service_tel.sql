-- 为eh_service_tel表添加tel_type字段，支持内部/外部号码分类功能
-- 用于区分内部号码（internal）和外部号码（external）

-- 添加tel_type字段
ALTER TABLE `eh_service_tel` 
ADD COLUMN `tel_type` varchar(10) DEFAULT 'external' COMMENT '号码类型(internal=内部号码,external=外部号码)' 
AFTER `tel_number`;

-- 为tel_type字段添加索引，提高查询性能
ALTER TABLE `eh_service_tel` 
ADD INDEX `idx_tel_type` (`tel_type`);

-- 为组合查询添加复合索引
ALTER TABLE `eh_service_tel` 
ADD INDEX `idx_community_tel_type` (`community_id`, `tel_type`);

-- 更新现有数据，默认设置为外部号码
UPDATE `eh_service_tel` SET `tel_type` = 'external' WHERE `tel_type` IS NULL;

-- 验证字段添加结果的查询语句
-- SELECT service_tel_id, service_name, tel_number, tel_type, parent_id, sort_order 
-- FROM eh_service_tel 
-- WHERE community_id = 'your_community_id' 
-- ORDER BY tel_type ASC, parent_id ASC, sort_order ASC;

-- 测试数据：添加一些内部号码示例
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `tel_type`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('INT001', '0', 'default', '8001', 'internal', '物业前台', '小区物业管理处', 'manager-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '内部号码'),
('INT002', '0', 'default', '8002', 'internal', '保安室', '小区保安部', 'shield-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '内部号码'),
('INT003', '0', 'default', '8003', 'internal', '维修部', '小区维修中心', 'setting-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '内部号码');

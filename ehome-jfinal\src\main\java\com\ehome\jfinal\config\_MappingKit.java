package com.ehome.jfinal.config;

import com.ehome.jfinal.annotation.TableScanner;
import com.ehome.jfinal.model.OcInfoModel;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class _MappingKit {

	private static volatile String scanPackageName = "com.ehome";

	@Value("${ruoyi.packageName}")
	private String packageName;

	@PostConstruct
	public void setScanPackageName() {
		_MappingKit.scanPackageName = packageName;
	}


	public static void mapping(ActiveRecordPlugin arp) {
		arp.addMapping("eh_community", "oc_id", OcInfoModel.class);
		annotationTable(arp);
	}


	public static void annotationTable(ActiveRecordPlugin arp) {
		TableScanner tableScanner = new TableScanner(scanPackageName, arp);
		tableScanner.scan();
	}


	public static void annotationTable(ActiveRecordPlugin arp, String configName) {
		TableScanner tableScanner = new TableScanner(scanPackageName, arp, configName);
		tableScanner.scan();
	}
}
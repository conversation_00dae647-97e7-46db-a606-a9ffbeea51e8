package com.ehome.system.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 菜单点击日志导出对象
 * 
 * <AUTHOR>
 */
public class SysMenuClickLogExport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String menuName;

    /** 菜单类型 */
    @Excel(name = "菜单类型", readConverterExp = "text=文本,pdf=PDF,url=链接,page=页面,miniprogram=小程序")
    private String menuType;

    /** 用户名称 */
    @Excel(name = "用户名称")
    private String userName;

    /** 首次点击时间 */
    @Excel(name = "首次点击时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date firstClickTime;

    /** 最后点击时间 */
    @Excel(name = "最后点击时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastClickTime;

    /** 点击次数 */
    @Excel(name = "点击次数")
    private Integer clickCount;

    /** 来源页面 */
    @Excel(name = "来源页面", readConverterExp = "index=首页,nav=导航页,mine=我的页面")
    private String source;

    /** 房屋名称 */
    @Excel(name = "房屋名称")
    private String houseName;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getFirstClickTime() {
        return firstClickTime;
    }

    public void setFirstClickTime(Date firstClickTime) {
        this.firstClickTime = firstClickTime;
    }

    public Date getLastClickTime() {
        return lastClickTime;
    }

    public void setLastClickTime(Date lastClickTime) {
        this.lastClickTime = lastClickTime;
    }

    public Integer getClickCount() {
        return clickCount;
    }

    public void setClickCount(Integer clickCount) {
        this.clickCount = clickCount;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}

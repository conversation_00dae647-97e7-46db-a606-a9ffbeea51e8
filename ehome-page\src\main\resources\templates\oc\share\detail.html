<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('分享详细记录')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style>
        .share-record-card {
            border-left: 4px solid #1ab394;
            margin-bottom: 15px;
        }
        .visitor-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .no-visitors {
            color: #999;
            font-style: italic;
        }
        .share-content {
            background-color: #fff;
            border: 1px solid #e7eaec;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .visitor-badge {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #1ab394;
            color: white;
            border-radius: 12px;
            font-size: 12px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>分享者：</label>
                            <input type="text" name="sharerName" placeholder="请输入分享者昵称" class="form-control" />
                        </li>
                        <li>
                            <label>分享时间：</label>
                            <input type="text" name="startTime" class="form-control" placeholder="开始时间" onclick="$.table.selectDictLabel()" />
                        </li>
                        <li>
                            <label>至：</label>
                            <input type="text" name="endTime" class="form-control" placeholder="结束时间" onclick="$.table.selectDictLabel()" />
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="loadDetailRecords()">
                                <i class="fa fa-search"></i>&nbsp;查询
                            </a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置
                            </a>
                            <a class="btn btn-secondary btn-rounded btn-sm" onclick="window.close()">
                                <i class="fa fa-times"></i>&nbsp;关闭
                            </a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- 详细记录列表 -->
        <div class="col-sm-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5><i class="fa fa-share-alt text-primary"></i> 分享详细记录</h5>
                    <div class="ibox-tools">
                        <span class="label label-info" id="recordCount">加载中...</span>
                    </div>
                </div>
                <div class="ibox-content">
                    <div id="shareDetailContainer">
                        <!-- 动态加载的分享记录 -->
                    </div>
                    
                    <!-- 分页 -->
                    <div class="text-center" id="paginationContainer" style="margin-top: 20px;">
                        <button class="btn btn-primary" id="loadMoreBtn" onclick="loadMoreRecords()" style="display: none;">
                            <i class="fa fa-plus"></i> 加载更多
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "oc/share";
    var currentPage = 1;
    var pageSize = 10;
    var hasMore = true;

    $(function() {
        loadDetailRecords();
    });

    function loadDetailRecords(reset = true) {
        if (reset) {
            currentPage = 1;
            hasMore = true;
            $("#shareDetailContainer").empty();
        }
        
        if (!hasMore) {
            return;
        }

        var formData = $("#formId").serializeObject();
        formData.page = currentPage;
        formData.pageSize = pageSize;
        
        $.ajax({
            url: ctx + "oc/share/detailList",
            type: "GET",
            data: formData,
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var records = data.records || [];
                    
                    if (reset) {
                        $("#recordCount").text("共 " + (data.total || 0) + " 条记录");
                    }
                    
                    if (records.length > 0) {
                        var html = "";
                        $.each(records, function(index, record) {
                            html += buildShareRecordHtml(record);
                        });
                        
                        if (reset) {
                            $("#shareDetailContainer").html(html);
                        } else {
                            $("#shareDetailContainer").append(html);
                        }
                        
                        currentPage++;
                        hasMore = records.length >= pageSize;
                        
                        if (hasMore) {
                            $("#loadMoreBtn").show();
                        } else {
                            $("#loadMoreBtn").hide();
                        }
                    } else {
                        if (reset) {
                            $("#shareDetailContainer").html('<div class="text-center text-muted" style="padding: 50px;"><i class="fa fa-info-circle fa-3x"></i><br><br>暂无分享记录</div>');
                        }
                        $("#loadMoreBtn").hide();
                        hasMore = false;
                    }
                } else {
                    $.modal.msgError(result.msg);
                }
            },
            error: function() {
                $.modal.msgError("获取分享记录失败");
            }
        });
    }
    
    function loadMoreRecords() {
        loadDetailRecords(false);
    }

    function buildShareRecordHtml(record) {
        var html = '<div class="share-record-card">';
        html += '<div class="share-content">';
        
        // 分享者信息和分享内容
        html += '<div class="row">';
        html += '<div class="col-sm-8">';
        html += '<h4><i class="fa fa-user text-primary"></i> ' + (record.sharer_name || '未知用户') + '</h4>';
        html += '<p><strong>分享内容：</strong>' + (record.share_title || '无标题') + '</p>';
        if (record.share_desc) {
            html += '<p><strong>分享描述：</strong>' + record.share_desc + '</p>';
        }
        html += '<p><strong>分享来源：</strong><span class="label label-info">' + getShareSourceText(record.share_source) + '</span></p>';
        html += '</div>';
        
        // 分享统计
        html += '<div class="col-sm-4 text-right">';
        html += '<p><strong>分享时间：</strong><br><small class="text-muted">' + formatDateTime(record.share_time) + '</small></p>';
        html += '<p><strong>访问统计：</strong><br>';
        html += '<span class="badge badge-success">' + (record.visit_count || 0) + ' 次访问</span> ';
        html += '<span class="badge badge-info">' + (record.unique_visitors || 0) + ' 独立访客</span>';
        html += '</p>';
        html += '</div>';
        html += '</div>';
        
        // 访问者信息
        if (record.visitors && record.visitors.length > 0) {
            html += '<div class="visitor-info">';
            html += '<strong><i class="fa fa-users"></i> 访问者：</strong><br>';
            $.each(record.visitors, function(index, visitor) {
                html += '<div class="visitor-badge">';
                html += (visitor.visitor_nickname || '匿名用户') + ' ';
                html += '<small>(' + formatDateTime(visitor.visit_time) + ')</small>';
                html += '</div>';
            });
            html += '</div>';
        } else {
            html += '<div class="visitor-info no-visitors">';
            html += '<i class="fa fa-info-circle"></i> 暂无访问记录';
            html += '</div>';
        }
        
        html += '</div>';
        html += '</div>';
        
        return html;
    }

    function getShareSourceText(source) {
        switch(source) {
            case 'index': return '首页';
            case 'notice': return '公告';
            case 'repair': return '报修';
            default: return source || '未知';
        }
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        var date = new Date(dateTimeStr);
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
    }
</script>
</body>
</html>

package com.ehome.system.service.impl;

import com.ehome.common.core.text.Convert;
import com.ehome.system.domain.SysNoticeShare;
import com.ehome.system.mapper.SysNoticeShareMapper;
import com.ehome.system.service.ISysNoticeShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公告分享记录 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysNoticeShareServiceImpl implements ISysNoticeShareService
{
    @Autowired
    private SysNoticeShareMapper shareMapper;

    /**
     * 查询分享记录信息
     * 
     * @param shareId 分享记录ID
     * @return 分享记录信息
     */
    @Override
    public SysNoticeShare selectShareById(Long shareId)
    {
        return shareMapper.selectShareById(shareId);
    }

    /**
     * 查询分享记录列表
     * 
     * @param share 分享记录信息
     * @return 分享记录集合
     */
    @Override
    public List<SysNoticeShare> selectShareList(SysNoticeShare share)
    {
        return shareMapper.selectShareList(share);
    }

    /**
     * 根据公告ID查询分享记录列表
     * 
     * @param noticeId 公告ID
     * @return 分享记录集合
     */
    @Override
    public List<SysNoticeShare> selectShareByNoticeId(Long noticeId)
    {
        return shareMapper.selectShareByNoticeId(noticeId);
    }

    /**
     * 查询指定小区所有公告的分享记录列表（包含公告标题）
     * 
     * @param share 分享记录信息（包含小区ID等查询条件）
     * @return 分享记录集合
     */
    @Override
    public List<SysNoticeShare> selectAllSharesByCommunity(SysNoticeShare share)
    {
        return shareMapper.selectAllSharesByCommunity(share);
    }

    /**
     * 新增分享记录
     * 
     * @param share 分享记录信息
     * @return 结果
     */
    @Override
    public int insertShare(SysNoticeShare share)
    {
        return shareMapper.insertShare(share);
    }

    /**
     * 修改分享记录
     * 
     * @param share 分享记录信息
     * @return 结果
     */
    @Override
    public int updateShare(SysNoticeShare share)
    {
        return shareMapper.updateShare(share);
    }

    /**
     * 删除分享记录信息
     * 
     * @param shareIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteShareByIds(String shareIds)
    {
        return shareMapper.deleteShareByIds(Convert.toStrArray(shareIds));
    }

    /**
     * 根据公告ID删除分享记录
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteShareByNoticeId(Long noticeId)
    {
        return shareMapper.deleteShareByNoticeId(noticeId);
    }
}

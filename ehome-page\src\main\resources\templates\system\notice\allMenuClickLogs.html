<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('菜单点击日志')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="menuClickLog-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                用户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                菜单名称：<input type="text" name="menuName"/>
                            </li>
                            <li>
                                菜单类型：
                                <select name="menuType" th:with="type=${@dict.getType('menu_type')}">
                                    <option value="">所有</option>
                                    <option value="text">文本</option>
                                    <option value="pdf">PDF</option>
                                    <option value="url">链接</option>
                                    <option value="page">页面</option>
                                    <option value="miniprogram">小程序</option>
                                </select>
                            </li>
                            <li>
                                来源页面：
                                <select name="source">
                                    <option value="">所有</option>
                                    <option value="index">首页</option>
                                    <option value="nav">导航页</option>
                                    <option value="mine">我的页面</option>
                                </select>
                            </li>
                            <li>
                                点击时间：<input type="text" class="time-input" name="clickTime"/>
                            </li>
                            <li>
                                房屋名称：<input type="text" name="houseName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-info" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice/allMenuClickLogs";

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }

        $(function() {
            // 检查是否有指定菜单参数
            var menuId = getUrlParam('menuId');
            var menuName = getUrlParam('menuName');

            if (menuId && menuName) {
                // 如果有指定菜单，自动填入搜索条件
                $("input[name='menuName']").val(menuName);
                // 修改页面标题
                $("title").text("菜单点击日志 - " + menuName);
            }
            var options = {
                url: prefix + "/list",
                createUrl: "",
                updateUrl: "",
                removeUrl: "",
                exportUrl: prefix + "/export",
                modalName: "菜单点击日志",
                search: false,
                showExport: true,
                columns: [{
                    checkbox: true
                }, {
                    field: 'menu_name',
                    title: '菜单名称',
                    sortable: true
                }, {
                    field: 'menu_type',
                    title: '菜单类型',
                    formatter: function(value, row, index) {
                        switch(value) {
                            case 'text': return '<span class="label label-primary">文本</span>';
                            case 'pdf': return '<span class="label label-info">PDF</span>';
                            case 'url': return '<span class="label label-success">链接</span>';
                            case 'page': return '<span class="label label-warning">页面</span>';
                            case 'miniprogram': return '<span class="label label-danger">小程序</span>';
                            default: return '<span class="label label-default">' + (value || '未知') + '</span>';
                        }
                    }
                }, {
                    field: 'user_name',
                    title: '用户名称',
                    sortable: true
                }, {
                    field: 'first_click_time',
                    title: '首次点击时间',
                    sortable: true
                }, {
                    field: 'last_click_time',
                    title: '最后点击时间',
                    sortable: true
                }, {
                    field: 'click_count',
                    title: '点击次数',
                    sortable: true
                }, {
                    field: 'source',
                    title: '来源页面',
                    formatter: function(value, row, index) {
                        switch(value) {
                            case 'index': return '<span class="label label-primary">首页</span>';
                            case 'nav': return '<span class="label label-info">导航页</span>';
                            case 'mine': return '<span class="label label-success">我的页面</span>';
                            default: return '<span class="label label-default">' + (value || '未知') + '</span>';
                        }
                    }
                }, {
                    field: 'house_name',
                    title: '房屋名称'
                }, {
                    field: 'ip_address',
                    title: 'IP地址'
                }]
            };
            $.table.init(options);

            // 如果有指定菜单，自动执行搜索
            if (menuId && menuName) {
                setTimeout(function() {
                    $.table.search();
                }, 100);
            }
        });
        
        function refreshTable() {
            $.table.refresh();
        }
        
        function exportData() {
            $.modal.confirm("确定导出所有菜单点击日志数据吗？", function() {
                $.post(prefix + "/export", $("#menuClickLog-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

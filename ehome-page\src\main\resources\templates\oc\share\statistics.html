<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('分享统计')" />
    <th:block th:include="include :: datetimepicker-css" />
    <style>
        .stat-icon {
            margin-bottom: 15px;
        }
        .ibox-content {
            padding: 20px;
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #676a6c;
        }
        .badge {
            font-size: 11px;
            padding: 4px 8px;
        }
        .label {
            font-size: 10px;
            padding: 3px 6px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>统计时间：</label>
                            <select name="days" class="form-control">
                                <option value="7">最近7天</option>
                                <option value="30">最近30天</option>
                                <option value="90">最近90天</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="loadStatistics()">
                                <i class="fa fa-search"></i>&nbsp;查询
                            </a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                                <i class="fa fa-refresh"></i>&nbsp;重置
                            </a>
                            <a class="btn btn-success btn-rounded btn-sm" onclick="updateStatistics()">
                                <i class="fa fa-cog"></i>&nbsp;更新统计
                            </a>
                            <a class="btn btn-info btn-rounded btn-sm" onclick="viewDetailRecords()">
                                <i class="fa fa-list"></i>&nbsp;详细记录
                            </a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <!-- 统计概览 -->
        <div class="col-sm-12" style="margin-top: 10px;margin-left: -15px;">
            <div class="row">
                <div class="col-sm-3">
                    <div class="ibox stat-card">
                        <div class="ibox-content text-center">
                            <div class="stat-icon">
                                <i class="fa fa-share-alt fa-3x text-primary"></i>
                            </div>
                            <h2 class="font-bold text-primary" id="totalShares">0</h2>
                            <span class="text-muted">总分享次数</span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="ibox stat-card">
                        <div class="ibox-content text-center">
                            <div class="stat-icon">
                                <i class="fa fa-eye fa-3x text-success"></i>
                            </div>
                            <h2 class="font-bold text-success" id="totalVisits">0</h2>
                            <span class="text-muted">总访问次数</span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="ibox stat-card">
                        <div class="ibox-content text-center">
                            <div class="stat-icon">
                                <i class="fa fa-users fa-3x text-warning"></i>
                            </div>
                            <h2 class="font-bold text-warning" id="uniqueVisitors">0</h2>
                            <span class="text-muted">独立访客</span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="ibox stat-card">
                        <div class="ibox-content text-center">
                            <div class="stat-icon">
                                <i class="fa fa-user-plus fa-3x text-info"></i>
                            </div>
                            <h2 class="font-bold text-info" id="newUsers">0</h2>
                            <span class="text-muted">新用户</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分享排行榜 -->
        <div class="col-sm-6" style="margin-left: -15px;">
            <div class="ibox">
                <div class="ibox-title">
                    <h5><i class="fa fa-trophy text-warning"></i> 分享排行榜</h5>
                    <div class="ibox-tools">
                        <span class="label label-primary">TOP 10</span>
                    </div>
                </div>
                <div class="ibox-content">
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th width="15%" class="text-center">排名</th>
                            <th width="50%">用户昵称</th>
                            <th width="35%" class="text-center">分享次数</th>
                        </tr>
                        </thead>
                        <tbody id="shareRankTable">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分享详细记录 -->
        <div class="col-sm-6">
            <div class="ibox">
                <div class="ibox-title">
                    <h5><i class="fa fa-list-alt text-info"></i> 最近分享记录</h5>
                    <div class="ibox-tools">
                        <span class="label label-info">最新10条</span>
                    </div>
                </div>
                <div class="ibox-content">
                    <table class="table table-hover">
                        <thead>
                        <tr>
                            <th width="30%">分享者</th>
                            <th width="25%" class="text-center">分享类型</th>
                            <th width="45%" class="text-center">分享时间</th>
                        </tr>
                        </thead>
                        <tbody id="shareRecordTable">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "oc/share";

    $(function() {
        loadStatistics();
    });

    function loadStatistics() {
        var days = $("select[name='days']").val();
        
        $.ajax({
            url: ctx + "oc/share/data",
            type: "GET",
            data: { days: days },
            success: function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    
                    // 更新统计数据
                    $("#totalShares").text(data.shareStats.total_shares || 0);
                    $("#totalVisits").text(data.visitStats.total_visits || 0);
                    $("#uniqueVisitors").text(data.visitStats.unique_visitors || 0);
                    $("#newUsers").text(data.visitStats.new_users || 0);
                    
                    // 更新分享排行榜
                    var rankHtml = "";
                    if (data.shareRank && data.shareRank.length > 0) {
                        $.each(data.shareRank, function(index, item) {
                            var rankClass = "";
                            var rankIcon = "";
                            if (index === 0) {
                                rankClass = "text-warning";
                                rankIcon = '<i class="fa fa-trophy"></i>';
                            } else if (index === 1) {
                                rankClass = "text-muted";
                                rankIcon = '<i class="fa fa-medal"></i>';
                            } else if (index === 2) {
                                rankClass = "text-info";
                                rankIcon = '<i class="fa fa-award"></i>';
                            } else {
                                rankIcon = index + 1;
                            }

                            rankHtml += "<tr>";
                            rankHtml += "<td class='text-center " + rankClass + "'>" + rankIcon + "</td>";
                            rankHtml += "<td><strong>" + (item.nickname || "未知用户") + "</strong></td>";
                            rankHtml += "<td class='text-center'><span class='badge badge-primary'>" + item.share_count + "</span></td>";
                            rankHtml += "</tr>";
                        });
                    } else {
                        rankHtml = "<tr><td colspan='3' class='text-center text-muted'><i class='fa fa-info-circle'></i> 暂无数据</td></tr>";
                    }
                    $("#shareRankTable").html(rankHtml);

                    // 更新最近分享记录
                    var recordHtml = "";
                    if (data.recentShares && data.recentShares.length > 0) {
                        $.each(data.recentShares, function(index, item) {
                            var shareTypeText = "";
                            var shareTypeClass = "";
                            if (item.share_type === 'app_message') {
                                shareTypeText = '<i class="fa fa-user"></i> 分享给朋友';
                                shareTypeClass = 'label-primary';
                            } else {
                                shareTypeText = '<i class="fa fa-globe"></i> 分享到朋友圈';
                                shareTypeClass = 'label-success';
                            }

                            recordHtml += "<tr>";
                            recordHtml += "<td><strong>" + (item.nickname || "未知用户") + "</strong></td>";
                            recordHtml += "<td class='text-center'><span class='label " + shareTypeClass + "'>" + shareTypeText + "</span></td>";
                            recordHtml += "<td class='text-center text-muted'><small>" + formatDateTime(item.create_time) + "</small></td>";
                            recordHtml += "</tr>";
                        });
                    } else {
                        recordHtml = "<tr><td colspan='3' class='text-center text-muted'><i class='fa fa-info-circle'></i> 暂无数据</td></tr>";
                    }
                    $("#shareRecordTable").html(recordHtml);
                    
                } else {
                    $.modal.msgError(result.msg);
                }
            },
            error: function() {
                $.modal.msgError("获取统计数据失败");
            }
        });
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        var date = new Date(dateTimeStr);
        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
    }

    // 手动更新统计数据
    function updateStatistics() {
        $.modal.confirm("确定要更新分享统计数据吗？", function() {
            $.ajax({
                url: ctx + "oc/share/updateStatistics",
                type: "POST",
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        loadStatistics(); // 重新加载统计数据
                    } else {
                        $.modal.msgError(result.msg);
                    }
                },
                error: function() {
                    $.modal.msgError("更新统计数据失败");
                }
            });
        });
    }

    // 查看详细记录
    function viewDetailRecords() {
        window.open(ctx + "oc/share/detail", "_blank");
    }
</script>
</body>
</html>

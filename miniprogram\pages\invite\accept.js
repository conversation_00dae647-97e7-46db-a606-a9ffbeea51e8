const app = getApp()

Page({
  data: {
    inviteToken: '',
    inviteInfo: null,
    loading: true,
    confirming: false
  },

  onLoad(options) {
    // 获取邀请token
    const { token } = options

    if (!token) {
      wx.showModal({
        title: '邀请无效',
        content: '邀请链接参数错误',
        showCancel: false,
        success: () => {
          wx.switchTab({ url: '/pages/index/index' })
        }
      })
      return
    }

    this.setData({
      inviteToken: token
    })

    // 直接加载邀请信息
    this.loadInviteInfo()
  },

  // 加载邀请信息
  async loadInviteInfo() {
    try {
      this.setData({ loading: true })

      const res = await app.request({
        url: `/api/wx/auth/invite/card-info/${this.data.inviteToken}`,
        method: 'GET'
      })

      if (res.code === 0) {
        this.setData({
          inviteInfo: res.data,
          loading: false
        })
      } else {
        throw new Error(res.msg || '获取邀请信息失败')
      }
    } catch (error) {
      console.error('获取邀请信息失败:', error)
      this.setData({ loading: false })
      
      wx.showModal({
        title: '邀请无效',
        content: error.message || '获取邀请信息失败',
        showCancel: false,
        success: () => {
          wx.switchTab({ url: '/pages/index/index' })
        }
      })
    }
  },

  // 确认接受邀请
  async confirmInvite() {
    if (!this.data.inviteInfo || this.data.confirming) return

    try {
      this.setData({ confirming: true })

      const res = await app.request({
        url: '/api/wx/auth/invite/card-confirm',
        method: 'POST',
        data: {
          inviteToken: this.data.inviteToken
        }
      })

      if (res.code === 0) {
        wx.showModal({
          title: '确认成功',
          content: '邀请已确认，您已注册到系统。请登录小程序使用相关功能。',
          showCancel: false,
          confirmText: '立即登录',
          success: () => {
            // 跳转到登录页面
            wx.redirectTo({
              url: '/pages/login/index'
            })
          }
        })
      } else {
        throw new Error(res.msg || '确认邀请失败')
      }
    } catch (error) {
      console.error('确认邀请失败:', error)
      wx.showModal({
        title: '确认失败',
        content: error.message || '确认邀请失败，请稍后重试',
        showCancel: false
      })
    } finally {
      this.setData({ confirming: false })
    }
  },

  // 拒绝邀请
  rejectInvite() {
    wx.showModal({
      title: '确认拒绝',
      content: '确定要拒绝这个邀请吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已拒绝邀请',
            icon: 'success'
          })
          setTimeout(() => {
            wx.switchTab({ url: '/pages/index/index' })
          }, 1500)
        }
      }
    })
  },



  // 获取关系类型颜色
  getRelTypeColor(relType) {
    switch (relType) {
      case 1: return '#ff6b6b'  // 业主 - 红色
      case 2: return '#4ecdc4'  // 家庭成员 - 青色
      case 3: return '#45b7d1'  // 租户 - 蓝色
      default: return '#95a5a6' // 默认 - 灰色
    }
  }
})

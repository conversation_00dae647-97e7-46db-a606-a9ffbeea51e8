-- 收费标准数据库字段清理脚本
-- 执行前请先备份数据！

-- 1. 备份现有数据（可选，建议在执行前手动备份）
-- CREATE TABLE eh_charge_standard_backup AS SELECT * FROM eh_charge_standard;

-- 2. 确保所有数字字段都有正确的值
UPDATE eh_charge_standard 
SET 
    -- 确保unit字段有值
    unit = CASE 
        WHEN unit IS NULL OR unit = 0 THEN
            CASE 
                WHEN precision_type LIKE '%元%' THEN 1
                WHEN precision_type LIKE '%角%' THEN 2  
                WHEN precision_type LIKE '%分%' THEN 3
                ELSE 1
            END
        ELSE unit
    END,
    
    -- 确保round_type字段有值
    round_type = CASE 
        WHEN round_type IS NULL THEN
            CASE 
                WHEN rounding_type = '四舍五入' THEN 0
                WHEN rounding_type = '抹零' THEN 1
                WHEN rounding_type = '向上取整' THEN 2
                ELSE 0
            END
        ELSE round_type
    END,
    
    -- 确保period_type字段有值
    period_type = CASE 
        WHEN period_type IS NULL OR period_type = 0 THEN
            CASE 
                WHEN collection_method LIKE '%按月%' THEN 101
                ELSE 101
            END
        ELSE period_type
    END,
    
    -- 确保count_type字段有值
    count_type = CASE 
        WHEN count_type IS NULL OR count_type = 0 THEN
            CASE 
                WHEN calculation_method = '固定金额' THEN 200
                WHEN calculation_method LIKE '%单价%' THEN 100
                WHEN fee_type = '走表收费' THEN 100
                ELSE 100
            END
        ELSE count_type
    END,
    
    -- 确保late_money_type字段有值
    late_money_type = CASE 
        WHEN late_money_type IS NULL THEN
            CASE 
                WHEN penalty_handling = '计算违约金' THEN 1
                ELSE 0
            END
        ELSE late_money_type
    END

WHERE is_deleted = 0;

-- 3. 修改incomplete_month_handling字段类型
-- 先更新数据格式
UPDATE eh_charge_standard 
SET incomplete_month_handling = CASE 
    WHEN incomplete_month_handling = '按天收费' THEN '1'
    WHEN incomplete_month_handling = '按月收费' THEN '2'
    WHEN incomplete_month_handling = '不收费' THEN '3'
    WHEN incomplete_month_handling IN ('1', '2', '3') THEN incomplete_month_handling
    ELSE '1'
END
WHERE is_deleted = 0;

-- 4. 验证数据完整性
SELECT 
    '数据验证' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN unit IS NULL OR unit = 0 THEN 1 END) as missing_unit,
    COUNT(CASE WHEN round_type IS NULL THEN 1 END) as missing_round_type,
    COUNT(CASE WHEN period_type IS NULL OR period_type = 0 THEN 1 END) as missing_period_type,
    COUNT(CASE WHEN count_type IS NULL OR count_type = 0 THEN 1 END) as missing_count_type,
    COUNT(CASE WHEN late_money_type IS NULL THEN 1 END) as missing_late_money_type
FROM eh_charge_standard 
WHERE is_deleted = 0;

-- 5. 如果验证通过，删除冗余字段（谨慎执行！）
-- 注意：删除字段是不可逆操作，请确保数据已正确迁移
/*
ALTER TABLE eh_charge_standard 
DROP COLUMN precision_type,
DROP COLUMN rounding_type,
DROP COLUMN collection_method,
DROP COLUMN calculation_method,
DROP COLUMN penalty_handling;
*/

-- 6. 修改字段类型（如果需要）
/*
ALTER TABLE eh_charge_standard 
MODIFY COLUMN incomplete_month_handling INT;
*/

-- 7. 最终验证
SELECT 
    id,
    fee_name,
    fee_type,
    unit,
    round_type,
    period_type,
    count_type,
    late_money_type,
    incomplete_month_handling
FROM eh_charge_standard 
WHERE is_deleted = 0
ORDER BY id
LIMIT 10;

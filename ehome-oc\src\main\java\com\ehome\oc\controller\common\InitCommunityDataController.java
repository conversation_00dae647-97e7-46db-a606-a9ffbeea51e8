package com.ehome.oc.controller.common;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.sql.SqlExecutorUtils;
import com.ehome.oc.service.IDataInitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小区数据初始化控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oc/initCommunityData")
public class InitCommunityDataController extends BaseController {

    @Autowired
    private IDataInitService dataInitService;

    /**
     * 初始化服务电话数据
     */
    @RequestMapping("/initServiceTel")
    public AjaxResult initServiceTel() {
        String communityId = getSysUser().getCommunityId();

        SqlExecutorUtils.ExecutionResult result = dataInitService.initServiceTelData(communityId);

        if (result.isSuccess()) {
            return AjaxResult.success(result.getMessage());
        } else {
            return AjaxResult.error(result.getMessage()).put("errors", result.getErrors());
        }
    }

    /**
     * 初始化微信导航数据
     */
    @RequestMapping("/initWxNav")
    public AjaxResult initWxNav() {
        String communityId = getSysUser().getCommunityId();

        SqlExecutorUtils.ExecutionResult result = dataInitService.initWxNavData(communityId);

        if (result.isSuccess()) {
            return AjaxResult.success(result.getMessage());
        } else {
            return AjaxResult.error(result.getMessage()).put("errors", result.getErrors());
        }
    }

    /**
     * 初始化公告类型数据
     */
    @RequestMapping("/initNoticeTypes")
    public AjaxResult initNoticeTypes() {
        String communityId = getSysUser().getCommunityId();

        SqlExecutorUtils.ExecutionResult result = dataInitService.initNoticeTypesData(communityId);

        if (result.isSuccess()) {
            return AjaxResult.success(result.getMessage());
        } else {
            return AjaxResult.error(result.getMessage()).put("errors", result.getErrors());
        }
    }

    /**
     * 批量初始化所有数据
     */
    @RequestMapping("/initAll")
    public AjaxResult initAll(@RequestBody(required = false) Map<String, Object> params) {
        String communityId = getSysUser().getCommunityId();

        List<String> templateNames = null;
        if (params != null && params.containsKey("templates")) {
            templateNames = (List<String>) params.get("templates");
        }

        List<SqlExecutorUtils.ExecutionResult> results = dataInitService.initAllData(communityId, templateNames);

        // 统计结果
        int successCount = 0;
        int failCount = 0;
        Map<String, Object> resultData = new HashMap<>();

        for (SqlExecutorUtils.ExecutionResult result : results) {
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
        }

        resultData.put("total", results.size());
        resultData.put("success", successCount);
        resultData.put("failed", failCount);
        resultData.put("details", results);

        if (failCount == 0) {
            return AjaxResult.success("批量初始化完成").put("data", resultData);
        } else {
            return AjaxResult.error("部分初始化失败").put("data", resultData);
        }
    }

    /**
     * 获取初始化状态
     */
    @RequestMapping("/status")
    public AjaxResult getStatus() {
        String communityId = getSysUser().getCommunityId();

        Map<String, Boolean> status = dataInitService.getInitializationStatus(communityId);

        return AjaxResult.success().put("status", status);
    }

    /**
     * 获取可用的初始化模板
     */
    @RequestMapping("/templates")
    public AjaxResult getTemplates() {
        List<String> templates = dataInitService.getAvailableTemplates();

        return AjaxResult.success().put("templates", templates);
    }

    /**
     * 获取小区数据统计信息
     * 用于删除前确认数据量
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        String communityId = getSysUser().getCommunityId();

        Map<String, Long> statistics = dataInitService.getCommunityDataStatistics(communityId);

        return AjaxResult.success().put("statistics", statistics);
    }

    /**
     * 彻底删除小区数据
     * 警告：此操作不可逆，请谨慎使用！
     */
    @PostMapping("/deleteCommunityData")
    public AjaxResult deleteCommunityData(@RequestBody(required = false) Map<String, Object> params) {
        String communityId = getSysUser().getCommunityId();

        // 检查是否包含删除小区基本信息的参数
        boolean includeBasicInfo = false;
        if (params != null && params.containsKey("includeBasicInfo")) {
            includeBasicInfo = Boolean.parseBoolean(params.get("includeBasicInfo").toString());
        }

        // 安全检查：需要确认参数
        if (params == null || !params.containsKey("confirm") ||
            !"DELETE_ALL_DATA".equals(params.get("confirm"))) {
            return AjaxResult.error("请提供确认参数：confirm = 'DELETE_ALL_DATA'");
        }

        SqlExecutorUtils.ExecutionResult result = dataInitService.deleteCommunityData(communityId, includeBasicInfo);

        if (result.isSuccess()) {
            return AjaxResult.success(result.getMessage());
        } else {
            return AjaxResult.error(result.getMessage()).put("errors", result.getErrors());
        }
    }
}

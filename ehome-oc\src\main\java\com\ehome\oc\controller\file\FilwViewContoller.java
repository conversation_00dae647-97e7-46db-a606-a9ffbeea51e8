package com.ehome.oc.controller.file;


import com.ehome.common.core.controller.BaseController;
import com.ehome.common.service.OssService;
import com.ehome.common.utils.ServletUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.file.FileUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 文件查看控制器
 */
@Controller
@RequestMapping("/fv")
public class FilwViewContoller extends BaseController {

    @Autowired
    private OssService ossService;

    @GetMapping("/download/{fileId}")
    public void downloadFileById(@PathVariable String fileId, HttpServletResponse response) {
        try {
            if (StringUtils.isEmpty(fileId)) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "文件ID不能为空");
                return;
            }

            // 查询文件信息
            Record fileInfo = Db.findFirst(
                    "SELECT file_id, original_name, file_path, access_url, oss_url, oss_key, storage_type, mime_type " +
                            "FROM eh_file_info WHERE file_id = ? AND status = '0'", fileId);

            if (fileInfo == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在或已删除");
                return;
            }

            String storageType = fileInfo.getStr("storage_type");
            String ossKey = fileInfo.getStr("oss_key");
            String filePath = fileInfo.getStr("file_path");
            String absolutePath = fileInfo.getStr("absolute_path");
            String originalName = fileInfo.getStr("original_name");
            String mimeType = fileInfo.getStr("mime_type");
            String ossUrl = fileInfo.getStr("oss_url");

            // 设置响应头
            response.setContentType(StringUtils.isNotEmpty(mimeType) ? mimeType : MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, originalName);

            // 根据存储类型处理下载
            if (("both".equals(storageType) || "oss".equals(storageType)) &&
                    StringUtils.isNotEmpty(ossKey) && ossService.isAvailable()) {
                // 重定向到OSS预签名URL
                try {
                    String newOssUrl = ossService.generatePresignedUrl(ossUrl,ossKey, 3600L); // 1小时有效期
                    if (StringUtils.isNotEmpty(newOssUrl)) {
                        response.sendRedirect(newOssUrl);
                        return;
                    }
                } catch (Exception ossException) {
                    logger.warn("OSS下载重定向失败，降级到本地文件: {} - {}", fileId, ossException.getMessage());
                }
            }

            // 本地文件下载 - 兼容不同环境的路径
            if (StringUtils.isNotEmpty(absolutePath)) {
                try {
                    String fullPath = absolutePath;

                    // 检查文件是否存在
                    File file = new File(fullPath);
                    if (file.exists() && file.isFile()) {
                        FileUtils.writeBytes(fullPath, response.getOutputStream());
                    } else {
                        // 文件不存在，尝试通过access_url重定向
                        String fileUrl = fileInfo.getStr("access_url");
                        if (StringUtils.isNotEmpty(fileUrl)) {
                            String encodeRedirectUrl = encodeRedirectUrl(fileUrl);
                            logger.warn("本地文件不存在，重定向到access_url: {} -> {},{}", fullPath, fileUrl,encodeRedirectUrl);
                            response.sendRedirect(encodeRedirectUrl);
                            return;
                        } else {
                            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在: " + fullPath);
                        }
                    }
                } catch (Exception e) {
                    logger.error("本地文件访问失败: {}", absolutePath, e);
                    // 降级到access_url重定向
                    String fileUrl = fileInfo.getStr("access_url");
                    if (StringUtils.isNotEmpty(fileUrl)) {
                        String encodeRedirectUrl = encodeRedirectUrl(fileUrl);
                        logger.warn("本地文件访问异常，重定向到access_url: {},{}", fileUrl,encodeRedirectUrl);
                        response.sendRedirect(encodeRedirectUrl);
                    } else {
                        response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件访问失败");
                    }
                }
            } else {
                // 没有file_path，尝试使用access_url
                String fileUrl = fileInfo.getStr("access_url");
                if (StringUtils.isNotEmpty(fileUrl)) {
                    logger.info("使用access_url进行重定向: {}", fileUrl);
                    response.sendRedirect(encodeRedirectUrl(fileUrl));
                } else {
                    response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件路径和URL都不存在");
                }
            }

        } catch (Exception e) {
            logger.error("文件下载失败: {}", fileId, e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件下载失败");
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * 对重定向URL进行编码处理，避免中文字符导致的HTTP头错误
     */
    private String encodeRedirectUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        try {
            // 若存在非ASCII字符
            if (!StandardCharsets.US_ASCII.newEncoder().canEncode(url)) {
                int schemeIdx = url.indexOf("://");
                if (schemeIdx > 0) {
                    int pathIdx = url.indexOf('/', schemeIdx + 3);
                    if (pathIdx > 0) {
                        String base = url.substring(0, pathIdx);
                        String encodedPath = Arrays.stream(url.substring(pathIdx).split("/"))
                                .filter(part -> !part.isEmpty())
                                .map(ServletUtils::urlEncode)
                                .collect(Collectors.joining("/", "/", ""));
                        return base + encodedPath;
                    }
                }
                // 非完整URL或不含路径部分
                return ServletUtils.urlEncode(url);
            }
            return url;
        } catch (Exception e) {
            logger.warn("URL编码失败，使用原始URL: {} - {}", url, e.getMessage());
            return url;
        }
    }
}

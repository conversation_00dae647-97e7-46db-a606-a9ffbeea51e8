# 收费标准数据格式修改完成

## 修改内容

修改前端页面的select选项，让它们提交数字代码而不是中文字符串，确保数据库中存储的是数字代码。

## 字段映射关系

### 计费精度 (unit)
- 1: "元（不保留小数）"
- 2: "角（保留一位小数）"
- 3: "分（保留两位小数）"

### 取整方式 (round_type)
- 0: "四舍五入"
- 1: "抹零"
- 2: "向上取整"

### 收费方式 (period_type)
- 101: "按月生成账单（推荐）"

### 金额计算方式 (count_type)
- 100: "单价*计量方式" 或 "单价*使用量"
- 200: "固定金额"

### 违约金处理 (late_money_type)
- 0: "不计算违约金"
- 1: "计算违约金"

## 修改的文件

### 1. add.html
- 修改计费精度选项：`item.name` → `item.id`
- 修改取整方式选项：`item.name` → `item.id`
- 修改金额计算方式选项：中文 → 数字代码
- 修改违约金处理选项：中文 → 数字代码
- 修改收费方式隐藏字段：中文 → 数字代码
- 修改JavaScript条件判断：中文 → 数字代码

### 2. edit.html
- 修改计费精度选项：`item.name` → `item.id`
- 修改取整方式选项：`item.name` → `item.id`
- 修改金额计算方式选项：中文 → 数字代码
- 修改违约金处理选项：中文 → 数字代码
- 修改收费方式隐藏字段：中文 → 数字代码
- 修改JavaScript条件判断：中文 → 数字代码
- 修改数据加载时的条件判断：使用数据库字段而不是中文字符串

## 主要修改点

1. **select选项的value属性**：从中文字符串改为数字代码
2. **JavaScript条件判断**：从中文字符串比较改为数字代码比较
3. **数据加载逻辑**：在edit.html中使用数据库字段进行条件判断

## 数据库更新

已完成对现有数据的更新：
- `unit` 字段：1=元（不保留小数）, 2=角（保留一位小数）, 3=分（保留两位小数）
- `round_type` 字段：0=四舍五入, 1=抹零, 2=向上取整
- `period_type` 字段：101=按月生成账单（推荐）
- `count_type` 字段：100=单价*计量方式, 200=固定金额
- `late_money_type` 字段：0=不计算违约金, 1=计算违约金
- `incomplete_month_handling` 字段：1=按天收费, 2=按月收费, 3=不收费

## list.html改造完成

1. **字段映射更新**：
   - `precision_type` → `precision_type_str`
   - `collection_method` → `collection_method_str`
   - `calculation_method` → `calculation_method_str`
   - `penalty_handling` → `penalty_handling_str`
   - `measurement_type` → `measurement_type_str`
   - `bill_generation_day` → `accounting_period_day`

2. **价格显示优化**：
   - 合并单价和固定金额显示为统一的"价格"字段
   - 根据计算方式自动显示相应的价格信息
   - 支持不同计量方式的单位显示

3. **后台字符串转换**：
   - 在`addDisplayStrings`方法中添加所有数字代码到中文字符串的转换
   - 支持计量方式的动态获取和转换

## 问题修复

### 1. 修复add.html中calculation_method字段重复问题
- 将走表收费的计算方式字段名改为`meter_calculation_method`
- 在后台处理时将其转换为标准的`calculation_method`字段

### 2. 修复edit.html中countInfo.price字段重复问题
- 将走表收费的单价字段名改为`countInfo.meter_price`
- 在后台处理时将其转换为标准的`countInfo.price`字段

### 3. 后台数据处理优化
- 在`convertFormDataToDbFormat`方法中添加字段名转换逻辑
- 在countInfo数据处理中添加meter_price到price的转换

### 4. 前端格式化优化
- **移除后台字符串转换方法**：删除`addDisplayStrings`方法，不再在后台生成显示字符串
- **前端formatter函数**：在list.html中使用formatter函数直接将数字代码转换为中文显示
- **查询条件优化**：修改查询条件select选项使用数字代码，后台查询使用正确的数据库字段名

### 5. 字段映射更新
- `collectionMethod` 查询条件 → `period_type` 数据库字段
- `calculationMethod` 查询条件 → `count_type` 数据库字段

### 6. 违约金配置优化
- **edit.html违约金显示**：当`late_money_type = 1`时，自动显示违约金详细配置区域
- **数据加载优化**：确保penalty_handling_select的值能正确设置
- **测试数据**：创建了包含违约金配置的测试数据（ID=11）

### 7. 最终简化方案：data-name属性
- **data-name属性**：所有可能重复的字段使用`data-name`属性存储真正的name值
- **动态name赋值**：根据字段显示状态，将`data-name`的值赋给`name`属性
- **简化逻辑**：
  ```javascript
  // 清除所有name属性
  $('[data-name]').removeAttr('name');
  // 为显示的字段设置name属性
  $('.charge-field:visible [data-name]').each(function() {
      $(this).attr('name', $(this).data('name'));
  });
  ```
- **优势**：方案简单、逻辑清晰、完全避免name重复问题

### 8. 修复违约金字段映射
- **字段映射修正**：将前端`penalty_handling`字段改为`late_money_type`
- **数据库字段**：
  - `late_money_type` (int) - 违约金类型的数字代码
  - `late_money_after_day` (int) - 违约金开始计算的天数
  - `late_money_proportion` (decimal) - 违约金比例
- **前端表单**：修改add.html和edit.html中的字段名，确保与数据库字段一致

### 9. 修复list.html显示问题
- **类型转换**：在formatter函数中添加`parseInt`和`parseFloat`类型转换
- **确保显示**：修复数字字段在前端的正确显示

### 10. 简化JavaScript选项数据
- **数据结构简化**：将复杂的对象结构改为简单数组
- **修改前**：`precisionOptions.data.list` 复杂嵌套结构
- **修改后**：`precisionOptions = [{ id: 1, name: "..." }]` 简单数组
- **优势**：代码更简洁，维护更容易

### 11. 优化list.html显示格式
- **收费方式优化**：显示完整信息，包括"不足一月"处理方式
  - 示例：`按月生成账单（推荐）<br/>不足一月按天收费`
- **金额计算方式优化**：根据收费类型显示不同的计算公式
  - 固定金额：`固定金额: 100元`
  - 走表收费：`单价*使用量:<br/>3元/度`（不显示计量方式）
  - 周期性/临时性收费：`单价*计量方式:<br/>建筑面积*3`
- **移除冗余列**：删除单独的"计量方式"和"价格"列，信息整合到"金额计算方式"中
- **数据来源**：从`count_info` JSON字段中解析`area_type`和`price`信息
- **收费类型区分**：走表收费不显示房屋面积等计量方式，只显示单价/度

### 12. 修复add.html和edit.html的走表收费逻辑
- **字段显示控制**：走表收费时不显示`unit_price_group`（计量方式选择组）
- **JavaScript逻辑优化**：
  - `initFieldsByChargeType`函数中隐藏所有子选项组
  - `calculation_method_select`的change事件只对周期性收费和临时性收费生效
  - 走表收费有独立的单价输入字段，不需要计量方式选择
- **用户体验**：确保走表收费界面简洁，不显示不相关的计量方式选项

## 🎯 优化实施完成

### 13. 数据库字段清理（已完成）
- **删除冗余字段**：precision_type, rounding_type, collection_method, calculation_method, penalty_handling
- **统一数据格式**：所有配置字段使用数字代码
- **修改字段类型**：incomplete_month_handling改为INT类型
- **数据验证**：确保所有记录的必要字段都有正确值

### 14. 前端JavaScript优化（已完成）
- **配置化管理**：使用CHARGE_OPTIONS对象统一管理选项数据
- **代码简化**：统一变量命名，减少硬编码
- **维护性提升**：选项数据集中管理，便于后续维护

### 15. 系统状态
- **数据库**：✅ 字段结构优化完成，数据完整性验证通过
- **前端**：✅ JavaScript逻辑简化，配置化管理实现
- **编译**：✅ 代码编译通过，无语法错误
- **功能**：✅ 核心业务逻辑保持完整

### 16. 表单字段名修正（已完成）
- **字段映射更新**：修改HTML表单字段名以匹配数据库结构
  - `base.precision_type` → `base.unit`
  - `base.rounding_type` → `base.round_type`
  - `base.collection_method` → `base.period_type`
  - `base.calculation_method` → `base.count_type`
- **数据一致性**：确保前端表单提交的字段名与数据库字段完全匹配
- **JavaScript选择器**：保持ID选择器不变，只修改name属性

## 测试建议

1. 测试新增收费标准，确保数据库中存储的是数字代码
2. 测试编辑收费标准，确保能正确加载和保存数字代码
3. 测试收费标准列表显示，确保所有字段都显示为中文字符串
4. 测试各种选项的切换，确保界面显示逻辑正常工作
5. 检查数据库中的数据，确认存储的是数字而不是中文字符串
6. 测试价格字段显示，确保固定金额和单价都能正确显示
7. **重点测试**：确保add和edit页面不再出现字段重复错误
8. **重点测试**：确保走表收费的单价能正确保存和显示
9. **重点测试**：编辑ID=11的测试记录，确保违约金配置能正确显示和编辑

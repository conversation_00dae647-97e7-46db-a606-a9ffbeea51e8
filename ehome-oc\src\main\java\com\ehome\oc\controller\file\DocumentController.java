package com.ehome.oc.controller.file;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公共文档库控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/document")
public class DocumentController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(DocumentController.class);
    private static final String PREFIX = "document";

    /**
     * 公共文档库主页面
     */
    @GetMapping("/list")
    public String listPage() {
        return PREFIX + "/list";
    }

    /**
     * 公共文档库单选页面
     */
    @GetMapping("/select")
    public String selectPage(String fileType, org.springframework.ui.ModelMap mmap) {
        mmap.put("fileType", fileType);
        return PREFIX + "/select";
    }

    /**
     * 公共文档库多选页面
     */
    @GetMapping("/multiSelect")
    public String multiSelectPage(String fileType, org.springframework.ui.ModelMap mmap) {
        mmap.put("fileType", fileType);
        return PREFIX + "/multiSelect";
    }

    /**
     * 移动文件对话框页面
     */
    @GetMapping("/moveFileDialog")
    public String moveFileDialog(String fileIds, org.springframework.ui.ModelMap mmap) {
        mmap.put("fileIds", fileIds);
        return PREFIX + "/moveFileDialog";
    }

    /**
     * 获取文件夹树形数据
     */
    @PostMapping("/folderTree")
    @ResponseBody
    public AjaxResult folderTree() {
        try {
            String sql = "SELECT folder_id, parent_id, folder_name, sort_order FROM eh_file_folder " +
                        "WHERE community_id = '0' AND status = '0' ORDER BY sort_order ASC, create_time ASC";
            List<Record> folders = Db.find(sql);
            
            List<Map<String, Object>> treeNodes = buildFolderTree(folders);
            return AjaxResult.success(treeNodes);
        } catch (Exception e) {
            log.error("获取文件夹树形数据失败", e);
            return AjaxResult.error("获取文件夹数据失败：" + e.getMessage());
        }
    }

    /**
     * 构建文件夹树形结构
     */
    private List<Map<String, Object>> buildFolderTree(List<Record> folders) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, List<Map<String, Object>>> childrenMap = new HashMap<>();
        
        // 先构建所有节点
        Map<String, Map<String, Object>> nodeMap = new HashMap<>();
        for (Record folder : folders) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", folder.getStr("folder_id"));
            node.put("pId", folder.getStr("parent_id"));
            node.put("name", folder.getStr("folder_name"));
            node.put("type", "folder");
            node.put("open", false);
            nodeMap.put(folder.getStr("folder_id"), node);
        }
        
        // 构建父子关系
        for (Record folder : folders) {
            String parentId = folder.getStr("parent_id");
            String folderId = folder.getStr("folder_id");
            
            if (StringUtils.isEmpty(parentId)) {
                // 根节点
                result.add(nodeMap.get(folderId));
            } else {
                // 子节点
                if (!childrenMap.containsKey(parentId)) {
                    childrenMap.put(parentId, new ArrayList<>());
                }
                childrenMap.get(parentId).add(nodeMap.get(folderId));
            }
        }
        
        // 设置children属性
        for (Map<String, Object> node : nodeMap.values()) {
            String nodeId = (String) node.get("id");
            if (childrenMap.containsKey(nodeId)) {
                node.put("children", childrenMap.get(nodeId));
            }
        }
        
        return result;
    }

    /**
     * 查询文件列表
     */
    @PostMapping("/fileList")
    @ResponseBody
    public TableDataInfo fileList() {
        JSONObject params = getParams();
        EasySQL sql = buildFileListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 构建文件列表查询条件
     */
    private EasySQL buildFileListQuery(JSONObject params) {
        EasySQL sql = new EasySQL("from eh_file_info where status = '0' and community_id = '0'");

        // 文件夹过滤
        String folderId = params.getString("folderId");
        if (StringUtils.isNotEmpty(folderId)) {
            // 根据folderId查询对应的folder_code
            Record folder = Db.findFirst("select folder_code from eh_file_folder where folder_id = ? and status = '0'", folderId);
            if (folder != null && StringUtils.isNotEmpty(folder.getStr("folder_code"))) {
                String folderCode = folder.getStr("folder_code");
                // 使用LIKE查询，包含该文件夹及其所有子文件夹的文件
                sql.appendRLike(folderCode, "and folder_code like ?");
            } else {
                // 如果找不到folder_code，则按原来的逻辑查询
                sql.append(folderId, "and folder_id = ?");
            }
        } else {
            // 如果没有指定文件夹，显示所有文件（不限制文件夹）
            // sql.append("and (folder_id is null or folder_id = '')");
        }
        
        // 文件名搜索
        if (params.containsKey("fileName") && StringUtils.isNotEmpty(params.getString("fileName"))) {
            sql.appendLike(params.getString("fileName"), "and original_name like ?");
        }

        // 文件类型筛选
        String fileType = params.getString("fileType");
        if (StringUtils.isNotEmpty(fileType)) {
            fileType = fileType.trim();
            if (!"".equals(fileType) && !"all".equalsIgnoreCase(fileType)) {
                sql.append(fileType, "and file_type = ?");
            }
        }

        sql.append("order by create_time desc");
        return sql;
    }

    /**
     * 移动文件到文件夹
     */
    @PostMapping("/moveFile")
    @ResponseBody
    @Log(title = "移动文件", businessType = BusinessType.UPDATE)
    public AjaxResult moveFile() {
        JSONObject params = getParams();
        String fileIds = params.getString("fileIds");
        String targetFolderId = params.getString("targetFolderId");
        
        if (StringUtils.isEmpty(fileIds)) {
            return AjaxResult.error("请选择要移动的文件");
        }
        
        try {
            String[] ids = fileIds.split(",");
            for (String fileId : ids) {
                Record updateRecord = new Record();
                updateRecord.set("file_id", fileId);
                updateRecord.set("folder_id", StringUtils.isEmpty(targetFolderId) ? null : targetFolderId);
                Db.update("eh_file_info", "file_id", updateRecord);
            }
            return AjaxResult.success("文件移动成功");
        } catch (Exception e) {
            log.error("移动文件失败", e);
            return AjaxResult.error("移动文件失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件夹信息
     */
    @PostMapping("/folderInfo")
    @ResponseBody
    public AjaxResult folderInfo() {
        JSONObject params = getParams();
        String folderId = params.getString("folderId");
        
        if (StringUtils.isEmpty(folderId)) {
            return AjaxResult.error("文件夹ID不能为空");
        }
        
        Record folder = Db.findFirst("select * from eh_file_folder where folder_id = ? and status = '0'", folderId);
        if (folder == null) {
            return AjaxResult.error("文件夹不存在");
        }
        
        return AjaxResult.success(folder.toMap());
    }

    /**
     * 获取文件夹统计信息
     */
    @PostMapping("/folderStats")
    @ResponseBody
    public AjaxResult folderStats() {
        JSONObject params = getParams();
        String folderId = params.getString("folderId");
        
        try {
            Map<String, Object> stats = new HashMap<>();
            
            if (StringUtils.isNotEmpty(folderId)) {
                // 统计指定文件夹下的文件数量
                Long fileCount = Db.queryLong("select count(*) from eh_file_info where folder_id = ? and status = '0'", folderId);
                stats.put("fileCount", fileCount);
                
                // 统计文件总大小
                Long totalSize = Db.queryLong("select sum(file_size) from eh_file_info where folder_id = ? and status = '0'", folderId);
                stats.put("totalSize", totalSize != null ? totalSize : 0);
            } else {
                // 统计根目录下的文件
                Long fileCount = Db.queryLong("select count(*) from eh_file_info where (folder_id is null or folder_id = '') and community_id = '0' and status = '0'");
                stats.put("fileCount", fileCount);
                
                Long totalSize = Db.queryLong("select sum(file_size) from eh_file_info where (folder_id is null or folder_id = '') and community_id = '0' and status = '0'");
                stats.put("totalSize", totalSize != null ? totalSize : 0);
            }
            
            return AjaxResult.success(stats);
        } catch (Exception e) {
            log.error("获取文件夹统计信息失败", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }
}

# 楼栋批量导入功能实现

## 任务概述
为楼栋管理模块添加批量导入功能，支持通过textarea输入楼栋名称进行批量导入，参考服务电话模块的弹出层导入效果。

## 实现内容

### 1. 前端修改
**文件**: `ehome-page/src/main/resources/templates/oc/building/list.html`
- 在工具栏添加"批量导入"按钮
- 实现批量导入弹出层，包含textarea输入框
- 添加数据解析和确认界面
- 实现导入结果反馈

### 2. 后端修改
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
- 添加`batchImport`方法处理批量导入请求
- 实现楼栋名称重复检查
- 自动设置默认字段值
- 添加操作日志记录

### 3. 功能特性
- 支持纯楼栋名称导入（每行一个）
- 自动去重和重复检查
- 显示导入结果统计（成功数量、跳过数量）
- 导入完成后自动刷新表格
- building_id自增，无需手动设置

### 4. 数据处理逻辑
- 解析textarea中的楼栋名称（每行一个）
- 自动设置community_id为当前用户小区
- 设置默认值：alias_name=""、total_units=0、house_count=0、house_area=0.00、manager=""
- 设置创建时间和创建人信息
- 跳过重复的楼栋名称（同一小区内）
- 添加"批量导入"备注

## 技术实现
- 前端使用layer弹出层组件
- 数据解析支持换行符分割
- 使用JFinal的Db工具进行数据库操作
- 支持事务处理，确保数据一致性
- 错误处理和用户友好的提示信息

## 楼栋单元批量导入功能（新增）

### 1. 前端修改
**文件**: `ehome-page/src/main/resources/templates/oc/building/list.html`
- 添加"导入楼栋单元"按钮
- 实现楼栋单元导入弹出层，支持两列数据格式
- 数据解析支持"楼栋名称 单元名称"格式
- 显示导入确认界面和结果统计

### 2. 后端修改
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
- 添加`batchImportUnit`方法处理楼栋单元批量导入
- 验证楼栋是否存在（必须先存在楼栋）
- 检查单元名称重复（同一楼栋内不能重复）
- 自动更新楼栋的单元总数统计
- 使用Seq.getId()生成单元ID

### 3. 功能特性
- 支持"楼栋名称 单元名称"格式导入
- 楼栋必须先存在，否则跳过该条记录
- 同一楼栋内单元名称不能重复
- 自动更新楼栋的total_units字段
- 显示详细的导入结果统计

### 4. 数据处理逻辑
- 解析两列数据：楼栋名称和单元名称
- 根据楼栋名称查找building_id
- 检查单元是否已存在于该楼栋
- 自动生成unit_id和设置默认值
- 批量更新所有楼栋的单元总数

## 重复名称防护完善（新增）

### 1. 楼栋重复检查完善
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
- 在`addData`方法中添加楼栋名称重复检查
- 在`editSave`方法中添加楼栋名称重复检查（排除自己）
- 确保同一小区内楼栋名称唯一

### 2. 单元重复检查实现
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/UnitController.java`
- 添加`checkName`方法，检查同一楼栋内单元名称重复
- 在`addSave`方法中添加单元名称重复检查
- 在`editSave`方法中添加单元名称重复检查（排除自己）
- 确保同一楼栋内单元名称唯一

### 3. 前端验证完善
**文件**: `ehome-page/src/main/resources/templates/oc/unit/add.html`
- 添加buildingId隐藏字段
- 添加单元名称远程验证规则

**文件**: `ehome-page/src/main/resources/templates/oc/unit/edit.html`
- 添加buildingId隐藏字段
- 添加单元名称远程验证规则（包含unitId排除自己）

### 4. 验证逻辑
- 楼栋：同一小区内楼栋名称不能重复
- 单元：同一楼栋内单元名称不能重复
- 前端实时验证，后端双重保护
- 编辑时排除自己，避免误报

## 数据库字段完善（新增）

### 1. eh_unit表添加community_id字段
```sql
ALTER TABLE `smarthome`.`eh_unit`
ADD COLUMN `community_id` varchar(32) NULL DEFAULT '' COMMENT '小区ID' AFTER `building_id`;
```

### 2. 相关代码更新
**文件**: `ehome-oc/src/main/java/com/ehome/oc/domain/Unit.java`
- 添加communityId属性及getter/setter方法

**文件**: `ehome-oc/src/main/resources/mapper/UnitMapper.xml`
- 更新selectUnitVo查询语句包含community_id
- 更新insertUnit语句支持community_id字段
- 更新updateUnit语句支持community_id字段

**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/UnitController.java`
- 所有重复检查逻辑更新为包含community_id条件
- 新增和编辑时自动设置community_id

**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
- 楼栋单元批量导入时设置community_id字段
- 重复检查逻辑包含community_id条件
- 修正：移除unit_id的Seq.getId()设置（unit_id是自增字段）

### 3. 数据一致性保证
- 确保所有单元记录都有正确的community_id
- 重复检查逻辑更加严格：同一小区+同一楼栋+单元名称
- 批量导入时自动设置正确的community_id

## 单元列表页面批量导入功能（新增）

### 1. 前端修改
**文件**: `ehome-page/src/main/resources/templates/oc/unit/list.html`
- 添加"批量导入"按钮
- 实现单元批量导入弹出层，支持纯单元名称格式
- 数据解析支持每行一个单元名称
- 显示导入确认界面和结果统计

### 2. 后端修改
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/UnitController.java`
- 添加`batchImport`方法处理单元批量导入
- 检查单元名称重复（同一小区同一楼栋内）
- 自动设置community_id和buildingId
- 自动更新楼栋的单元总数统计
- 添加`updateBuildingTotalUnits`私有方法

### 3. 功能特性
- 支持纯单元名称格式导入（每行一个）
- 自动使用当前楼栋ID
- 同一楼栋内单元名称不能重复
- 自动更新楼栋的total_units字段
- 显示详细的导入结果统计

### 4. 数据处理逻辑
- 解析单行数据：单元名称
- 自动使用当前页面的buildingId
- 检查单元是否已存在于该楼栋
- 自动生成unit_id和设置默认值
- 更新对应楼栋的单元总数

## 删除校验和房屋数据查看功能（新增）

### 1. 删除校验功能
**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
- 删除楼栋前检查是否存在房屋数据
- 删除楼栋前检查是否存在单元数据
- 提供友好的错误提示信息

**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/UnitController.java`
- 删除单元前检查是否存在房屋数据
- 提供友好的错误提示信息

### 2. 房屋数据查看功能
**文件**: `ehome-page/src/main/resources/templates/oc/building/list.html`
- 添加"房屋"按钮，点击查看楼栋下的房屋数据
- 添加viewHouses JavaScript函数，跳转到 `/oc/house/mgr?buildingId=XX`

**文件**: `ehome-page/src/main/resources/templates/oc/unit/list.html`
- 添加"房屋"按钮，点击查看单元下的房屋数据
- 添加viewUnitHouses JavaScript函数，跳转到 `/oc/house/mgr?unitId=XX`

**文件**: `ehome-oc/src/main/java/com/ehome/oc/controller/HouseMgrController.java`
- 修改mgr方法支持buildingId和unitId参数
- 将参数传递给前端页面用于过滤

**文件**: `ehome-page/src/main/resources/templates/oc/house/list.html`
- 添加URL参数处理逻辑，支持buildingId和unitId过滤
- 页面初始化时自动应用过滤条件

## UI样式优化（新增）

### 1. 操作按钮样式优化
**文件**: `ehome-page/src/main/resources/templates/oc/building/list.html`
- 操作按钮改为链接样式，去掉图标和按钮样式
- 使用 " | " 分隔符连接操作链接
- 房屋查看功能移至房屋数量列，显示为外链图标

**文件**: `ehome-page/src/main/resources/templates/oc/unit/list.html`
- 操作按钮改为链接样式，去掉图标和按钮样式
- 使用 " | " 分隔符连接操作链接
- 房屋查看功能移至房屋数量列，显示为外链图标

### 2. 数量列增强
- 楼栋列表：单元总数 > 0 时显示外链图标，点击查看单元
- 楼栋列表：房屋数量 > 0 时显示外链图标，点击查看房屋
- 单元列表：房屋数量 > 0 时显示外链图标，点击查看房屋
- 使用 `fa-external-link` 图标表示可点击查看
- 添加 title 属性提供操作提示
- 操作列中移除对应的操作链接，避免重复

### 3. Hover交互效果
- 外链图标默认隐藏（style="display:none;"）
- 鼠标悬停在表格行时显示外链图标
- 鼠标离开时隐藏外链图标
- 使用jQuery事件委托处理动态内容
- 提供更清爽的界面和更好的交互体验

### 4. 表格工具栏隐藏
- 楼栋列表：添加 `showToolbar: false` 配置
- 单元列表：添加 `showToolbar: false` 配置
- 隐藏Bootstrap Table默认的工具栏
- 提供更简洁的表格界面

### 3. 功能特性
- 删除前数据完整性校验
- 防止误删除有关联数据的记录
- 便捷的房屋数据查看入口
- 友好的错误提示信息

### 4. 业务逻辑
- 楼栋删除：检查eh_house_info和eh_unit表
- 单元删除：检查eh_house_info表
- 房屋查看：通过buildingId或unitId过滤
- 新标签页打开，不影响当前操作

## 测试要点
1. 验证批量导入功能的正确性
2. 测试重复楼栋名称的跳过逻辑
3. 验证导入结果统计的准确性
4. 检查前端交互体验
5. 测试异常情况的处理
6. 验证楼栋单元导入功能
7. 测试楼栋不存在时的跳过逻辑
8. 验证单元重复检查功能
9. 测试楼栋单元总数自动更新
10. 测试楼栋名称重复检查（新增、编辑）
11. 测试单元名称重复检查（新增、编辑）
12. 验证前端实时验证功能
13. 测试后端重复检查保护
14. 验证单元列表页面批量导入功能
15. 测试单元导入时楼栋单元总数更新
16. 验证单元导入的重复检查逻辑
17. 测试删除校验功能（楼栋、单元）
18. 验证房屋数据查看功能
19. 测试删除有关联数据时的错误提示
20. 验证房屋数据页面跳转功能

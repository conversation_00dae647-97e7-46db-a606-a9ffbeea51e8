#toolbar .btn{margin-right: 5px;}


body.fixed-sidebar.mini-navbar .navbar-default .nav>li>.nav-second-level li a{
    color:#333;
}
body.fixed-sidebar.mini-navbar .navbar-default .nav>li>.nav-second-level li a{
    color: red!important;
}

body.mini-navbar .navbar-default .nav>li>a{
    color: red!important;
}

.fixed-sidebar.mini-navbar .nav li:hover>.nav-second-level{
    background-color: #eee;
}
.fixed-sidebar.mini-navbar .nav li:hover>a> span.nav-label{
    background-color: #eee;
}

#page-wrapper .footer{
    display: none;
}

button, input, select, textarea{
    padding-left: 6px;
}

.ml-5{
    margin-left: 5px;
}

.ml-10{
    margin-left: 10px;
}

.ml-20{
    margin-left: 20px;
}

.ml-30{
    margin-left: 30px;
}

.ml-40{
    margin-left: 40px;
}

.ml-50{
    margin-left: 50px;
}

.mr-0{
    margin-right: 0;
}

.mr-5{
    margin-right: 5px;
}

.mr-10{
    margin-right: 10px;
}

.mr-20{
    margin-right: 20px;
}

.mr-30{
    margin-right: 30px;
}

.mr-40{
    margin-right: 40px;
}

.mr-50{
    margin-right: 50px;
}

.mr-60{
    margin-right: 60px;
}

.logo-lg {
    display: inline-block;
}

.header-logo{
    width: 40px!important;
    height: 32px!important;
}

.btn-rounded {
    border-radius: 5px;
}
.nav>li>a {
    color: #333333;
}

.nav>li.active>a {
    color: #333333;
}
.navbar-default .nav>li>a:hover,.navbar-default .nav>li>a:focus {
    background-color: inherit;
    color: inherit;
}
.btn-rounded {
    border-radius: 5px;
}
.nav-tabs>li>a {
    font-weight: 400;
}

.select-list li p, .select-list li label:not(.radio-box){
    min-width: 65px;
}
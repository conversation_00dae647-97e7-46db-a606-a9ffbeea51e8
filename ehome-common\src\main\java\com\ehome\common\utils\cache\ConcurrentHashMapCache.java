package com.ehome.common.utils.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class ConcurrentHashMapCache implements DisposableBean {

    private static final Logger logger = LoggerFactory.getLogger(ConcurrentHashMapCache.class);

    private final Map<String, CacheObject> cache = new ConcurrentHashMap<>();

    // 默认清理频率，10分钟
    private long cleanupIntervalMinutes = 10;

    private ScheduledExecutorService executor;

    @PostConstruct
    public void init() {
        executor = Executors.newSingleThreadScheduledExecutor();
        startCleaner();
    }

    private void startCleaner() {
        executor.scheduleAtFixedRate(() -> {
            logger.info("Starting scheduled cache cleanup.");
            int removed = cleanUp();
            logger.info("Scheduled cache cleanup completed. Removed {} expired entries.", removed);
        }, cleanupIntervalMinutes, cleanupIntervalMinutes, TimeUnit.MINUTES);
    }

    /**
     * @param key       缓存键
     * @param value     缓存值
     * @param duration  缓存时长
     * @param unit      时间单位
     */
    public void putWithTTL(String key, Object value, long duration, TimeUnit unit) {
        long expiryTime = System.currentTimeMillis() + unit.toMillis(duration);
        cache.put(key, new CacheObject(value, expiryTime));
        logger.debug("Put key '{}' with TTL {} {}", key, duration, unit);
    }

    /**
     * 默认缓存3小时
     */
    public void put(String key, Object value) {
        putWithTTL(key, value, 3, TimeUnit.HOURS);
    }

    /**
     * 获取缓存，包含惰性清理
     */
    public <T> T get(String key, Class<T> clazz) {
        CacheObject obj = cache.get(key);
        if (obj == null || obj.isExpired()) {
            cache.remove(key);
            logger.debug("Cache miss or expired for key '{}'", key);
            return null;
        }
        obj.recordAccess();
        logger.debug("Cache hit for key '{}'", key);

        try {
            return (T) obj.value;
        } catch (ClassCastException e) {
            logger.error("Cache value type mismatch for key '{}'. Expected: {}, actual: {}",
                    key, clazz.getName(), obj.value.getClass().getName(), e);
            return null;
        }
    }


    /**
     * 删除缓存
     */
    public void remove(String key) {
        cache.remove(key);
        logger.info("Cache removed for key '{}'", key);
    }

    /**
     * 清空缓存
     */
    public void clear() {
        cache.clear();
        logger.info("Cache cleared.");
    }

    /**
     * 清理过期缓存，返回清理数量
     */
    public int cleanUp() {
        int initialSize = cache.size();
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        int removed = initialSize - cache.size();
        return removed;
    }

    /**
     * 优雅关闭清理线程
     */
    @Override
    public void destroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
                logger.info("Cache cleaner executor shut down.");
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
                logger.error("Interrupted during cache cleaner shutdown.", e);
            }
        }
    }

    /**
     * 可选：允许外部设置清理间隔（单位：分钟）
     */
    public void setCleanupIntervalMinutes(long minutes) {
        if (minutes <= 0) {
            throw new IllegalArgumentException("Cleanup interval must be positive.");
        }
        this.cleanupIntervalMinutes = minutes;
        if (executor != null && !executor.isShutdown()) {
            executor.shutdownNow();
            executor = Executors.newSingleThreadScheduledExecutor();
            startCleaner();
            logger.info("Cache cleanup interval updated to {} minutes.", minutes);
        }
    }

    private static class CacheObject {
        private final Object value;
        private final long expiryTime;
        private long lastAccessTime;
        private long hitCount;

        CacheObject(Object value, long expiryTime) {
            this.value = value;
            this.expiryTime = expiryTime;
            this.lastAccessTime = System.currentTimeMillis();
            this.hitCount = 0;
        }

        boolean isExpired() {
            return System.currentTimeMillis() > expiryTime;
        }

        void recordAccess() {
            hitCount++;
            lastAccessTime = System.currentTimeMillis();
        }
    }
}

# 修复登录重复点击和首页loading问题

## 问题描述
1. **登录重复点击问题**: 用户在登录过程中可能多次点击登录按钮，导致重复请求
2. **首页loading卡住问题**: 首页加载时一直显示loading状态，即使已经检测到登录状态也不显示内容

## 解决方案

### 1. 登录页面防重复点击
**文件**: `miniprogram/pages/login/index.js`

**修改内容**:
- 在data中添加`isLogging: false`状态标记
- 在`handleOneClickLogin`方法开始时检查`isLogging`状态
- 登录开始时设置`isLogging: true`
- 登录结束时（无论成功失败）重置`isLogging: false`

**关键代码**:
```javascript
// 防止重复点击
if (this.data.isLogging) {
  console.log('[Login] 正在登录中，忽略重复点击')
  return
}

// 设置登录状态
this.setData({ isLogging: true })

// 在finally块中重置状态
finally {
  this.setData({ isLogging: false })
  loadingManager.hide()
}
```

### 2. 首页loading状态优化
**文件**: `miniprogram/pages/index/index.js`

**修改内容**:
- 缩短初始化超时时间从15秒到3秒
- 添加兜底保护机制，3.5秒后强制显示内容
- 优化登录状态检测逻辑，有登录状态时立即显示页面
- 将登录检查改为异步执行，不阻塞页面显示

**关键改进**:
1. **立即显示策略**: 检测到登录状态时立即显示页面内容
2. **兜底保护**: 无论什么情况，最多3.5秒后强制显示内容
3. **异步加载**: 数据加载不阻塞页面显示
4. **错误容错**: 即使出错也显示基础内容

**核心逻辑**:
```javascript
if (token && userInfo?.nickName) {
  // 有登录状态，立即显示首页内容
  console.log('[Index] 检测到登录状态，立即显示页面内容')
  
  // 立即显示页面内容
  this.setData({
    pageLoading: false,
    contentVisible: true
  })
  
  // 异步加载其他数据，不阻塞页面显示
  this.loadPageDataAsync()
}
```

## 测试要点
1. 登录页面快速多次点击登录按钮，确认只执行一次登录请求
2. 首页在有登录状态时应立即显示内容，不出现长时间loading
3. 网络异常情况下，首页应在3.5秒内显示基础内容
4. 登录状态检查失败不应影响页面基础功能显示

## 预期效果
- 登录按钮在登录过程中不可重复点击
- 首页检测到登录状态时立即显示内容
- 即使出现异常情况，也不会出现永久loading状态
- 用户体验更加流畅，减少等待时间

## 附加修复：TokenManager Base64解码错误

### 问题描述
TokenManager在解析JWT token时出现base64解码错误：
```
DOMException: Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.
```

### 解决方案
**文件**: `miniprogram/utils/tokenManager.js`

**修改内容**:
1. **增强getRemainingTime方法**:
   - 添加token格式详细检查
   - 使用安全的base64解码方法
   - 增强错误处理和日志记录

2. **新增safeParseJWTPayload方法**:
   - 安全的JWT payload解析
   - Base64格式验证
   - JSON解析错误处理

3. **优化isTokenNearExpiry方法**:
   - 使用相同的安全解析逻辑
   - 确保错误处理一致性

4. **添加debugTokenInfo方法**:
   - 提供token调试信息
   - 便于问题诊断

**关键改进**:
- 防止base64解码异常导致的错误
- 提供详细的错误诊断信息
- 保持token管理功能的稳定性
- 增强开发调试能力

package com.ehome.oc.constants;

/**
 * 收费模块常量类
 */
public class ChargeConstants {

    /**
     * 资产类型
     */
    public static class AssetType {
        /** 房屋 */
        public static final int HOUSE = 1;
        /** 车位 */
        public static final int PARKING = 2;
        /** 车辆 */
        public static final int CAR = 3;
    }

    /**
     * 收费类型
     */
    public static class ChargeType {
        /** 周期性收费 */
        public static final int PERIODIC = 1;
        /** 走表收费 */
        public static final int METER = 2;
        /** 临时性收费 */
        public static final int TEMPORARY = 3;
    }

    /**
     * 金额计算方式
     */
    public static class CountType {
        /** 单价*计量 */
        public static final int UNIT_PRICE_MULTIPLY = 100;
        /** 固定金额 */
        public static final int FIXED_AMOUNT = 200;
    }

    /**
     * 取整方式
     */
    public static class RoundType {
        /** 四舍五入 */
        public static final int ROUND_HALF_UP = 0;
        /** 抹零 */
        public static final int ROUND_DOWN = 1;
        /** 向上取整 */
        public static final int ROUND_UP = 2;
    }

    /**
     * 计费精度
     */
    public static class Unit {
        /** 元 */
        public static final int YUAN = 1;
        /** 角 */
        public static final int JIAO = 2;
        /** 分 */
        public static final int FEN = 3;
    }

    /**
     * 面积类型
     */
    public static class AreaType {
        /** 建筑面积 */
        public static final int TOTAL_AREA = 1;
        /** 使用面积 */
        public static final int AREA = 2;
    }

    /**
     * 账单类型
     */
    public static class BillType {
        /** 手工账单 */
        public static final int MANUAL = 1;
        /** 补缴账单 */
        public static final int SUPPLEMENT = 2;
        /** 系统生成 */
        public static final int SYSTEM_GENERATED = 3;
    }

    /**
     * 支付状态
     */
    public static class PayStatus {
        /** 未缴 */
        public static final int UNPAID = 0;
        /** 已缴 */
        public static final int PAID = 1;
        /** 部分缴费 */
        public static final int PARTIAL_PAID = 2;
    }

    /**
     * 支付方式
     */
    public static class PayType {
        /** 未支付 */
        public static final int NONE = 0;
        /** 现金 */
        public static final int CASH = 1;
        /** 微信 */
        public static final int WECHAT = 2;
        /** 支付宝 */
        public static final int ALIPAY = 3;
        /** 银行转账 */
        public static final int BANK_TRANSFER = 4;
    }

    /**
     * 违约金处理
     */
    public static class LateMoneyType {
        /** 不计算 */
        public static final int NO_CALCULATE = 0;
        /** 计算违约金 */
        public static final int CALCULATE = 1;
    }

    /**
     * 收费方式
     */
    public static class PeriodType {
        /** 按月收费 */
        public static final int MONTHLY = 101;
        /** 按季度收费 */
        public static final int QUARTERLY = 102;
        /** 按年收费 */
        public static final int YEARLY = 103;
    }

    /**
     * 不足一月处理方式
     */
    public static class IncompleteMonthHandling {
        /** 按天收费 */
        public static final String BY_DAY = "1";
        /** 按月收费 */
        public static final String BY_MONTH = "2";
        /** 不收费 */
        public static final String NO_CHARGE = "3";
    }

    /**
     * 启用状态
     */
    public static class ActiveStatus {
        /** 禁用 */
        public static final int DISABLED = 0;
        /** 启用 */
        public static final int ENABLED = 1;
    }

    /**
     * 是否删除
     */
    public static class DeleteStatus {
        /** 未删除 */
        public static final int NOT_DELETED = 0;
        /** 已删除 */
        public static final int DELETED = 1;
    }

    /**
     * 默认值
     */
    public static class DefaultValue {
        /** 默认账单金额（分） */
        public static final long DEFAULT_BILL_AMOUNT = 10000L; // 100元
        /** 分转元的倍数 */
        public static final int FEN_TO_YUAN = 100;
    }

    /**
     * 资产类型显示名称
     */
    public static String getAssetTypeName(int assetType) {
        switch (assetType) {
            case AssetType.HOUSE: return "房屋";
            case AssetType.PARKING: return "车位";
            case AssetType.CAR: return "车辆";
            default: return "未知";
        }
    }

    /**
     * 收费类型显示名称
     */
    public static String getChargeTypeName(int chargeType) {
        switch (chargeType) {
            case ChargeType.PERIODIC: return "周期性收费";
            case ChargeType.METER: return "走表收费";
            case ChargeType.TEMPORARY: return "临时性收费";
            default: return "未知";
        }
    }

    /**
     * 支付状态显示名称
     */
    public static String getPayStatusName(int payStatus) {
        switch (payStatus) {
            case PayStatus.UNPAID: return "未缴";
            case PayStatus.PAID: return "已缴";
            case PayStatus.PARTIAL_PAID: return "部分缴费";
            default: return "未知";
        }
    }

    /**
     * 支付方式显示名称
     */
    public static String getPayTypeName(int payType) {
        switch (payType) {
            case PayType.NONE: return "";
            case PayType.CASH: return "现金";
            case PayType.WECHAT: return "微信";
            case PayType.ALIPAY: return "支付宝";
            case PayType.BANK_TRANSFER: return "银行转账";
            default: return "其他";
        }
    }

    /**
     * 账单类型显示名称
     */
    public static String getBillTypeName(int billType) {
        switch (billType) {
            case BillType.MANUAL: return "手工账单";
            case BillType.SUPPLEMENT: return "补缴账单";
            case BillType.SYSTEM_GENERATED: return "系统生成";
            default: return "未知";
        }
    }
}

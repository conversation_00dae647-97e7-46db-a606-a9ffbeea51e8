package com.ehome.oc.controller.wxconfig;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.SecurityUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/wx/tool")
public class WxToolController extends BaseController {

    /**
     * 动态设置强制下线日期
     * @param forceLogoutDate 强制下线日期，格式：yyyyMMdd，空值表示取消强制下线
     * @return 操作结果
     */
    @PostMapping("/setForceLogoutDate")
    public AjaxResult setForceLogoutDate(@RequestParam(value = "forceLogoutDate", required = false) String forceLogoutDate) {
        try {
            // 验证日期格式
            if (forceLogoutDate != null && !forceLogoutDate.trim().isEmpty()) {
                String dateStr = forceLogoutDate.trim();
                if (!dateStr.matches("^\\d{8}$")) {
                    return AjaxResult.error("日期格式错误，请使用yyyyMMdd格式，如：20250115");
                }

                // 验证日期有效性
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
                    sdf.setLenient(false); // 严格模式
                    sdf.parse(dateStr);
                } catch (Exception e) {
                    return AjaxResult.error("无效的日期：" + dateStr);
                }
            }

            // 更新强制下线日期
            SecurityUtils.updateForceLogoutDate(forceLogoutDate);

            String message = (forceLogoutDate == null || forceLogoutDate.trim().isEmpty())
                ? "已取消强制下线设置"
                : "强制下线日期已设置为：" + forceLogoutDate.trim();

            logger.info("管理员设置强制下线日期: {}", forceLogoutDate);
            return AjaxResult.success(message);

        } catch (Exception e) {
            logger.error("设置强制下线日期失败: " + e.getMessage(), e);
            return AjaxResult.error("设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前强制下线设置状态
     * @return 当前设置
     */
    @GetMapping("/getForceLogoutStatus")
    public AjaxResult getForceLogoutStatus() {
        try {
            // 通过反射获取当前日期设置
            java.lang.reflect.Field dateField = SecurityUtils.class.getDeclaredField("FORCE_LOGOUT_DATE");
            dateField.setAccessible(true);
            String currentDate = (String) dateField.get(null);

            // 获取开关状态
            boolean enabled = SecurityUtils.isForceLogoutEnabled();

            Map<String, Object> data = new HashMap<>();
            data.put("enabled", enabled);
            data.put("forceLogoutDate", currentDate);
            data.put("hasDate", currentDate != null && !currentDate.trim().isEmpty());
            data.put("effectiveStatus", enabled && currentDate != null && !currentDate.trim().isEmpty());

            return AjaxResult.success("获取成功", data);
        } catch (Exception e) {
            logger.error("获取强制下线状态失败: " + e.getMessage(), e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前强制下线日期设置（保持向后兼容）
     * @return 当前设置
     */
    @GetMapping("/getForceLogoutDate")
    public AjaxResult getForceLogoutDate() {
        try {
            // 通过反射获取当前设置（因为没有公开的getter方法）
            java.lang.reflect.Field field = SecurityUtils.class.getDeclaredField("FORCE_LOGOUT_DATE");
            field.setAccessible(true);
            String currentDate = (String) field.get(null);

            Map<String, Object> data = new HashMap<>();
            data.put("forceLogoutDate", currentDate);
            data.put("isEnabled", SecurityUtils.isForceLogoutEnabled());
            data.put("hasDate", currentDate != null && !currentDate.trim().isEmpty());

            return AjaxResult.success("获取成功", data);
        } catch (Exception e) {
            logger.error("获取强制下线日期失败: " + e.getMessage(), e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 设置强制下线功能开关
     * @param enabled 是否启用强制下线功能
     * @return 操作结果
     */
    @PostMapping("/setForceLogoutEnabled")
    public AjaxResult setForceLogoutEnabled(@RequestParam("enabled") boolean enabled) {
        try {
            SecurityUtils.updateForceLogoutEnabled(enabled);

            String message = enabled ? "强制下线功能已启用" : "强制下线功能已禁用";
            logger.info("管理员设置强制下线开关: {}", enabled);

            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("设置强制下线开关失败: " + e.getMessage(), e);
            return AjaxResult.error("设置失败: " + e.getMessage());
        }
    }

    /**
     * 统一设置强制下线功能（开关+日期）
     * @param enabled 是否启用强制下线功能
     * @param forceLogoutDate 强制下线日期，格式：yyyyMMdd，可选
     * @return 操作结果
     */
    @PostMapping("/setForceLogout")
    public AjaxResult setForceLogout(
            @RequestParam("enabled") boolean enabled,
            @RequestParam(value = "forceLogoutDate", required = false) String forceLogoutDate) {
        try {
            // 设置开关
            SecurityUtils.updateForceLogoutEnabled(enabled);

            // 如果提供了日期，则更新日期
            if (forceLogoutDate != null && !forceLogoutDate.trim().isEmpty()) {
                String dateStr = forceLogoutDate.trim();
                if (!dateStr.matches("^\\d{8}$")) {
                    return AjaxResult.error("日期格式错误，请使用yyyyMMdd格式，如：20250115");
                }

                // 验证日期有效性
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
                    sdf.setLenient(false);
                    sdf.parse(dateStr);
                } catch (Exception e) {
                    return AjaxResult.error("无效的日期：" + dateStr);
                }

                SecurityUtils.updateForceLogoutDate(dateStr);
            }

            String message = enabled
                ? (forceLogoutDate != null ? "强制下线功能已启用，日期设置为：" + forceLogoutDate : "强制下线功能已启用")
                : "强制下线功能已禁用";

            logger.info("管理员统一设置强制下线: enabled={}, date={}", enabled, forceLogoutDate);
            return AjaxResult.success(message);

        } catch (Exception e) {
            logger.error("统一设置强制下线失败: " + e.getMessage(), e);
            return AjaxResult.error("设置失败: " + e.getMessage());
        }
    }
}
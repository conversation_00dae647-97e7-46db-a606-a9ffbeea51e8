-- 数据库优化脚本
-- 解决金额字段类型不一致和时间字段类型不统一的问题

-- =====================================================
-- 1. 金额字段优化
-- =====================================================

-- 备份现有数据（可选）
-- CREATE TABLE eh_charge_bill_backup AS SELECT * FROM eh_charge_bill;

-- 修改 eh_charge_bill 表的金额字段类型
-- 从 bigint(20) 改为 decimal(10,2)，并将分转换为元

-- 1.1 添加临时列存储转换后的金额（元）
ALTER TABLE eh_charge_bill 
ADD COLUMN amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '账单金额（元）',
ADD COLUMN bill_amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '应收金额（元）',
ADD COLUMN discount_amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额（元）',
ADD COLUMN late_money_amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '滞纳金金额（元）',
ADD COLUMN deposit_amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '押金金额（元）',
ADD COLUMN second_pay_amount_decimal DECIMAL(10,2) DEFAULT 0.00 COMMENT '第二支付金额（元）';

-- 1.2 将分转换为元（除以100）
UPDATE eh_charge_bill SET 
amount_decimal = amount / 100.0,
bill_amount_decimal = bill_amount / 100.0,
discount_amount_decimal = discount_amount / 100.0,
late_money_amount_decimal = late_money_amount / 100.0,
deposit_amount_decimal = deposit_amount / 100.0,
second_pay_amount_decimal = second_pay_amount / 100.0;

-- 1.3 删除旧的金额字段
ALTER TABLE eh_charge_bill 
DROP COLUMN amount,
DROP COLUMN bill_amount,
DROP COLUMN discount_amount,
DROP COLUMN late_money_amount,
DROP COLUMN deposit_amount,
DROP COLUMN second_pay_amount;

-- 1.4 重命名新字段
ALTER TABLE eh_charge_bill 
CHANGE COLUMN amount_decimal amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '账单金额（元）',
CHANGE COLUMN bill_amount_decimal bill_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '应收金额（元）',
CHANGE COLUMN discount_amount_decimal discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠金额（元）',
CHANGE COLUMN late_money_amount_decimal late_money_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '滞纳金金额（元）',
CHANGE COLUMN deposit_amount_decimal deposit_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '押金金额（元）',
CHANGE COLUMN second_pay_amount_decimal second_pay_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '第二支付金额（元）';

-- =====================================================
-- 2. 时间字段优化
-- =====================================================

-- 2.1 统一 eh_charge_bill 表的时间字段类型
-- 将 update_time 从 bigint(20) 改为 varchar(19)
ALTER TABLE eh_charge_bill 
ADD COLUMN update_time_varchar VARCHAR(19) DEFAULT '' COMMENT '更新时间';

-- 转换现有的 update_time 数据（从时间戳转为字符串格式）
UPDATE eh_charge_bill 
SET update_time_varchar = FROM_UNIXTIME(update_time, '%Y-%m-%d %H:%i:%s')
WHERE update_time IS NOT NULL AND update_time > 0;

-- 删除旧字段，重命名新字段
ALTER TABLE eh_charge_bill 
DROP COLUMN update_time;

ALTER TABLE eh_charge_bill 
CHANGE COLUMN update_time_varchar update_time VARCHAR(19) DEFAULT '' COMMENT '更新时间';

-- 2.2 为了保持一致性，也可以考虑将其他表的时间字段统一
-- 但这需要根据实际业务需求决定，这里提供参考方案：

-- 选项A：将所有时间字段统一为 VARCHAR(19) 格式
-- ALTER TABLE eh_charge_standard 
-- MODIFY COLUMN created_at VARCHAR(19) DEFAULT '' COMMENT '创建时间',
-- MODIFY COLUMN updated_at VARCHAR(19) DEFAULT '' COMMENT '更新时间';

-- ALTER TABLE eh_charge_binding 
-- MODIFY COLUMN create_time VARCHAR(19) DEFAULT '' COMMENT '创建时间',
-- MODIFY COLUMN update_time VARCHAR(19) DEFAULT '' COMMENT '更新时间';

-- 选项B：将所有时间字段统一为 DATETIME 格式（推荐）
ALTER TABLE eh_charge_bill 
MODIFY COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
MODIFY COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
MODIFY COLUMN last_op_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后操作时间';

-- =====================================================
-- 3. 添加索引优化（可选）
-- =====================================================

-- 为金额字段添加索引（如果需要按金额查询）
CREATE INDEX idx_charge_bill_amount ON eh_charge_bill(amount);
CREATE INDEX idx_charge_bill_pay_status_amount ON eh_charge_bill(pay_status, amount);

-- 为时间字段添加索引
CREATE INDEX idx_charge_bill_create_time ON eh_charge_bill(create_time);
CREATE INDEX idx_charge_bill_update_time ON eh_charge_bill(update_time);

-- =====================================================
-- 4. 验证数据完整性
-- =====================================================

-- 检查金额转换是否正确
SELECT 
    id,
    amount,
    bill_amount,
    discount_amount,
    late_money_amount,
    deposit_amount,
    second_pay_amount
FROM eh_charge_bill 
LIMIT 10;

-- 检查时间字段格式
SELECT 
    id,
    create_time,
    update_time,
    last_op_time
FROM eh_charge_bill 
LIMIT 10;

-- =====================================================
-- 5. 更新相关视图和存储过程（如果有）
-- =====================================================

-- 注意：执行此脚本后，需要更新相关的：
-- 1. Java代码中的金额计算逻辑（不再需要乘以100）
-- 2. 前端显示逻辑
-- 3. 报表和统计查询
-- 4. 数据导入导出脚本

-- =====================================================
-- 6. 回滚方案（紧急情况使用）
-- =====================================================

-- 如果需要回滚，可以执行以下操作：
-- 1. 恢复备份表数据
-- 2. 或者将 decimal 字段重新转换为 bigint（乘以100）

/*
-- 回滚金额字段的示例：
ALTER TABLE eh_charge_bill 
ADD COLUMN amount_bigint BIGINT(20) DEFAULT 0;

UPDATE eh_charge_bill 
SET amount_bigint = amount * 100;

ALTER TABLE eh_charge_bill 
DROP COLUMN amount;

ALTER TABLE eh_charge_bill 
CHANGE COLUMN amount_bigint amount BIGINT(20) NOT NULL DEFAULT 0;
*/

/* 邀请住户页面样式 */
page {
  background: #f5f5f5;
  min-height: 100vh;
}

/* CSS变量定义 */
:root {
  --primary-color: #6366f1;
  --primary-light: #a5b4fc;
  --primary-dark: #4f46e5;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --bg-white: #ffffff;
  --bg-gray: #f8fafc;
  --border-color: #e5e7eb;
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  --shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;
}

.page-container {
  padding: 0 24rpx 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 顶部Banner */
.page-banner {
  background: linear-gradient(135deg, var(--primary-color) 0%, #8b5cf6 100%);
  margin: 0 -24rpx 32rpx;
  padding: 60rpx 24rpx 40rpx;
  position: relative;
  overflow: hidden;
}

.page-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.banner-content {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.banner-icon {
  font-size: 64rpx;
  line-height: 1;
}

.banner-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.banner-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

.banner-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 通用卡片样式 */
.info-card,
.settings-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e5e7eb;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 房屋信息样式 */
.house-info-content {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e5e7eb;
}

.house-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f1f5f9;
}

.house-item:last-child {
  border-bottom: none;
}

.house-label {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
  width: 120rpx;
  flex-shrink: 0;
}

.house-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
  flex: 1;
  text-align: left;
}

/* 表单样式 */
.form-section {
  margin-bottom: 40rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20rpx;
}

.required {
  color: var(--error-color);
  font-size: 28rpx;
}

.optional {
  font-size: 26rpx;
  font-weight: 400;
  color: var(--text-tertiary);
}

/* 手机号输入框 */
.phone-input-wrapper {
  position: relative;
}

.phone-input-wrapper .van-field {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.phone-input-wrapper .van-field:focus-within {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
}

.phone-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: var(--error-color);
}

/* 关系类型选择 */
.relation-types {
  display: flex;
  gap: 20rpx;
}

.relation-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.relation-item.active {
  background: rgba(99, 102, 241, 0.05);
  border-color: var(--primary-color);
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-sm);
}

.relation-icon {
  font-size: 48rpx;
  line-height: 1;
}

.relation-text {
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-primary);
}

/* 字符计数 */
.char-count {
  text-align: right;
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-top: 12rpx;
}



/* 操作区域 */
.action-section {
  margin-top: 60rpx;
  padding-bottom: 40rpx;
}

/* 分享弹窗 */
.share-modal {
  padding: 40rpx 32rpx;
}

.success-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.success-icon {
  font-size: 80rpx;
  line-height: 1;
  margin-bottom: 16rpx;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 邀请预览 */
.invite-preview {
  margin-bottom: 40rpx;
}

.preview-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  text-align: left;
  border: 1rpx solid var(--border-color);
}

.preview-header {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: var(--bg-gray);
  border-bottom: 1rpx solid var(--border-color);
}

.app-name {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 600;
}

.preview-content {
  padding: 32rpx 24rpx;
}

.invite-title {
  font-size: 32rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.invite-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-primary);
  font-weight: 600;
}



/* 分享操作 */
.share-actions {
  margin-bottom: 32rpx;
}

.share-btn-single {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.share-btn-single::after {
  border: none;
}

.share-btn-single:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.3);
}

/* 弹窗提示 */
.modal-tips {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
  margin-top: 24rpx;
}

.tip-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  color: #6b7280;
}



/* Vant组件样式覆盖 */
.van-field {
  padding: 20rpx !important;
  background: transparent !important;
}

.van-field__input {
  font-size: 30rpx !important;
  color: var(--text-primary) !important;
}

.van-field__placeholder {
  color: var(--text-tertiary) !important;
}

.van-button {
  border-radius: var(--radius-md) !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

.van-button--large {
  height: 88rpx !important;
  font-size: 32rpx !important;
}

.van-popup__close-icon {
  color: var(--text-tertiary) !important;
  font-size: 32rpx !important;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .page-container {
    padding: 0 20rpx 40rpx;
  }

  .banner-title {
    font-size: 36rpx;
  }

  .relation-types {
    flex-direction: column;
    gap: 12rpx;
  }

  .relation-item {
    flex-direction: row;
    justify-content: flex-start;
    padding: 20rpx;
  }
}

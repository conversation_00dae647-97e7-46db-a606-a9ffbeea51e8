-- 为eh_house_info表添加欠费金额字段
-- 执行时间：2025-01-27

-- 添加欠费金额字段
ALTER TABLE `eh_house_info` 
ADD COLUMN `arrear_amount` decimal(10,2) NULL DEFAULT 0.00 COMMENT '欠费金额' AFTER `owner_str`;

-- 初始化现有房屋的欠费金额
-- 根据未缴费账单计算每个房屋的欠费总额
UPDATE eh_house_info h
SET arrear_amount = (
    SELECT COALESCE(SUM(cb.bill_amount), 0.00)
    FROM eh_charge_bill cb
    WHERE cb.asset_type = 1
    AND cb.asset_id = h.house_id
    AND cb.pay_status = 0
    AND cb.is_bad_bill = 0
);

-- 验证数据
SELECT 
    house_id,
    combina_name,
    room,
    arrear_amount,
    (SELECT COUNT(*) FROM eh_charge_bill cb WHERE cb.asset_type = 1 AND cb.asset_id = h.house_id AND cb.pay_status = 0 AND cb.is_bad_bill = 0) as unpaid_bill_count
FROM eh_house_info h 
WHERE arrear_amount > 0 
ORDER BY arrear_amount DESC 
LIMIT 10;

# 文件管理功能说明

## 功能概述

本文件管理功能提供了简单易用的文件上传、存储、管理和选择功能，主要用于其他模块选择已上传的文件。

## 功能特点

- ✅ 文件上传并自动记录到数据库
- ✅ 文件列表展示和搜索
- ✅ 文件选择弹窗（供其他模块使用）
- ✅ 文件预览和下载
- ✅ 文件逻辑删除
- ✅ 支持多种文件类型
- ✅ 集成现有上传接口

## 数据库设计

### 文件信息表 (eh_file_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| file_id | varchar(32) | 文件ID（主键）|
| original_name | varchar(255) | 原始文件名 |
| file_name | varchar(255) | 存储文件名 |
| file_path | varchar(500) | 文件路径 |
| file_url | varchar(500) | 访问URL |
| file_size | bigint(20) | 文件大小（字节）|
| file_type | varchar(50) | 文件扩展名 |
| mime_type | varchar(100) | MIME类型 |
| upload_user | varchar(64) | 上传用户 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| status | char(1) | 状态（0正常 1删除）|
| remark | varchar(500) | 备注 |

## API接口

### 文件管理接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/file/list` | GET | 文件管理页面 |
| `/file/list` | POST | 查询文件列表 |
| `/file/select` | GET | 文件选择页面 |
| `/file/upload` | POST | 文件上传 |
| `/file/detail` | POST | 获取文件详情 |
| `/file/delete` | POST | 删除文件 |
| `/file/typeStats` | POST | 文件类型统计 |
| `/file/sizeStats` | POST | 文件大小统计 |

### 通用上传接口（已扩展）

| 接口 | 方法 | 说明 |
|------|------|------|
| `/common/upload` | POST | 单文件上传（已集成文件信息保存）|
| `/common/uploads` | POST | 多文件上传（已集成文件信息保存）|

## 使用方法

### 1. 文件管理页面

访问 `/file/list` 可以进入文件管理页面，支持：
- 文件上传
- 文件列表查看
- 文件搜索和筛选
- 文件删除
- 文件预览和下载

### 2. 独立的文件组件

#### 文件选择器
- 访问路径：`/common/file/select`
- 功能：从已上传的文件中选择
- 用途：供其他模块弹窗选择文件

#### 文件上传器
- 访问路径：`/common/file/upload`
- 功能：上传新文件
- 用途：供其他模块弹窗上传文件

### 3. 在其他页面中集成文件功能

#### HTML代码：
```html
<div class="input-group">
    <input type="text" id="selectedFileUrl" class="form-control" placeholder="请选择文件" readonly>
    <input type="hidden" id="selectedFileId" name="fileId">
    <span class="input-group-btn">
        <button class="btn btn-primary" type="button" onclick="openFileSelector()">
            <i class="fa fa-folder-open"></i> 选择文件
        </button>
        <button class="btn btn-success" type="button" onclick="openFileUploader()">
            <i class="fa fa-upload"></i> 上传新文件
        </button>
    </span>
</div>
```

#### JavaScript代码：
```javascript
// 打开文件选择器
function openFileSelector() {
    layer.open({
        type: 2,
        title: '选择文件',
        shadeClose: true,
        shade: 0.8,
        area: ['70%', '70%'],
        content: ctx + 'common/file/select'
    });
}

// 打开文件上传器
function openFileUploader() {
    layer.open({
        type: 2,
        title: '上传文件',
        shadeClose: true,
        shade: 0.8,
        area: ['60%', '60%'],
        content: ctx + 'common/file/upload'
    });
}

// 文件选择回调函数（必须定义）
function setSelectedFile(fileUrl, fileName, fileId) {
    $('#selectedFileUrl').val(fileName);
    $('#selectedFileId').val(fileId);
    // 可以在这里添加文件预览逻辑
}

// 文件上传后刷新回调函数（可选）
function refreshFileList() {
    // 上传完成后可以在这里做一些处理
}
```

### 3. 示例页面

访问 `/demo/file/example.html` 可以查看完整的使用示例。

## 支持的文件类型

- **图片**：jpg, jpeg, png, gif, bmp
- **文档**：pdf, doc, docx, xls, xlsx, txt
- **压缩包**：zip, rar

## 文件大小限制

- 单个文件最大：20MB
- 批量上传总大小：50MB

## 部署说明

### 1. 执行数据库脚本

执行 `sql/file_management.sql` 创建文件信息表。

### 2. 确保文件上传目录权限

确保配置的文件上传目录（`ruoyi.profile`）有读写权限。

### 3. 访问权限配置

根据需要配置文件管理页面的访问权限。

## 技术实现

- **后端**：Spring Boot + JFinal Db
- **前端**：Thymeleaf + Bootstrap + jQuery
- **文件上传**：Bootstrap FileInput
- **弹窗**：Layer
- **数据库**：MySQL

## 注意事项

1. 文件删除采用逻辑删除，不会真正删除物理文件
2. 文件信息保存失败不会影响文件上传的正常流程
3. 匿名用户上传的文件，上传用户记录为 "anonymous"
4. 建议定期清理逻辑删除的文件记录和对应的物理文件

## 扩展建议

1. 添加文件分类功能
2. 添加文件标签系统
3. 添加文件版本控制
4. 添加文件权限管理
5. 添加文件预览功能增强
6. 添加文件压缩和缩略图生成

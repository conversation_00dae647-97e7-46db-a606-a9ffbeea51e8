# Markicam API数据同步数据库设计文档

## 概述

本文档描述了用于同步Markicam开放平台数据的数据库表结构设计和实施方案。

## 表结构设计

### 1. eh_markicam_config (配置表)
存储Markicam API的配置信息，支持多社区配置。

**主要字段：**
- `org_id`: Markicam组织ID
- `api_key`: API密钥
- `base_url`: API基础URL
- `sync_interval`: 同步间隔(秒)
- `last_sync_time`: 最后同步时间

### 2. eh_markicam_team (团队表)
存储团队基础信息，从团队列表API获取。

**主要字段：**
- `team_id`: Markicam团队ID
- `team_name`: 团队名称
- `member_count`: 成员总数
- `reg_member_count`: 已注册成员数
- `unreg_member_count`: 未注册成员数

### 3. eh_markicam_moment (照片视频表)
存储组织/团队的照片和视频数据。

**主要字段：**
- `markicam_id`: Markicam照片/视频唯一ID
- `uid`: 用户ID
- `team_id`: 团队ID
- `url`: 照片/视频链接
- `moment_type`: 动态类型(1:照片 2:视频)
- `content`: 水印内容
- `lng/lat`: 地理位置坐标
- `post_time`: 上传时间戳

### 4. eh_markicam_member (成员表)
存储团队成员信息。

**主要字段：**
- `uid`: Markicam用户ID
- `team_id`: 团队ID
- `nickname`: 昵称
- `phone`: 电话
- `member_type`: 成员类型
- `join_time`: 加入时间戳

### 5. eh_markicam_illegal_park (违规车辆表)
存储违规车辆信息。

**主要字段：**
- `team_id`: 团队ID
- `report_uid`: 上报人ID
- `car_plate`: 车牌号
- `car_picture`: 车辆照片
- `report_time`: 上报时间戳

### 6. eh_markicam_sync_log (同步日志表)
记录数据同步的执行情况。

**主要字段：**
- `sync_type`: 同步类型
- `sync_status`: 同步状态
- `sync_count`: 同步数量
- `error_msg`: 错误信息
- `duration`: 耗时

## 执行步骤

### 1. 执行SQL脚本
```bash
# 方式1: 直接在MySQL客户端执行
mysql -u username -p database_name < sql/create_markicam_tables.sql

# 方式2: 在MySQL命令行中执行
mysql> source sql/create_markicam_tables.sql;
```

### 2. 验证表创建
```sql
-- 查看创建的表
SHOW TABLES LIKE 'eh_markicam%';

-- 查看表结构
DESC eh_markicam_config;
DESC eh_markicam_team;
DESC eh_markicam_moment;
DESC eh_markicam_member;
DESC eh_markicam_illegal_park;
DESC eh_markicam_sync_log;
```

### 3. 配置API信息
```sql
-- 更新配置表中的API信息
UPDATE eh_markicam_config 
SET org_id = '你的组织ID', 
    api_key = '你的API密钥'
WHERE community_id = 'test_community';
```

## 数据同步策略

### 1. 增量同步
- 使用`last_sync_time`字段记录最后同步时间
- 每次同步时传入时间范围参数
- 避免重复同步已有数据

### 2. 去重策略
- 照片视频表：使用`markicam_id`唯一索引
- 成员表：使用`community_id + uid + team_id`联合唯一索引
- 违规车辆表：允许重复记录（同一车辆可能多次违规）

### 3. 错误处理
- 记录同步日志到`eh_markicam_sync_log`表
- 失败时记录错误信息，便于排查
- 支持重试机制

## API接口映射

### 1. 照片视频接口 (/marki/moment)
```
API字段 -> 数据库字段
id -> markicam_id
uid -> uid
teamId -> team_id
url -> url
momentType -> moment_type
content -> content
markName -> mark_name
lng -> lng
lat -> lat
postTime -> post_time
```

### 2. 成员列表接口 (/marki/team/mem)
```
API字段 -> 数据库字段
uid -> uid
nickname -> nickname
phone -> phone
joinTime -> join_time
memberType -> member_type
```

### 3. 违规车辆接口 (/marki/illegal_park)
```
API字段 -> 数据库字段
teamId -> team_id
reportUID -> report_uid
carPlate -> car_plate
carPicture -> car_picture
createTime -> report_time
```

## 注意事项

1. **时间戳转换**: API返回秒级时间戳，需要转换为标准时间格式
2. **字符编码**: 确保数据库使用utf8mb4字符集支持emoji等特殊字符
3. **索引优化**: 为常用查询字段添加索引提升性能
4. **数据安全**: API密钥等敏感信息需要加密存储
5. **同步频率**: 根据业务需求合理设置同步间隔，避免频繁调用API

# Token强制下线功能实现

## 功能概述

实现了基于日期的Token强制下线功能，当Token的签发时间早于指定的强制下线日期时，该Token将被视为失效，用户需要重新登录。

## 实现方案

### 1. 配置参数
在yml配置文件中添加了`force-logout-date`参数：
- **格式**：yyyyMMdd（如：20250115）
- **默认值**：空字符串（不启用强制下线）
- **配置位置**：application-dev.yml、application-test.yml、application-prod.yml

```yaml
token:
  expire-time: 2592000000
  refresh-threshold: 604800000
  auto-refresh-enabled: true
  secret: ehomeDevSecretKey2025!@#
  force-logout-date: ""     # 强制下线日期，格式：yyyyMMdd，空值表示不启用强制下线
```

### 2. 核心实现

#### SecurityUtils.java 修改
- ✅ 添加`forceLogoutDate`配置属性和静态变量
- ✅ 修改`createToken()`方法添加`.setIssuedAt(new Date())`记录签发时间
- ✅ 新增`checkTokenForceLogout(String token)`方法检查是否强制下线
- ✅ 新增`updateForceLogoutDate(String newDate)`方法支持动态更新
- ✅ 修改`getLoginUser()`方法集成强制下线检查

#### WxTokenInterceptor.java 修改
- ✅ 修改异常处理，支持返回具体的错误消息
- ✅ 当Token被强制下线时，返回"登录失效"消息

#### WechatAuthController.java 新增接口
- ✅ `POST /api/wx/auth/setForceLogoutDate` - 设置强制下线日期
- ✅ `GET /api/wx/auth/getForceLogoutDate` - 获取当前设置

## 使用方法

### 1. 通过yml配置（需要重启服务）
```yaml
token:
  force-logout-date: "20250115"  # 2025年1月15日之前签发的Token全部失效
```

### 2. 通过API动态设置（无需重启）

#### 设置强制下线开关
```bash
POST /api/wx/auth/setForceLogoutEnabled
Content-Type: application/x-www-form-urlencoded

enabled=true
```

#### 设置强制下线日期
```bash
POST /api/wx/auth/setForceLogoutDate
Content-Type: application/x-www-form-urlencoded

forceLogoutDate=20250115
```

#### 统一设置（开关+日期）
```bash
POST /api/wx/auth/setForceLogout
Content-Type: application/x-www-form-urlencoded

enabled=true&forceLogoutDate=20250115
```

#### 查询当前设置
```bash
GET /api/wx/auth/getForceLogoutStatus
```

#### 查询日期设置（向后兼容）
```bash
GET /api/wx/auth/getForceLogoutDate
```

## 强制下线功能开关

### 配置结构
```yaml
token:
  force-logout-enabled: false           # 强制下线功能开关，默认关闭
  force-logout-date: "@build.date@"     # 强制下线日期，自动设置为打包日期
```

### 逻辑优先级
1. 首先检查 `force-logout-enabled` 是否为true
2. 如果开关关闭，直接返回false（不强制下线）
3. 如果开关开启，再检查日期逻辑

### 使用场景
- **正常发版**：开关默认关闭，不影响用户，日期自动设置但不生效
- **需要强制下线**：通过API开启开关，立即生效
- **紧急情况**：可以同时修改日期和开关

## 技术要点

### 1. 向后兼容
- 对于没有`iat`（签发时间）字段的旧Token，跳过强制下线检查
- 不影响现有的Token过期和刷新机制
- 保持原有API接口的兼容性

### 2. 错误处理
- 开关关闭时，即使有日期也不启用强制下线
- 日期格式错误时，返回明确的错误提示
- Token被强制下线时，返回401状态码和"登录失效"消息

### 3. 性能考虑
- 开关检查在日期检查之前，提高效率
- 强制下线检查在Token基本验证通过后进行
- 日期比较操作性能开销极小
- 异常情况下默认不强制下线，确保系统稳定性

## 测试场景

### 1. 基础功能测试
- [ ] 配置为空时，Token正常使用
- [ ] 设置强制下线日期后，旧Token失效
- [ ] 新生成的Token正常使用
- [ ] 取消强制下线后，功能恢复正常

### 2. API接口测试
- [ ] 设置有效日期格式（yyyyMMdd）
- [ ] 设置无效日期格式，返回错误
- [ ] 设置空值取消强制下线
- [ ] 查询当前设置状态

### 3. 异常处理测试
- [ ] 日期解析异常时的处理
- [ ] Token解析失败时的处理
- [ ] 反射获取配置失败时的处理

## 发版使用场景

1. **发版前准备**：通过API设置强制下线日期为发版时间
2. **发版部署**：正常部署新版本
3. **强制重新登录**：所有用户的旧Token自动失效，需要重新登录
4. **验证完成**：确认用户都使用新版本后，可以取消强制下线设置

## Maven自动打包日期功能

### 实现原理
使用`buildnumber-maven-plugin`插件在Maven打包时自动生成当前日期，并通过资源过滤机制替换yml配置文件中的占位符。

### 配置详情
**ehome-web/pom.xml**：
```xml
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>buildnumber-maven-plugin</artifactId>
    <version>3.2.0</version>
    <executions>
        <execution>
            <phase>validate</phase>
            <goals>
                <goal>create-timestamp</goal>
            </goals>
        </execution>
    </executions>
    <configuration>
        <timestampFormat>yyyyMMdd</timestampFormat>
        <timestampPropertyName>build.date</timestampPropertyName>
    </configuration>
</plugin>
```

**yml配置文件**：
```yaml
token:
  force-logout-date: "@build.date@"     # Maven打包时自动替换为打包日期
```

### 使用效果
- 每次执行`mvn clean package`时，自动将当天日期写入force-logout-date
- 例如：2025年7月9日打包，自动设置为`force-logout-date: "20250709"`
- 所有环境（dev、test、prod）都会自动设置相同的打包日期

### 验证方法
1. 执行打包命令：`mvn clean package -P prod -DskipTests`
2. 检查生成的jar包：`jar -xf ehome.jar BOOT-INF/classes/application-prod.yml`
3. 确认force-logout-date已替换为实际日期

## 注意事项

1. 强制下线日期使用yyyyMMdd格式，不包含时分秒
2. Maven打包时会自动设置为打包日期，也可通过API动态修改
3. 动态设置的日期在服务重启后会恢复为打包时的日期
4. 建议在非业务高峰期进行强制下线操作
5. 强制下线会影响所有用户，请谨慎使用
6. 每次发版打包都会自动更新强制下线日期，实现自动化版本控制

package com.ehome.common.utils;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.exception.ServiceException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class SecurityUtils {

    private static final Logger logger = LoggerFactory.getLogger(SecurityUtils.class);

    /**
     * 密钥
     */
    @Value("${token.secret:ehomeSecretKey}")
    private String secretKey;

    /**
     * token有效期（毫秒）
     */
    @Value("${token.expire-time:86400000}")
    private long expireTime;

    /**
     * token刷新阈值（毫秒），当token剩余有效期小于此值时自动刷新
     */
    @Value("${token.refresh-threshold:7200000}")
    private long refreshThreshold;

    /**
     * Bearer前缀
     */
    @Value("${token.token-prefix:Bearer }")
    private String tokenPrefix;

    /**
     * 是否启用自动刷新
     */
    @Value("${token.auto-refresh-enabled:true}")
    private boolean autoRefreshEnabled;

    /**
     * 是否启用强制下线功能
     */
    @Value("${token.force-logout-enabled:false}")
    private boolean forceLogoutEnabled;

    /**
     * 强制下线日期，格式：yyyyMMdd
     */
    @Value("${token.force-logout-date:}")
    private String forceLogoutDate;

    // 静态变量，用于静态方法访问
    private static volatile String SECRET = "ehomeSecretKey"; // 默认值，防止初始化前调用
    private static volatile long EXPIRE_TIME = 24 * 60 * 60 * 1000; // 默认24小时
    private static volatile long REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // 默认2小时
    private static volatile String TOKEN_PREFIX = "Bearer "; // 默认前缀
    private static volatile boolean AUTO_REFRESH_ENABLED = true; // 默认启用
    private static volatile boolean FORCE_LOGOUT_ENABLED = false; // 强制下线功能开关
    private static volatile String FORCE_LOGOUT_DATE = ""; // 强制下线日期

    // 单例实例，确保配置已加载
    private static SecurityUtils instance;

    @PostConstruct
    public void init() {
        SECRET = this.secretKey;
        EXPIRE_TIME = this.expireTime;
        REFRESH_THRESHOLD = this.refreshThreshold;
        TOKEN_PREFIX = this.tokenPrefix;
        AUTO_REFRESH_ENABLED = this.autoRefreshEnabled;
        FORCE_LOGOUT_ENABLED = this.forceLogoutEnabled;
        FORCE_LOGOUT_DATE = this.forceLogoutDate;
        instance = this;
    }

    /**
     * 获取配置实例，确保配置已加载
     */
    private static void ensureConfigLoaded() {
        if (instance == null) {
            // 如果Spring容器还未初始化，使用默认配置
            // 这种情况通常发生在单元测试或特殊场景下
            synchronized (SecurityUtils.class) {
                if (instance == null) {
                    // 使用默认配置
                    SECRET = "ehomeSecretKey";
                    EXPIRE_TIME = 24 * 60 * 60 * 1000;
                    REFRESH_THRESHOLD = 2 * 60 * 60 * 1000;
                    TOKEN_PREFIX = "Bearer ";
                    AUTO_REFRESH_ENABLED = true;
                    FORCE_LOGOUT_ENABLED = false;
                    FORCE_LOGOUT_DATE = "";
                }
            }
        }
    }
    
    /**
     * 解析token获取Claims
     * @param token JWT token
     * @return Claims对象，如果解析失败返回null
     */
    private static Claims parseToken(String token) {
        try {
            ensureConfigLoaded();

            // 如果token以Bearer 开头，去掉这个前缀
            if (token != null && token.startsWith(TOKEN_PREFIX)) {
                token = token.substring(TOKEN_PREFIX.length());
            }

            return Jwts.parser()
                    .setSigningKey(SECRET)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从token中获取用户信息
     */
    public static LoginUser getLoginUser(String token) {
        try {
            Claims claims = parseToken(token);
            if (claims == null) {
                logger.error("Token解析异常: {}", token);
                throw new ServiceException("token解析失败>"+token);
            }

            // 检查是否应该强制下线
            if (checkTokenForceLogout(token)) {
                logger.warn("Token已被强制下线: {}", token);
                throw new ServiceException("登录失效");
            }

            LoginUser loginUser = new LoginUser();

            // 获取并安全处理claims中的值
            Object userId = claims.get("userId");
            if (userId != null) {
                loginUser.setUserId(Long.valueOf(userId.toString()));
            }

            Object openid = claims.get("openId");
            if (openid != null) {
                loginUser.setOpenId(openid.toString());
            }

            Object communityId = claims.get("communityId");
            if (communityId != null) {
                loginUser.setCommunityId(communityId.toString());
            }

            Object username = claims.get("username");
            if (username != null) {
                loginUser.setUsername(username.toString());
            }

            Object mobile = claims.get("mobile");
            if (mobile != null) {
                loginUser.setMobile(mobile.toString());
            }

            Object userType = claims.get("userType");
            if (userType != null) {
                loginUser.setUserType(userType.toString());
            }

            Object ownerId = claims.get("ownerId");
            if (ownerId != null) {
                loginUser.setOwnerId(ownerId.toString());
            }

            Object houseId = claims.get("houseId");
            if (houseId != null) {
                loginUser.setHouseId(houseId.toString());
            }

            Object houseName = claims.get("houseName");
            if (houseName != null) {
                loginUser.setHouseName(houseName.toString());
            }

            // 保存原始token（不含Bearer前缀）
            if (token != null && token.startsWith(TOKEN_PREFIX)) {
                loginUser.setToken(token.substring(TOKEN_PREFIX.length()));
            } else {
                loginUser.setToken(token);
            }

            return loginUser;
        } catch (Exception e) {
            logger.error("Token解析异常: {}", e.getMessage(), e);
            throw new ServiceException("token解析失败: " + e.getMessage()+">"+token);
        }
    }
    
    /**
     * 生成token
     */
    public static String createToken(LoginUser loginUser) {
        ensureConfigLoaded();

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", loginUser.getUserId());
        claims.put("openId", loginUser.getOpenId());
        claims.put("username", loginUser.getUsername());
        claims.put("mobile", loginUser.getMobile());
        claims.put("userType", loginUser.getUserType());
        claims.put("ownerId", loginUser.getOwnerId());
        claims.put("communityId", loginUser.getCommunityId());
        claims.put("houseId", loginUser.getHouseId());
        claims.put("houseName", loginUser.getHouseName());

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new java.util.Date()) // 添加签发时间
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .setExpiration(new java.util.Date(System.currentTimeMillis() + EXPIRE_TIME))
                .compact();
    }

    /**
     * 验证token是否有效
     */
    public static boolean validateToken(String token) {
        Claims claims = parseToken(token);
        return claims != null;
    }

    /**
     * 检查token是否需要刷新
     * @param token JWT token
     * @return true表示需要刷新，false表示不需要或token无效
     */
    public static boolean needsRefresh(String token) {
        Claims claims = parseToken(token);
        if (claims == null) {
            return false; // token无效，不需要刷新
        }

        ensureConfigLoaded();

        Date expiration = claims.getExpiration();
        long currentTime = System.currentTimeMillis();
        long expirationTime = expiration.getTime();

        // 如果剩余有效期小于刷新阈值，则需要刷新
        return (expirationTime - currentTime) < REFRESH_THRESHOLD;
    }

    /**
     * 获取token剩余有效期（毫秒）
     * @param token JWT token
     * @return 剩余有效期，-1表示token无效
     */
    public static long getRemainingTime(String token) {
        Claims claims = parseToken(token);
        if (claims == null) {
            return -1; // token无效
        }

        Date expiration = claims.getExpiration();
        long currentTime = System.currentTimeMillis();
        long expirationTime = expiration.getTime();

        return Math.max(0, expirationTime - currentTime);
    }

    /**
     * 刷新token（基于现有token生成新token）
     * @param token 旧token
     * @return 新token，如果旧token无效则返回null
     */
    public static String refreshToken(String token) {
        try {
            LoginUser loginUser = getLoginUser(token);
            if (loginUser != null) {
                return createToken(loginUser);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查token是否应该被强制下线
     * @param token JWT token
     * @return true表示应该强制下线，false表示正常
     */
    public static boolean checkTokenForceLogout(String token) {
        try {
            ensureConfigLoaded();

            // 首先检查强制下线功能是否启用
            if (!FORCE_LOGOUT_ENABLED) {
                return false;
            }

            // 如果没有配置强制下线日期，则不强制下线
            if (FORCE_LOGOUT_DATE == null || FORCE_LOGOUT_DATE.trim().isEmpty()) {
                return false;
            }

            Claims claims = parseToken(token);
            if (claims == null) {
                return false; // token无效，交给其他逻辑处理
            }

            Date issuedAt = claims.getIssuedAt();
            if (issuedAt == null) {
                // 旧token没有签发时间，跳过强制下线检查
                return false;
            }

            // 解析强制下线日期
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMdd");
            java.util.Date forceLogoutDateTime = sdf.parse(FORCE_LOGOUT_DATE.trim());

            // 如果token签发时间早于强制下线日期，则强制下线
            return issuedAt.before(forceLogoutDateTime);

        } catch (Exception e) {
            logger.warn("检查token强制下线失败: {}", e.getMessage());
            return false; // 出错时不强制下线
        }
    }

    /**
     * 动态更新强制下线日期
     * @param newDate 新的强制下线日期，格式：yyyyMMdd
     */
    public static void updateForceLogoutDate(String newDate) {
        FORCE_LOGOUT_DATE = newDate != null ? newDate : "";
        logger.info("强制下线日期已更新为: {}", FORCE_LOGOUT_DATE);
    }

    /**
     * 动态更新强制下线功能开关
     * @param enabled 是否启用强制下线功能
     */
    public static void updateForceLogoutEnabled(boolean enabled) {
        FORCE_LOGOUT_ENABLED = enabled;
        logger.info("强制下线功能开关已更新为: {}", FORCE_LOGOUT_ENABLED);
    }

    /**
     * 获取当前强制下线功能开关状态
     * @return 当前开关状态
     */
    public static boolean isForceLogoutEnabled() {
        ensureConfigLoaded();
        return FORCE_LOGOUT_ENABLED;
    }

    /**
     * 检查是否启用自动刷新
     * @return true表示启用，false表示禁用
     */
    public static boolean isAutoRefreshEnabled() {
        ensureConfigLoaded();
        return AUTO_REFRESH_ENABLED;
    }

    /**
     * 获取token有效期（毫秒）
     * @return token有效期
     */
    public static long getExpireTime() {
        ensureConfigLoaded();
        return EXPIRE_TIME;
    }

    /**
     * 获取刷新阈值（毫秒）
     * @return 刷新阈值
     */
    public static long getRefreshThreshold() {
        ensureConfigLoaded();
        return REFRESH_THRESHOLD;
    }

    /**
     * 获取token前缀
     * @return token前缀
     */
    public static String getTokenPrefix() {
        ensureConfigLoaded();
        return TOKEN_PREFIX;
    }
}
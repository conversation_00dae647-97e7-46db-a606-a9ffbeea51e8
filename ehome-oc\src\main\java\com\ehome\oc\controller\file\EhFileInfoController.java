package com.ehome.oc.controller.file;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.config.ServerConfig;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.service.OssService;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.file.FileUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 文件管理控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/file")
public class EhFileInfoController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(EhFileInfoController.class);

    private static final String PREFIX = "file";

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private OssService ossService;

    /**
     * 文件管理页面
     */
    @GetMapping("/list")
    public String listPage() {
        return PREFIX + "/list";
    }

    /**
     * 文件选择页面
     */
    @GetMapping("/select")
    public String select() {
        return PREFIX + "/select";
    }

    /**
     * 多选文件选择页面
     */
    @GetMapping("/multiSelect")
    public String multiSelect() {
        return PREFIX + "/multiSelect";
    }


    /**
     * 文件上传页面
     */
    @GetMapping("/uploader")
    public String fileUpload()
    {
        return PREFIX+"/uploader";
    }


    /**
     * 查询文件列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 构建查询条件
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL("from eh_file_info where status = '0'");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("businessType"), "and business_type = ?");
        // 文件名搜索
        if (params.containsKey("fileName") && StringUtils.isNotEmpty(params.getString("fileName"))) {
            sql.appendLike(params.getString("fileName"),"and original_name like ?");
        }
        
        // 文件类型筛选
        if (params.containsKey("fileType") && StringUtils.isNotEmpty(params.getString("fileType"))) {
            String fileType = params.getString("fileType");
            if (fileType.contains(",")) {
                // 多种文件类型，使用IN查询
                String[] types = fileType.split(",");
                // 去除空格
                for (int i = 0; i < types.length; i++) {
                    types[i] = types[i].trim();
                }
                sql.appendIn(types, "and file_type");
            } else {
                // 单一文件类型
                sql.append(fileType, "and file_type = ?");
            }
        }
        
        // 上传用户筛选
        if (params.containsKey("uploadUser") && StringUtils.isNotEmpty(params.getString("uploadUser"))) {
            sql.append(params.getString("uploadUser"),"and upload_user = ?");
        }
        
        // 时间范围筛选
        if (params.containsKey("beginTime") && StringUtils.isNotEmpty(params.getString("beginTime"))) {
            sql.append( params.getString("beginTime"),"and create_time >= ?");
        }
        if (params.containsKey("endTime") && StringUtils.isNotEmpty(params.getString("endTime"))) {
            sql.append(params.getString("endTime")," and create_time <= ?");
        }
        
        sql.append("order by create_time desc");
        return sql;
    }


    /**
     * 获取文件详情
     */
    @PostMapping("/detail")
    @ResponseBody
    public AjaxResult detail() {
        JSONObject params = getParams();
        String fileId = params.getString("fileId");
        if (StringUtils.isEmpty(fileId)) {
            return AjaxResult.error("文件ID不能为空");
        }
        
        Record fileInfo = Db.findFirst("select * from eh_file_info where file_id = ? and status = '0'", fileId);
        if (fileInfo == null) {
            return AjaxResult.error("文件不存在");
        }
        
        return AjaxResult.success(fileInfo.toMap());
    }

    /**
     * 删除文件（逻辑删除）
     */
    @PostMapping("/delete")
    @ResponseBody
    @Log(title = "删除文件", businessType = BusinessType.DELETE)
    public AjaxResult delete() {
        JSONObject params = getParams();
        String fileIds = params.getString("ids");
        if (StringUtils.isEmpty(fileIds)) {
            return AjaxResult.error("文件ID不能为空");
        }
        
        try {
            String[] ids = fileIds.split(",");
            for (String fileId : ids) {
                Record updateRecord = new Record();
                updateRecord.set("file_id", fileId);
                updateRecord.set("status", "1");
                updateRecord.set("update_time", DateUtils.getNowDate());
                Db.update("eh_file_info", "file_id", updateRecord);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("删除文件失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    @PostMapping("/remove")
    @ResponseBody
    @Log(title = "彻底删除文件", businessType = BusinessType.DELETE)
    public AjaxResult remove() {
        JSONObject params = getParams();
        String fileIds = params.getString("ids");
        if (StringUtils.isEmpty(fileIds)) {
            return AjaxResult.error("文件ID不能为空");
        }

        try {
            String[] ids = fileIds.split(",");
            for (String fileId : ids) {
                Record fileInfo = Db.findFirst("select * from eh_file_info where file_id = ?", fileId);
                if (fileInfo == null) {
                    continue;
                }
                String path = fileInfo.getStr("absolute_path");
                if(StringUtils.isNotEmpty(path)){
                    FileUtils.deleteFile(path);
                }
                String ossKey = fileInfo.getStr("oss_key");
                if(StringUtils.isNotEmpty(ossKey)){
                    String ossUrl = fileInfo.getStr("oss_url");
                    if(ossUrl.contains("ehome-public")){
                        ossService.deleteFile(ossKey, true);
                    }else{
                        ossService.deleteFile(ossKey, false);
                    }
                }
                Db.deleteById("eh_file_info", "file_id", fileId);
            }
            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("删除文件失败", e);
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @PostMapping("/batchDelete")
    @ResponseBody
    @Log(title = "批量删除文件", businessType = BusinessType.DELETE)
    public AjaxResult batchDelete(@RequestParam("ids") String ids) {
        return delete();
    }

    /**
     * 获取文件类型统计
     */
    @PostMapping("/typeStats")
    @ResponseBody
    public AjaxResult typeStats() {
        String sql = "select file_type, count(*) as count from eh_file_info where status = '0' group by file_type";
        return AjaxResult.success(Db.find(sql));
    }

    /**
     * 获取文件大小统计
     */
    @PostMapping("/sizeStats")
    @ResponseBody
    public AjaxResult sizeStats() {
        String sql = "select sum(file_size) as totalSize, count(*) as totalCount from eh_file_info where status = '0'";
        Record stats = Db.findFirst(sql);
        return AjaxResult.success(stats.toMap());
    }

    /**
     * 查看文件下载记录页面
     */
    @GetMapping("/downloadLogs/{fileId}")
    public String downloadLogs(@PathVariable String fileId, ModelMap mmap) {
        // 获取文件信息
        Record fileInfo = Db.findFirst("SELECT file_id, original_name FROM eh_file_info WHERE file_id = ? AND status = '0'", fileId);
        if (fileInfo != null) {
            mmap.put("fileId", fileId);
            mmap.put("fileName", fileInfo.getStr("original_name"));
        }
        return PREFIX + "/downloadLogs";
    }
    @GetMapping("/downloadLogPage")
    public String downloadLogPage(ModelMap mmap) {
        mmap.put("fileId", "");
        mmap.put("fileName", "");
        return PREFIX + "/downloadLogs";
    }

    /**
     * 获取文件下载记录数据
     */
    @PostMapping("/downloadLogsList")
    @ResponseBody
    public TableDataInfo downloadLogsList() {
        JSONObject params = getParams();
        String fileId = params.getString("fileId");

        EasySQL sql = new EasySQL("from eh_file_download_log dl LEFT JOIN eh_file_info fi ON dl.file_id = fi.file_id where 1=1");
        sql.append(fileId,"and dl.file_id = ?");
        if(StringUtils.isEmpty(fileId)){
            sql.append(getSysUser().getCommunityId(), "and dl.community_id = ?");
        }
        //如果不为空 要加 00:00:00 23:59:59
        String downloadTimeStart = params.getString("downloadTimeStart");
        if(StringUtils.isNotEmpty(downloadTimeStart)){
            downloadTimeStart += " 00:00:00";
            sql.append(downloadTimeStart, "and dl.download_time >= ?");
        }
        String downloadTimeEnd = params.getString("downloadTimeEnd");
        if(StringUtils.isNotEmpty(downloadTimeEnd)){
            downloadTimeEnd += " 23:59:59";
            sql.append(downloadTimeEnd, "and dl.download_time <= ?");
        }
        sql.appendLike(params.getString("userName"), "and dl.user_name like ?");
        sql.append("order by dl.download_time desc");
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "SELECT dl.log_id, dl.user_name, dl.user_type, dl.download_ip, dl.download_time, " +
            "CASE dl.user_type WHEN 'sys_user' THEN '系统用户' WHEN 'wx_user' THEN '小程序用户' ELSE dl.user_type END as user_type_name, " +
            "dl.house_name, fi.original_name as file_name",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }
}

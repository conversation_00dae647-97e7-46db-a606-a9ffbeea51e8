<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.BuildingMapper">
    
    <resultMap type="com.ehome.oc.domain.Building" id="BuildingResult">
        <id property="buildingId" column="building_id"/>
        <result property="communityId" column="community_id"/>
        <result property="name" column="name"/>
        <result property="totalUnits" column="total_units"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="houseCount" column="house_count"/>
        <result property="houseArea" column="house_area"/> 
        <result property="manager" column="manager"/>
    </resultMap>

    <sql id="selectBuildingVo">
        select building_id, community_id, name, total_units, house_count, house_area, manager, create_by, create_time, update_by, update_time, remark
        from eh_building
    </sql>

    <select id="selectBuildingList" parameterType="com.ehome.oc.domain.Building" resultMap="BuildingResult">
        <include refid="selectBuildingVo"/>
        <where>
            <if test="communityId != null and communityId != ''">
                AND community_id = #{communityId}
            </if>
            <if test="name != null and name != ''">
                AND name like concat('%', #{name}, '%')
            </if>
            <if test="manager != null and manager != ''">
                AND manager like concat('%', #{manager}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND create_time >= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </select>
    
    <select id="selectBuildingById" parameterType="Integer" resultMap="BuildingResult">
        <include refid="selectBuildingVo"/>
        where building_id = #{buildingId}
    </select>
        
    <insert id="insertBuilding" parameterType="com.ehome.oc.domain.Building" useGeneratedKeys="true" keyProperty="buildingId">
        insert into eh_building
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="communityId != null">community_id,</if>
            <if test="name != null">name,</if>
            <if test="totalUnits != null">total_units,</if>
            <if test="houseCount != null">house_count,</if>
            <if test="houseArea != null">house_area,</if>
            <if test="manager != null">manager,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="communityId != null">#{communityId},</if>
            <if test="name != null">#{name},</if>
            <if test="totalUnits != null">#{totalUnits},</if>
            <if test="houseCount != null">#{houseCount},</if>
            <if test="houseArea != null">#{houseArea},</if>
            <if test="manager != null">#{manager},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBuilding" parameterType="com.ehome.oc.domain.Building">
        update eh_building
        <trim prefix="SET" suffixOverrides=",">
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="totalUnits != null">total_units = #{totalUnits},</if>
            <if test="houseCount != null">house_count = #{houseCount},</if>
            <if test="houseArea != null">house_area = #{houseArea},</if>
            <if test="manager != null">manager = #{manager},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where building_id = #{buildingId}
    </update>

    <delete id="deleteBuildingById" parameterType="String">
        delete from eh_building where building_id = #{buildingId}
    </delete>

    <delete id="deleteBuildingByIds" parameterType="String">
        delete from eh_building where building_id in 
        <foreach item="buildingId" collection="array" open="(" separator="," close=")">
            #{buildingId}
        </foreach>
    </delete>

    <select id="selectBuildingByCommunityId" parameterType="String" resultMap="BuildingResult">
        <include refid="selectBuildingVo"/>
        where community_id = #{communityId}
    </select>

    <update id="updateBuildingTotalUnits">
        update eh_building
        set total_units = (
            select count(1) from eh_unit where building_id = #{buildingId}
        )
        where building_id = #{buildingId}
    </update>
</mapper> 
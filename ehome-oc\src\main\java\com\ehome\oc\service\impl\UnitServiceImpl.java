package com.ehome.oc.service.impl;

import com.ehome.oc.domain.Unit;
import com.ehome.oc.mapper.UnitMapper;
import com.ehome.oc.service.IUnitService;
import com.ehome.oc.service.IBuildingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UnitServiceImpl implements IUnitService {
    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private IBuildingService buildingService;

    @Override
    public Unit selectUnitById(String unitId) {
        return unitMapper.selectUnitById(unitId);
    }

    @Override
    public List<Unit> selectUnitList(Unit unit) {
        return unitMapper.selectUnitList(unit);
    }

    @Override
    public List<Unit> selectUnitsByBuildingId(String buildingId) {
        return unitMapper.selectUnitsByBuildingId(buildingId);
    }

    @Override
    @Transactional
    public int insertUnit(Unit unit) {
        int rows = unitMapper.insertUnit(unit);
        if (rows > 0) {
            buildingService.updateBuildingTotalUnits(unit.getBuildingId());
        }
        return rows;
    }

    @Override
    @Transactional
    public int updateUnit(Unit unit) {
        int rows = unitMapper.updateUnit(unit);
        if (rows > 0) {
            buildingService.updateBuildingTotalUnits(unit.getBuildingId());
        }
        return rows;
    }

    @Override
    @Transactional
    public int deleteUnitById(String unitId) {
        Unit unit = selectUnitById(unitId);
        if (unit != null) {
            int rows = unitMapper.deleteUnitById(unitId);
            if (rows > 0) {
                buildingService.updateBuildingTotalUnits(unit.getBuildingId());
            }
            return rows;
        }
        return 0;
    }

    @Override
    @Transactional
    public int deleteUnitByIds(String[] unitIds) {
        int rows = 0;
        for (String unitId : unitIds) {
            Unit unit = selectUnitById(unitId);
            if (unit != null) {
                rows += unitMapper.deleteUnitById(unitId);
                buildingService.updateBuildingTotalUnits(unit.getBuildingId());
            }
        }
        return rows;
    }
} 
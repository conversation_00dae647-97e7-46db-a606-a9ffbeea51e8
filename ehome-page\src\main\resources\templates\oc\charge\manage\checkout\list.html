<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('收银台')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style type="text/css">
        #treeSearchInput{width: 130px;}
        .unit-stats-bar {
            padding: 10px 15px;
            float: right;
            display: none;
        }
        .unit-stats-bar .stats-text {
            color: #1976d2;
            font-weight: bold;
            font-size: 14px;
        }
        .arrear-highlight {
            color: #f56c6c !important;
            font-weight: bold;
        }
        .normal-highlight {
            color: #67c23a !important;
            font-weight: bold;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa fa-money"></i> 收银台
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool" href="javascript:void(0)" onclick="updateArrearAmount()" title="更新欠费"><i class="fa fa-refresh"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div style="padding: 0px;padding-bottom: 5px;">
                    <div class="input-group" style="margin-bottom: 0px;">
                        <input type="text" id="treeSearchInput" placeholder="搜索楼栋或单元" class="form-control form-control-sm" onkeypress="if(event.keyCode==13) searchTree()"/>
                        <span class="input-group-btn">
                            <button class="btn btn-sm btn-default" type="button" onclick="searchTree()"><i class="fa fa-search"></i></button>
                            <button class="btn btn-sm btn-default" type="button" onclick="clearTreeSearch()"><i class="fa fa-times"></i></button>
                        </span>
                    </div>
                </div>
                <div id="buildingTree" class="ztree"></div>
            </div>
        </div>
    </div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <!-- 搜索区域 -->
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" name="buildingId" id="buildingId"/>
                        <input type="hidden" name="unitId" id="unitId"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>搜索：</label>
                                    <input type="text" name="searchKeyword" placeholder="请输入楼栋号或单元号" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>
                                <li>
                                    <label>房间号：</label>
                                    <input type="text" name="room" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>
                                <li>
                                    <label>楼栋号：</label>
                                    <input type="text" name="buildingName" placeholder="请选择左侧楼栋单元" readonly/>
                                </li>
                                <li style="display: none;">
                                    <label>单元号：</label>
                                    <input type="text" name="unitName" readonly/>
                                </li>
                                <li>
                                    <label>缴费状态：</label>
                                    <select name="arrear_status">
                                        <option value="">所有</option>
                                        <option value="1">有欠费</option>
                                        <option value="0">无欠费</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm ml-10" onclick="resetPre()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- 工具栏和表格区域 -->
                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-success" onclick="updateArrearAmount()">
                        <i class="fa fa-refresh"></i> 更新欠费
                    </a>
                    <a class="btn btn-primary ml-10" onclick="batchPayment()">
                        <i class="fa fa-money"></i> 批量收款
                    </a>

                    <div class="unit-stats-bar pull-right" id="unitStatsBar">
                        <span class="stats-text" id="statsText"></span>
                    </div>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">

        var prefix = ctx + "oc/charge/manage/checkout";
        var currentUnitId = null;
        var currentBuildingName = '';
        var currentUnitName = '';

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 220, resizeWithWindow: false });

            var buildingId = /*[[${buildingId}]]*/ null;
            var unitId = /*[[${unitId}]]*/ null;

            if (buildingId) {
                $("#buildingId").val(buildingId);
            }
            if (unitId) {
                $("#unitId").val(unitId);
            }

            loadBuildingZTree();

            var options = {
                url: prefix + "/getUnitHouseList",
                modalName: "房屋缴费信息",
                pagination: true,
                sidePagination: "server",
                pageSize: 30,
                pageList: [20, 30, 50, 100],
                layer:{
                    area:['800px','500px'],
                    offset: '70px'
                },
                responseHandler: function(res) {
                    // 处理统计信息
                    if (res.msg) {
                        try {
                            var statsData = JSON.parse(res.msg);
                            if (currentUnitId && statsData.totalHouseCount !== undefined) {
                                showUnitStats(statsData);
                            }
                        } catch (e) {
                            console.error("解析统计信息失败", e);
                        }
                    }
                    return res;
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'combina_name',
                    title: '房屋',
                    formatter: function(value, row, index) {
                        var combina_name = row.combina_name || '';
                        var room = row.room || '';
                        var houseText = combina_name + '/' + room;
                        return '<a href="javascript:void(0)" onclick="houseDetail(\'' + row.house_id + '\')" style="color: #007bff;">' + houseText + '</a>';
                    }
                },
                {
                    field: 'owner_str',
                    title: '住户信息'
                },
                {
                    field: 'house_status',
                    title: '房屋状态'
                },
                {
                    field: 'arrear_amount',
                    title: '欠费金额',
                    formatter: function(value, row, index) {
                        var amount = parseFloat(value || 0);
                        if (amount > 0) {
                            return '<span class="arrear-highlight">¥' + amount.toFixed(2) + '</span>';
                        } else {
                            return '<span class="normal-highlight">无欠费</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="houseDetail(\'' + row.house_id + '\')"><i class="fa fa-eye"></i> 详情</a> ');
                        if (parseFloat(row.arrear_amount || 0) > 0) {
                            actions.push('<a class="btn btn-link btn-xs" href="javascript:void(0)" onclick="payHouse(\'' + row.house_id + '\')"><i class="fa fa-money"></i> 收款</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 如果有URL参数，立即触发搜索
            if ((buildingId && buildingId !== 'null' && buildingId !== '') ||
                (unitId && unitId !== 'null' && unitId !== '')) {
                setTimeout(function() {
                    $.table.search();
                }, 100);
            }
        });

        // 房屋详情
        function houseDetail(houseId) {
            var url = prefix + "/detail/" + houseId;
            $.modal.openTab("房屋收款", url);
        }

        // 房屋收款
        function payHouse(houseId) {
            var url = prefix + "/detail/" + houseId;
            $.modal.openTab("房屋收款", url);
        }

        // 批量收款
        function batchPayment() {
            var rows = $("#bootstrap-table").bootstrapTable('getSelections');
            if (rows.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }

            var arrearHouses = rows.filter(function(row) {
                return parseFloat(row.arrear_amount || 0) > 0;
            });

            if (arrearHouses.length == 0) {
                $.modal.alertWarning("所选房屋均无欠费");
                return;
            }

            $.modal.confirm("确认对选中的 " + arrearHouses.length + " 套有欠费房屋进行批量收款吗？", function() {
                $.modal.alertSuccess("批量收款功能待实现");
            });
        }

        function loadBuildingZTree() {
            $.ajax({
                url: ctx + "oc/building/tree",
                type: "GET",
                success: function(res) {
                    if (res.code == 0) {
                        var zNodes = convertToZTreeNodes(res.data);
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    if(treeNode.type === 'building') {
                                        $("#buildingId").val(treeNode.id);
                                        $("#unitId").val("");
                                        $("input[name='buildingName']").val(treeNode.name);
                                        $("input[name='unitName']").val("");
                                        currentBuildingName = treeNode.name;
                                        currentUnitName = '';
                                        currentUnitId = null;
                                        loadBuildingStats(treeNode.id);
                                    } else if(treeNode.type === 'unit') {
                                        $("#buildingId").val(treeNode.pId);
                                        $("#unitId").val(treeNode.id);
                                        $("input[name='buildingName']").val(treeNode.buildingName);
                                        $("input[name='unitName']").val(treeNode.name);
                                        currentBuildingName = treeNode.buildingName;
                                        currentUnitName = treeNode.name;
                                        currentUnitId = treeNode.id;
                                        loadUnitStats(treeNode.id);
                                    }
                                    $.table.search();
                                }
                            }
                        };
                        $.fn.zTree.init($("#buildingTree"), setting, zNodes);
                    } else {
                        $.modal.alertError("加载楼栋单元树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载楼栋单元树失败：" + error);
                }
            });
        }

        // 加载楼栋统计信息
        function loadBuildingStats(buildingId) {
            $.ajax({
                url: prefix + "/getBuildingHouseList",
                type: "POST",
                data: { buildingId: buildingId },
                success: function(res) {
                    if (res.code == 0 && res.data) {
                        showBuildingStats(res.data);
                    } else {
                        hideUnitStats();
                    }
                },
                error: function() {
                    hideUnitStats();
                }
            });
        }

        // 加载单元统计信息
        function loadUnitStats(unitId) {
            $.ajax({
                url: prefix + "/getUnitHouseList",
                type: "POST",
                data: { unitId: unitId },
                success: function(res) {
                    if (res.code == 0 && res.msg) {
                        try {
                            var statsData = JSON.parse(res.msg);
                            showUnitStats(statsData);
                        } catch (e) {
                            hideUnitStats();
                        }
                    } else {
                        hideUnitStats();
                    }
                },
                error: function() {
                    hideUnitStats();
                }
            });
        }

        // 显示楼栋统计信息
        function showBuildingStats(data) {
            var totalCount = data.totalHouseCount || 0;
            var arrearCount = data.totalArrearageHouseCount || 0;

            var statsText = currentBuildingName + ' 欠费/总户数：' + arrearCount + '/' + totalCount;
            $("#statsText").text(statsText);
            $("#unitStatsBar").show();
        }

        // 显示单元统计信息
        function showUnitStats(data) {
            var totalCount = data.totalHouseCount || 0;
            var arrearCount = data.arrearageHouseCount || 0;
            var normalCount = totalCount - arrearCount;

            var statsText = currentBuildingName + '/' + currentUnitName + ' 欠费/总户数：' + arrearCount + '/' + totalCount;
            $("#statsText").text(statsText);
            $("#unitStatsBar").show();
        }

        // 隐藏单元统计信息
        function hideUnitStats() {
            $("#unitStatsBar").hide();
        }

        // 转换接口数据为ztree扁平结构
        function convertToZTreeNodes(data) {
            var nodes = [];
            $.each(data, function(i, building) {
                var bNode = {
                    id: building.buildingId,
                    pId: null,
                    name: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋',
                    type: 'building',
                    open: false
                };
                nodes.push(bNode);
                if(building.children && building.children.length > 0) {
                    $.each(building.children, function(j, unit) {
                        nodes.push({
                            id: unit.unitId,
                            pId: building.buildingId,
                            name: unit.unitName.endsWith('单元') ? unit.unitName : unit.unitName + '单元',
                            type: 'unit',
                            buildingId: building.buildingId,
                            buildingName: building.buildingName.endsWith('栋') ? building.buildingName : building.buildingName + '栋'
                        });
                    });
                }
            });
            return nodes;
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadBuildingZTree();
        });

        // 自定义重置
        function resetPre() {
            $("#formId")[0].reset();
            $("#buildingId").val("");
            $("#unitId").val("");
            hideUnitStats();
            $.table.search();
        }

        // 搜索树节点
        function searchTree() {
            var keyword = $("#treeSearchInput").val();
            var treeObj = $.fn.zTree.getZTreeObj("buildingTree");
            if (!treeObj) return;

            if (keyword) {
                // 获取所有节点
                var allNodes = treeObj.getNodes();
                var nodesToShow = [];

                // 递归搜索所有节点
                function searchNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        var node = nodes[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            // 匹配的节点
                            nodesToShow.push(node);

                            // 添加所有父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                if (nodesToShow.indexOf(parent) === -1) {
                                    nodesToShow.push(parent);
                                }
                                parent = parent.getParentNode();
                            }

                            // 添加所有子节点
                            function addChildNodes(parentNode) {
                                if (parentNode.children && parentNode.children.length > 0) {
                                    for (var j = 0; j < parentNode.children.length; j++) {
                                        var childNode = parentNode.children[j];
                                        if (nodesToShow.indexOf(childNode) === -1) {
                                            nodesToShow.push(childNode);
                                        }
                                        addChildNodes(childNode);
                                    }
                                }
                            }
                            addChildNodes(node);
                        }
                        if (node.children && node.children.length > 0) {
                            searchNodes(node.children);
                        }
                    }
                }

                searchNodes(allNodes);

                if (nodesToShow.length > 0) {
                    // 隐藏所有节点
                    function hideAllNodes(nodes) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.hideNode(nodes[i]);
                            if (nodes[i].children && nodes[i].children.length > 0) {
                                hideAllNodes(nodes[i].children);
                            }
                        }
                    }
                    hideAllNodes(allNodes);

                    // 显示需要显示的节点
                    for (var i = 0; i < nodesToShow.length; i++) {
                        treeObj.showNode(nodesToShow[i]);
                    }

                    // 展开父节点并选中第一个匹配的节点
                    var firstMatchNode = null;
                    for (var i = 0; i < nodesToShow.length; i++) {
                        var node = nodesToShow[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            if (!firstMatchNode) {
                                firstMatchNode = node;
                            }
                            // 展开匹配节点的父节点
                            var parent = node.getParentNode();
                            while (parent) {
                                treeObj.expandNode(parent, true, false, false);
                                parent = parent.getParentNode();
                            }
                        }
                    }

                    if (firstMatchNode) {
                        treeObj.selectNode(firstMatchNode, true);
                    }
                } else {
                    $.modal.alertWarning("未找到匹配的楼栋或单元");
                }
            } else {
                // 清空搜索，显示所有节点
                var allNodes = treeObj.getNodes();
                function showAllNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        treeObj.showNode(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length > 0) {
                            showAllNodes(nodes[i].children);
                        }
                    }
                }
                showAllNodes(allNodes);
                treeObj.expandAll(false);
            }
        }

        // 清空树搜索
        function clearTreeSearch() {
            $("#treeSearchInput").val("");
            searchTree(); // 调用搜索函数来重置显示
        }

        // 更新欠费金额
        function updateArrearAmount() {
            $.modal.confirm("确认要更新所有房屋的欠费金额吗？", function() {
                $.ajax({
                    url: prefix + "/updateArrearAmount",
                    type: "POST",
                    success: function(res) {
                        if (res.code == 0) {
                            $.modal.alertSuccess(res.msg);
                            // 刷新表格
                            $("#bootstrap-table").bootstrapTable('refresh');
                            // 刷新统计信息
                            if (currentUnitId) {
                                loadUnitStats(currentUnitId);
                            }
                        } else {
                            $.modal.alertError("更新失败：" + res.msg);
                        }
                    },
                    error: function() {
                        $.modal.alertError("更新失败");
                    }
                });
            });
        }
    </script>
</body>
</html>

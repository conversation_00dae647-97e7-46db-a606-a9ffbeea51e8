# 小程序卡片邀请功能测试指南

## 功能概述

新的小程序卡片邀请功能替换了原有的复杂邀请方式（邀请码、二维码、手机号），实现了简洁的邀请流程：
1. 发起邀请时输入接收人手机号
2. 生成小程序卡片分享给接收人
3. 接收人确认后自动注册到系统
4. 接收人登录小程序即可使用

## 测试前准备

### 1. 数据库迁移
执行数据库迁移脚本：
```sql
-- 执行 sql/migrate_invite_card.sql
```

### 2. 后端部署
确保新的控制器已部署：
- `HouseCardInviteController.java`

### 3. 小程序部署
确保新页面已上传：
- `pages/invite/create`
- `pages/invite/accept`

## 测试场景

### 场景1：已登录用户发起邀请

**测试步骤：**
1. 登录小程序
2. 进入房屋列表页面
3. 点击"邀请住户"按钮
4. 选择房屋（如果有多个）
5. 输入接收人手机号
6. 选择关系类型（业主/家庭成员/租户）
7. 输入备注信息（可选）
8. 点击"创建邀请"
9. 查看邀请预览卡片
10. 点击"分享给好友"

**预期结果：**
- 成功创建邀请记录
- 生成唯一的邀请令牌
- 显示邀请预览卡片
- 可以正常分享小程序卡片

### 场景2：接收人确认邀请

**测试步骤：**
1. 接收人点击分享的小程序卡片
2. 查看邀请详情页面
3. 点击"确认接受"
4. 查看确认成功提示
5. 点击"立即登录"
6. 完成微信授权登录

**预期结果：**
- 直接显示邀请详情（无需登录）
- 确认后自动注册到eh_owner表
- 邀请状态更新为已使用
- 成功跳转到登录页面

### 场景3：已登录用户接受邀请

**测试步骤：**
1. 使用另一个微信账号登录小程序
2. 点击分享的小程序卡片
3. 查看邀请详情
4. 点击"立即加入"

**预期结果：**
- 直接显示邀请详情
- 成功加入房屋
- 跳转到房屋列表页面

### 场景4：邀请令牌一次性使用验证

**测试步骤：**
1. 创建一个邀请
2. 第一个用户成功接受邀请
3. 第二个用户尝试使用相同的邀请链接

**预期结果：**
- 第一个用户成功加入
- 第二个用户收到"邀请已使用"提示

### 场景5：邀请过期验证

**测试步骤：**
1. 创建一个邀请
2. 修改数据库中的过期时间为过去时间
3. 尝试接受邀请

**预期结果：**
- 显示"邀请已过期"提示
- 邀请状态更新为过期

## API测试

### 1. 创建邀请接口
```
POST /api/wx/house/card-invite/create
{
  "houseId": "房屋ID",
  "relType": 2,
  "remark": "备注信息"
}
```

### 2. 查询邀请信息接口
```
GET /api/wx/auth/invite/card-info/{inviteToken}
```

### 3. 确认邀请接口
```
POST /api/wx/auth/invite/card-confirm
{
  "inviteToken": "邀请令牌"
}
```

## 数据验证

### 检查邀请记录
```sql
SELECT * FROM eh_house_invite 
WHERE invite_type = 1 
ORDER BY create_time DESC;
```

### 检查房屋关联关系
```sql
SELECT * FROM eh_house_owner_rel 
WHERE remark LIKE '%小程序卡片邀请%'
ORDER BY create_time DESC;
```

## 常见问题排查

### 1. 邀请创建失败
- 检查用户是否有房屋权限
- 检查房屋信息是否存在
- 检查数据库连接

### 2. 邀请接受失败
- 检查邀请令牌是否有效
- 检查用户登录状态
- 检查是否重复加入

### 3. 分享功能异常
- 检查小程序分享配置
- 检查页面路径参数
- 检查分享图片路径

## 性能测试

### 1. 并发邀请创建
- 同时创建多个邀请
- 验证令牌唯一性

### 2. 大量邀请处理
- 创建大量邀请记录
- 测试查询性能

## 回滚方案

如果新功能出现问题，可以：
1. 恢复旧的邀请页面注册
2. 回滚数据库迁移
3. 重新部署旧版本代码

## 测试完成标准

- [ ] 所有测试场景通过
- [ ] API接口正常响应
- [ ] 数据库记录正确
- [ ] 用户体验流畅
- [ ] 性能满足要求

# 小区配置缓存工具类使用指南

## 概述

本文档介绍了新开发的小区配置缓存工具类，该工具类提供了小区配置的内存缓存管理，支持ext_json解析和配置热更新。

## 核心组件

### 1. CommunityConfig (配置实体类)
- **文件位置**: `ehome-common/src/main/java/com/ehome/common/domain/CommunityConfig.java`
- **继承关系**: 继承自 `JSONObject`
- **特性**: 
  - 统一管理基础字段和扩展配置
  - ext_json字段自动解析并合并到JSONObject中
  - 提供便捷的类型安全访问方法

### 2. CommunityConfigCache (缓存工具类)
- **文件位置**: `ehome-common/src/main/java/com/ehome/common/utils/cache/CommunityConfigCache.java`
- **特性**:
  - 基于ConcurrentHashMapCache实现
  - 提供静态方法便于调用
  - 支持配置的增删改查
  - 自动缓存管理

### 3. ICommunityConfigService (服务接口)
- **文件位置**: `ehome-oc/src/main/java/com/ehome/oc/service/ICommunityConfigService.java`
- **实现类**: `CommunityConfigServiceImpl`
- **特性**:
  - 负责数据库操作
  - 应用启动时自动初始化缓存
  - 配置更新时同步缓存

## 使用方法

### 初始化缓存

在使用缓存之前，需要先初始化缓存数据：

```java
// 注入服务
@Autowired
private ICommunityConfigService communityConfigService;

// 初始化所有小区配置到缓存
communityConfigService.initAllConfigs();

// 或者初始化单个小区配置
CommunityConfig config = communityConfigService.getConfig("oc_id");
```

### 基础配置访问

```java
// 获取完整配置（需要先初始化缓存）
CommunityConfig config = CommunityConfigCache.getConfig("oc_id");

// 获取配置值（支持默认值）
String title = CommunityConfigCache.getConfigString("oc_id", "miniprogram_title", "默认标题");
Integer menuRows = CommunityConfigCache.getConfigInteger("oc_id", "menu_rows", 4);
Boolean enableShare = CommunityConfigCache.getConfigBoolean("oc_id", "enable_share", true);

// 获取基础字段
String ocName = CommunityConfigCache.getConfigString("oc_id", "oc_name", "");
String ocAddress = CommunityConfigCache.getConfigString("oc_id", "oc_address", "");
```

### 配置更新

```java
// 注入服务
@Autowired
private ICommunityConfigService communityConfigService;

// 更新单个配置
boolean success = communityConfigService.updateConfig("oc_id", "miniprogram_title", "新标题");

// 批量更新配置
Map<String, Object> configMap = new HashMap<>();
configMap.put("miniprogram_title", "新标题");
configMap.put("enable_share", true);
configMap.put("menu_rows", 6);
boolean success = communityConfigService.updateConfigs("oc_id", configMap);
```

### 缓存管理

```java
// 重载指定小区配置
communityConfigService.reloadConfig("oc_id");

// 重载所有小区配置
communityConfigService.reloadAllConfigs();

// 手动添加配置到缓存
CommunityConfig config = new CommunityConfig();
config.put("oc_id", "test_id");
config.put("oc_name", "测试小区");
CommunityConfigCache.putConfig(config);
```

## 配置字段说明

### 基础字段
- `oc_id`: 小区唯一标识
- `oc_code`: 小区代码
- `oc_name`: 小区名称
- `oc_address`: 小区地址
- `oc_link`: 小区联系人信息
- `oc_state`: 小区状态（0:正常, 1:维护中, 2:关闭）
- `owner_count`: 业主数量
- `building_num`: 楼宇栋数
- `pms_id`: 物业ID
- `pms_name`: 物业名称
- `service_phone`: 服务电话

### 扩展配置字段（存储在ext_json中）
- `miniprogram_title`: 小程序标题
- `miniprogram_subtitle`: 小程序副标题
- `enable_share`: 是否启用分享
- `menu_rows`: 菜单行数
- `enable_screenshot`: 是否允许截屏
- 其他自定义配置...

## 在Controller中的使用示例

```java
@Controller
@RequestMapping("/api/community")
public class CommunityApiController extends BaseController {
    
    @Autowired
    private ICommunityConfigService communityConfigService;
    
    /**
     * 获取小区首页配置
     */
    @GetMapping("/homeConfig/{ocId}")
    @ResponseBody
    public AjaxResult getHomeConfig(@PathVariable("ocId") String ocId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取小区基本信息
        result.put("ocName", CommunityConfigCache.getConfigString(ocId, "oc_name", ""));
        result.put("ocAddress", CommunityConfigCache.getConfigString(ocId, "oc_address", ""));
        
        // 获取小程序配置
        result.put("title", CommunityConfigCache.getConfigString(ocId, "miniprogram_title", "智慧社区"));
        result.put("subtitle", CommunityConfigCache.getConfigString(ocId, "miniprogram_subtitle", ""));
        result.put("menuRows", CommunityConfigCache.getConfigInteger(ocId, "menu_rows", 4));
        result.put("enableShare", CommunityConfigCache.getConfigBoolean(ocId, "enable_share", true));
        
        return AjaxResult.success(result);
    }
    
    /**
     * 更新小程序配置
     */
    @PostMapping("/updateConfig/{ocId}")
    @ResponseBody
    public AjaxResult updateConfig(@PathVariable("ocId") String ocId, 
                                 @RequestBody Map<String, Object> configMap) {
        boolean success = communityConfigService.updateConfigs(ocId, configMap);
        return success ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
    }
}
```

## 在Service中的使用示例

```java
@Service
public class WxIndexService {
    
    /**
     * 获取小程序首页数据
     */
    public Map<String, Object> getIndexData(String ocId) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取小区配置
        CommunityConfig config = CommunityConfigCache.getConfig(ocId);
        if (config != null) {
            data.put("communityName", config.getConfigString("oc_name", ""));
            data.put("title", config.getConfigString("miniprogram_title", "智慧社区"));
            data.put("subtitle", config.getConfigString("miniprogram_subtitle", ""));
            data.put("enableShare", config.getConfigBoolean("enable_share", true));
        }
        
        return data;
    }
}
```

## 最佳实践

### 1. 统一访问方式
```java
// ✅ 推荐：使用CommunityConfigCache
String title = CommunityConfigCache.getConfigString(ocId, "miniprogram_title", "默认标题");

// ❌ 避免：直接查询数据库
// OcInfoModel model = OcInfoModel.dao.findById(ocId);
```

### 2. 合理使用默认值
```java
// ✅ 推荐：提供合理的默认值
Integer menuRows = CommunityConfigCache.getConfigInteger(ocId, "menu_rows", 4);
Boolean enableShare = CommunityConfigCache.getConfigBoolean(ocId, "enable_share", true);

// ❌ 避免：不提供默认值可能导致空指针
// Integer menuRows = CommunityConfigCache.getConfigInteger(ocId, "menu_rows", null);
```

### 3. 配置更新后及时刷新缓存
```java
// 在OcInfoController的maintainSave方法中已自动处理
boolean success = model.update();
if (success) {
    // 重载小区配置缓存
    communityConfigService.reloadConfig(model.getStr("oc_id"));
}
```

## 性能优化

1. **缓存时间**: 默认缓存24小时，可根据需要调整
2. **内存管理**: 基于ConcurrentHashMapCache，支持TTL和自动清理
3. **并发安全**: 使用ConcurrentHashMap保证线程安全
4. **懒加载**: 缓存未命中时自动从数据库加载

## 故障排除

### 常见问题

1. **配置未生效**
   - 检查配置是否正确保存到数据库
   - 确认缓存是否已更新
   - 验证配置键名是否正确

2. **缓存不一致**
   - 配置更新后会自动刷新缓存
   - 可手动调用 `communityConfigService.reloadConfig(ocId)`

3. **性能问题**
   - 缓存命中率高，性能优于直接查询数据库
   - 如有性能问题，检查缓存配置和清理策略

## 扩展配置

### 添加新配置项

1. **直接使用**: 新配置项可直接存储在ext_json中，无需修改代码
2. **类型安全**: 使用对应的类型安全方法获取配置值
3. **默认值**: 为新配置项提供合理的默认值

```java
// 添加新配置项示例
communityConfigService.updateConfig(ocId, "new_feature_enabled", true);

// 使用新配置项
Boolean newFeatureEnabled = CommunityConfigCache.getConfigBoolean(ocId, "new_feature_enabled", false);
```



## 总结

小区配置缓存工具类提供了统一、高效的小区配置管理方案，具有以下优势：

1. **统一管理**: 基础字段和扩展配置统一在JSONObject中
2. **高性能**: 内存缓存，避免频繁数据库查询
3. **类型安全**: 提供类型安全的访问方法
4. **易于使用**: 静态方法调用，简单便捷
5. **自动管理**: 应用启动自动初始化，配置更新自动刷新缓存
6. **扩展性强**: 支持任意扩展配置，无需修改代码结构

## 下一步计划

1. **监控集成**: 添加缓存命中率和性能监控
2. **配置验证**: 添加配置值的验证机制
3. **配置版本**: 支持配置版本管理和回滚
4. **分布式缓存**: 考虑集群环境下的缓存同步

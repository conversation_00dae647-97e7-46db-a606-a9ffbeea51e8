/**
 * 微信订阅消息管理工具
 */

// 消息模板ID常量
const TEMPLATE_IDS = {
  REPAIR_NOTICE: 'yLMlq8zOxgHkyUsAbE2ltdekZdvEO_U5iOl7eWP_Phk',    // 物业报修提醒
  PROPERTY_NOTICE: 'oFsuyzgpcAvENaOqokhmD-Fa8EmBpDsh_QFLccPmRUY'  // 物业通知
}

// 模板类型常量
const TEMPLATE_TYPES = {
  REPAIR_NOTICE: 'repair_notice',
  PROPERTY_NOTICE: 'property_notice'
}

class SubscribeManager {
  constructor() {
    this.app = getApp()
  }

  /**
   * 检查用户订阅状态
   * @param {string} templateType 模板类型
   * @returns {Promise<boolean>} 是否已订阅
   */
  async checkSubscribeStatus(templateType) {
    try {
      const result = await this.app.request({
        url: '/api/wx/subscribe/checkStatus',
        method: 'POST',
        data: { templateType }
      })
      
      if (result.code === 0) {
        return result.data.subscribed || false
      }
      return false
    } catch (error) {
      console.error('检查订阅状态失败:', error)
      return false
    }
  }

  /**
   * 请求订阅授权
   * @param {Array|string} templateTypes 模板类型数组或单个类型
   * @param {Object} options 配置选项
   * @returns {Promise<Object>} 授权结果
   */
  async requestSubscribe(templateTypes, options = {}) {
    try {
      // 确保templateTypes是数组
      const types = Array.isArray(templateTypes) ? templateTypes : [templateTypes]
      
      // 构建模板ID数组
      const templateIds = types.map(type => {
        switch (type) {
          case TEMPLATE_TYPES.REPAIR_NOTICE:
            return TEMPLATE_IDS.REPAIR_NOTICE
          case TEMPLATE_TYPES.PROPERTY_NOTICE:
            return TEMPLATE_IDS.PROPERTY_NOTICE
          default:
            console.warn('未知的模板类型:', type)
            return null
        }
      }).filter(id => id !== null)

      if (templateIds.length === 0) {
        throw new Error('没有有效的模板ID')
      }

      // 请求订阅授权
      const result = await new Promise((resolve, reject) => {
        wx.requestSubscribeMessage({
          tmplIds: templateIds,
          success: resolve,
          fail: reject
        })
      })

      // 处理授权结果
      const subscribeResult = this.processSubscribeResult(result, types)
      
      // 同步订阅状态到后端
      await this.syncSubscribeStatus(subscribeResult)
      
      return {
        success: true,
        data: subscribeResult,
        message: '订阅授权完成'
      }

    } catch (error) {
      console.error('请求订阅授权失败:', error)
      
      // 用户拒绝授权的特殊处理
      if (error.errMsg && error.errMsg.includes('requestSubscribeMessage:fail cancel')) {
        return {
          success: false,
          error: 'USER_CANCEL',
          message: '用户取消了订阅授权'
        }
      }
      
      return {
        success: false,
        error: error.message || '订阅授权失败',
        message: options.errorMessage || '订阅授权失败，请稍后重试'
      }
    }
  }

  /**
   * 处理订阅授权结果
   * @param {Object} result 微信返回的结果
   * @param {Array} templateTypes 模板类型数组
   * @returns {Object} 处理后的结果
   */
  processSubscribeResult(result, templateTypes) {
    const subscribeResult = {}
    
    templateTypes.forEach(type => {
      const templateId = this.getTemplateId(type)
      if (templateId && result[templateId]) {
        subscribeResult[type] = {
          templateId,
          status: result[templateId], // 'accept', 'reject', 'ban'
          subscribed: result[templateId] === 'accept'
        }
      }
    })
    
    return subscribeResult
  }

  /**
   * 同步订阅状态到后端
   * @param {Object} subscribeResult 订阅结果
   */
  async syncSubscribeStatus(subscribeResult) {
    try {
      await this.app.request({
        url: '/api/wx/subscribe/syncStatus',
        method: 'POST',
        data: { subscribeResult }
      })
    } catch (error) {
      console.error('同步订阅状态失败:', error)
    }
  }

  /**
   * 获取模板ID
   * @param {string} templateType 模板类型
   * @returns {string} 模板ID
   */
  getTemplateId(templateType) {
    switch (templateType) {
      case TEMPLATE_TYPES.REPAIR_NOTICE:
        return TEMPLATE_IDS.REPAIR_NOTICE
      case TEMPLATE_TYPES.PROPERTY_NOTICE:
        return TEMPLATE_IDS.PROPERTY_NOTICE
      default:
        return null
    }
  }

  /**
   * 智能订阅检查 - 在需要时自动弹出订阅授权
   * @param {string} templateType 模板类型
   * @param {Object} options 配置选项
   * @returns {Promise<boolean>} 是否已订阅
   */
  async smartSubscribeCheck(templateType, options = {}) {
    try {
      // 先检查当前订阅状态
      const isSubscribed = await this.checkSubscribeStatus(templateType)
      
      if (isSubscribed) {
        return true
      }

      // 如果未订阅且配置了自动请求，则弹出授权
      if (options.autoRequest !== false) {
        const title = options.title || '消息通知'
        const content = options.content || this.getSubscribePromptContent(templateType)
        
        // 显示确认对话框
        const confirmed = await this.showSubscribeConfirm(title, content)
        
        if (confirmed) {
          const result = await this.requestSubscribe(templateType, options)
          return result.success && result.data[templateType]?.subscribed
        }
      }
      
      return false
    } catch (error) {
      console.error('智能订阅检查失败:', error)
      return false
    }
  }

  /**
   * 显示订阅确认对话框
   * @param {string} title 标题
   * @param {string} content 内容
   * @returns {Promise<boolean>} 用户是否确认
   */
  showSubscribeConfirm(title, content) {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        confirmText: '立即订阅',
        cancelText: '暂不订阅',
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  /**
   * 获取订阅提示内容
   * @param {string} templateType 模板类型
   * @returns {string} 提示内容
   */
  getSubscribePromptContent(templateType) {
    switch (templateType) {
      case TEMPLATE_TYPES.REPAIR_NOTICE:
        return '订阅报修通知后，您将及时收到报修进度更新消息'
      case TEMPLATE_TYPES.PROPERTY_NOTICE:
        return '订阅物业通知后，您将及时收到重要的物业公告信息'
      default:
        return '订阅消息通知后，您将及时收到相关信息'
    }
  }

  /**
   * 批量检查多个模板的订阅状态
   * @param {Array} templateTypes 模板类型数组
   * @returns {Promise<Object>} 各模板的订阅状态
   */
  async batchCheckSubscribeStatus(templateTypes) {
    const results = {}
    
    for (const type of templateTypes) {
      try {
        results[type] = await this.checkSubscribeStatus(type)
      } catch (error) {
        console.error(`检查${type}订阅状态失败:`, error)
        results[type] = false
      }
    }
    
    return results
  }
}

// 创建单例实例
const subscribeManager = new SubscribeManager()

// 导出常量和实例
export {
  TEMPLATE_IDS,
  TEMPLATE_TYPES,
  subscribeManager
}

export default subscribeManager

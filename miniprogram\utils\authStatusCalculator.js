// 认证状态计算器 - 专门处理用户认证状态逻辑
class AuthStatusCalculator {
  /**
   * 计算用户认证状态
   * @param {Object} userData 用户数据
   * @returns {string} 认证状态：none, pending, verified
   */
  static calculateAuthStatus(userData) {
    if (!userData) return 'none'

    const hasBindPhone = userData.hasBindPhone || false
    const isHouseAuth = userData.isHouseAuth || false
    const hasOwnerInfo = !!(userData.ownerInfo)

    if (isHouseAuth) {
      return 'verified'  // 已认证
    } else if (hasBindPhone && hasOwnerInfo) {
      return 'pending'   // 待认证（有业主信息但无房屋）
    } else {
      return 'none'      // 未认证
    }
  }

  /**
   * 检查是否已登录
   * @param {Object} state 状态对象
   * @returns {boolean} 是否已登录
   */
  static isLoggedIn(state) {
    return state.isLogin && state.hasBindPhone
  }

  /**
   * 检查是否已完成房屋认证
   * @param {Object} state 状态对象
   * @returns {boolean} 是否已认证
   */
  static isHouseAuthenticated(state) {
    return state.isHouseAuth
  }

  /**
   * 从存储数据计算初始状态
   * @param {Object} storageData 存储数据
   * @returns {Object} 计算后的状态
   */
  static calculateInitialState(storageData) {
    const { token, userInfo, ownerInfo, houseInfo } = storageData

    const isLogin = !!(token && userInfo)
    const hasBindPhone = !!(userInfo && userInfo.mobile)
    const isHouseAuth = !!(ownerInfo && houseInfo)

    const authStatus = this.calculateAuthStatus({
      hasBindPhone,
      isHouseAuth,
      ownerInfo
    })

    return {
      isLogin,
      hasBindPhone,
      isHouseAuth,
      authStatus
    }
  }
}

export default AuthStatusCalculator

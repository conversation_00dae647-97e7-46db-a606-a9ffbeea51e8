<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小区公告评论管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="comment-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                评论用户：<input type="text" name="userName"/>
                            </li>
                            <li>
                                公告标题：<input type="text" name="noticeTitle"/>
                            </li>
                            <li>
                                用户类型：<select name="userType">
                                    <option value="">所有</option>
                                    <option value="wx_user">微信用户</option>
                                    <option value="sys_user">系统用户</option>
                                </select>
                            </li>
                            <li>
                                评论状态：<select name="status">
                                    <option value="">所有</option>
                                    <option value="0">正常</option>
                                    <option value="1">已删除</option>
                                    <option value="2">审核中</option>
                                </select>
                            </li>
                            <li>
                                评论内容：<input type="text" name="content"/>
                            </li>
                            <li>
                                房屋名称：<input type="text" name="houseName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:notice:remove">
                    <i class="fa fa-remove"></i> 批量删除
                </a>
                <a class="btn btn-warning multiple disabled" onclick="auditComments('2')" shiro:hasPermission="system:notice:edit">
                    <i class="fa fa-eye-slash"></i> 批量隐藏
                </a>
                <a class="btn btn-info multiple disabled" onclick="auditComments('0')" shiro:hasPermission="system:notice:edit">
                    <i class="fa fa-eye"></i> 批量显示
                </a>
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-success" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice/allComments";
        var removeUrl = ctx + "system/notice/comments/remove";

        $(function() {
            var options = {
                url: prefix + "/list",
                removeUrl: removeUrl,
                modalName: "评论",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'commentId',
                    title: '评论ID',
                    visible: false
                },
                {
                    field: 'noticeTitle',
                    title: '公告标题',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '未知公告';
                    }
                },
                {
                    field: 'userName',
                    title: '评论住户',
                    formatter: function(value, row, index) {
                        var badge = row.userType === 'sys_user' ? 
                            '<span class="badge badge-danger">管理端</span>' :
                            '<span class="badge badge-success">小程序</span>';
                        return value + ' ' + badge;
                    }
                },
                {
                    field: 'content',
                    title: '评论内容',
                    formatter: function(value, row, index) {
                        if (value && value.length > 40) {
                            return '<span title="' + value + '">' + value.substring(0, 40) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'houseName',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="badge badge-info">' + value + '</span>';
                        }
                        return '<span class="text-muted">未绑定</span>';
                    }
                },
                {
                    field: 'parentId',
                    title: '类型',
                    formatter: function(value, row, index) {
                        return value ? '<span class="badge badge-info">回复</span>' : '<span class="badge badge-primary">评论</span>';
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value === '0') {
                            return '<span class="badge badge-success">正常</span>';
                        } else if (value === '1') {
                            return '<span class="badge badge-danger">已删除</span>';
                        } else if (value === '2') {
                            return '<span class="badge badge-warning">审核中</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'createTime',
                    title: '评论时间',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewComment(\'' + row.commentId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewNotice(\'' + row.noticeId + '\')"><i class="fa fa-file-text"></i>查看公告</a> ');
                        
                        if (row.status === '0') {
                            actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="auditComment(\'' + row.commentId + '\', \'2\')"><i class="fa fa-eye-slash"></i>隐藏</a> ');
                        } else if (row.status === '2') {
                            actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="auditComment(\'' + row.commentId + '\', \'0\')"><i class="fa fa-eye"></i>显示</a> ');
                        }
                        
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="deleteComment(\'' + row.commentId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 查看评论详情
        function viewComment(commentId) {
            // 从表格中获取评论数据
            var table = $("#bootstrap-table");
            var data = table.bootstrapTable('getData');
            var comment = null;

            for (var i = 0; i < data.length; i++) {
                if (data[i].commentId == commentId) {
                    comment = data[i];
                    break;
                }
            }

            if (!comment) {
                $.modal.msgError("评论数据不存在");
                return;
            }

            var statusText = '';
            if (comment.status === '0') {
                statusText = '<span class="badge badge-success">正常</span>';
            } else if (comment.status === '1') {
                statusText = '<span class="badge badge-danger">已删除</span>';
            } else if (comment.status === '2') {
                statusText = '<span class="badge badge-warning">审核中</span>';
            }

            var typeText = comment.parentId ?
                '<span class="badge badge-info">回复</span>' :
                '<span class="badge badge-primary">评论</span>';

            var userTypeText = comment.userType === 'sys_user' ?
                '<span class="badge badge-danger">管理员</span>' :
                '<span class="badge badge-success">小程序</span>';

            var createTime = $.common.dateFormat(comment.createTime, 'yyyy-MM-dd HH:mm:ss');

            var content = '<div class="row">' +
                '<div class="col-sm-12">' +
                '<table class="table table-bordered">' +
                '<tr><td width="120px"><strong>评论ID</strong></td><td>' + comment.commentId + '</td></tr>' +
                '<tr><td><strong>所属公告</strong></td><td>' + (comment.noticeTitle || '未知公告') + '</td></tr>' +
                '<tr><td><strong>评论用户</strong></td><td>' + comment.userName + ' ' + userTypeText + '</td></tr>' +
                '<tr><td><strong>评论类型</strong></td><td>' + typeText + '</td></tr>' +
                '<tr><td><strong>评论状态</strong></td><td>' + statusText + '</td></tr>' +
                '<tr><td><strong>评论时间</strong></td><td>' + createTime + '</td></tr>' +
                '<tr><td><strong>评论内容</strong></td><td style="word-wrap: break-word; max-width: 400px;">' + comment.content + '</td></tr>' +
                '</table>' +
                '</div>' +
                '</div>';

            // 使用自定义模态框
            var modalHtml = '<div class="modal fade" id="commentDetailModal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog modal-lg" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<h4 class="modal-title">评论详情</h4>' +
                '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
                '</div>' +
                '<div class="modal-body">' + content + '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除已存在的模态框
            $('#commentDetailModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalHtml);
            $('#commentDetailModal').modal('show');
        }

        // 查看公告详情
        function viewNotice(noticeId) {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/view/" + noticeId;
            $.modal.openTab("公告详情", url);
        }

        // 审核评论
        function auditComment(commentId, status) {
            var statusText = status === '2' ? '隐藏' : '显示';
            $.modal.confirm("确定要" + statusText + "这条评论吗？", function() {
                $.ajax({
                    url: ctx + "system/notice/comments/audit",
                    type: "POST",
                    data: {
                        commentId: commentId,
                        status: status
                    },
                    dataType: "json",
                    success: function(result) {
                        if (result.code === 0) {
                            $.modal.msgSuccess("操作成功");
                            $("#bootstrap-table").bootstrapTable('refresh');
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        $.modal.msgError("操作失败：" + error);
                    }
                });
            });
        }

        // 批量审核评论
        function auditComments(status) {
            var rows = $.table.selectColumns("commentId");
            if (rows.length === 0) {
                $.modal.msgError("请选择要操作的数据");
                return;
            }

            var statusText = status === '2' ? '隐藏' : '显示';
            $.modal.confirm("确定要" + statusText + "选中的评论吗？", function() {
                // 批量处理每个评论
                var promises = [];
                for (var i = 0; i < rows.length; i++) {
                    promises.push(
                        $.ajax({
                            url: ctx + "system/notice/comments/audit",
                            type: "POST",
                            data: {
                                commentId: rows[i],
                                status: status
                            },
                            dataType: "json"
                        })
                    );
                }

                // 使用jQuery的when方法替代Promise.all以提高兼容性
                $.when.apply($, promises).done(function() {
                    var results = arguments;
                    var successCount = 0;

                    // 处理单个结果和多个结果的情况
                    if (promises.length === 1) {
                        if (results[0] && results[0].code === 0) {
                            successCount = 1;
                        }
                    } else {
                        for (var i = 0; i < results.length; i++) {
                            if (results[i] && results[i][0] && results[i][0].code === 0) {
                                successCount++;
                            }
                        }
                    }

                    $.modal.msgSuccess("成功处理 " + successCount + " 条评论");
                    $("#bootstrap-table").bootstrapTable('refresh');
                }).fail(function() {
                    $.modal.msgError("批量操作失败");
                });
            });
        }

        // 删除评论
        function deleteComment(commentId) {
            $.modal.confirm("确定要删除这条评论吗？删除后无法恢复。", function() {
                $.ajax({
                    url: ctx + "system/notice/comments/remove",
                    type: "POST",
                    data: {
                        ids: commentId
                    },
                    dataType: "json",
                    success: function(result) {
                        if (result.code === 0) {
                            $.modal.msgSuccess("删除成功");
                            $("#bootstrap-table").bootstrapTable('refresh');
                        } else {
                            $.modal.msgError(result.msg);
                        }
                    },
                    error: function(xhr, status, error) {
                        $.modal.msgError("删除失败：" + error);
                    }
                });
            });
        }

        // 导出数据
        function exportData() {
            $.modal.confirm("确定要导出评论数据吗？", function() {
                $.post(prefix + "/export", $("#comment-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

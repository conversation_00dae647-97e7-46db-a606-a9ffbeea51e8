package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.IpUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/wx/common")
public class WxCommonController extends BaseWxController {

    /**
     * 获取客户端IP地址
     */
    @GetMapping("/getClientIP")
    public AjaxResult getClientIP() {
        try {
            String clientIP = IpUtils.getIpAddr(getRequest());
            Map<String, Object> result = new HashMap<>();
            result.put("ip", clientIP);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取客户端IP失败: " + e.getMessage(), e);
            return AjaxResult.error("获取客户端IP失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户OpenID
     */
    @GetMapping("/getOpenId")
    public AjaxResult getOpenId() {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("openid", currentUser.getOpenId());
            result.put("userId", currentUser.getUserId());
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取OpenID失败: " + e.getMessage(), e);
            return AjaxResult.error("获取OpenID失败: " + e.getMessage());
        }
    }
}

package com.ehome.oc.controller.wx;

import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.IdUtils;
import com.ehome.oc.service.ShareStatisticsService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信分享统计控制器
 */
@RestController
@RequestMapping("/api/wx/share")
public class WxShareController extends BaseWxController {

    @Autowired
    private ShareStatisticsService shareStatisticsService;

    /**
     * 记录分享行为
     */
    @PostMapping("/record")
    public AjaxResult recordShare(@RequestBody Map<String, Object> params) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String shareId = IdUtils.simpleUUID();
            String userId = currentUser.getOwnerId();
            if (StringUtils.isEmpty(userId)) {
                userId = String.valueOf(currentUser.getUserId());
            }
            String openid = currentUser.getOpenId();
            String nickname = currentUser.getUsername();


            String communityId = currentUser.getCommunityId();

            // 获取请求信息
            HttpServletRequest request = getRequest();
            String ipAddress = IpUtils.getIpAddr(request);
            String userAgent = request.getHeader("User-Agent");

            // 分享参数
            String shareType = (String) params.get("shareType"); // app_message 或 timeline
            String shareSource = (String) params.getOrDefault("shareSource", "index");
            String sourceId = (String) params.get("sourceId");
            String sourceTitle = (String) params.get("sourceTitle");
            String shareTitle = (String) params.get("shareTitle");
            String shareDesc = (String) params.get("shareDesc");
            String sharePath = (String) params.get("sharePath");
            String shareImage = (String) params.get("shareImage");

            // 插入分享记录
            Record shareRecord = new Record();
            shareRecord.set("id", shareId);
            shareRecord.set("user_id", userId);
            shareRecord.set("openid", openid);
            shareRecord.set("nickname", nickname);
            shareRecord.set("share_type", shareType);
            shareRecord.set("share_source", shareSource);
            shareRecord.set("source_id", sourceId);
            shareRecord.set("source_title", sourceTitle);
            shareRecord.set("share_title", shareTitle);
            shareRecord.set("share_desc", shareDesc);
            shareRecord.set("share_path", sharePath);
            shareRecord.set("share_image", shareImage);
            shareRecord.set("community_id", communityId);
            shareRecord.set("ip_address", ipAddress);
            shareRecord.set("user_agent", userAgent);
            shareRecord.set("create_time", new Date());

            Db.save("eh_wx_share_record", shareRecord);

            // 返回分享ID，用于后续访问统计
            Map<String, Object> result = new HashMap<>();
            result.put("shareId", shareId);
            return AjaxResult.success("分享记录成功", result);

        } catch (Exception e) {
            logger.error("记录分享失败", e);
            return AjaxResult.error("记录分享失败: " + e.getMessage());
        }
    }

    /**
     * 记录分享访问
     */
    @PostMapping("/visit")
    public AjaxResult recordVisit(@RequestBody Map<String, Object> params) {
        try {
            String shareId = (String) params.get("shareId");

            if (StringUtils.isEmpty(shareId)) {
                return AjaxResult.error("分享记录ID不能为空");
            }

            // 通过分享ID查找分享记录
            Record shareRecord = Db.findFirst("SELECT * FROM eh_wx_share_record WHERE id = ?", shareId);
            if (shareRecord == null) {
                return AjaxResult.error("分享记录不存在");
            }

            LoginUser currentUser = getCurrentUser();
            String visitorUserId = null;
            String visitorOpenid = null;
            String visitorNickname = null;
            boolean isNewUser = true;

            if (currentUser != null) {
                visitorUserId = currentUser.getOwnerId();
                if (StringUtils.isEmpty(visitorUserId)) {
                    visitorUserId = String.valueOf(currentUser.getUserId());
                }
                visitorOpenid = currentUser.getOpenId();
                visitorNickname = currentUser.getUsername();
                isNewUser = false; // 已登录用户不算新用户
            }

            // 获取请求信息
            HttpServletRequest request = getRequest();
            String ipAddress = IpUtils.getIpAddr(request);
            String userAgent = request.getHeader("User-Agent");
            String sessionId = request.getSession().getId();

            // 检查是否已经记录过此次访问（防重复）
            String checkSql = "SELECT COUNT(*) as count FROM eh_wx_share_visit WHERE share_record_id = ? AND ip_address = ? AND session_id = ? AND visit_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            Record existRecord = Db.findFirst(checkSql, shareId, ipAddress, sessionId);
            if (existRecord != null && existRecord.getInt("count") > 0) {
                return AjaxResult.success("访问已记录");
            }

            // 插入访问记录
            Record visitRecord = new Record();
            visitRecord.set("id", IdUtils.simpleUUID());
            visitRecord.set("share_record_id", shareId);
            visitRecord.set("visitor_openid", visitorOpenid);
            visitRecord.set("visitor_user_id", visitorUserId);
            visitRecord.set("visitor_nickname", visitorNickname);
            visitRecord.set("is_new_user", isNewUser ? 1 : 0);
            visitRecord.set("visit_time", new Date());
            visitRecord.set("ip_address", ipAddress);
            visitRecord.set("user_agent", userAgent);
            visitRecord.set("session_id", sessionId);
            visitRecord.set("stay_duration", 0);
            visitRecord.set("page_views", 1);
            visitRecord.set("community_id", shareRecord.getStr("community_id"));

            Db.save("eh_wx_share_visit", visitRecord);

            // 异步更新统计数据
            try {
                shareStatisticsService.updateShareStatistics(shareId);
            } catch (Exception e) {
                // 统计更新失败不影响主流程
                logger.warn("更新分享统计失败: shareId = {}", shareId, e);
            }

            return AjaxResult.success("访问记录成功");

        } catch (Exception e) {
            logger.error("记录分享访问失败", e);
            return AjaxResult.error("记录分享访问失败: " + e.getMessage());
        }
    }

    /**
     * 获取分享统计数据
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            LoginUser currentUser = getCurrentUser();
            String communityId = currentUser.getCommunityId();

            // 分享总数统计
            String shareSql = "SELECT COUNT(*) as total_shares, " +
                    "SUM(CASE WHEN share_type = 'app_message' THEN 1 ELSE 0 END) as friend_shares, " +
                    "SUM(CASE WHEN share_type = 'timeline' THEN 1 ELSE 0 END) as timeline_shares " +
                    "FROM eh_wx_share_record WHERE community_id = ? AND create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            Record shareStats = Db.findFirst(shareSql, communityId, days);

            // 访问统计
            String visitSql = "SELECT COUNT(*) as total_visits, COUNT(DISTINCT visitor_openid) as unique_visitors, " +
                    "SUM(is_new_user) as new_users FROM eh_wx_share_visit v " +
                    "JOIN eh_wx_share_record r ON v.share_record_id = r.id " +
                    "WHERE r.community_id = ? AND v.visit_time >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            Record visitStats = Db.findFirst(visitSql, communityId, days);

            // 分享排行榜
            String rankSql = "SELECT r.nickname, COUNT(*) as share_count FROM eh_wx_share_record r " +
                    "WHERE r.community_id = ? AND r.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    "GROUP BY r.user_id, r.nickname ORDER BY share_count DESC LIMIT 10";
            List<Record> shareRank = Db.find(rankSql, communityId, days);

            Map<String, Object> result = new HashMap<>();
            result.put("shareStats", shareStats.toMap());
            result.put("visitStats", visitStats.toMap());
            result.put("shareRank", shareRank);

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取分享统计失败", e);
            return AjaxResult.error("获取分享统计失败: " + e.getMessage());
        }
    }


}

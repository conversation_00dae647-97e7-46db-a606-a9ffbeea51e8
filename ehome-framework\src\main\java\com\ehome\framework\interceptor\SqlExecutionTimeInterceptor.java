package com.ehome.framework.interceptor;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * MyBatis SQL执行时间拦截器
 * 用于记录SQL执行时间到统一的SQL日志文件
 * 
 * <AUTHOR>
 */
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class SqlExecutionTimeInterceptor implements Interceptor {
    
    private static final Logger sqlLogger = LoggerFactory.getLogger("sql-log");
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        
        String sqlId = mappedStatement.getId();
        String sqlType = mappedStatement.getSqlCommandType().toString();
        
        Object result = null;
        Throwable exception = null;
        
        try {
            result = invocation.proceed();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录SQL执行时间日志
            logSqlExecution(sqlId, sqlType, executionTime, parameter, result, exception);
        }
    }
    
    /**
     * 记录SQL执行日志
     */
    private void logSqlExecution(String sqlId, String sqlType, long executionTime, 
                                Object parameter, Object result, Throwable exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[MyBatis] ");
        logMessage.append("SQL执行 - ");
        logMessage.append("类型: ").append(sqlType).append(", ");
        logMessage.append("方法: ").append(sqlId).append(", ");
        logMessage.append("执行时间: ").append(executionTime).append("ms");
        
        if (parameter != null) {
            logMessage.append(", 参数: ").append(formatParameter(parameter));
        }
        
        if (result != null && sqlType.equals("SELECT")) {
            logMessage.append(", 结果数量: ").append(getResultCount(result));
        }
        
        if (exception != null) {
            logMessage.append(", 异常: ").append(exception.getMessage());
            sqlLogger.error(logMessage.toString(), exception);
        } else {
            // 根据执行时间选择日志级别
            if (executionTime > 1000) {
                // 超过1秒的SQL记录为WARN级别
                sqlLogger.warn(logMessage.toString() + " [慢SQL警告]");
            } else if (executionTime > 500) {
                // 超过500ms的SQL记录为INFO级别
                sqlLogger.info(logMessage.toString() + " [性能关注]");
            } else {
                // 正常SQL记录为DEBUG级别
                sqlLogger.debug(logMessage.toString());
            }
        }
    }
    
    /**
     * 格式化参数信息
     */
    private String formatParameter(Object parameter) {
        if (parameter == null) {
            return "null";
        }
        
        String paramStr = parameter.toString();
        // 限制参数长度，避免日志过长
        if (paramStr.length() > 200) {
            return paramStr.substring(0, 200) + "...";
        }
        return paramStr;
    }
    
    /**
     * 获取结果数量
     */
    private String getResultCount(Object result) {
        if (result == null) {
            return "0";
        }
        
        if (result instanceof java.util.List) {
            return String.valueOf(((java.util.List<?>) result).size());
        } else if (result instanceof java.util.Map) {
            return "1(Map)";
        } else {
            return "1";
        }
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties配置拦截器参数
    }
}

-- 为eh_wx_nav表添加parent_id字段，支持二级菜单功能
-- 用于实现菜单的父子关系，0表示顶级菜单

ALTER TABLE `eh_wx_nav` 
ADD COLUMN `parent_id` int(11) DEFAULT 0 COMMENT '父菜单ID(0表示顶级菜单)' 
AFTER `nav_id`;

-- 为parent_id字段添加索引，提高查询性能
ALTER TABLE `eh_wx_nav` 
ADD INDEX `idx_parent_id` (`parent_id`);

-- 验证字段添加结果的查询语句
-- SELECT nav_id, nav_name, parent_id, sort, source 
-- FROM eh_wx_nav 
-- WHERE community_id = 'your_community_id' 
-- ORDER BY parent_id ASC, sort ASC;

-- 修复分享统计表结构
-- 问题：id字段长度不够，UUID()函数生成的ID超过32字符

-- 方案1：扩展id字段长度到36字符（推荐）
ALTER TABLE eh_wx_share_statistics MODIFY COLUMN id VARCHAR(36) NOT NULL COMMENT '主键ID';

-- 方案2：如果需要保持32字符，可以使用REPLACE(UUID(), '-', '')去掉连字符
-- 但这样需要修改Java代码中的ID生成逻辑

-- 验证修改结果
DESCRIBE eh_wx_share_statistics;

-- 清理可能的错误数据（如果有的话）
DELETE FROM eh_wx_share_statistics WHERE LENGTH(id) > 36;

-- 测试插入一条记录验证修复效果
-- INSERT INTO eh_wx_share_statistics (
--     id, share_record_id, total_visits, unique_visitors, new_users,
--     total_stay_duration, avg_stay_duration, last_visit_time, update_time
-- ) VALUES (
--     UUID(), 'test_share_id', 1, 1, 0, 0, 0.00, NOW(), NOW()
-- );

-- 查看测试结果
-- SELECT * FROM eh_wx_share_statistics WHERE share_record_id = 'test_share_id';

-- 清理测试数据
-- DELETE FROM eh_wx_share_statistics WHERE share_record_id = 'test_share_id';

-- 创建车位房屋关系表
CREATE TABLE `eh_parking_house_rel` (
    `rel_id` varchar(32) NOT NULL COMMENT '关系ID',
    `parking_id` varchar(32) NOT NULL COMMENT '车位ID',
    `house_id` varchar(32) NOT NULL COMMENT '房屋ID',
    `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认:0否 1是',
    `check_status` tinyint(1) DEFAULT '1' COMMENT '审核状态:0未审核 1已审核 2审核不通过',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`rel_id`),
    UNIQUE KEY `uk_parking_house` (`parking_id`,`house_id`),
    KEY `idx_house` (`house_id`),
    KEY `idx_parking` (`parking_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='车位房屋关系表';

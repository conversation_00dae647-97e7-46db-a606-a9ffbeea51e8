package com.ehome.oc.controller.wxconfig;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 微信用户管理Controller
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/oc/wxuser")
public class WxUserController extends BaseController {

    private static final String PREFIX = "oc/wx";

    /**
     * 用户管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/user-list";
    }

    /**
     * 查询用户列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select u.*, o.owner_name, o.mobile as owner_mobile, o.house_info as house_room",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    /**
     * 构建查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_user u");
        sql.append("left join eh_owner o on u.owner_id = o.owner_id");
        sql.append("where 1=1");

        // 社区过滤
        String communityId = getSysUser().getCommunityId();
        if (StringUtils.isNotEmpty(communityId)) {
            sql.append(communityId, "and u.community_id = ?");
        }

        // 搜索条件
        sql.appendLike(params.getString("nick_name"), "and u.nick_name like ?");
        sql.appendLike(params.getString("mobile"), "and u.mobile like ?");
        sql.append(params.getString("status"), "and u.status = ?");

        // 时间范围查询
        if (StringUtils.isNotEmpty(params.getString("beginTime"))) {
            sql.append(params.getString("beginTime"), "and u.create_time >= ?");
        }
        if (StringUtils.isNotEmpty(params.getString("endTime"))) {
            sql.append(params.getString("endTime"), "and u.create_time <= ?");
        }

        sql.append("order by u.create_time desc");
        return sql;
    }

    /**
     * 查询用户登录日志
     */
    @PostMapping("/loginLogs")
    @ResponseBody
    public TableDataInfo loginLogs() {
        JSONObject params = getParams();
        String userId = params.getString("userId");
        
        if (StringUtils.isEmpty(userId)) {
            return getDataTable(new Page<>());
        }
        
        EasySQL sql = new EasySQL();
        sql.append("from wx_login_log where 1=1");
        sql.append(userId, "and user_id = ?");
        
        // 时间范围查询
        if (StringUtils.isNotEmpty(params.getString("beginTime"))) {
            sql.append(params.getString("beginTime"), "and login_date >= ?");
        }
        if (StringUtils.isNotEmpty(params.getString("endTime"))) {
            sql.append(params.getString("endTime"), "and login_date <= ?");
        }
        
        sql.append("order by login_date desc");
        
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),
            "select *",
            sql.toFullSql()
        );
        
        return getDataTable(paginate);
    }

    /**
     * 获取用户详情
     */
    @PostMapping("/detail")
    @ResponseBody
    public AjaxResult detail() {
        JSONObject params = getParams();
        String userId = params.getString("userId");
        
        if (StringUtils.isEmpty(userId)) {
            return AjaxResult.error("用户ID不能为空");
        }
        
        Record user = Db.findFirst(
            "select u.*, o.owner_name, o.mobile as owner_mobile, h.room as house_room from eh_wx_user u " +
            "left join eh_owner o on u.owner_id = o.owner_id " +
            "left join eh_house_owner_rel hor on o.owner_id = hor.owner_id and hor.is_default = 1 " +
            "left join eh_house_info h on hor.house_id = h.house_id " +
            "where u.user_id = ?", userId
        );
        
        if (user == null) {
            return AjaxResult.error("用户不存在");
        }
        
        return AjaxResult.success(user.toMap());
    }

    /**
     * 更新用户状态
     */
    @Log(title = "微信用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus() {
        JSONObject params = getParams();
        String userId = params.getString("userId");
        String status = params.getString("status");
        
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(status)) {
            return AjaxResult.error("参数不能为空");
        }
        
        Record user = new Record();
        user.set("user_id", userId);
        user.set("status", status);

        boolean success = Db.update("eh_wx_user", "user_id", user);
        return toAjax(success);
    }

    /**
     * 登录日志弹窗页面
     */
    @GetMapping("/loginLogs/{userId}")
    public String loginLogsPage(@PathVariable("userId") String userId, ModelMap mmap) {
        // 获取用户基本信息
        Record user = Db.findFirst("select * from eh_wx_user where user_id = ?", userId);
        mmap.put("user", user != null ? user.toMap() : null);
        mmap.put("userId", userId);
        return PREFIX + "/login-logs";
    }
}

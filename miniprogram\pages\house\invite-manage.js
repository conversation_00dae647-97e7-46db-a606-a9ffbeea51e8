const app = getApp()

Page({
  data: {
    sentInvites: [],
    loading: false
  },

  onLoad() {
    this.loadSentInvites()
  },

  onShow() {
    this.loadSentInvites()
  },

  // 加载我发出的邀请
  async loadSentInvites() {
    try {
      this.setData({ loading: true })

      const res = await app.request({
        url: '/api/wx/house/card-invite/my-invites',
        method: 'GET'
      })

      if (res.code === 0) {
        const invites = (res.data || []).map(item => ({
          ...item,
          used_time_formatted: item.used_time ? this.formatTime(item.used_time) : ''
        }))
        this.setData({ sentInvites: invites })
      } else {
        wx.showToast({
          title: res.msg || '获取邀请列表失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载发出的邀请失败:', error)
      wx.showToast({
        title: '获取邀请列表失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 去邀请住户
  goInvite() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  },

  // 格式化时间
  formatTime(time) {
    if (!time) return ''
    
    const date = new Date(time)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }
})

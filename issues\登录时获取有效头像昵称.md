# 登录时获取有效头像昵称功能实现

## 任务背景
用户反馈登录时送到后台的 nickName 和 avatarUrl 都是无效的值，需要参考 profile 页面的获取授权头像和昵称的实现，在登录时获取有效的用户信息。

## 问题分析
1. `wx.getUserProfile` 在新版本微信中返回的头像和昵称可能是默认值
2. 需要使用新的授权方式：头像用 `chooseAvatar`，昵称用 `getUserProfile`
3. profile 页面已有正确的实现方式

## 解决方案
在登录成功后添加头像昵称授权流程：
1. 登录成功后检查头像昵称是否为默认值
2. 弹出友好的授权引导，用户可选择授权或跳过
3. 使用 `chooseAvatar` 获取头像，`getUserProfile` 获取昵称
4. 调用 `/api/wx/auth/update` 接口更新

## 修改文件

### 1. miniprogram/pages/login/index.js
**新增数据状态：**
- `showUserInfoModal`: 显示用户信息授权弹窗
- `showAvatarPopup`: 显示头像选择弹窗
- `showNicknamePopup`: 显示昵称编辑弹窗
- `tempNickname`: 临时昵称
- `currentUserInfo`: 当前用户信息

**新增方法：**
- `checkUserInfoAuth()`: 检查用户信息授权
- `closeUserInfoModal()`: 关闭用户信息授权弹窗
- `startAvatarAuth()`: 开始头像授权
- `onChooseAvatar()`: 头像选择回调
- `uploadAvatarFile()`: 上传头像文件到服务器
- `continueAfterAvatar()`: 头像授权后继续流程
- `startNicknameAuth()`: 开始昵称授权
- `getWechatNickname()`: 获取微信昵称
- `confirmNickname()`: 确认昵称
- `completeAuth()`: 完成授权流程

**修改逻辑：**
- 在 `continueLoginFlow()` 中添加用户信息检查
- 检查头像是否包含默认标识（wx.qlogo.cn、/132）
- 检查昵称是否为"微信用户"等默认值

### 2. miniprogram/pages/login/index.wxml
**新增弹窗组件：**
- 用户信息授权引导弹窗
- 头像选择弹窗（使用 `open-type="chooseAvatar"`）
- 昵称编辑弹窗（使用 `van-field` 输入框）

**功能特点：**
- 用户可以选择跳过授权
- 头像使用原生 button 组件的 chooseAvatar
- 昵称支持手动输入和微信授权获取

### 3. miniprogram/pages/login/index.wxss
**新增样式：**
- `.userinfo-popup`: 用户信息授权弹窗样式
- `.avatar-popup`: 头像选择弹窗样式
- `.nickname-popup`: 昵称编辑弹窗样式
- `.auth-btn`: 授权按钮样式
- `.auth-btn-native`: 原生按钮样式（头像选择）
- `.skip-btn`: 跳过按钮样式
- `.confirm-btn`: 确认按钮样式
- `.nickname-input`: 昵称输入框样式

## 功能流程
1. 用户点击"微信一键登录"
2. 完成基础登录流程
3. **仅首次登录时**检查用户头像昵称是否为默认值
4. 如果是首次登录且信息为默认值，弹出授权引导弹窗
5. 用户可选择"设置头像和昵称"或"暂时跳过"
6. 选择设置时，先进行头像授权
7. 头像授权完成后，**自动尝试获取微信昵称**
8. 如果自动获取成功，直接更新；如果失败，显示手动输入弹窗
9. 所有授权完成后，跳转到首页

## 修正内容
1. **首次登录限制**：只有首次登录且信息为默认值时才提示授权
2. **自动获取昵称**：优先自动获取微信昵称，失败时才显示手动输入
3. **昵称可修改**：支持用户手动输入昵称
4. **简化登录流程**：移除登录时的userInfo传递，头像昵称完全通过单独授权获取
5. **后台优化**：给WxUser添加isFirstLogin字段，简化首次登录判断逻辑
6. **代码优化**：前端方法添加序号标识，简化复杂逻辑，减少冗余代码
7. **UnionID支持**：登录时获取并保存unionid和session_key，支持跨应用用户识别
8. **最终简化**：移除登录时的复杂解密逻辑，保持登录流程简洁，用户信息完全通过后续单独授权获取

## 前端流程序号说明
1. `handleOneClickLogin()` - 一键登录主流程
2. `step2_handleLoginResult()` - 处理登录结果
3. `step3_showPhoneBinding()` - 显示手机号绑定
4. `step4_showHouseAuthGuide()` - 显示房屋认证引导
5. `step5_needUserInfoAuth()` - 检查是否需要用户信息授权
6. `step6_showUserInfoModal()` - 显示用户信息授权弹窗
7. `step7_startAvatarAuth()` - 开始头像授权
8. `step8_checkNicknameAuth()` / `step8_tryGetWechatNickname()` - 检查/获取昵称
9. `step9_completeLogin()` - 完成登录

## 技术要点
1. **头像授权**：使用 `<button open-type="chooseAvatar">` 获取真实头像
2. **昵称授权**：使用 `wx.getUserProfile` 获取昵称，支持手动输入
3. **文件上传**：头像需要先上传到服务器获取URL
4. **状态管理**：更新本地存储和状态管理器中的用户信息
5. **错误处理**：授权失败不影响登录流程，用户可跳过

## 预期效果
- 用户登录后能获取到有效的头像和昵称
- 提供友好的授权体验，不强制用户授权
- 授权信息正确更新到后台数据库
- 保持登录流程的流畅性

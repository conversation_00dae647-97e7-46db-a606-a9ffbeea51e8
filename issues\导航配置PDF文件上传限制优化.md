# 导航配置PDF文件上传限制优化

## 任务背景
用户需求：如果 var source = "infoNav"; pdf文件类别时候允许上传多个，如果是indexNav 只能单选，也就是只有一个pdf文件。同时indexNav类型的PDF应该直接打开，而不是显示内容页面后再点击打开。

## 实施计划

### 1. 前端文件选择器调用逻辑修改
- **文件**: `add.html` 和 `edit.html`
- **修改内容**: 
  - `infoNav`: 调用 `multiSelect.html`（支持多选）
  - `indexNav`: 调用 `selector.html`（单选模式）

### 2. 前端回调函数优化
- **单选回调**: `setSelectedFile()` - 用于indexNav
- **多选回调**: `setSelectedAttachments()` - 用于infoNav
- 根据source类型分别处理文件选择结果

### 3. 后端数据处理逻辑
- **文件**: `WxNavController.java`
- **修改内容**: `processPdfFileList()` 方法
  - `indexNav`: 单选模式，必须设置url字段为PDF文件地址
  - `infoNav`: 多选模式，如果只有一个文件也设置url字段

### 4. 小程序端直接打开PDF
- **文件**: `miniprogram/pages/menu/content.js`
- **修改内容**: 
  - 检查source类型和nav_type
  - `indexNav` + `pdf`: 直接下载并打开PDF文件，成功后返回上一页
  - `infoNav` + `pdf`: 显示PDF文件列表供用户选择

### 5. 后端API增强
- **文件**: `WxDataController.java`
- **修改内容**: `/api/wx/data/menuContent` 接口返回source字段

## 实施结果

### 已完成的修改

1. ✅ **前端文件选择器调用逻辑**
   - 修改了 `add.html` 和 `edit.html` 中的 `selectPdfFiles()` 函数
   - 根据source类型调用不同的文件选择器

2. ✅ **前端回调函数**
   - 优化了 `setSelectedFile()` 和 `setSelectedAttachments()` 函数
   - 根据source类型分别处理文件选择

3. ✅ **后端数据处理**
   - 修改了 `WxNavController.java` 中的 `processPdfFileList()` 方法
   - indexNav类型强制设置url字段

4. ✅ **小程序端直接打开**
   - 修改了 `content.js` 中的PDF处理逻辑
   - indexNav类型直接打开PDF文件

5. ✅ **后端API增强**
   - 修改了 `WxDataController.java` 的 `menuContent` 接口
   - 添加了source字段返回

### 功能验证要点

1. **infoNav类型**:
   - 可以选择多个PDF文件
   - 在小程序中显示PDF文件列表
   - 用户可以选择具体的PDF文件打开

2. **indexNav类型**:
   - 只能选择单个PDF文件
   - 自动设置url字段为PDF文件地址
   - 小程序中直接打开PDF文件，无需显示中间页面

## 技术要点

- 利用现有的 `selector.html`（单选）和 `multiSelect.html`（多选）组件
- 通过source字段区分不同的处理逻辑
- 确保向后兼容性，保留原有的pdf_file和pdf_file_id字段
- 小程序端使用wx.downloadFile和wx.openDocument直接打开PDF

## 注意事项

- 确保数据库中eh_wx_nav表的url、pdf_file、pdf_file_id字段正确更新
- 小程序端需要处理PDF下载失败的降级方案
- 前端界面需要明确显示单选/多选的区别

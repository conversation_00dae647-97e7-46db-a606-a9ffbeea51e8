<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.system.mapper.SysNoticeShareMapper">
    
    <resultMap type="SysNoticeShare" id="SysNoticeShareResult">
        <result property="shareId"       column="share_id"       />
        <result property="noticeId"      column="notice_id"      />
        <result property="userId"        column="user_id"        />
        <result property="userName"      column="user_name"      />
        <result property="userType"      column="user_type"      />
        <result property="communityId"   column="community_id"   />
        <result property="ownerId"       column="owner_id"       />
        <result property="houseId"       column="house_id"       />
        <result property="houseName"     column="house_name"     />
        <result property="sharePlatform" column="share_platform" />
        <result property="shareTime"     column="create_time"    />
        <result property="noticeTitle"   column="notice_title"   />
    </resultMap>
    
    <sql id="selectShareVo">
        select share_id, notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, share_platform, create_time
        from sys_notice_share
    </sql>
    
    <select id="selectShareById" parameterType="Long" resultMap="SysNoticeShareResult">
        <include refid="selectShareVo"/>
        where share_id = #{shareId}
    </select>
    
    <select id="selectShareList" parameterType="SysNoticeShare" resultMap="SysNoticeShareResult">
        <include refid="selectShareVo"/>
        <where>
            <if test="noticeId != null">
                AND notice_id = #{noticeId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userType != null and userType != ''">
                AND user_type = #{userType}
            </if>
            <if test="communityId != null and communityId != ''">
                AND community_id = #{communityId}
            </if>
            <if test="sharePlatform != null and sharePlatform != ''">
                AND share_platform = #{sharePlatform}
            </if>
            <if test="shareTime != null">
                AND date_format(create_time,'%y%m%d') = date_format(#{shareTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectShareByNoticeId" parameterType="Long" resultMap="SysNoticeShareResult">
        <include refid="selectShareVo"/>
        where notice_id = #{noticeId}
        order by create_time desc
    </select>
    
    <select id="selectAllSharesByCommunity" parameterType="SysNoticeShare" resultMap="SysNoticeShareResult">
        select 
            s.share_id, 
            s.notice_id, 
            s.user_id, 
            s.user_name, 
            s.user_type,
            s.community_id,
            s.owner_id,
            s.house_id,
            s.house_name,
            s.share_platform,
            s.create_time,
            n.notice_title
        from sys_notice_share s
        left join sys_notice n on s.notice_id = n.notice_id
        <where>
            AND n.deleted = 0
            <if test="communityId != null and communityId != ''">
                AND s.community_id = #{communityId}
            </if>
            <if test="userName != null and userName != ''">
                AND s.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userType != null and userType != ''">
                AND s.user_type = #{userType}
            </if>
            <if test="sharePlatform != null and sharePlatform != ''">
                AND s.share_platform = #{sharePlatform}
            </if>
            <if test="shareTime != null">
                AND date_format(s.create_time,'%y%m%d') = date_format(#{shareTime},'%y%m%d')
            </if>
            <if test="noticeTitle != null and noticeTitle != ''">
                AND n.notice_title like concat('%', #{noticeTitle}, '%')
            </if>
            <if test="houseName != null and houseName != ''">
                AND s.house_name like concat('%', #{houseName}, '%')
            </if>
        </where>
        order by s.create_time desc
    </select>
    
    <insert id="insertShare" parameterType="SysNoticeShare" useGeneratedKeys="true" keyProperty="shareId">
        insert into sys_notice_share (
            <if test="noticeId != null">notice_id, </if>
            <if test="userId != null and userId != ''">user_id, </if>
            <if test="userName != null and userName != ''">user_name, </if>
            <if test="userType != null and userType != ''">user_type, </if>
            <if test="communityId != null and communityId != ''">community_id, </if>
            <if test="ownerId != null and ownerId != ''">owner_id, </if>
            <if test="houseId != null and houseId != ''">house_id, </if>
            <if test="houseName != null and houseName != ''">house_name, </if>
            <if test="sharePlatform != null and sharePlatform != ''">share_platform, </if>
            create_time
        )values(
            <if test="noticeId != null">#{noticeId}, </if>
            <if test="userId != null and userId != ''">#{userId}, </if>
            <if test="userName != null and userName != ''">#{userName}, </if>
            <if test="userType != null and userType != ''">#{userType}, </if>
            <if test="communityId != null and communityId != ''">#{communityId}, </if>
            <if test="ownerId != null and ownerId != ''">#{ownerId}, </if>
            <if test="houseId != null and houseId != ''">#{houseId}, </if>
            <if test="houseName != null and houseName != ''">#{houseName}, </if>
            <if test="sharePlatform != null and sharePlatform != ''">#{sharePlatform}, </if>
            sysdate()
        )
    </insert>
    
    <update id="updateShare" parameterType="SysNoticeShare">
        update sys_notice_share
        <trim prefix="SET" suffixOverrides=",">
            <if test="sharePlatform != null and sharePlatform != ''">share_platform = #{sharePlatform},</if>
        </trim>
        where share_id = #{shareId}
    </update>
    
    <delete id="deleteShareById" parameterType="Long">
        delete from sys_notice_share where share_id = #{shareId}
    </delete>
    
    <delete id="deleteShareByIds" parameterType="String">
        delete from sys_notice_share where share_id in 
        <foreach item="shareId" collection="array" open="(" separator="," close=")">
            #{shareId}
        </foreach>
    </delete>
    
    <delete id="deleteShareByNoticeId" parameterType="Long">
        delete from sys_notice_share where notice_id = #{noticeId}
    </delete>
    
</mapper>

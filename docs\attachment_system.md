# 通用附件关联系统

## 功能概述

本系统为公告等业务模块提供了通用的附件关联功能，支持多文件选择、附件管理和展示。

## 数据库设计

### 附件关联表 (eh_attachment_relation)

```sql
CREATE TABLE `eh_attachment_relation` (
  `relation_id` varchar(32) NOT NULL COMMENT '关联ID',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型(notice-公告,menu-菜单,etc)',
  `business_id` varchar(32) NOT NULL COMMENT '业务ID',
  `file_id` varchar(32) NOT NULL COMMENT '文件ID',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `community_id` int(11) DEFAULT 0 COMMENT '小区ID',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1删除)',
  PRIMARY KEY (`relation_id`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_community` (`community_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附件关联表';
```

## 后端实现

### 1. 服务接口
- `IEhAttachmentRelationService` - 附件关联服务接口
- `EhAttachmentRelationServiceImpl` - 服务实现类

### 2. 控制器
- `com.ehome.oc.controller.file.com.ehome.admin.controller.file.EhAttachmentRelationController` - 通用附件关联控制器
- `SysNoticeController` - 扩展了附件管理功能

### 3. 主要API接口

#### 获取业务附件列表
```
POST /attachment/list
参数: businessType, businessId
```

#### 保存业务附件关联
```
POST /attachment/save
参数: businessType, businessId, fileIds
```

#### 删除附件关联
```
POST /attachment/delete
参数: relationId
```

## 前端实现

### 1. 多选文件页面
- `file/multiSelect.html` - 支持多文件选择的页面
- 提供文件搜索、筛选、分页功能
- 支持文件上传

### 2. 公告页面集成
- `system/notice/add.html` - 新增公告页面，添加了附件选择功能
- `system/notice/edit.html` - 编辑公告页面，支持附件管理

### 3. JavaScript功能
- 附件选择弹窗
- 附件列表展示
- 附件删除和排序
- 文件类型图标显示

## 使用方法

### 1. 数据库初始化
执行 `sql/eh_attachment_relation.sql` 创建附件关联表

### 2. 在公告中使用附件
1. 进入公告新增/编辑页面
2. 点击"选择附件"按钮
3. 在弹出的文件选择页面中选择需要的文件
4. 确认选择后返回公告编辑页面
5. 保存公告时会自动保存附件关联

### 3. 扩展到其他业务模块
1. 在业务页面中添加附件选择区域
2. 调用 `selectAttachments()` 函数打开文件选择页面
3. 实现 `setSelectedAttachments(attachments)` 回调函数
4. 在保存业务数据时调用附件关联API

## 特性

- ✅ 支持多文件选择
- ✅ 文件类型图标显示
- ✅ 附件排序功能
- ✅ 附件删除功能
- ✅ 通用业务类型支持
- ✅ 数据权限隔离（按小区）
- ✅ 响应式界面设计

## 扩展说明

本系统设计为通用附件关联系统，可以轻松扩展到其他业务模块：

1. **菜单附件**: business_type = 'menu'
2. **通知附件**: business_type = 'notice'  
3. **其他业务**: 自定义 business_type

只需要在对应的业务控制器中集成附件关联服务即可。

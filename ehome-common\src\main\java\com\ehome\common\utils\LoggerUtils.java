package com.ehome.common.utils;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志工具类
 */
public class LoggerUtils {
    
    /**
     * 获取日志实例
     * @param name 日志名称
     * @return Logger实例
     */
    public static Logger getLog(String name) {
        return (Logger) LoggerFactory.getLogger(name);
    }

    /**
     * 获取指定级别的日志实例
     */
    public static Logger getLog(String name, Level level) {
        Logger logger = (Logger) LoggerFactory.getLogger(name);
        logger.setLevel(level);
        return logger;
    }

    /**
     * 检查日志实例是否存在
     */
    public static boolean exists(String name) {
        return LoggerFactory.getLogger(name) != null;
    }
} 
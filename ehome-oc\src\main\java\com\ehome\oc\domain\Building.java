package com.ehome.oc.domain;

import com.ehome.common.core.domain.BaseEntity;

/**
 * 楼栋信息对象 eh_building
 */
public class Building extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 楼栋ID */
    private String buildingId;

    /** 小区ID */
    private String communityId;

    /** 楼栋名称或编号 */
    private String name;

    /** 楼栋别称 */
    private String aliasName;

    /** 单元总数 */
    private Integer totalUnits;

    /** 户数 */
    private Integer houseCount;

    /** 房屋面积 */
    private Double houseArea;

    /** 楼栋管家 */
    private String manager;


    public String getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(String buildingId) {
        this.buildingId = buildingId;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public Integer getTotalUnits() {
        return totalUnits;
    }

    public void setTotalUnits(Integer totalUnits) {
        this.totalUnits = totalUnits;
    }

    public Integer getHouseCount() {
        return houseCount;
    }

    public void setHouseCount(Integer houseCount) {
        this.houseCount = houseCount;
    }

    public Double getHouseArea() {
        return houseArea;
    }

    public void setHouseArea(Double houseArea) {
        this.houseArea = houseArea;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }
} 
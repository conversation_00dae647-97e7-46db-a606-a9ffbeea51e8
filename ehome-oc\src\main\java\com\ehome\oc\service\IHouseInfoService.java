package com.ehome.oc.service;

import com.ehome.oc.domain.HouseInfo;
import com.jfinal.plugin.activerecord.Record;

public interface IHouseInfoService {

    public HouseInfo recordToObj(Record record);


    /**
     * 新增房屋
     */
    int addHouse(HouseInfo house);


    /**
     * 删除房屋
     */
    int deleteHouse(Long houseId);

    /**
     * 根据ID查询房屋
     */
    HouseInfo selectHouseById(Long houseId);

    /**
     * 统计用户的房屋数量
     */
    int countHouseByUserId(String  ownerId);

    /**
     * 设置默认房屋
     * @param houseId 房屋ID
     * @param wxUserId 用户ID
     * @return 影响的行数
     */
    int setDefaultHouse(Long houseId, String ownerId);

    void updateOwnerHouseInfo(String ownerId);

    void updateHouseOwnerInfo(String houseId);
}
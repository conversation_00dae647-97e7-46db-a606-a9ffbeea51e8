-- 初始化房屋类型数据字典
-- 1. 插入字典类型
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('房屋类型', 'house_type', '0', 'admin', NOW(), '房屋类型字典')
ON DUPLICATE KEY UPDATE dict_name = VALUES(dict_name), remark = VALUES(remark);

-- 2. 插入字典数据
INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '住宅', '1', 'house_type', '', 'default', 'Y', '0', 'admin', NOW(), '住宅类型'),
(2, '公寓', '2', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '公寓类型'),
(3, '商铺', '3', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '商铺类型'),
(4, '办公', '4', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '办公类型'),
(5, '仓库', '5', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '仓库类型'),
(6, '其他', '6', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '其他类型'),
(7, '厂房', '7', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '厂房类型'),
(8, '多层住宅', '1217', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '多层住宅类型'),
(9, '联排别墅', '1216', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '联排别墅类型'),
(10, '叠加洋房', '1213', 'house_type', '', 'default', 'N', '0', 'admin', NOW(), '叠加洋房类型')
ON DUPLICATE KEY UPDATE 
    dict_label = VALUES(dict_label),
    dict_sort = VALUES(dict_sort),
    remark = VALUES(remark);

// 收费标准配置文件 - 简化JavaScript逻辑

// 字段配置
const CHARGE_FIELD_CONFIG = {
    // 收费类型对应的字段组
    fieldGroups: {
        '1': ['periodic-field'],
        '2': ['meter-field'],
        '3': ['temporary-field']
    },
    
    // 通用字段（所有收费类型都显示）
    commonFields: ['charge-field.periodic-field.meter-field.temporary-field'],
    
    // 子选项组配置
    subGroups: {
        calculation: {
            '200': { show: ['#fixed_amount_group'], hide: ['#unit_price_group'] },
            '100': { show: ['#unit_price_group'], hide: ['#fixed_amount_group'] },
            'default': { show: [], hide: ['#fixed_amount_group', '#unit_price_group'] }
        },
        period: {
            '101': { show: ['#incomplete_month_group'], hide: [] },
            'default': { show: [], hide: ['#incomplete_month_group'] }
        },
        penalty: {
            '1': { show: ['#penalty_config_group'], hide: [] },
            'default': { show: [], hide: ['#penalty_config_group'] }
        }
    }
};

// 选项数据配置
const CHARGE_OPTIONS = {
    precision: [
        { id: 1, name: "元（不保留小数）" },
        { id: 2, name: "角（保留一位小数）" },
        { id: 3, name: "分（保留两位小数）" }
    ],
    
    rounding: [
        { id: 0, name: "四舍五入", expand: "在账单金额中，X下一位的数值小于5会进行舍弃，大于等于5则会进一位" },
        { id: 1, name: "抹零", expand: "在账单金额中，X下一位数值不管小于5还是大于5都直接会进行舍弃" },
        { id: 2, name: "向上取整", expand: "在账单金额中，X下一位数值不管小于5还是大于5都直接会进一位" }
    ],
    
    category: [
        { id: 1, name: "物业管理费" },
        { id: 2, name: "水费" },
        { id: 3, name: "电费" },
        { id: 4, name: "燃气费" },
        { id: 5, name: "停车费" },
        { id: 6, name: "其他费用" }
    ],
    
    calculation: [
        { id: 100, name: "单价*计量方式" },
        { id: 200, name: "固定金额" }
    ],
    
    period: [
        { id: 101, name: "按月生成账单（推荐）" }
    ],
    
    incompleteMonth: [
        { id: 1, name: "按天收费" },
        { id: 2, name: "按月收费" },
        { id: 3, name: "不收费" }
    ],
    
    areaType: [
        { id: 1, name: "房屋建筑面积" },
        { id: 2, name: "房屋使用面积" }
    ],
    
    penalty: [
        { id: 0, name: "不计算违约金" },
        { id: 1, name: "计算违约金" }
    ]
};

// 工具函数
const ChargeStandardUtils = {
    // 初始化选项
    initOptions: function() {
        this.initSelectOptions('#precision_type_select', CHARGE_OPTIONS.precision, 0);
        this.initSelectOptions('#rounding_type_select', CHARGE_OPTIONS.rounding, 0, 'expand');
        this.initSelectOptions('#category_select', CHARGE_OPTIONS.category);
        this.initSelectOptions('#calculation_method_select', CHARGE_OPTIONS.calculation);
        this.initSelectOptions('#period_type_select', CHARGE_OPTIONS.period, 0);
        this.initSelectOptions('#incomplete_month_select', CHARGE_OPTIONS.incompleteMonth, 0);
        this.initSelectOptions('#area_type_select', CHARGE_OPTIONS.areaType, 0);
        this.initSelectOptions('#penalty_handling_select', CHARGE_OPTIONS.penalty, 0);
    },
    
    // 初始化下拉选项
    initSelectOptions: function(selector, options, defaultIndex = -1, expandAttr = null) {
        const $select = $(selector);
        if ($select.length === 0) return;
        
        $select.empty();
        options.forEach((item, index) => {
            const selected = (defaultIndex >= 0 && index === defaultIndex) ? 'selected' : '';
            const expandData = expandAttr && item[expandAttr] ? `data-${expandAttr}="${item[expandAttr]}"` : '';
            $select.append(`<option value="${item.id}" ${expandData} ${selected}>${item.name}</option>`);
        });
    },
    
    // 根据收费类型显示字段
    showFieldsByChargeType: function(chargeType) {
        // 隐藏所有字段
        $('.charge-field').hide();
        this.hideAllSubGroups();
        this.clearAllNames();
        
        // 显示对应的字段组
        const fieldGroups = CHARGE_FIELD_CONFIG.fieldGroups[chargeType] || [];
        fieldGroups.forEach(group => $(`.${group}`).show());
        
        // 显示通用字段
        CHARGE_FIELD_CONFIG.commonFields.forEach(selector => $(selector).show());
        
        // 设置name属性
        this.setNamesForVisibleFields();
    },
    
    // 显示/隐藏子选项组
    toggleSubGroup: function(groupType, value) {
        const config = CHARGE_FIELD_CONFIG.subGroups[groupType];
        if (!config) return;
        
        const setting = config[value] || config['default'];
        setting.show.forEach(selector => $(selector).show());
        setting.hide.forEach(selector => $(selector).hide());
    },
    
    // 隐藏所有子选项组
    hideAllSubGroups: function() {
        $('#fixed_amount_group, #unit_price_group, #incomplete_month_group, #penalty_config_group').hide();
    },
    
    // 清除所有name属性
    clearAllNames: function() {
        $('[data-name]').removeAttr('name');
    },
    
    // 为可见字段设置name属性
    setNamesForVisibleFields: function() {
        $('.charge-field:visible [data-name]').each(function() {
            $(this).attr('name', $(this).data('name'));
        });
    },
    
    // 格式化显示文本
    formatDisplayText: function(type, value, row = null) {
        switch(type) {
            case 'precision':
                const precision = CHARGE_OPTIONS.precision.find(p => p.id == value);
                return precision ? precision.name : '-';
                
            case 'rounding':
                const rounding = CHARGE_OPTIONS.rounding.find(r => r.id == value);
                return rounding ? rounding.name : '-';
                
            case 'category':
                const category = CHARGE_OPTIONS.category.find(c => c.id == value);
                return category ? category.name : '-';
                
            case 'calculation':
                return this.formatCalculationMethod(value, row);
                
            case 'period':
                return this.formatPeriodType(value, row);
                
            default:
                return value || '-';
        }
    },
    
    // 格式化计算方式显示
    formatCalculationMethod: function(value, row) {
        const countValue = parseInt(value);
        
        if (countValue === 200) {
            const amount = row.fixed_amount || '0';
            return `固定金额: ${amount}元`;
        } else if (countValue === 100) {
            if (row.charge_type === 2) {
                return this.formatMeterCalculation(row);
            } else {
                return this.formatAreaCalculation(row);
            }
        }
        
        return '-';
    },
    
    // 格式化走表收费计算
    formatMeterCalculation: function(row) {
        let result = '单价*使用量';
        const price = this.extractPrice(row);
        if (price) {
            result += `:<br/>${price}元/度`;
        }
        return result;
    },
    
    // 格式化面积计算
    formatAreaCalculation: function(row) {
        let result = '单价*计量方式';
        const measurementType = this.extractMeasurementType(row);
        const price = this.extractPrice(row);
        
        if (measurementType && price) {
            result += `:<br/>${measurementType}*${price}`;
        }
        
        return result;
    },
    
    // 提取价格
    extractPrice: function(row) {
        if (row.count_info) {
            try {
                const countInfo = JSON.parse(row.count_info);
                // 优先获取走表收费价格
                if (countInfo.meter_price && parseFloat(countInfo.meter_price) > 0) {
                    return countInfo.meter_price;
                }
                // 其次获取周期性收费价格
                if (countInfo.price && parseFloat(countInfo.price) > 0) {
                    return countInfo.price;
                }
            } catch (e) {
                console.error('解析count_info失败:', e);
            }
        }

        return null;
    },
    
    // 提取计量方式
    extractMeasurementType: function(row) {
        if (row.count_info) {
            try {
                const countInfo = JSON.parse(row.count_info);
                const areaType = parseInt(countInfo.area_type);
                const areaOption = CHARGE_OPTIONS.areaType.find(a => a.id === areaType);
                return areaOption ? areaOption.name.replace('房屋', '') : null;
            } catch (e) {
                console.error('解析count_info失败:', e);
            }
        }

        return null;
    }
};

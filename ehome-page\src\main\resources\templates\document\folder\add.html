<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增文件夹')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-folder-add">
            <input type="hidden" name="parentId" th:value="${parentId}"/>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">文件夹名称：</label>
                <div class="col-sm-8">
                    <input name="folderName" class="form-control" type="text" maxlength="100" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请输入文件夹名称，不能与同级目录下的其他文件夹重名</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3" maxlength="500" placeholder="请输入备注信息（可选）"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "document/folder";
        
        $("#form-folder-add").validate({
            focusCleanup: true
        });
        
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix+"/add", $('#form-folder-add').serialize());
            }
        }
    </script>
</body>
</html>

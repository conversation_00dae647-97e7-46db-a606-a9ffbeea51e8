# 微信小程序登录流程图

## 完整登录流程图

```mermaid
flowchart TD
    A[用户点击登录] --> B[wx.login获取code]
    B --> C[调用smartLogin接口]
    
    C --> D{后端检查用户是否存在}
    
    %% 新用户流程
    D -->|新用户| E[返回needPhoneAuth=true]
    E --> F[前端保存checkResult]
    F --> G[显示手机号授权弹窗]
    G --> H[用户授权手机号]
    H --> I[获取phoneCode]
    I --> J[再次调用smartLogin<br/>传入phoneCode和checkResult]
    J --> K[后端解密手机号]
    K --> L[创建新用户记录]
    
    %% 老用户流程
    D -->|老用户| M[直接执行登录逻辑]
    
    %% 合并到房屋认证检查
    L --> N[检查房屋认证状态]
    M --> N
    
    N --> O{查询业主信息}
    O -->|手机号不在系统| P[authStatus: none<br/>isHouseAuth: false]
    O -->|有业主信息但无房屋| Q[authStatus: pending<br/>isHouseAuth: false]
    O -->|有业主信息且有房屋| R[authStatus: verified<br/>isHouseAuth: true]
    
    %% Token生成逻辑
    P --> S{isHouseAuth?}
    Q --> S
    R --> S
    
    S -->|true| T[生成JWT Token]
    S -->|false| U[不生成Token]
    
    T --> V[返回登录成功数据]
    U --> V
    
    %% 前端处理逻辑
    V --> W[前端分析登录结果]
    W --> X{needHouseAuth?}
    
    X -->|true| Y[显示房屋认证引导]
    X -->|false| Z{isFirstLogin && needUserInfo?}
    
    Z -->|true| AA[显示用户信息授权]
    Z -->|false| BB[step9_completeLogin]
    
    %% 房屋认证流程
    Y --> CC[用户点击确认]
    CC --> DD[显示认证说明]
    DD --> EE{用户选择}
    EE -->|重新认证| Y
    EE -->|联系物业| FF[显示联系方式]
    
    %% 用户信息授权流程
    AA --> GG{用户选择}
    GG -->|授权| HH[获取用户信息]
    GG -->|跳过| BB
    HH --> BB
    
    %% 最终完成登录
    BB --> II{isHouseAuth?}
    II -->|true| JJ[跳转首页]
    II -->|false| KK[显示认证失败弹窗]
    KK --> Y
    
    %% 样式定义
    classDef successNode fill:#d4edda,stroke:#155724,color:#155724
    classDef errorNode fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef warningNode fill:#fff3cd,stroke:#856404,color:#856404
    classDef processNode fill:#e2e3e5,stroke:#383d41,color:#383d41
    
    class JJ successNode
    class P,KK errorNode
    class Q,Y,AA warningNode
    class C,J,N,W processNode
```

## 房屋认证状态详细流程

```mermaid
flowchart TD
    A[检查房屋认证] --> B[根据手机号查询eh_owner表]
    
    B --> C{查询结果}
    C -->|未找到记录| D[authStatus: none<br/>提示联系物业录入信息]
    C -->|找到记录| E[检查house_count字段]
    
    E --> F{house_count > 0?}
    F -->|是| G[authStatus: verified<br/>isHouseAuth: true<br/>生成Token]
    F -->|否| H[authStatus: pending<br/>isHouseAuth: false]
    
    H --> I[查询eh_house_owner_rel表验证]
    I --> J{实际是否有房屋绑定?}
    J -->|有绑定关系| K[数据不一致问题<br/>需要修复house_count]
    J -->|无绑定关系| L[确实无房屋<br/>引导用户联系物业]
    
    %% 样式
    classDef dataIssue fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef success fill:#d4edda,stroke:#155724,color:#155724
    classDef pending fill:#fff3cd,stroke:#856404,color:#856404
    
    class K dataIssue
    class G success
    class H,L pending
```

## 错误处理流程

```mermaid
flowchart TD
    A[登录过程中的错误] --> B{错误类型}
    
    B -->|微信接口调用失败| C[显示重试提示]
    B -->|手机号解密失败| D[清除登录状态<br/>重新开始流程]
    B -->|网络超时| E[自动重试机制]
    B -->|数据库操作失败| F[记录错误日志<br/>显示系统错误]
    B -->|Token验证失败| G[清除本地存储<br/>重新登录]
    
    C --> H[用户手动重试]
    D --> I[返回登录页面]
    E --> J{重试次数}
    J -->|< 3次| K[延迟后重试]
    J -->|>= 3次| L[显示错误提示]
    
    F --> M[联系技术支持]
    G --> I
    H --> N[重新执行登录流程]
    K --> N
    
    %% 样式
    classDef errorNode fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef retryNode fill:#fff3cd,stroke:#856404,color:#856404
    
    class C,D,F,G,L errorNode
    class E,H,K retryNode
```

## 新用户注册详细流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant W as 微信服务器
    participant DB as 数据库

    U->>F: 点击登录
    F->>F: wx.login()
    F->>B: smartLogin(code)
    B->>W: 调用微信接口获取openid
    W-->>B: 返回用户信息
    B->>DB: 查询用户是否存在
    DB-->>B: 用户不存在
    B-->>F: needPhoneAuth=true, checkResult
    F->>F: 保存checkResult
    F->>U: 显示手机号授权弹窗
    U->>F: 授权手机号
    F->>F: 获取phoneCode
    F->>B: smartLogin(phoneCode, checkResult)
    B->>B: 解密手机号
    B->>DB: 创建新用户
    DB-->>B: 创建成功
    B->>DB: 查询房屋认证状态
    DB-->>B: 返回认证状态
    alt 房屋认证成功
        B->>B: 生成Token
        B-->>F: 登录成功(token, isHouseAuth=true)
        F->>U: 跳转首页
    else 房屋认证失败
        B-->>F: 登录成功(isHouseAuth=false)
        F->>U: 显示房屋认证引导
    end
```

## 老用户登录详细流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant W as 微信服务器
    participant DB as 数据库

    U->>F: 点击登录
    F->>F: wx.login()
    F->>B: smartLogin(code)
    B->>W: 调用微信接口获取openid
    W-->>B: 返回用户信息
    B->>DB: 查询用户是否存在
    DB-->>B: 用户存在
    B->>DB: 查询房屋认证状态
    DB-->>B: 返回认证状态
    alt 房屋认证成功
        B->>B: 生成Token
        B-->>F: 登录成功(token, isHouseAuth=true)
        F->>U: 跳转首页
    else 房屋认证失败
        B-->>F: 登录成功(isHouseAuth=false)
        F->>U: 显示房屋认证引导
        U->>F: 点击重新认证
        F->>U: 循环显示认证引导
    end
```

## 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> 未登录

    未登录 --> 获取微信凭证: 用户点击登录
    获取微信凭证 --> 调用登录接口: wx.login成功
    获取微信凭证 --> 未登录: wx.login失败

    调用登录接口 --> 需要手机号授权: 新用户
    调用登录接口 --> 检查房屋认证: 老用户

    需要手机号授权 --> 等待手机号授权: 显示授权弹窗
    等待手机号授权 --> 手机号授权完成: 用户授权
    等待手机号授权 --> 未登录: 用户取消
    手机号授权完成 --> 检查房屋认证: 重新调用登录接口

    检查房屋认证 --> 已登录已认证: isHouseAuth=true
    检查房屋认证 --> 已登录未认证: isHouseAuth=false

    已登录已认证 --> 首页: 跳转成功
    已登录未认证 --> 房屋认证引导: 显示认证弹窗
    房屋认证引导 --> 房屋认证引导: 重新认证(循环)
    房屋认证引导 --> 未登录: 用户放弃

    首页 --> 未登录: Token过期/退出登录
```

## 问题场景分析

```mermaid
flowchart TD
    A[登录问题场景] --> B[数据一致性问题]
    A --> C[用户体验问题]
    A --> D[系统异常问题]

    B --> B1[house_count字段不准确]
    B --> B2[业主信息与房屋绑定不同步]
    B --> B3[微信用户与业主信息关联错误]

    C --> C1[用户手机号不在系统中]
    C --> C2[认证失败后无其他解决渠道]
    C --> C3[错误提示不够友好]

    D --> D1[微信接口调用失败]
    D --> D2[网络超时]
    D --> D3[数据库操作异常]

    %% 问题影响
    B1 --> E1[即使有房屋也无法认证]
    B2 --> E2[认证状态不准确]
    B3 --> E3[用户信息混乱]

    C1 --> F1[用户无法使用小程序]
    C2 --> F2[用户陷入认证循环]
    C3 --> F3[用户不知如何解决]

    D1 --> G1[登录流程中断]
    D2 --> G2[用户体验差]
    D3 --> G3[系统不稳定]

    %% 样式
    classDef problemNode fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef impactNode fill:#fff3cd,stroke:#856404,color:#856404

    class B1,B2,B3,C1,C2,C3,D1,D2,D3 problemNode
    class E1,E2,E3,F1,F2,F3,G1,G2,G3 impactNode
```

## 建议的改进方案

```mermaid
flowchart TD
    A[改进方案] --> B[数据一致性改进]
    A --> C[用户体验改进]
    A --> D[系统稳定性改进]

    B --> B1[实时查询房屋绑定关系]
    B --> B2[定期同步house_count字段]
    B --> B3[增加数据校验机制]

    C --> C1[增加申请认证渠道]
    C --> C2[优化错误提示信息]
    C --> C3[提供客服联系方式]

    D --> D1[增加自动重试机制]
    D --> D2[改善异常处理]
    D --> D3[增强日志记录]

    %% 具体实现
    B1 --> B1A["SELECT COUNT(*) FROM eh_house_owner_rel<br/>WHERE owner_id = ? AND check_status = 1"]
    B2 --> B2A[定时任务同步统计数据]
    B3 --> B3A[登录时校验数据一致性]

    C1 --> C1A[添加"申请认证"按钮]
    C2 --> C2A[根据不同错误类型显示具体解决方案]
    C3 --> C3A[在认证失败页面显示客服信息]

    D1 --> D1A[微信接口调用失败时自动重试3次]
    D2 --> D2A[统一异常处理和用户友好提示]
    D3 --> D3A[记录详细的登录流程日志]

    %% 样式
    classDef solutionNode fill:#d4edda,stroke:#155724,color:#155724
    classDef implementNode fill:#e2e3e5,stroke:#383d41,color:#383d41

    class B1,B2,B3,C1,C2,C3,D1,D2,D3 solutionNode
    class B1A,B2A,B3A,C1A,C2A,C3A,D1A,D2A,D3A implementNode
```

## 优化后的房屋认证流程

```mermaid
flowchart TD
    A[开始房屋认证检查] --> B[查询eh_owner表获取基本信息]
    B --> C{是否找到业主记录?}

    C -->|否| D[authStatus: none]
    C -->|是| E[实时查询房屋绑定关系]

    E --> F["SELECT COUNT(*) FROM eh_house_owner_rel<br/>WHERE owner_id = ? AND check_status = 1"]
    F --> G{绑定房屋数量}

    G -->|> 0| H[authStatus: verified<br/>isHouseAuth: true]
    G -->|= 0| I[authStatus: pending<br/>isHouseAuth: false]

    %% 数据一致性检查
    H --> J[检查house_count字段一致性]
    I --> K[检查是否有未审核的绑定关系]

    J --> L{数据是否一致?}
    L -->|否| M[记录数据不一致日志<br/>触发数据修复]
    L -->|是| N[认证成功]

    K --> O{是否有待审核记录?}
    O -->|是| P[提示等待审核]
    O -->|否| Q[确实无房屋绑定]

    %% 用户引导
    D --> R[显示申请认证选项]
    P --> S[显示审核状态]
    Q --> T[显示联系物业选项]

    R --> U[用户可以申请录入系统]
    S --> V[用户可以查看审核进度]
    T --> W[用户可以联系物业确认]

    %% 样式
    classDef successNode fill:#d4edda,stroke:#155724,color:#155724
    classDef warningNode fill:#fff3cd,stroke:#856404,color:#856404
    classDef errorNode fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef processNode fill:#e2e3e5,stroke:#383d41,color:#383d41

    class H,N successNode
    class I,P,S warningNode
    class D,M,Q errorNode
    class B,E,F,J,K processNode
```

## 总结

### 当前登录流程的关键特点

1. **智能登录机制**：通过`smartLogin`接口统一处理新老用户登录
2. **checkResult复用**：避免微信code重复使用的问题
3. **分步骤认证**：手机号授权 → 房屋认证 → 用户信息授权
4. **严格的权限控制**：只有房屋认证成功才能生成Token并进入首页

### 主要问题点

1. **数据一致性**：依赖`house_count`字段可能导致认证失败
2. **用户体验**：认证失败时缺乏有效的解决渠道
3. **错误处理**：部分异常情况处理不够完善

### 建议的改进方向

1. **实时查询**：改为实时查询房屋绑定关系表
2. **用户引导**：增加申请认证和客服联系渠道
3. **异常处理**：增强错误恢复和重试机制
4. **数据维护**：定期同步和校验数据一致性

### 使用说明

这些流程图可以帮助开发团队：
- 理解完整的登录流程
- 识别潜在的问题点
- 规划改进方案
- 进行问题排查和调试

建议在进行登录相关的开发或问题排查时参考这些流程图。

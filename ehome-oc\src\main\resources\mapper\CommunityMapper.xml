<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.CommunityMapper">
    
    <select id="selectList" resultType="com.ehome.oc.domain.Community">
        select 
            oc_id as ocId,
            oc_code as ocCode,
            oc_name as ocName,
            oc_address as ocAddress,
            oc_state as ocState,
            pms_name as pmsName
        from eh_community 
        where oc_state = 0 
        order by oc_name
    </select>
    
</mapper> 
package com.ehome.oc.controller.charge.setting;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.constants.ChargeConstants;
import com.ehome.oc.service.ChargeBillService;
import com.ehome.oc.service.ChargeBindingService;
import com.ehome.oc.service.ChargeCommonService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/oc/charge/setting/binding")
public class ChargeBindingController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBindingController.class);

    @Autowired
    private ChargeBindingService chargeBindingService;

    @Autowired
    private ChargeBillService chargeBillService;

    @Autowired
    private ChargeCommonService chargeCommonService;

    private static final String PREFIX = "oc/charge/setting/binding";

    /**
     * 收费绑定管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 收费绑定列表查询
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select cb.*, cs.name as charge_standard_name, cs.charge_type, cs.period_type, cs.count_type",
                sql.toFullSql()
        );

        // 处理列表数据，添加显示字段
        for (Record record : paginate.getList()) {
            processListRecord(record);
        }

        return getDataTable(paginate);
    }

    /**
     * 获取绑定详情
     */
    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String id = params.getString("id");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(id, "绑定ID");
        if (validateResult != null) {
            return validateResult;
        }

        Record binding = chargeCommonService.getBindingDetail(Long.parseLong(id));
        if (binding == null) {
            return chargeCommonService.buildErrorResponse("收费绑定不存在");
        }

        return chargeCommonService.buildSuccessResponse("获取成功", binding.toMap());
    }

    /**
     * 新增收费绑定页面
     */
    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    /**
     * 编辑收费绑定页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Record binding = Db.findFirst("select * from eh_charge_binding where id = ?", id);
        if (binding != null) {
            mmap.put("binding", binding.toMap());
        }
        return PREFIX + "/edit";
    }

    /**
     * 新增收费绑定
     */
    @Log(title = "新增收费绑定", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();

        // 获取选中的资产列表
        String assetIds = params.getString("assetIds");
        if (StringUtils.isEmpty(assetIds)) {
            return AjaxResult.error("请选择要绑定的资产");
        }

        String[] assetIdArray = assetIds.split(",");
        int successCount = 0;

        for (String assetId : assetIdArray) {
            if (StringUtils.isNotEmpty(assetId.trim())) {
                Record binding = new Record();
                binding.set("asset_type", params.getIntValue("assetType"));
                binding.set("asset_id", assetId.trim());
                binding.set("asset_name", getAssetName(params.getIntValue("assetType"), assetId.trim()));
                binding.set("charge_standard_id", params.getLongValue("chargeStandardId"));
                binding.set("start_time", params.getLongValue("startTime"));
                binding.set("end_time", params.getLongValue("endTime"));
                binding.set("period_num", params.getIntValue("periodNum"));
                binding.set("natural_period", params.getIntValue("naturalPeriod"));

                binding.set("community_id", getCurrentUser().getCommunityId());
                binding.set("is_active", 1);

                chargeCommonService.setCreateAndUpdateInfo(binding, getLoginName(), getCurrentUser().getCommunityId());

                // 检查是否已存在相同的绑定
                if (!chargeCommonService.checkDuplicateBinding(binding, getCurrentUser())) {
                    Db.save("eh_charge_binding", "id", binding);

                    // 更新收费标准的关联资产数
                    chargeCommonService.updateChargeStandardAssetCount(params.getLongValue("chargeStandardId"));

                    // 同步刷新下次生成账单日期
                    chargeBindingService.updateNextBillTime(binding.getLong("id"));

                    successCount++;
                }
            }
        }

        if (successCount > 0) {
            return AjaxResult.success("成功绑定 " + successCount + " 个资产");
        } else {
            return AjaxResult.error("绑定失败，可能存在重复绑定");
        }
    }

    /**
     * 编辑收费绑定
     */
    @Log(title = "编辑收费绑定", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record binding = new Record();

        binding.set("id", params.getLongValue("id"));
        binding.set("charge_standard_id", params.getLongValue("chargeStandardId"));
        binding.set("start_time", params.getLongValue("startTime"));
        binding.set("end_time", params.getLongValue("endTime"));
        binding.set("period_num", params.getIntValue("periodNum"));
        binding.set("natural_period", params.getIntValue("naturalPeriod"));


        chargeCommonService.setUpdateInfo(binding, getLoginName());

        // 获取原来的收费标准ID
        Record oldBinding = Db.findFirst("select charge_standard_id from eh_charge_binding where id = ?", params.getLongValue("id"));
        Long oldChargeStandardId = oldBinding != null ? oldBinding.getLong("charge_standard_id") : null;

        boolean result = Db.update("eh_charge_binding", "id", binding);

        if (result) {
            // 更新新的收费标准关联资产数
            chargeCommonService.updateChargeStandardAssetCount(params.getLongValue("chargeStandardId"));
            // 如果收费标准发生了变化，也要更新原来的收费标准
            if (oldChargeStandardId != null && !oldChargeStandardId.equals(params.getLongValue("chargeStandardId"))) {
                chargeCommonService.updateChargeStandardAssetCount(oldChargeStandardId);
            }

            // 同步刷新下次生成账单日期
            chargeBindingService.updateNextBillTime(params.getLongValue("id"));
        }

        return toAjax(result);
    }

    /**
     * 删除收费绑定
     */
    @Log(title = "删除收费绑定", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(ids, "参数id");
        if (validateResult != null) {
            return validateResult;
        }

        String[] idArr = ids.split(",");
        for (String id : idArr) {
            // 获取要删除的绑定信息
            Record binding = Db.findFirst("select charge_standard_id from eh_charge_binding where id = ?", id);
            if (binding != null) {
                Long chargeStandardId = binding.getLong("charge_standard_id");
                Db.update("delete from eh_charge_binding where id = ?", id);
                // 更新收费标准的关联资产数
                chargeCommonService.updateChargeStandardAssetCount(chargeStandardId);

                // 删除绑定后，需要重新计算所有相关绑定的下次账单时间
                // 这里可以选择性地更新所有绑定，或者在定时任务中统一更新
                logger.info("已删除收费绑定，ID：{}，收费标准ID：{}", id, chargeStandardId);
            }
        }

        // 删除绑定后，建议重新计算所有绑定的下次账单时间
        // 可以通过定时任务或手动触发来完成
        try {
            chargeBindingService.refreshAllNextBillTime();
            logger.info("删除收费绑定后，已更新所有绑定的下次账单时间");
        } catch (Exception e) {
            logger.warn("更新下次账单时间失败：{}", e.getMessage());
        }
        return success();
    }

    /**
     * 解绑资产收费标准
     */
    @PostMapping("/unbind")
    @ResponseBody
    public AjaxResult unbind() {
        JSONObject params = getParams();
        Long id = params.getLong("id");
        if (id == null || id <= 0) {
            return AjaxResult.error("绑定ID不能为空");
        }

        // 获取绑定信息
        Record binding = Db.findFirst("select charge_standard_id from eh_charge_binding where id = ?", id);
        if (binding == null) {
            return AjaxResult.error("绑定记录不存在");
        }

        Long chargeStandardId = binding.getLong("charge_standard_id");

        // 将绑定状态设置为禁用而不是删除
        int updateCount = Db.update("update eh_charge_binding set is_active = 0, update_time = now(), update_by = ? where id = ?",
                getCurrentUser().getUserName(), id);
        boolean result = updateCount > 0;

        if (result) {
            // 更新收费标准的关联资产数
            chargeCommonService.updateChargeStandardAssetCount(chargeStandardId);
        }

        return toAjax(result);
    }



    /**
     * 获取资产列表
     */
    @PostMapping("/getAssets")
    @ResponseBody
    public AjaxResult getAssets() {
        JSONObject params = getParams();
        int assetType = params.getIntValue("assetType");
        String communityId = getCurrentUser().getCommunityId();

        List<Record> assets = null;

        switch (assetType) {
            case ChargeConstants.AssetType.HOUSE: // 房屋
                assets = Db.find("select house_id as id, concat(building_name, '/', unit_name, '/', room) as name " +
                        "from eh_house_info where community_id = ? and check_status = 1 order by building_name, unit_name, room",
                        communityId);
                break;
            case ChargeConstants.AssetType.PARKING: // 车位
                assets = Db.find("select parking_id as id, parking_no as name " +
                        "from eh_parking_space where community_id = ? and check_status = 1 order by parking_no",
                        communityId);
                break;
            case ChargeConstants.AssetType.CAR: // 车辆
                assets = Db.find("select vehicle_id as id, plate_no as name " +
                        "from eh_vehicle where community_id = ? order by plate_no",
                        communityId);
                break;
            default:
                return AjaxResult.error("不支持的资产类型");
        }

        return AjaxResult.success(recordToMap(assets));
    }

    /**
     * 获取收费标准列表
     */
    @PostMapping("/getChargeStandards")
    @ResponseBody
    public AjaxResult getChargeStandards() {
        List<Record> standards = chargeCommonService.getChargeStandards(getSysUser().getCommunityId());
        return AjaxResult.success(chargeCommonService.recordToMap(standards));
    }

    /**
     * 获取收费标准详情
     */
    @PostMapping("/getChargeStandardDetail")
    @ResponseBody
    public AjaxResult getChargeStandardDetail() {
        JSONObject params = getParams();
        Long id = params.getLong("id");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(id, "收费标准ID");
        if (validateResult != null) {
            return validateResult;
        }

        Record standard = chargeCommonService.getChargeStandardDetail(id);
        if (standard == null) {
            return chargeCommonService.buildErrorResponse("收费标准不存在");
        }

        return chargeCommonService.buildSuccessResponse("获取成功", standard.toMap());
    }

    /**
     * 获取楼栋树形结构
     */
    @PostMapping("/getBuildingTree")
    @ResponseBody
    public AjaxResult getBuildingTree() {
        String communityId = getCurrentUser().getCommunityId();

        // 获取楼栋列表
        List<Record> buildings = Db.find(
                "select building_id as id, name " +
                "from eh_building where community_id = ? order by name",
                communityId);

        // 为每个楼栋获取单元和房屋
        for (Record building : buildings) {
            Long buildingId = building.getLong("id");

            // 获取单元列表
            List<Record> units = Db.find(
                    "select unit_id as id, name " +
                    "from eh_unit where building_id = ? order by name",
                    buildingId);

            // 为每个单元获取房屋列表
            for (Record unit : units) {
                Long unitId = unit.getLong("id");
                List<Record> houses = Db.find(
                        "select house_id as id, room as name " +
                        "from eh_house_info where unit_id = ? and check_status = 1 order by room",
                        unitId);
                unit.set("houseList", recordToMap(houses));
            }

            building.set("unitList", recordToMap(units));
        }

        return AjaxResult.success(recordToMap(buildings));
    }

    /**
     * 异步获取楼栋树形结构
     */
    @PostMapping("/getBuildingTreeAsync")
    @ResponseBody
    public AjaxResult getBuildingTreeAsync() {
        JSONObject params = getParams();
        String communityId = getCurrentUser().getCommunityId();
        String id = params.getString("id");
        int level = params.getIntValue("level");

        List<Record> nodes = new ArrayList<>();

        if (level == 0) {
            // 根节点，返回楼栋列表
            nodes = Db.find(
                    "select building_id as id, name, 'building' as type, " +
                    "case when (select count(*) from eh_unit where building_id = eb.building_id) > 0 then 'true' else 'false' end as isParent " +
                    "from eh_building eb where community_id = ? order by name",
                    communityId);
        } else if (level == 1 && id != null && id.startsWith("building_")) {
            // 楼栋节点，返回单元列表
            String buildingId = id.replace("building_", "");
            nodes = Db.find(
                    "select concat('unit_', unit_id) as id, name, 'unit' as type, " +
                    "case when (select count(*) from eh_house_info where unit_id = eu.unit_id and check_status = 1) > 0 then 'true' else 'false' end as isParent " +
                    "from eh_unit eu where building_id = ? order by name",
                    buildingId);
        } else if (level == 2 && id != null && id.startsWith("unit_")) {
            // 单元节点，返回房屋列表
            String unitId = id.replace("unit_", "");
            nodes = Db.find(
                    "select house_id as id, room as name, 'house' as type, 'false' as isParent " +
                    "from eh_house_info where unit_id = ? and check_status = 1 order by room",
                    unitId);
        }

        // 转换数据格式
        for (Record node : nodes) {
            String type = node.getStr("type");
            if ("building".equals(type)) {
                node.set("id", "building_" + node.get("id"));
                node.set("chkDisabled", false);
            } else if ("unit".equals(type)) {
                node.set("chkDisabled", false);
            } else if ("house".equals(type)) {
                node.set("assetId", node.get("id"));
            }
        }

        return AjaxResult.success(recordToMap(nodes));
    }

    /**
     * 获取简化的楼栋树形结构（只返回有房屋的楼栋和单元）
     */
    @PostMapping("/getBuildingTreeSimple")
    @ResponseBody
    public AjaxResult getBuildingTreeSimple() {
        String communityId = getCurrentUser().getCommunityId();

        List<Record> treeData = new ArrayList<>();

        // 获取有房屋的楼栋列表
        List<Record> buildings = Db.find(
                "select distinct b.building_id, b.name as building_name " +
                "from eh_building b " +
                "inner join eh_house_info h on b.name = h.building_name " +
                "where b.community_id = ? and h.check_status = 1 " +
                "order by b.name",
                communityId);

        for (Record building : buildings) {
            // 添加楼栋节点
            Record buildingNode = new Record();
            buildingNode.set("id", "building_" + building.getLong("building_id"));
            buildingNode.set("pId", 0);
            buildingNode.set("name", building.getStr("building_name"));
            buildingNode.set("chkDisabled", false);
            treeData.add(buildingNode);

            // 获取该楼栋下有房屋的单元
            List<Record> units = Db.find(
                    "select distinct h.unit_id, h.unit_name " +
                    "from eh_house_info h " +
                    "where h.building_name = ? and h.check_status = 1 " +
                    "order by h.unit_name",
                    building.getStr("building_name"));

            for (Record unit : units) {
                // 添加单元节点
                Record unitNode = new Record();
                unitNode.set("id", "unit_" + unit.getStr("unit_id"));
                unitNode.set("pId", "building_" + building.getLong("building_id"));
                unitNode.set("name", unit.getStr("unit_name"));
                unitNode.set("chkDisabled", false);
                treeData.add(unitNode);

                // 获取该单元下的房屋
                List<Record> houses = Db.find(
                        "select house_id, room " +
                        "from eh_house_info " +
                        "where building_name = ? and unit_name = ? and check_status = 1 " +
                        "order by room",
                        building.getStr("building_name"), unit.getStr("unit_name"));

                for (Record house : houses) {
                    // 添加房屋节点
                    Record houseNode = new Record();
                    houseNode.set("id", house.getLong("house_id"));
                    houseNode.set("pId", "unit_" + unit.getStr("unit_id"));
                    houseNode.set("name", house.getStr("room"));
                    houseNode.set("assetId", house.getLong("house_id"));
                    treeData.add(houseNode);
                }
            }
        }

        return AjaxResult.success(recordToMap(treeData));
    }

    /**
     * 构建列表查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "where 1=1");

        sql.append(getCurrentUser().getCommunityId(),"and cb.community_id = ?");

        // 只有当值不为空且不为0时才添加查询条件
        Integer assetType = params.getInteger("assetType");
        if (assetType != null && assetType > 0) {
            sql.append(assetType, "and cb.asset_type = ?");
        }

        sql.appendLike(params.getString("assetName"), "and cb.asset_name like ?");

        Long chargeStandardId = params.getLong("chargeStandardId");
        if (chargeStandardId != null && chargeStandardId > 0) {
            sql.append(chargeStandardId, "and cb.charge_standard_id = ?");
        }

        Integer isActive = params.getInteger("isActive");
        if (isActive != null) {
            sql.append(isActive, "and cb.is_active = ?");
        }

        String beginTime = params.getString("beginTime");
        sql.append(beginTime, "and cb.create_time >= ?");
        String endTime = params.getString("endTime");
        sql.append(endTime, "and cb.create_time <= ?");

        sql.append("order by cb.start_time desc,cb.create_time desc");
        return sql;
    }

    /**
     * 处理列表记录，添加显示字段
     */
    private void processListRecord(Record record) {

        // 处理时间显示
        Long startTime = record.getLong("start_time");
        if (startTime != null && startTime > 0) {
            record.set("startTimeStr", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,
                    new java.util.Date(startTime * 1000)));
        }

        Long endTime = record.getLong("end_time");
        if (endTime != null && endTime > 0) {
            record.set("endTimeStr", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,
                    new java.util.Date(endTime * 1000)));
        } else {
            record.set("endTimeStr", "无结束日期");
        }

        // 处理下次账单时间显示
        Long nextBillTime = record.getLong("next_bill_time");
        if (nextBillTime != null && nextBillTime > 0) {
            record.set("nextBillTimeStr", DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,
                    new java.util.Date(nextBillTime * 1000)));
        } else {
            record.set("nextBillTimeStr", "待计算");
        }

        // 处理收费标准类型显示
        Integer chargeType = record.getInt("charge_type");
        if (chargeType != null) {
            record.set("chargeTypeStr", getChargeTypeStr(chargeType));
        }

        // 处理收费方式显示
        Integer periodType = record.getInt("period_type");
        if (periodType != null) {
            record.set("periodTypeStr", getPeriodTypeStr(periodType));
        }

        // 处理计算方式显示
        Integer countType = record.getInt("count_type");
        if (countType != null) {
            record.set("countTypeStr", getCountTypeStr(countType));
        }
    }

    /**
     * 获取资产名称
     */
    private String getAssetName(int assetType, String assetId) {
        String name = "";
        switch (assetType) {
            case ChargeConstants.AssetType.HOUSE: // 房屋
                Record house = Db.findFirst(
                        "select concat(building_name, '/', unit_name, '/', room) as name " +
                        "from eh_house_info where house_id = ?", assetId);
                if (house != null) {
                    name = house.getStr("name");
                }
                break;
            case ChargeConstants.AssetType.PARKING: // 车位
                Record parking = Db.findFirst(
                        "select parking_no as name from eh_parking_space where parking_id = ?", assetId);
                if (parking != null) {
                    name = parking.getStr("name");
                }
                break;
            case ChargeConstants.AssetType.CAR: // 车辆
                Record vehicle = Db.findFirst("select plate_no as name from eh_vehicle where vehicle_id = ?", assetId);
                if (vehicle != null) {
                    name = vehicle.getStr("name");
                }
                break;
        }
        return name;
    }



    /**
     * 获取收费类型显示名称
     */
    private String getChargeTypeStr(int chargeType) {
        return ChargeConstants.getChargeTypeName(chargeType);
    }

    /**
     * 获取收费方式显示名称
     */
    private String getPeriodTypeStr(int periodType) {
        switch (periodType) {
            case ChargeConstants.PeriodType.MONTHLY: return "按月收费";
            case ChargeConstants.PeriodType.QUARTERLY: return "按季度收费";
            case ChargeConstants.PeriodType.YEARLY: return "按年收费";
            default: return "未知";
        }
    }

    /**
     * 获取计算方式显示名称
     */
    private String getCountTypeStr(int countType) {
        switch (countType) {
            case ChargeConstants.CountType.UNIT_PRICE_MULTIPLY: return "单价*计量方式";
            case ChargeConstants.CountType.FIXED_AMOUNT: return "固定金额";
            default: return "未知";
        }
    }



    /**
     * 获取当前用户
     */
    private com.ehome.common.core.domain.entity.SysUser getCurrentUser() {
        return getSysUser();
    }

    /**
     * 刷新所有收费绑定的下次账单日期
     */
    @Log(title = "刷新账单日期", businessType = BusinessType.UPDATE)
    @PostMapping("/refreshAllBillDates")
    @ResponseBody
    public AjaxResult refreshAllBillDates() {
        try {
            // 调用业务服务刷新下次账单时间
            chargeBindingService.refreshAllNextBillTime();
            return AjaxResult.success("刷新账单日期成功");
        } catch (Exception e) {
            return AjaxResult.error("刷新账单日期失败：" + e.getMessage());
        }
    }

    /**
     * 生成预收账单
     */
    @Log(title = "生成预收账单", businessType = BusinessType.INSERT)
    @PostMapping("/generatePreBills")
    @ResponseBody
    public AjaxResult generatePreBills() {
        try {
            JSONObject params = getParams();
            String startMonth = params.getString("startMonth"); // 格式：2025-01
            String endMonth = params.getString("endMonth");     // 格式：2025-06

            if (StringUtils.isEmpty(startMonth) || StringUtils.isEmpty(endMonth)) {
                return AjaxResult.error("请选择账期范围");
            }

            // 获取绑定ID列表（逗号分隔的字符串）
            String bindingIdsStr = params.getString("bindingIds");
            if (StringUtils.isEmpty(bindingIdsStr)) {
                return AjaxResult.error("请选择要生成预收账单的记录");
            }

            String[] bindingIdArray = bindingIdsStr.split(",");

            int totalGenerateCount = 0;
            int successBindingCount = 0;
            int failedBindingCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            // 循环处理每个绑定ID
            for (String bindingIdStr : bindingIdArray) {
                if (StringUtils.isEmpty(bindingIdStr.trim())) {
                    continue;
                }

                Long bindingId;
                try {
                    bindingId = Long.parseLong(bindingIdStr.trim());
                } catch (NumberFormatException e) {
                    failedBindingCount++;
                    errorMessages.append("绑定ID ").append(bindingIdStr).append(" 格式错误；");
                    continue;
                }

                if (bindingId <= 0) {
                    continue;
                }

                try {
                    // 验证收费绑定是否存在且有效
                    Record binding = Db.findFirst(
                            "select cb.*, cs.name as charge_standard_name from eh_charge_binding cb " +
                            "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                            "where cb.id = ? and cb.is_active = 1 and cs.is_active = 1", bindingId);

                    if (binding == null) {
                        failedBindingCount++;
                        errorMessages.append("绑定ID ").append(bindingId).append(" 不存在或已禁用；");
                        continue;
                    }

                    // 调用服务生成预收账单
                    int generateCount = chargeBillService.batchGenerateBillsByBindingAndRange(
                            bindingId, startMonth, endMonth, getSysUser());

                    totalGenerateCount += generateCount;
                    successBindingCount++;

                } catch (Exception e) {
                    failedBindingCount++;
                    errorMessages.append("绑定ID ").append(bindingId).append(" 生成失败：").append(e.getMessage()).append("；");
                }
            }

            // 构建返回消息
            StringBuilder resultMessage = new StringBuilder();
            resultMessage.append("处理完成！");
            if (successBindingCount > 0) {
                resultMessage.append("成功处理 ").append(successBindingCount).append(" 个绑定，生成 ").append(totalGenerateCount).append(" 条预收账单");
            }
            if (failedBindingCount > 0) {
                resultMessage.append("；失败 ").append(failedBindingCount).append(" 个绑定");
                if (errorMessages.length() > 0) {
                    resultMessage.append("：").append(errorMessages.toString());
                }
            }

            if (successBindingCount > 0) {
                return AjaxResult.success(resultMessage.toString());
            } else {
                return AjaxResult.error(resultMessage.toString());
            }

        } catch (Exception e) {
            return AjaxResult.error("生成预收账单失败：" + e.getMessage());
        }
    }


}

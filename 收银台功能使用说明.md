# 收银台功能使用说明

## 功能概述

收银台功能为物业管理人员提供了一个直观的房屋缴费情况查看和管理界面，支持：
- 楼栋单元树形导航
- 房屋缴费状态可视化展示
- 房屋详情和未缴账单管理
- 批量收款操作

## 部署步骤

### 1. 数据库更新

执行以下SQL脚本添加欠费金额字段：

```sql
-- 执行 sql/add_arrear_amount_field.sql
ALTER TABLE `eh_house_info` 
ADD COLUMN `arrear_amount` decimal(10,2) NULL DEFAULT 0.00 COMMENT '欠费金额' AFTER `owner_str`;

-- 初始化现有房屋的欠费金额
UPDATE eh_house_info h 
SET arrear_amount = (
    SELECT COALESCE(SUM(cb.bill_amount), 0.00)
    FROM eh_charge_bill cb 
    WHERE cb.asset_type = 1 
    AND cb.asset_id = h.house_id 
    AND cb.pay_status = 0 
    AND cb.is_bad_bill = 0
);
```

### 2. 代码部署

已修改的文件：
- `ehome-oc/src/main/java/com/ehome/oc/service/ChargeBillService.java` - 新增房屋欠费金额更新方法
- `ehome-oc/src/main/java/com/ehome/oc/controller/charge/manage/CheckOutController.java` - 收银台控制器
- `ehome-page/src/main/resources/templates/oc/charge/manage/checkout/list.html` - 收银台主页面
- `ehome-page/src/main/resources/templates/oc/charge/manage/checkout/detail.html` - 房屋详情页面
- `sql/eh.sql` - 数据库表结构更新

### 3. 功能测试

使用 `sql/test_checkout_system.sql` 脚本验证功能：

```sql
-- 检查字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'eh_house_info' AND COLUMN_NAME = 'arrear_amount';

-- 验证欠费金额计算是否正确
SELECT h.house_id, h.arrear_amount, 
       COALESCE(SUM(cb.bill_amount), 0.00) as calculated_amount
FROM eh_house_info h 
LEFT JOIN eh_charge_bill cb ON cb.asset_type = 1 AND cb.asset_id = h.house_id 
    AND cb.pay_status = 0 AND cb.is_bad_bill = 0
GROUP BY h.house_id, h.arrear_amount
HAVING h.arrear_amount > 0
LIMIT 10;
```

## 使用方法

### 1. 访问收银台

访问路径：`/oc/charge/manage/checkout/mgr`

### 2. 界面说明

**左侧面板：**
- 楼栋单元树形结构
- 支持搜索功能
- 点击单元查看房屋列表

**右侧面板：**
- 房屋网格展示
- 红色边框：有欠费的房屋
- 绿色边框：无欠费的房屋
- 显示房屋编号、住户信息、欠费金额

### 3. 房屋详情

点击房屋卡片打开详情页面，包含：
- **房屋信息标签页**：基本信息和住户信息
- **未缴账单标签页**：未缴费账单列表，支持批量收款

### 4. 数据同步

- 系统会在账单生成、缴费、作废、删除时自动更新房屋欠费金额
- 可手动点击"更新欠费"按钮批量更新所有房屋的欠费金额

## 技术特点

### 1. 数据一致性
- 在所有账单操作中自动维护房屋欠费金额
- 支持手动批量更新确保数据准确性

### 2. 性能优化
- 通过冗余存储避免复杂的关联查询
- 房屋列表加载速度快

### 3. 用户体验
- 直观的颜色区分欠费状态
- 响应式网格布局
- 支持搜索和筛选

## API接口

### 1. 获取单元房屋列表
```
POST /oc/charge/manage/checkout/getUnitHouseList
参数：unitId - 单元ID
返回：房屋列表和统计信息
```

### 2. 获取房屋详情
```
POST /oc/charge/manage/checkout/getHouseDetail
参数：houseId - 房屋ID
返回：房屋详细信息
```

### 3. 获取房屋账单
```
POST /oc/charge/manage/checkout/getHouseBills
参数：houseId - 房屋ID
返回：未缴费账单列表
```

### 4. 更新欠费金额
```
POST /oc/charge/manage/checkout/updateArrearAmount
返回：更新结果
```

## 注意事项

1. **数据库字段类型**：arrear_amount使用decimal(10,2)存储，单位为元
2. **账单金额字段**：使用bill_amount字段而非amount字段
3. **权限控制**：需要相应的菜单权限才能访问收银台功能
4. **数据同步**：建议定期执行批量更新确保数据准确性

## 故障排除

### 1. 欠费金额显示不正确
- 执行批量更新欠费金额功能
- 检查账单数据的pay_status和is_bad_bill字段

### 2. 房屋列表不显示
- 检查楼栋单元数据是否完整
- 确认房屋与单元的关联关系

### 3. 账单列表为空
- 确认账单数据中的asset_type=1（房屋类型）
- 检查账单的pay_status=0（未缴费状态）

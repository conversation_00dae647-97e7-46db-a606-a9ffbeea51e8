/* 权限演示页面样式 */
.container {
  padding: 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 卡片通用样式 */
.status-card,
.action-card,
.test-card,
.permissions-card,
.results-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 32rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  padding: 0 32rpx 32rpx 32rpx;
}

/* 状态项样式 */
.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.status-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-value.success {
  color: #07c160;
}

.status-value.warning {
  color: #ff8c00;
}

.status-value.error {
  color: #ee0a24;
}

.status-value.auth-verified {
  color: #07c160;
}

.status-value.auth-pending {
  color: #ff8c00;
}

.status-value.auth-none {
  color: #ee0a24;
}

/* 按钮行样式 */
.button-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.button-row:last-child {
  margin-bottom: 0;
}

.button-row .van-button {
  flex: 1;
}

/* 测试区域样式 */
.test-section {
  margin-bottom: 32rpx;
}

.test-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.test-btn {
  margin-bottom: 16rpx !important;
  margin-right: 16rpx !important;
}

/* 权限列表样式 */
.permission-count {
  font-size: 24rpx;
  color: #999;
  background: #f7f8fa;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.permission-item {
  background: #f7f8fa;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ebedf0;
}

.permission-name {
  font-size: 24rpx;
  color: #666;
}

.empty-permissions {
  text-align: center;
  padding: 64rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 测试结果样式 */
.results-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.result-item {
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
}

.result-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.result-function {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.result-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
}

.result-status.success {
  background: #f0f9ff;
  color: #07c160;
}

.result-status.error {
  background: #fff1f0;
  color: #ee0a24;
}

.result-time {
  font-size: 22rpx;
  color: #999;
}

.result-message {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .button-row {
    flex-direction: column;
  }
  
  .button-row .van-button {
    margin-bottom: 16rpx;
  }
  
  .permissions-list {
    justify-content: center;
  }
}

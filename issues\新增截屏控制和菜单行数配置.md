# 新增截屏控制和菜单行数配置功能

## 功能概述
为小区配置系统新增两个配置字段：
1. **是否可以截屏**：控制小程序是否允许用户截屏
2. **菜单行显示个数**：控制首页菜单每行显示3个或4个图标

## 实现内容

### 1. 后端配置页面更新
**文件**：`ehome-page/src/main/resources/templates/oc/info/wxsetting.html`

**新增配置项**：
- 菜单行显示个数：下拉选择（4行/3行），默认4行
- 是否可以截屏：下拉选择（允许/禁止），默认允许

**位置**：在"首页显示设置"和新增的"安全设置"部分

### 2. 小程序配置解析器更新
**文件**：`miniprogram/utils/configParser.js`

**新增字段**：
```javascript
enable_screenshot: this.safeGetString(extConfig.enable_screenshot, '1'),
menu_rows: this.safeGetString(extConfig.menu_rows, '4')
```

### 3. 首页菜单布局动态调整
**文件**：`miniprogram/pages/index/index.js`

**功能**：
- 根据`menu_rows`配置动态调整每行显示的菜单个数
- 支持3个一排（最多3行9个菜单）或4个一排（最多2行8个菜单）布局
- 自动计算最大显示数量：
  - 4个一排：4 × 2 = 8个菜单
  - 3个一排：3 × 3 = 9个菜单
- 超出数量时显示"更多服务"按钮

**文件**：`miniprogram/pages/index/index.wxss`

**样式调整**：
- 将grid-item的固定宽度改为flex布局
- 支持动态适应不同行数的布局

### 4. 截屏控制功能
**文件**：`miniprogram/app.js`

**功能**：
- 应用启动时根据配置设置截屏控制
- 使用`wx.setVisualEffectOnCapture` API
- 禁止截屏时设置`visualEffect: 'hidden'`

**文件**：`miniprogram/utils/stateManager.js`

**功能**：
- 配置更新时自动重新设置截屏控制
- 支持动态切换允许/禁止截屏

## 配置字段说明

### enable_screenshot（是否可以截屏）
- **类型**：字符串
- **可选值**：
  - `'1'`：允许截屏（默认）
  - `'0'`：禁止截屏
- **效果**：当设置为禁止时，用户截屏时会看到隐藏效果

### menu_rows（菜单行显示个数）
- **类型**：字符串
- **可选值**：
  - `'4'`：每行4个菜单，最多2行8个菜单（默认）
  - `'3'`：每行3个菜单，最多3行9个菜单
- **效果**：影响首页菜单网格的布局和最大显示数量

## 使用方法

### 管理员配置
1. 登录后台管理系统
2. 进入"小区管理" → "小程序设置"
3. 在"首页显示设置"中配置菜单行数
4. 在"安全设置"中配置截屏权限
5. 点击"提交保存"

### 小程序效果
1. **菜单布局**：根据配置自动调整为3行或4行布局
2. **截屏控制**：
   - 允许截屏：正常截屏功能
   - 禁止截屏：截屏时显示隐藏效果

## 技术实现要点

### 1. 配置解析流程
```
后端extJson → ConfigParser.parseExtJson() → StateManager → App.globalData.extConfig
```

### 2. 菜单布局算法
```javascript
const itemsPerRow = parseInt(extConfig.menu_rows) || 4
// 根据每行显示个数确定最大行数
const maxRows = itemsPerRow === 3 ? 3 : 2
const maxItems = itemsPerRow * maxRows
```

### 3. 截屏控制API
```javascript
wx.setVisualEffectOnCapture({
  visualEffect: enableScreenshot === '0' ? 'hidden' : 'none'
})
```

## 兼容性说明
- 截屏控制功能需要微信版本支持`wx.setVisualEffectOnCapture` API
- 不支持的版本会在控制台输出警告信息，但不影响其他功能
- 菜单布局功能完全兼容所有版本

## 测试建议
1. 测试后台配置页面的保存功能
2. 验证小程序配置解析是否正确
3. 测试3行和4行菜单布局切换
4. 验证截屏控制在不同设置下的效果
5. 测试配置更新后的实时生效

## 相关功能
本次还同时实现了**全局水印功能**，详见：`issues/全局水印功能实现.md`

### 功能整合
- 截屏控制、菜单布局、全局水印三个功能都通过extJson配置管理
- 统一的配置解析和状态管理机制
- 一致的用户体验和管理界面

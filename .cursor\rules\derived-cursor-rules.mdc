---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## PROJECT OVERVIEW
This project, ehome, is a comprehensive property management system.  It includes modules for building management, unit management, owner management, vehicle management, and fee collection.  The system utilizes a multi-module architecture built with Java and Spring Boot.  Version 4.7.9 is currently deployed.

## CODE STYLE
Follow standard Java coding conventions.  Use consistent indentation (4 spaces).  Keep methods concise and focused on a single task.

## FOLDER ORGANIZATION
The project follows a standard Maven multi-module structure.  Each module is responsible for a specific aspect of the application.

## TECH STACK
- Java
- Spring Boot 2.7.18
- Maven
- MySQL
- Thymeleaf
- JFinal 5.2.2
- MyBatis 3.5.13
- Shiro 1.13.0
- Druid 1.2.23
- Ehcache 2.6.11
- Quartz 2.3.2
- MCP (added 2025-03-18)
- jQuery layout plugin (added 2025-03-18)
- jQuery dialog plugin (added 2025-03-19)
- Bootstrap (added 2025-05-14)
- Other libraries listed in ehome-web/target/WEB-INF/lib

## PROJECT-<PERSON><PERSON><PERSON><PERSON> STANDARDS
- All database interactions should use MyBatis, unless the table structure allows for MCP usage (as decided on 2025-03-18 for `detail-owner.html`).  Further clarification needed regarding the use of MCP for database interaction in other contexts.
- Utilize Spring's dependency injection framework.
- Adhere to the Ruoyi framework conventions where applicable.
- **Updated 2025-05-17:**  In `finance.js` of the WeChat Mini Program, the `getFinanceData` method should use the absolute path `/api/wx/ccb/getData` for the API endpoint. Error handling should include a `wx.showToast` message for both success and failure scenarios. The success scenario should check if `res.data` and `res.data.list` are valid before updating the `financeList` state.  Consider also updating `monthIncome`, `monthExpense`, and `balance` if returned by the API. The `getFinanceData` method should utilize `app.request` for API calls, handle `res.code` for error checking, and use `async/await` for cleaner code. The `data` object should include initial values for `currentDate`, `monthIncome`, `monthExpense`, `balance`, and `financeList`. To handle cases where `monthExpense` is 0.0, use a strict equality check (===) instead of loose equality (==) or the || operator when setting the `monthExpense` value in `setData`. For example: `monthExpense: res.data.monthExpense`. This will ensure that 0.0 is treated as a valid value and not replaced with an empty string.
- **Updated 2025-05-14:** The `main.html` (system homepage) has been redesigned to include a simpler, more visually appealing layout with key metrics, quick access links, announcements, and system information. Bootstrap framework is utilized. The implementation includes a HomeController with a `/oc/home/<USER>/oc/home/<USER>/oc/home/<USER>
- **Updated 2025-05-17:** The `needUpdateProfile` check in the WeChat Mini Program login process should be updated to: `const needUpdateProfile = !userInfo.avatarUrl || !userInfo.nickName || userInfo.nickName === '微信用户';` This ensures that the prompt to update profile information is displayed when the avatar URL is null, the nickname is null, or the nickname is "微信用户".  This addresses issues where the prompt was displayed even when a nickname was already present but the avatar URL was missing.  Always clear WeChat Mini Program login session data (cache and global variables) upon login failure.
- **Updated 2025-03-18:**  `detail-owner.html` now uses AJAX requests for data population instead of Thymeleaf `th:text` and `th:if` expressions. The `OcOwnerController` has been updated to support these AJAX requests, including methods for retrieving house and vehicle lists (`houseList` and `vehicleList`). The `fillRecord` method is used for data population where applicable.  Javascript functions `loadHouseList()`, `loadVehicleList()`, and `loadOwnerInfo()` handle AJAX requests.  The `detail-owner.html` page now includes functionality to unbind and bind houses.  Further development is needed to implement the full functionality of binding and unbinding houses. The AJAX requests should use absolute paths (e.g., `/oc/owner/houseList`) to avoid pathing issues.
- **Updated 2025-03-18:** The code for `@user.html` (house management) and `ehome-page/src/main/resources/templates/oc/house/list.html` should reference the user management code (`ehome-page/src/main/resources/templates/system/user/user.html`, lines 1-36, 36-73, 73-108, 108-137, 137-186, 186-216, 349-388, 388-442) for restructuring the left and right sections and the collapsible tree on the left, especially the expand and collapse functionality.  Further adjustments to spacing and styling are needed, particularly for the collapsible panel, refresh button, and spacing between left and right sections (as noted 2025-03-18).
- **Updated 2025-03-19:** The `houseInfo` section should display all relevant house information including: floor, total area, use area, house type (residential/commercial), house status (occupied/unsold etc.), creation time, and remarks.  The house type and status should utilize dictionary data for display.
- **Updated 2025-05-14:**  PC-style dashboard layout for `main.html` homepage is now implemented. The design prioritizes a wider layout with horizontal sections for statistics, announcements, and quick access links. The backend `HomeController` and `/oc/home/<USER>
- **Added 2025-03-19:** Implement `selectOwner` and `selectParkingSpace` methods with corresponding selection pages (`oc/vehicle/ownerDialog.html`, `oc/vehicle/parkingSpaceDialog.html`) and backend code to fully support the functionality.  Utilize the existing jQuery dialog functionality (`$.openDialog`) for modal dialogs. The `VehicleMgrController` now includes `parkingSpaceDialog` and related view.  The `$.openDialog` function is assumed to exist based on previous context. Further investigation is needed to confirm its location and proper usage.  The `VehicleMgrController` has been updated (2025-03-19) to include `ownerDialog` and `parkingSpaceDialog` methods, returning "oc/vehicle/ownerDialog" and "oc/vehicle/parkingSpaceDialog" views respectively.  Corresponding Thymeleaf templates (`ownerDialog.html` and `parkingSpaceDialog.html`) have been created (2025-03-19) under `ehome-page/src/main/resources/templates/oc/vehicle/`.  The controller methods should use `PREFIX + "/..."` for pathing.  The AJAX requests should use absolute paths (e.g., `/oc/vehicle/ownerDialog`) to avoid pathing issues.
- **Added 2025-05-17:**  Upon login failure in WeChat Mini Program, clear local cache and global variables to ensure a clean login process next time.  Specifically, `wx.removeStorageSync('token')`, `wx.removeStorageSync('userInfo')`, `app.globalData.isLogin = false`, `app.globalData.userInfo = null`, and `app.globalData.hasBindPhone = false` should be executed.  The `needUpdateProfile` check should be made more robust by handling cases where `userInfo` is null or an empty object.  In `getPhoneNumber`, verify that `userInfo` and `userInfo.userId` exist before proceeding; otherwise, prompt the user to log in again.
- **Added 2025-05-17:** When the `updatePhone` method is called, the `mobile` field in the `LoginUser` object should be updated synchronously to reflect the new phone number. This ensures consistency between the database and the user's current session. This update should occur in the `wechatAuthService.updateUserPhone` method after successful phone number update.  The `com.ehome.common.core.controller.BaseWxController.setCurrentUserMobile(phoneNumber)` method should be used to update the `LoginUser` object.
- **Added 2025-05-17:** Always clear WeChat Mini Program login session data (cache and global variables) upon login failure to prevent unexpected behavior in subsequent login attempts.  Specifically, `wx.removeStorageSync('token')`, `wx.removeStorageSync('userInfo')`, `app.globalData.isLogin = false`, `app.globalData.userInfo = null`, and `app.globalData.hasBindPhone = false` should be executed. The `needUpdateProfile` check should be made more robust by handling cases where `userInfo` is null or an empty object. In `getPhoneNumber`, verify that `userInfo` and `userInfo.userId` exist before proceeding; otherwise, prompt the user to log in again.  The WeChat Mini Program should directly use the authorized WeChat phone number instead of prompting the user to bind the phone number.  Remove the prompt "Please bind your phone number".
- **Added 2025-05-17:** Always update the `mobile` field in the `LoginUser` object when the `updatePhone` method successfully updates the phone number in the database. Use `com.ehome.common.core.controller.BaseWxController.setCurrentUserMobile(phoneNumber)` to perform this update. After successful phone number authorization via `updateUserPhone`, the `mobile` field within `SecurityUtils.createToken` must be updated to reflect the newly authorized phone number. This requires updating the `LoginUser` object in `BaseWxController` to ensure consistency across the application.
- **Added 2025-05-17:** Upon WeChat Mini Program login, if `hasBindPhone` is false, a modal should appear prompting the user to bind their phone number using the WeChat `getPhoneNumber` API. The modal should only contain a button with `open-type="getPhoneNumber"`.  The modal should be dismissed upon successful phone number binding.  The prompt "Please bind your phone number" should be removed from all other instances.
- **Added 2025-05-17:** In the WeChat Mini Program login process, automatically retrieve user profile information (avatar and nickname) upon successful login. Only prompt the user to bind their phone number if `hasBindPhone` is false. Remove all other prompts for binding phone numbers.
- **Added 2025-05-17:** In the WeChat Mini Program login process, `isLogin` should be true if and only if `hasBindPhone` is true. If `hasBindPhone` is false, `isLogin` must be false. This ensures that a user is only considered logged in if they have bound their phone number.  In the WeChat Mini Program, `isLogin` should be `false` if `hasBindPhone` is `false`. `isLogin` should only be set to `true` after successful phone number binding.
- **Added 2025-05-17:** For MyBatis queries, ensure that the parameter names in your Java Mapper interfaces match the parameter names used in your XML Mapper files.  If using a different naming convention (e.g., camelCase in Java vs. snake_case in XML), explicitly define parameter names using the `@Param` annotation in your Java code.  Failure to do so will result in `BindingException: Parameter '...' not found` errors.
- **Added 2025-05-17:**  To ensure token updates are effective, after updating the token using `wx.setStorageSync('token', newToken)`, also update `app.globalData.token = newToken` in the WeChat Mini Program's `getPhoneNumber` method.  Additionally, review the implementation of `app.request` to ensure it consistently retrieves the token from `wx.getStorageSync('token')` and not a potentially outdated cached value.  On the backend, consider implementing a token blacklisting mechanism or versioning to invalidate older tokens upon sensitive information updates.
- **Added 2025-05-17:** If `isHouseAuth` is false, the WeChat Mini Program login should be blocked, displaying a modal prompting the user to contact the administrator for house binding.  All login-related cache and global variables should be cleared.  A `clearLoginState()` function should be implemented to handle this clearing consistently.
- **Added 2025-05-17:**  Always use absolute paths for API endpoints in AJAX calls to avoid pathing issues.  In WeChat Mini Program development, ensure that all API calls use absolute paths.
- **Added 2025-05-17:** In WeChat Mini Program's `finance.js`, the `getFinanceData` method should use the absolute path `/api/wx/ccb/getData` for the API endpoint. Error handling should include a `wx.showToast` message for both success and failure scenarios. The success scenario should check if `res.data` and `res.data.list` are valid before updating the `financeList` state. Consider also updating `monthIncome`, `monthExpense`, and `balance` if returned by the API.  The `getFinanceData` method should utilize `app.request` for API calls, handle `res.code` for error checking, and use `async/await` for cleaner code.  The `data` object should include initial values for `currentDate`, `monthIncome`, `monthExpense`, `balance`, and `financeList`.  To handle cases where `monthExpense` is 0.0, use a strict equality check (===) instead of loose equality (==) or the || operator when setting the `monthExpense` value in `setData`.  For example: `monthExpense: res.data.monthExpense`  This will ensure that 0.0 is treated as a valid value and not replaced with an empty string.
- **Added 2025-05-17:** In the WeChat Mini Program, if the user is not logged in, redirect to the login page.
- **Added 2025-05-17:**  In the WeChat Mini Program, always use absolute paths for API endpoints in all AJAX calls to avoid pathing issues.


## WORKFLOW & RELEASE RULES
- All changes must be documented in `.specstory/history` with a descriptive markdown file.
- Use Git for version control.  Follow a standard Gitflow workflow.
- Releases should be tagged with semantic versioning.

## REFERENCE EXAMPLES
- Refer to the Ruoyi framework documentation for common patterns and best practices.
- **Added 2025-03-29:**  See `.specstory/history/2025-03-29_11-42-提取deepseek网站内容为markdown.md` for an example of extracting website content and presenting it in Markdown format.
- **Added 2025-03-29:** Example of DeepSeek website integration Markdown:

# DeepSeek接入网站导航

## 重要说明
- 收集平台数：58个
- 本页收集了已接入DeepSeek的站点
- 信息可能因平台政策调整而不准确,请以实际使用情况为准

## 完整网站列表

| 网站名称 | 网址 | 版本 | 是否免费 | 备注说明 |
|---------|------|------|----------|----------|
| DeepSeek | https://chat.deepseek.com | 纯满血 | 免费 | - |
| 腾讯元宝 | https://yuanbao.tencent.com/chat | 满血R1 | 免费 | - |
| 当贝AI | https://ai.dangbei.com/chat | 满血R1 | 免费 | - |
| 华为小艺 | https://xiaoyi.huawei.com/chat | 满血R1 | 免费 | - |
| 问小白 | https://www.wenxiaobai.com/chat | 满血R1 | 免费 | - |
| QQ浏览器 | https://aisearch.qq.com | 满血R1 | 免费 | QQ浏览器专线版部署,打开即用 |
| 跃问 | https://yuewen.cn/chats | 满血R1 | 免费 | - |
| AskMany | https://askmany.cn/login | 满血R1 | 免费 | - |
| 纳米AI搜索 | https://deepseek.n.cn | 满血R1 | 免费 | - |
| 秘塔搜索 | https://metaso.cn | 满血R1 | 免费 | 即开即用,无需注册 |
- 天工AI | https://www.tiangong.cn | 满血R1 | 免费 | 打开就能使用 |
- 百度AI | https://chat.baidu.com/search | 满血R1 | 免费 | - |
| Heck.ai | https://heck.ai/zh | 双版本 | 免费 | 打开即用,不用注册 |
| 紫东太初 | https://ai-maas.wair.ac.cn/taichu/#/chat | 多版本 | 免费 | 登录即用,多版本可选择 |
| 华知 | https://huazhi.cnki.net/#/sw | 双版本 | 免费 | 免费使用 |
| CSDN-C知道 | https://so.csdn.net/chat | 满血R1 | 免费 | - |


*Note:  This table only shows a subset of the 58 integrated platforms.  A complete list would require further data extraction.*
- **Added 2025-05-14:** Add documentation for the redesigned main.html homepage.  Include details of the HomeController and `/oc/home/<USER>

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
- All design decisions and discussions are recorded in `.specstory/history`.
- Project documentation is in `doc/若依环境使用手册.docx`.  Consider migrating to a more easily versioned format (e.g., Markdown).  Version 4.7.9 documentation is current.
- **Added 2025-03-29:** Added Markdown documentation for DeepSeek website integration.
- **Added 2025-05-14:**  Add documentation for the redesigned main.html homepage.  Include details of the HomeController and `/oc/home/<USER>

## DEBUGGING
Standard debugging techniques should be employed. Utilize logging effectively. When using AJAX, ensure you are using absolute paths for API endpoints to avoid pathing issues.  Check your browser's Network tab (F12) to inspect requests and responses for debugging purposes.  Note that on iOS, `new Date()` only supports specific date formats: "yyyy/MM/dd"、"yyyy/MM/dd HH:mm:ss"、"yyyy-MM-dd"、"yyyy-MM-ddTHH:mm:ss"、"yyyy-MM-ddTHH:mm:ss+HH:mm".  Additionally, when working with dates in WeChat Mini Programs on iOS, ensure date strings are formatted to be compatible with iOS's `new Date()` function.  Supported formats include: "yyyy/MM/dd"、"yyyy/MM/dd HH:mm:ss"、"yyyy-MM-dd"、"yyyy-MM-ddTHH:mm:ss"、"yyyy-MM-ddTHH:mm:ss+HH:mm".  If using a different format, consider pre-processing the date string to match one of these supported formats before using `new Date()`.


## FINAL DOs AND DON'Ts
- **DO** use descriptive variable and method names.
- **DO** write unit tests.
- **DO** commit frequently with meaningful commit messages.
- **DON'T** commit directly to master.
- **DON'T** leave commented-out code.
- **DON'T** hardcode values.
- **Updated 2025-05-17:** In the WeChat Mini Program, always use absolute paths for API endpoints in all AJAX calls to avoid pathing issues.  Always clear WeChat Mini Program login session data (cache and global variables) upon login failure to prevent unexpected behavior in subsequent login attempts.  Specifically, `wx.removeStorageSync('token')`, `wx.removeStorageSync('userInfo')`, `app.globalData.isLogin = false`, `app.globalData.userInfo = null`, and `app.globalData.hasBindPhone = false` should be executed. The `needUpdateProfile` check should be made more robust by handling cases where `userInfo` is null or an empty object. In `getPhoneNumber`, verify that `userInfo` and `userInfo.userId` exist before proceeding; otherwise, prompt the user to log in again. The WeChat Mini Program should directly use the authorized WeChat phone number instead of prompting the user to bind the phone number. Remove the prompt "Please bind your phone number". If `isHouseAuth` is false, prevent login and display a modal stating that the user needs to contact the administrator to bind a house. Clear all login-related cache and global variables. Implement a `clearLoginState()` function in the WeChat Mini Program to consolidate the clearing logic. If the user is not logged in, redirect to the login page. Always update the `mobile` field in the `LoginUser` object when the `updatePhone` method successfully updates the phone number in the database. Use `com.ehome.common.core.controller.BaseWxController.setCurrentUserMobile(phoneNumber)` to perform this update. After successful phone number authorization via `updateUserPhone`, the `mobile` field within `SecurityUtils.createToken` must be updated to reflect the newly authorized phone number. This requires updating the `LoginUser` object in `BaseWxController` to ensure consistency across the application. Upon WeChat Mini Program login, if `hasBindPhone` is false, a modal should appear prompting the user to bind their phone number using the WeChat `getPhoneNumber` API. The modal should only contain a button with `open-type="getPhoneNumber"`. The modal should be dismissed upon successful phone number binding. In the WeChat Mini Program login process, automatically retrieve user profile information (avatar and nickname) upon successful login. Only prompt the user to bind their phone number if `hasBindPhone` is false. Remove all other prompts for binding phone numbers. In the WeChat Mini Program login process, `isLogin` should be true if and only if `hasBindPhone` is true. If `hasBindPhone` is false, `isLogin` must be false. This ensures that a user is only considered logged in if they have bound their phone number. In the WeChat Mini Program, `isLogin` should be `false` if `hasBindPhone` is `false`. `isLogin` should only be set to `true` after successful phone number binding. For MyBatis queries, ensure that the parameter names in your Java Mapper interfaces match the parameter names used in your XML Mapper files. If using a different naming convention (e.g., camelCase in Java vs. snake_case in XML), explicitly define parameter names using the `@Param` annotation in your Java code. Failure to do so will result in `BindingException: Parameter '...' not found` errors. To ensure token updates are effective, after updating the token using `wx.setStorageSync('token', newToken)`, also update `app.globalData.token = newToken` in the WeChat Mini Program's `getPhoneNumber` method. Additionally, review the implementation of `app.request` to ensure it consistently retrieves the token from `wx.getStorageSync('token')` and not a potentially outdated cached value. On the backend, consider implementing a token blacklisting mechanism or versioning to invalidate older tokens upon sensitive information updates.
- **Updated 2025-05-14:** The `main.html` homepage has been redesigned for a PC-style dashboard layout.  The design prioritizes a wider layout with horizontal sections for statistics, announcements, and quick access links.  The backend `HomeController` and `/oc/home/<USER>
- **Updated 2025-03-18:** The `detail-owner.html` file design has been revised based on user feedback and now uses AJAX requests instead of Thymeleaf `th:text` and `th:if` expressions for data population. The updated design places owner information at the top, followed by tabbed sections for houses and vehicles. House information is displayed as an unpaginated list (assuming a small number of houses per owner). The `OcOwnerController` has been updated to support AJAX requests for house and vehicle data.  MCP can be used for database interaction if the table structure allows it.  The `fillRecord` method should be used to populate data.  The AJAX requests are handled in Javascript functions `loadHouseList()`, `loadVehicleList()`, and `loadOwnerInfo()`. Further user confirmation is needed to ensure the revised design meets expectations.  The `detail-owner.html` page now includes functionality to unbind and bind houses.  Further development is needed to implement the full functionality of binding and unbinding houses. Use absolute paths for API endpoints in AJAX calls.
- **Updated 2025-03-19:**  The `houseInfo` area in `detail-owner.html` now displays floor, total area, use area, house type, house status, creation time, and remarks.  House type and status are displayed using dictionary data.
- **Added 2025-03-19:**  The `selectOwner` and `selectParkingSpace` dialogs (`oc/vehicle/ownerDialog.html`, `oc/vehicle/parkingSpaceDialog.html`) should leverage the existing jQuery dialog implementation within the project.  The implementation should utilize the `$.openDialog` function, and the location of this function needs further investigation. Use absolute paths for API endpoints in AJAX calls.
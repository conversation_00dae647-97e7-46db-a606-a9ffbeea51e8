package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.system.domain.SysNotice;
import com.ehome.system.domain.SysNoticeComment;
import com.ehome.system.service.ISysNoticeCommentService;
import com.ehome.system.service.ISysNoticeService;
import com.ehome.oc.service.ICommunityConfigService;
import com.ehome.common.domain.CommunityConfig;
import com.ehome.system.service.impl.SysNoticeCommentServiceImpl;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/wx/notice")
public class WxNoticeController extends BaseWxController {

    @Autowired
    private ISysNoticeCommentService commentService;

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private ICommunityConfigService communityConfigService;

    /**
     * 获取公告评论列表（支持分页）
     */
    @GetMapping("/{noticeId}/comments")
    public AjaxResult getNoticeComments(@PathVariable("noticeId") Long noticeId,
                                       @RequestParam(value = "page", defaultValue = "1") Integer page,
                                       @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            // 检查公告是否开启评论功能
            SysNotice notice = noticeService.selectNoticeById(noticeId);
            if (notice == null) {
                return AjaxResult.error("公告不存在");
            }

            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            String communityId = currentUser != null ? currentUser.getCommunityId() : null;

            // 检查全局评论开关
            if (communityId != null) {
                CommunityConfig config = communityConfigService.getConfig(communityId);
                if (config != null) {
                    String globalCommentEnabled = config.getString("enable_comment");
                    if (!"1".equals(globalCommentEnabled)) {
                        return AjaxResult.error("评论功能已被管理员关闭");
                    }
                }
            }

            // 检查公告评论开关
            if (notice.getEnableComment() == null || notice.getEnableComment() != 1) {
                return AjaxResult.error("该公告未开启评论功能");
            }

            // 获取用户ID
            String userId = currentUser != null ? String.valueOf(currentUser.getUserId()) : null;

            // 使用分页查询顶级评论
            EasySQL sql = new EasySQL();
            sql.append("from sys_notice_comment where 1=1");
            sql.append(noticeId, "and notice_id = ?");
            sql.append("and parent_id is null");
            // 显示已审核通过的评论，或者当前用户自己的待审核评论
            if (userId != null) {
                sql.append("and (status = '0' or (status = '2' and user_id = '" + userId + "'))");
            } else {
                sql.append("and status = '0'");
            }
            sql.append("order by create_time desc");

            Page<Record> pageResult = Db.paginate(page, pageSize,
                "select comment_id, notice_id, parent_id, content, user_id, user_name, user_type, like_count, status, create_by, create_time, update_by, update_time",
                sql.toFullSql());

            List<Record> commentRecords = pageResult.getList();
            long totalCount = pageResult.getTotalRow();

            // 转换为前端需要的格式
            List<Map<String, Object>> commentList = new ArrayList<>();
            for (Record commentRecord : commentRecords) {
                Map<String, Object> commentMap = new HashMap<>();
                Long commentId = commentRecord.getLong("comment_id");
                commentMap.put("id", commentId);
                commentMap.put("content", commentRecord.getStr("content"));
                commentMap.put("userName", commentRecord.getStr("user_name"));
                commentMap.put("userType", commentRecord.getStr("user_type"));
                commentMap.put("createTime", commentRecord.getDate("create_time"));
                commentMap.put("status", commentRecord.getStr("status"));
                commentMap.put("isPending", "2".equals(commentRecord.getStr("status")));

                // 动态查询回复数量
                Record replyCountRecord;
                if (userId != null) {
                    replyCountRecord = Db.findFirst("SELECT COUNT(*) as reply_count FROM sys_notice_comment WHERE parent_id = ? AND (status = '0' or (status = '2' and user_id = ?))", commentId, userId);
                } else {
                    replyCountRecord = Db.findFirst("SELECT COUNT(*) as reply_count FROM sys_notice_comment WHERE parent_id = ? AND status = '0'", commentId);
                }
                int replyCount = replyCountRecord != null ? replyCountRecord.getInt("reply_count") : 0;
                commentMap.put("replyCount", replyCount);

                // 获取点赞数和用户是否已点赞
                int likeCount = commentRecord.getInt("like_count") != null ? commentRecord.getInt("like_count") : 0;
                commentMap.put("likeCount", likeCount);

                boolean isLiked = false;
                if (userId != null) {
                    Record likeRecord = Db.findFirst(
                        "SELECT * FROM sys_comment_like WHERE comment_id = ? AND user_id = ? AND status = '0'",
                        commentId, userId
                    );
                    isLiked = likeRecord != null;
                }
                commentMap.put("isLiked", isLiked);

                // 获取回复列表（最多显示3条）
                List<SysNoticeComment> replies;
                if (userId != null) {
                    replies = commentService.selectRepliesByParentIdWithUser(commentId, userId);
                } else {
                    replies = commentService.selectRepliesByParentId(commentId);
                }
                List<Map<String, Object>> replyList = new ArrayList<>();
                int maxReplies = Math.min(replies.size(), 3);
                for (int i = 0; i < maxReplies; i++) {
                    SysNoticeComment reply = replies.get(i);
                    Map<String, Object> replyMap = new HashMap<>();
                    replyMap.put("id", reply.getCommentId());
                    replyMap.put("content", reply.getContent());
                    replyMap.put("userName", reply.getUserName());
                    replyMap.put("userType", reply.getUserType());
                    replyMap.put("createTime", reply.getCreateTime());
                    replyMap.put("status", reply.getStatus());
                    replyMap.put("isPending", "2".equals(reply.getStatus()));

                    // 获取回复的点赞数和用户是否已点赞
                    Record replyStats = Db.findFirst("SELECT like_count FROM sys_notice_comment WHERE comment_id = ?", reply.getCommentId());
                    int replyLikeCount = replyStats != null ? replyStats.getInt("like_count") : 0;
                    replyMap.put("likeCount", replyLikeCount);

                    boolean replyIsLiked = false;
                    if (userId != null) {
                        Record replyLikeRecord = Db.findFirst(
                            "SELECT * FROM sys_comment_like WHERE comment_id = ? AND user_id = ? AND status = '0'",
                            reply.getCommentId(), userId
                        );
                        replyIsLiked = replyLikeRecord != null;
                    }
                    replyMap.put("isLiked", replyIsLiked);

                    replyList.add(replyMap);
                }
                commentMap.put("replies", replyList);
                commentMap.put("hasMoreReplies", replies.size() > 3);

                commentList.add(commentMap);
            }

            // 构建分页返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", commentList);
            result.put("total", totalCount);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("hasMore", (long) page * pageSize < totalCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取评论列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取评论列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取评论回复列表
     */
    @GetMapping("/comments/{parentId}/replies")
    public AjaxResult getCommentReplies(@PathVariable("parentId") Long parentId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            String userId = currentUser != null ? String.valueOf(currentUser.getUserId()) : null;

            List<SysNoticeComment> replies;
            if (userId != null) {
                replies = commentService.selectRepliesByParentIdWithUser(parentId, userId);
            } else {
                replies = commentService.selectRepliesByParentId(parentId);
            }

            // 转换为前端需要的格式
            List<Map<String, Object>> replyList = new ArrayList<>();
            for (SysNoticeComment reply : replies) {
                Map<String, Object> replyMap = new HashMap<>();
                replyMap.put("id", reply.getCommentId());
                replyMap.put("content", reply.getContent());
                replyMap.put("userName", reply.getUserName());
                replyMap.put("userType", reply.getUserType());
                replyMap.put("createTime", reply.getCreateTime());
                replyMap.put("status", reply.getStatus());
                replyMap.put("isPending", "2".equals(reply.getStatus()));

                // 获取点赞数和用户是否已点赞
                Record replyStats = Db.findFirst("SELECT like_count FROM sys_notice_comment WHERE comment_id = ?", reply.getCommentId());
                int likeCount = replyStats != null ? replyStats.getInt("like_count") : 0;
                replyMap.put("likeCount", likeCount);

                boolean isLiked = false;
                if (userId != null) {
                    Record likeRecord = Db.findFirst(
                        "SELECT * FROM sys_comment_like WHERE comment_id = ? AND user_id = ? AND status = '0'",
                        reply.getCommentId(), userId
                    );
                    isLiked = likeRecord != null;
                }
                replyMap.put("isLiked", isLiked);

                replyList.add(replyMap);
            }

            return AjaxResult.success(replyList);
        } catch (Exception e) {
            logger.error("获取回复列表失败: " + e.getMessage(), e);
            return AjaxResult.error("获取回复列表失败: " + e.getMessage());
        }
    }

    /**
     * 发表评论
     */
    @PostMapping("/{noticeId}/comments")
    public AjaxResult postComment(@PathVariable("noticeId") Long noticeId, @RequestBody JSONObject requestBody) {
        try {
            String content = requestBody.getString("content");
            Long parentId = requestBody.getLong("parentId"); // 可选，回复评论时使用

            if (StringUtils.isEmpty(content) || content.trim().length() < 1) {
                return AjaxResult.error("评论内容不能为空");
            }

            if (content.trim().length() > 500) {
                return AjaxResult.error("评论内容不能超过500字");
            }

            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String userName = currentUser.getUsername();
            String pmsId = ""; // 默认值，如果需要可以从其他地方获取
            String communityId = currentUser.getCommunityId();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 设置默认值
            if (StringUtils.isEmpty(pmsId)) {
                pmsId = ""; // 默认值
            }
            if (StringUtils.isEmpty(communityId)) {
                communityId = ""; // 默认值
            }

            // 检查全局评论开关
            if (StringUtils.isNotEmpty(communityId)) {
                CommunityConfig config = communityConfigService.getConfig(communityId);
                if (config != null) {
                    String globalCommentEnabled = config.getString("enable_comment");
                    if (!"1".equals(globalCommentEnabled)) {
                        return AjaxResult.error("评论功能已被管理员关闭");
                    }
                }
            }

            // 检查公告是否开启评论功能
            SysNotice notice = noticeService.selectNoticeById(noticeId);
            if (notice == null) {
                return AjaxResult.error("公告不存在");
            }

            // 检查公告评论开关
            if (notice.getEnableComment() == null || notice.getEnableComment() != 1) {
                return AjaxResult.error("该公告未开启评论功能");
            }

            // 发表评论（使用新的重载方法）
            SysNoticeCommentServiceImpl commentServiceImpl = (SysNoticeCommentServiceImpl) commentService;
            int result = commentServiceImpl.postComment(
                noticeId,
                parentId,
                userId,
                userName,
                "wx_user",
                content.trim(),
                pmsId,
                communityId,
                ownerId,
                houseId,
                houseName
            );

            if (result > 0) {
                return AjaxResult.success("评论发表成功");
            } else {
                return AjaxResult.error("评论发表失败");
            }
        } catch (Exception e) {
            logger.error("发表评论失败: " + e.getMessage(), e);
            return AjaxResult.error("发表评论失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告评论数量
     */
    @GetMapping("/{noticeId}/comments/count")
    public AjaxResult getCommentCount(@PathVariable("noticeId") Long noticeId) {
        try {
            int count = commentService.countCommentsByNoticeId(noticeId);
            return AjaxResult.success(count);
        } catch (Exception e) {
            logger.error("获取评论数量失败: " + e.getMessage(), e);
            return AjaxResult.error("获取评论数量失败: " + e.getMessage());
        }
    }

    /**
     * 评论点赞/取消点赞
     */
    @PostMapping("/comments/{commentId}/like")
    public AjaxResult likeComment(@PathVariable("commentId") Long commentId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 检查是否已经点赞
            Record existingLike = Db.findFirst(
                "SELECT * FROM sys_comment_like WHERE comment_id = ? AND user_id = ? AND status = '0'",
                commentId, userId
            );

            boolean isLiked = false;
            int likeCount = 0;

            if (existingLike != null) {
                // 已点赞，取消点赞
                Db.update("UPDATE sys_comment_like SET status = '1', update_time = NOW() WHERE comment_id = ? AND user_id = ?",
                    commentId, userId);

                // 减少点赞数
                Db.update("UPDATE sys_notice_comment SET like_count = GREATEST(like_count - 1, 0) WHERE comment_id = ?", commentId);
                isLiked = false;
            } else {
                // 未点赞，添加点赞记录
                Db.update(
                    "INSERT INTO sys_comment_like (comment_id, user_id, user_name, user_type, owner_id, house_id, house_name, status, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, '0', NOW()) " +
                    "ON DUPLICATE KEY UPDATE status = '0', update_time = NOW()",
                    commentId, userId, currentUser.getUsername(), "wx_user", ownerId, houseId, houseName
                );

                // 增加点赞数
                Db.update("UPDATE sys_notice_comment SET like_count = like_count + 1 WHERE comment_id = ?", commentId);
                isLiked = true;
            }

            // 获取最新的点赞数
            likeCount = Db.queryInt("SELECT like_count FROM sys_notice_comment WHERE comment_id = ?", commentId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("评论点赞操作失败: " + e.getMessage(), e);
            return AjaxResult.error("评论点赞操作失败: " + e.getMessage());
        }
    }

    /**
     * 公告点赞/取消点赞
     */
    @PostMapping("/{noticeId}/like")
    public AjaxResult likeNotice(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String communityId = currentUser.getCommunityId();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 检查是否已经点赞
            Record existingLike = Db.findFirst(
                "SELECT * FROM sys_notice_like WHERE notice_id = ? AND user_id = ? AND status = '0'",
                noticeId, userId
            );

            boolean isLiked = false;
            int likeCount = 0;

            if (existingLike != null) {
                // 已点赞，取消点赞
                Db.update("UPDATE sys_notice_like SET status = '1', update_time = NOW() WHERE notice_id = ? AND user_id = ?",
                    noticeId, userId);

                // 减少点赞数
                Db.update("UPDATE sys_notice SET like_count = GREATEST(like_count - 1, 0) WHERE notice_id = ?", noticeId);
                isLiked = false;
            } else {
                // 未点赞，添加点赞记录
                Db.update(
                    "INSERT INTO sys_notice_like (notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, status, create_time) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, '0', NOW()) " +
                    "ON DUPLICATE KEY UPDATE status = '0', update_time = NOW()",
                    noticeId, userId, currentUser.getUsername(), "wx_user", communityId, ownerId, houseId, houseName
                );

                // 增加点赞数
                Db.update("UPDATE sys_notice SET like_count = like_count + 1 WHERE notice_id = ?", noticeId);
                isLiked = true;
            }

            // 获取最新的点赞数
            likeCount = Db.queryInt("SELECT like_count FROM sys_notice WHERE notice_id = ?", noticeId);

            Map<String, Object> result = new HashMap<>();
            result.put("isLiked", isLiked);
            result.put("likeCount", likeCount);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("公告点赞操作失败: " + e.getMessage(), e);
            return AjaxResult.error("公告点赞操作失败: " + e.getMessage());
        }
    }

    /**
     * 公告分享统计
     */
    @PostMapping("/{noticeId}/share")
    public AjaxResult shareNotice(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String communityId = currentUser.getCommunityId();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 记录分享行为并获取分享ID
            Db.update(
                "INSERT INTO sys_notice_share (notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())",
                noticeId, userId, currentUser.getUsername(), "wx_user", communityId, ownerId, houseId, houseName
            );

            // 获取刚插入的分享记录ID
            Long shareId = Db.queryLong("SELECT LAST_INSERT_ID()");

            // 增加分享数
            Db.update("UPDATE sys_notice SET share_count = share_count + 1 WHERE notice_id = ?", noticeId);

            // 获取最新的分享数
            int shareCount = Db.queryInt("SELECT share_count FROM sys_notice WHERE notice_id = ?", noticeId);

            Map<String, Object> result = new HashMap<>();
            result.put("shareCount", shareCount);
            result.put("shareId", shareId);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("公告分享统计失败: " + e.getMessage(), e);
            return AjaxResult.error("公告分享统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告统计信息（点赞数、分享数、用户是否已点赞）
     */
    @GetMapping("/{noticeId}/stats")
    public AjaxResult getNoticeStats(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            String userId = currentUser != null ? String.valueOf(currentUser.getUserId()) : null;

            // 获取公告统计信息
            Record noticeStats = Db.findFirst(
                "SELECT like_count, share_count FROM sys_notice WHERE notice_id = ?", noticeId
            );

            if (noticeStats == null) {
                return AjaxResult.error("公告不存在");
            }

            boolean isLiked = false;
            if (userId != null) {
                // 检查用户是否已点赞
                Record likeRecord = Db.findFirst(
                    "SELECT * FROM sys_notice_like WHERE notice_id = ? AND user_id = ? AND status = '0'",
                    noticeId, userId
                );
                isLiked = likeRecord != null;
            }

            Map<String, Object> result = new HashMap<>();
            result.put("likeCount", noticeStats.getInt("like_count"));
            result.put("shareCount", noticeStats.getInt("share_count"));
            result.put("isLiked", isLiked);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取公告统计信息失败: " + e.getMessage(), e);
            return AjaxResult.error("获取公告统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 记录公告阅读统计
     */
    @PostMapping("/{noticeId}/read")
    public AjaxResult recordReadLog(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String userId = String.valueOf(currentUser.getUserId());
            String userName = currentUser.getUsername();
            String ipAddress = getRequest().getRemoteAddr();
            String ownerId = currentUser.getOwnerId();
            String houseId = currentUser.getHouseId();
            String houseName = currentUser.getHouseName();

            // 调用Service记录阅读统计（使用新的重载方法）
            noticeService.viewNoticeAndRecord(noticeId, userId, userName, ipAddress, ownerId, houseId, houseName);

            return AjaxResult.success("记录成功");
        } catch (Exception e) {
            logger.error("记录阅读统计失败: " + e.getMessage(), e);
            return AjaxResult.error("记录阅读统计失败: " + e.getMessage());
        }
    }

    /**
     * 记录分享访问
     */
    @PostMapping("/share/visit")
    public AjaxResult recordShareVisit(@RequestBody Map<String, Object> params) {
        try {
            String shareId = (String) params.get("shareId");
            if (StringUtils.isEmpty(shareId)) {
                return AjaxResult.error("分享ID不能为空");
            }

            // 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            String visitorId = currentUser != null ? String.valueOf(currentUser.getUserId()) : null;
            String visitorName = currentUser != null ? currentUser.getUsername() : "匿名访问者";
            String communityId = currentUser != null ? currentUser.getCommunityId() : null;
            String ownerId = currentUser != null ? currentUser.getOwnerId() : null;
            String houseId = currentUser != null ? currentUser.getHouseId() : null;
            String houseName = currentUser != null ? currentUser.getHouseName() : null;

            // 查找分享记录
            Record shareRecord = Db.findFirst("SELECT * FROM sys_notice_share WHERE share_id = ?", shareId);
            if (shareRecord == null) {
                return AjaxResult.error("分享记录不存在");
            }

            Long noticeId = shareRecord.getLong("notice_id");
            String visitIp = getRequest().getRemoteAddr();
            String userAgent = getRequest().getHeader("User-Agent");

            // 检查是否为新访问者（同一个分享链接，同一个用户首次访问）
            boolean isNewVisitor = false;
            if (visitorId != null) {
                Record existingVisit = Db.findFirst(
                    "SELECT * FROM sys_notice_share_visit WHERE share_id = ? AND visitor_id = ?",
                    shareId, visitorId
                );
                isNewVisitor = (existingVisit == null);
            } else {
                // 匿名用户，通过IP判断
                Record existingVisit = Db.findFirst(
                    "SELECT * FROM sys_notice_share_visit WHERE share_id = ? AND visit_ip = ? AND visitor_id IS NULL",
                    shareId, visitIp
                );
                isNewVisitor = (existingVisit == null);
            }

            // 记录详细访问信息
            Db.update(
                "INSERT INTO sys_notice_share_visit (share_id, notice_id, visitor_id, visitor_name, visitor_type, " +
                "visit_time, visit_ip, user_agent, community_id, owner_id, house_id, house_name, is_new_visitor) " +
                "VALUES (?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?)",
                shareId, noticeId, visitorId, visitorName, "wx_user", visitIp, userAgent,
                communityId, ownerId, houseId, houseName, isNewVisitor ? 1 : 0
            );

            // 更新分享记录的访问统计
            Db.update(
                "UPDATE sys_notice_share SET visit_count = visit_count + 1, last_visit_time = NOW(), " +
                "last_visitor_id = ?, last_visitor_name = ? WHERE share_id = ?",
                visitorId, visitorName, shareId
            );

            return AjaxResult.success("访问记录成功");
        } catch (Exception e) {
            logger.error("记录分享访问失败: " + e.getMessage(), e);
            return AjaxResult.error("记录分享访问失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告分享统计详情
     */
    @GetMapping("/{noticeId}/share/stats")
    public AjaxResult getShareStats(@PathVariable("noticeId") Long noticeId) {
        try {
            // 获取分享总数
            int shareCount = Db.queryInt("SELECT COUNT(*) FROM sys_notice_share WHERE notice_id = ?", noticeId);

            // 获取总访问数
            int totalVisits = Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share_visit WHERE notice_id = ?", noticeId
            );

            // 获取独立访问者数
            int uniqueVisitors = Db.queryInt(
                "SELECT COUNT(DISTINCT COALESCE(visitor_id, visit_ip)) FROM sys_notice_share_visit WHERE notice_id = ?",
                noticeId
            );

            // 获取新访问者数
            int newVisitors = Db.queryInt(
                "SELECT COUNT(*) FROM sys_notice_share_visit WHERE notice_id = ? AND is_new_visitor = 1",
                noticeId
            );

            // 获取最近的分享记录
            List<Record> recentShares = Db.find(
                "SELECT s.user_name, s.create_time, s.visit_count, s.last_visit_time " +
                "FROM sys_notice_share s WHERE s.notice_id = ? ORDER BY s.create_time DESC LIMIT 10",
                noticeId
            );

            // 获取最近的访问记录
            List<Record> recentVisits = Db.find(
                "SELECT v.visitor_name, v.visit_time, v.is_new_visitor " +
                "FROM sys_notice_share_visit v WHERE v.notice_id = ? ORDER BY v.visit_time DESC LIMIT 10",
                noticeId
            );

            Map<String, Object> result = new HashMap<>();
            result.put("shareCount", shareCount);
            result.put("totalVisits", totalVisits);
            result.put("uniqueVisitors", uniqueVisitors);
            result.put("newVisitors", newVisitors);
            result.put("recentShares", recentShares);
            result.put("recentVisits", recentVisits);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取分享统计失败: " + e.getMessage(), e);
            return AjaxResult.error("获取分享统计失败: " + e.getMessage());
        }
    }
}

// 防抖管理器
class DebounceManager {
  constructor() {
    this.timers = new Map() // 存储各种防抖定时器
    this.pendingUpdates = new Map() // 存储待处理的更新
  }

  /**
   * 防抖执行函数
   * @param {string} key 防抖键名
   * @param {Function} fn 要执行的函数
   * @param {number} delay 延迟时间（毫秒）
   * @param {boolean} immediate 是否立即执行第一次
   */
  debounce(key, fn, delay = 300, immediate = false) {
    // 清除之前的定时器
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key))
    }

    // 立即执行模式
    if (immediate && !this.timers.has(key)) {
      fn()
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      if (!immediate) {
        fn()
      }
      this.timers.delete(key)
    }, delay)

    this.timers.set(key, timer)
  }

  /**
   * 批量防抖更新
   * @param {string} key 防抖键名
   * @param {Object} updates 更新数据
   * @param {Function} executor 执行函数
   * @param {number} delay 延迟时间
   */
  batchUpdate(key, updates, executor, delay = 100) {
    // 合并待处理的更新
    if (this.pendingUpdates.has(key)) {
      const existing = this.pendingUpdates.get(key)
      this.pendingUpdates.set(key, { ...existing, ...updates })
    } else {
      this.pendingUpdates.set(key, updates)
    }

    // 防抖执行
    this.debounce(key, () => {
      const finalUpdates = this.pendingUpdates.get(key)
      if (finalUpdates) {
        executor(finalUpdates)
        this.pendingUpdates.delete(key)
      }
    }, delay)
  }

  /**
   * 取消防抖
   * @param {string} key 防抖键名
   */
  cancel(key) {
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key))
      this.timers.delete(key)
    }
    if (this.pendingUpdates.has(key)) {
      this.pendingUpdates.delete(key)
    }
  }

  /**
   * 清除所有防抖
   */
  clear() {
    this.timers.forEach(timer => clearTimeout(timer))
    this.timers.clear()
    this.pendingUpdates.clear()
  }

  /**
   * 立即执行所有待处理的更新
   */
  flush() {
    this.timers.forEach((timer, key) => {
      clearTimeout(timer)
      // 如果有待处理的更新，立即执行
      if (this.pendingUpdates.has(key)) {
        const updates = this.pendingUpdates.get(key)
        // 这里需要知道对应的执行函数，暂时跳过
        console.warn(`[DebounceManager] 无法立即执行 ${key} 的更新，缺少执行函数`)
      }
    })
    this.clear()
  }
}

// 创建单例
const debounceManager = new DebounceManager()

export default debounceManager

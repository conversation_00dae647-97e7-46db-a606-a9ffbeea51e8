package com.ehome.oc.service;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.ehome.common.utils.uuid.IdUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 分享统计服务
 */
@Service
public class ShareStatisticsService {
    
    private static final Logger logger = LoggerFactory.getLogger(ShareStatisticsService.class);
    
    /**
     * 更新单个分享记录的统计数据
     * @param shareRecordId 分享记录ID
     */
    public void updateShareStatistics(String shareRecordId) {
        try {
            if (shareRecordId == null || shareRecordId.startsWith("unknown_")) {
                return; // 跳过无效的分享记录ID
            }
            
            // 计算统计数据
            String sql = "SELECT " +
                    "COUNT(*) as total_visits, " +
                    "COUNT(DISTINCT visitor_openid) as unique_visitors, " +
                    "SUM(is_new_user) as new_users, " +
                    "SUM(stay_duration) as total_stay_duration, " +
                    "AVG(stay_duration) as avg_stay_duration, " +
                    "MAX(visit_time) as last_visit_time " +
                    "FROM eh_wx_share_visit WHERE share_record_id = ?";
            
            Record stats = Db.findFirst(sql, shareRecordId);
            if (stats == null) {
                return;
            }
            
            // 插入或更新统计记录
            String insertSql = "INSERT INTO eh_wx_share_statistics (" +
                    "id, share_record_id, total_visits, unique_visitors, new_users, " +
                    "total_stay_duration, avg_stay_duration, last_visit_time, update_time" +
                    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE " +
                    "total_visits = VALUES(total_visits), " +
                    "unique_visitors = VALUES(unique_visitors), " +
                    "new_users = VALUES(new_users), " +
                    "total_stay_duration = VALUES(total_stay_duration), " +
                    "avg_stay_duration = VALUES(avg_stay_duration), " +
                    "last_visit_time = VALUES(last_visit_time), " +
                    "update_time = VALUES(update_time)";
            
            Db.update(insertSql,
                    IdUtils.fastUUID(),  // 使用fastUUID避免长度问题
                    shareRecordId,
                    stats.getInt("total_visits"),
                    stats.getInt("unique_visitors"),
                    stats.getInt("new_users"),
                    stats.getInt("total_stay_duration"),
                    stats.getBigDecimal("avg_stay_duration"),
                    stats.getDate("last_visit_time"),
                    new Date()
            );
            
            logger.info("更新分享统计成功: shareRecordId = {}", shareRecordId);
            
        } catch (Exception e) {
            logger.error("更新分享统计失败: shareRecordId = {}", shareRecordId, e);
        }
    }
    
    /**
     * 批量更新所有分享记录的统计数据
     */
    public void updateAllShareStatistics() {
        try {
            // 获取所有有效的分享记录ID
            List<Record> shareRecords = Db.find(
                    "SELECT DISTINCT share_record_id FROM eh_wx_share_visit WHERE share_record_id NOT LIKE 'unknown_%'"
            );
            
            logger.info("开始批量更新分享统计，共 {} 条记录", shareRecords.size());
            
            for (Record record : shareRecords) {
                String shareRecordId = record.getStr("share_record_id");
                updateShareStatistics(shareRecordId);
            }
            
            logger.info("批量更新分享统计完成");
            
        } catch (Exception e) {
            logger.error("批量更新分享统计失败", e);
        }
    }
    
    /**
     * 获取分享统计数据
     * @param communityId 社区ID
     * @param days 统计天数
     * @return 统计数据
     */
    public Record getShareStatisticsSummary(String communityId, Integer days) {
        try {
            String sql = "SELECT " +
                    "COUNT(DISTINCT r.id) as total_shares, " +
                    "SUM(CASE WHEN r.share_type = 'app_message' THEN 1 ELSE 0 END) as friend_shares, " +
                    "SUM(CASE WHEN r.share_type = 'timeline' THEN 1 ELSE 0 END) as timeline_shares, " +
                    "COALESCE(SUM(s.total_visits), 0) as total_visits, " +
                    "COALESCE(SUM(s.unique_visitors), 0) as unique_visitors, " +
                    "COALESCE(SUM(s.new_users), 0) as new_users, " +
                    "COALESCE(AVG(s.avg_stay_duration), 0) as avg_stay_duration " +
                    "FROM eh_wx_share_record r " +
                    "LEFT JOIN eh_wx_share_statistics s ON r.id = s.share_record_id " +
                    "WHERE r.community_id = ? AND r.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            
            return Db.findFirst(sql, communityId, days);
            
        } catch (Exception e) {
            logger.error("获取分享统计汇总失败: communityId = {}, days = {}", communityId, days, e);
            return null;
        }
    }
    
    /**
     * 获取分享排行榜
     * @param communityId 社区ID
     * @param days 统计天数
     * @param limit 返回条数
     * @return 排行榜数据
     */
    public List<Record> getShareRanking(String communityId, Integer days, Integer limit) {
        try {
            String sql = "SELECT " +
                    "r.nickname, " +
                    "COUNT(r.id) as share_count, " +
                    "COALESCE(SUM(s.total_visits), 0) as total_visits, " +
                    "COALESCE(SUM(s.unique_visitors), 0) as unique_visitors " +
                    "FROM eh_wx_share_record r " +
                    "LEFT JOIN eh_wx_share_statistics s ON r.id = s.share_record_id " +
                    "WHERE r.community_id = ? AND r.create_time >= DATE_SUB(NOW(), INTERVAL ? DAY) " +
                    "GROUP BY r.user_id, r.nickname " +
                    "ORDER BY share_count DESC, total_visits DESC " +
                    "LIMIT ?";
            
            return Db.find(sql, communityId, days, limit);
            
        } catch (Exception e) {
            logger.error("获取分享排行榜失败: communityId = {}, days = {}", communityId, days, e);
            return null;
        }
    }
}

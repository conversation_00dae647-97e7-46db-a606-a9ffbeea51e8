package com.ehome.oc;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/oc/home")
public class HomeController extends BaseController {

    /**
     * 首页统计数据和公告
     */
    @PostMapping("/summary")
    @ResponseBody
    public AjaxResult summary() {
        Map<String, Object> data = new HashMap<>();
        String communityId = getSysUser().getCommunityId();

        // 收支数据统计（基于收支流水）
        data.put("financeData", getFinanceData(communityId));

        // 报修报告统计
        data.put("repairData", getRepairData(communityId));

        // 投诉建议统计
        data.put("complaintData", getComplaintData(communityId));

        // 资产信息统计
        data.put("assetData", getAssetData(communityId));

        // 小程序使用记录统计
        data.put("miniProgramData", getMiniProgramData(communityId));

        // 最新公告（取5条）
        List<Record> announcementList = Db.find("SELECT notice_id id, notice_title title, create_time publish_date, read_count, comment_count FROM sys_notice where community_id = ? and status = '0' and deleted = 0 ORDER BY create_time DESC LIMIT 4", communityId);
        data.put("announcements", getDataList(announcementList).getRows());

        return AjaxResult.success(data);
    }

    /**
     * 获取收支数据统计
     */
    private Map<String, Object> getFinanceData(String communityId) {
        Map<String, Object> financeData = new HashMap<>();

        // 本月收入
        Record monthIncome = Db.findFirst("SELECT COALESCE(SUM(amt), 0) as amount FROM eh_tran_record WHERE community_id = ? AND direction = 'in' AND status = 'published' AND DATE_FORMAT(tran_datetime, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')", communityId);
        financeData.put("monthIncome", monthIncome != null ? monthIncome.getBigDecimal("amount") : 0);

        // 本月支出
        Record monthExpense = Db.findFirst("SELECT COALESCE(SUM(amt), 0) as amount FROM eh_tran_record WHERE community_id = ? AND direction = 'out' AND status = 'published' AND DATE_FORMAT(tran_datetime, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')", communityId);
        financeData.put("monthExpense", monthExpense != null ? monthExpense.getBigDecimal("amount") : 0);

        // 本年收入
        Record yearIncome = Db.findFirst("SELECT COALESCE(SUM(amt), 0) as amount FROM eh_tran_record WHERE community_id = ? AND direction = 'in' AND status = 'published' AND YEAR(tran_datetime) = YEAR(NOW())", communityId);
        financeData.put("yearIncome", yearIncome != null ? yearIncome.getBigDecimal("amount") : 0);

        // 本年支出
        Record yearExpense = Db.findFirst("SELECT COALESCE(SUM(amt), 0) as amount FROM eh_tran_record WHERE community_id = ? AND direction = 'out' AND status = 'published' AND YEAR(tran_datetime) = YEAR(NOW())", communityId);
        financeData.put("yearExpense", yearExpense != null ? yearExpense.getBigDecimal("amount") : 0);

        // 本月收入率（收入/(收入+支出)*100）
        Record monthStats = Db.findFirst("SELECT COALESCE(SUM(CASE WHEN direction = 'in' THEN amt ELSE 0 END), 0) as income, COALESCE(SUM(CASE WHEN direction = 'out' THEN amt ELSE 0 END), 0) as expense FROM eh_tran_record WHERE community_id = ? AND status = 'published' AND DATE_FORMAT(tran_datetime, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')", communityId);
        if (monthStats != null) {
            double monthIncomeVal = monthStats.getBigDecimal("income").doubleValue();
            double monthExpenseVal = monthStats.getBigDecimal("expense").doubleValue();
            double monthTotal = monthIncomeVal + monthExpenseVal;
            financeData.put("monthRate", monthTotal > 0 ? Math.round(monthIncomeVal / monthTotal * 100 * 100) / 100.0 : 0);
        } else {
            financeData.put("monthRate", 0);
        }

        // 本年收入率
        Record yearStats = Db.findFirst("SELECT COALESCE(SUM(CASE WHEN direction = 'in' THEN amt ELSE 0 END), 0) as income, COALESCE(SUM(CASE WHEN direction = 'out' THEN amt ELSE 0 END), 0) as expense FROM eh_tran_record WHERE community_id = ? AND status = 'published' AND YEAR(tran_datetime) = YEAR(NOW())", communityId);
        if (yearStats != null) {
            double yearIncomeVal = yearStats.getBigDecimal("income").doubleValue();
            double yearExpenseVal = yearStats.getBigDecimal("expense").doubleValue();
            double yearTotal = yearIncomeVal + yearExpenseVal;
            financeData.put("yearRate", yearTotal > 0 ? Math.round(yearIncomeVal / yearTotal * 100 * 100) / 100.0 : 0);
        } else {
            financeData.put("yearRate", 0);
        }

        //查余额
        Record bankInfo = Db.findFirst("select * from eh_pms_bank_account where community_id = ?", communityId);
        if (bankInfo != null) {
            financeData.put("balance", bankInfo.getBigDecimal("balance"));
        } else {
                financeData.put("balance", 0);
        }

        return financeData;
    }

    /**
     * 获取报修数据统计
     */
    private Map<String, Object> getRepairData(String communityId) {
        Map<String, Object> repairData = new HashMap<>();

        // 任务总数
        Long totalCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_bx WHERE community_id = ?", communityId);
        repairData.put("totalCount", totalCount != null ? totalCount : 0);

        // 待审核（状态0）
        Long pendingCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_bx WHERE community_id = ? AND status = 0", communityId);
        repairData.put("pendingCount", pendingCount != null ? pendingCount : 0);

        // 未开始（状态0）
        repairData.put("notStartedCount", pendingCount != null ? pendingCount : 0);

        // 进行中（状态1）
        Long inProgressCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_bx WHERE community_id = ? AND status = 1", communityId);
        repairData.put("inProgressCount", inProgressCount != null ? inProgressCount : 0);

        // 待验收（状态2）
        Long waitingAcceptanceCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_bx WHERE community_id = ? AND status = 2", communityId);
        repairData.put("waitingAcceptanceCount", waitingAcceptanceCount != null ? waitingAcceptanceCount : 0);

        // 已完成（状态3）
        Long completedCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_bx WHERE community_id = ? AND status = 3", communityId);
        repairData.put("completedCount", completedCount != null ? completedCount : 0);

        return repairData;
    }

    /**
     * 获取投诉建议数据统计
     */
    private Map<String, Object> getComplaintData(String communityId) {
        Map<String, Object> complaintData = new HashMap<>();

        // 总数
        Long totalCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_complaint WHERE community_id = ?", communityId);
        complaintData.put("totalCount", totalCount != null ? totalCount : 0);

        // 待处理
        Long pendingCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_complaint WHERE community_id = ? AND status = 0", communityId);
        complaintData.put("pendingCount", pendingCount != null ? pendingCount : 0);

        // 处理中
        Long processingCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_complaint WHERE community_id = ? AND status = 1", communityId);
        complaintData.put("processingCount", processingCount != null ? processingCount : 0);

        // 已完成
        Long completedCount = Db.queryLong("SELECT COUNT(*) FROM eh_wx_complaint WHERE community_id = ? AND status = 2", communityId);
        complaintData.put("completedCount", completedCount != null ? completedCount : 0);

        return complaintData;
    }

    /**
     * 获取资产信息统计
     */
    private Map<String, Object> getAssetData(String communityId) {
        Map<String, Object> assetData = new HashMap<>();

        // 房屋数
        Long houseCount = Db.queryLong("SELECT COUNT(*) FROM eh_house_info WHERE community_id = ?", communityId);
        assetData.put("houseCount", houseCount != null ? houseCount : 0);

        // 房屋建筑面积
        Record areaRecord = Db.findFirst("SELECT COALESCE(SUM(total_area), 0) as totalArea FROM eh_house_info WHERE community_id = ?", communityId);
        assetData.put("totalArea", areaRecord != null ? areaRecord.getBigDecimal("totalArea") : 0);

        // 户数（业主数）
        Long ownerCount = Db.queryLong("SELECT COUNT(*) FROM eh_owner WHERE community_id = ?", communityId);
        assetData.put("ownerCount", ownerCount != null ? ownerCount : 0);

        // 车位数
        Long parkingCount = Db.queryLong("SELECT COUNT(*) FROM eh_parking_space WHERE community_id = ?", communityId);
        assetData.put("parkingCount", parkingCount != null ? parkingCount : 0);

        // 车辆数
        Long vehicleCount = Db.queryLong("SELECT COUNT(*) FROM eh_vehicle WHERE community_id = ?", communityId);
        assetData.put("vehicleCount", vehicleCount != null ? vehicleCount : 0);

        return assetData;
    }

    /**
     * 获取小程序使用记录统计
     */
    private Map<String, Object> getMiniProgramData(String communityId) {
        Map<String, Object> miniProgramData = new HashMap<>();

        // 今日活跃用户数（基于登录记录）
        Long todayActiveUsers = Db.queryLong("SELECT COUNT(DISTINCT owner_id) FROM eh_wx_user WHERE community_id = ? AND DATE(login_date) = CURDATE()", communityId);
        miniProgramData.put("todayActiveUsers", todayActiveUsers != null ? todayActiveUsers : 0);

        // 本周活跃用户数
        Long weekActiveUsers = Db.queryLong("SELECT COUNT(DISTINCT owner_id) FROM eh_wx_user WHERE community_id = ? AND YEARWEEK(login_date, 1) = YEARWEEK(CURDATE(), 1)", communityId);
        miniProgramData.put("weekActiveUsers", weekActiveUsers != null ? weekActiveUsers : 0);

        // 本月活跃用户数
        Long monthActiveUsers = Db.queryLong("SELECT COUNT(DISTINCT owner_id) FROM eh_wx_user WHERE community_id = ? AND DATE_FORMAT(login_date, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')", communityId);
        miniProgramData.put("monthActiveUsers", monthActiveUsers != null ? monthActiveUsers : 0);

        // 总注册用户数
        Long totalUsers = Db.queryLong("SELECT COUNT(*) FROM eh_wx_user WHERE community_id = ?", communityId);
        miniProgramData.put("totalUsers", totalUsers != null ? totalUsers : 0);

        // 最近7天每日活跃用户数（用于趋势图）
        List<Record> dailyActiveUsers = Db.find("SELECT DATE(login_date) as date, COUNT(DISTINCT owner_id) as count FROM eh_wx_user WHERE community_id = ? AND login_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY DATE(login_date) ORDER BY date", communityId);
        miniProgramData.put("dailyTrend", getDataList(dailyActiveUsers).getRows());

        return miniProgramData;
    }
}
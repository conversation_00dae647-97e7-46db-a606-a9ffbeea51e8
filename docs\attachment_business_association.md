# 附件与业务数据关联实现方案

## 方案概述

通过在 `eh_file_info` 表中添加 `business_type` 和 `business_id` 字段，实现文件与业务数据的直接关联，同时保持现有的 `media_urls` 字段作为兼容性保障。

## 数据库变更

### 1. 表结构修改

在 `eh_file_info` 表中添加以下字段：

```sql
-- 添加业务类型字段
ALTER TABLE `eh_file_info` 
ADD COLUMN `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(bx-报修,complaint-投诉建议)' AFTER `upload_user`;

-- 添加业务ID字段  
ALTER TABLE `eh_file_info` 
ADD COLUMN `business_id` varchar(32) DEFAULT NULL COMMENT '业务ID' AFTER `business_type`;

-- 添加业务关联索引
ALTER TABLE `eh_file_info` 
ADD INDEX `idx_business`(`business_type`, `business_id`) USING BTREE;
```

### 2. 字段说明

- `business_type`: 业务类型，如 'bx'(报修)、'complaint'(投诉建议)
- `business_id`: 业务记录的ID，关联到具体的报修或投诉记录
- `idx_business`: 复合索引，提高查询性能

## 实现流程

### 1. 文件上传阶段

1. **前端生成临时业务ID**：页面初始化时生成唯一的临时业务ID
2. **上传文件时传递业务信息**：
   - `source`: 业务类型 (bx/complaint)
   - `businessId`: 临时业务ID
3. **后端保存文件信息**：将业务类型和临时业务ID保存到 `eh_file_info` 表

### 2. 业务数据提交阶段

1. **提交业务数据**：创建报修或投诉记录，生成正式的业务ID
2. **更新文件关联**：采用双重机制确保文件关联：
   - 方式1：通过临时业务ID批量更新
   - 方式2：解析 `media_urls` 中的 `fileId` 逐个更新关联

### 3. 查询阶段

可以通过以下方式查询附件：
- 传统方式：通过 `media_urls` 字段的JSON数据
- 新方式：通过 `business_type` 和 `business_id` 直接查询

## 代码变更

### 1. 前端变更

#### feedbackManager.js
- 添加 `generateBusinessId()` 方法生成临时业务ID
- 修改 `uploadMedia()` 方法支持传递业务ID
- 修改 `handleFileUpload()` 方法传递业务ID参数

#### 页面文件 (bx.js, complaint.js)
- 在页面初始化时生成业务ID
- 上传文件时传递业务ID
- 提交表单时传递业务ID

### 2. 后端变更

#### BxController.java / ComplaintController.java
- 修改 `upload()` 方法：
  - 接收 `businessId` 参数
  - 保存业务类型和业务ID到文件记录
  - 返回文件ID
- 修改 `addData()` 方法：
  - 接收业务ID参数
  - 提交成功后更新文件的业务ID关联
- 添加 `getAttachments()` 方法：
  - 根据业务ID查询相关附件

## 优势

1. **双重保障**：保持现有 `media_urls` 字段，同时建立正式的数据库关联
2. **向后兼容**：不影响现有功能，历史数据仍可正常访问
3. **便于管理**：可以通过数据库直接查询和管理文件关联
4. **性能优化**：通过索引提高查询性能
5. **扩展性强**：可以轻松扩展到其他业务模块
6. **双重关联机制**：既支持临时业务ID批量关联，也支持通过fileId精确关联
7. **容错性强**：即使一种关联方式失败，另一种方式仍可确保数据完整性

## 使用示例

### 查询业务相关附件

```javascript
// 前端调用
const result = await app.request({
  url: '/api/wx/bx/getAttachments',
  method: 'POST',
  data: { businessId: 'your-business-id' }
})
```

```sql
-- 后端SQL查询
SELECT file_id, original_name, file_url, file_size, file_type, create_time 
FROM eh_file_info 
WHERE business_type = 'bx' AND business_id = ? AND status = '0' 
ORDER BY create_time ASC
```

## 注意事项

1. **数据库迁移**：在生产环境执行表结构变更前请做好备份
2. **临时业务ID**：确保临时业务ID的唯一性，避免冲突
3. **错误处理**：文件关联更新失败不应影响业务数据的正常保存
4. **清理机制**：可以考虑定期清理没有关联到正式业务ID的临时文件

## 后续扩展

1. **其他业务模块**：可以将此方案扩展到公告、菜单等其他需要附件的业务模块
2. **文件管理界面**：可以开发专门的文件管理界面，按业务类型和ID管理文件
3. **文件统计**：可以统计各业务模块的文件使用情况
4. **文件清理**：可以实现自动清理无关联的临时文件

// 权限控制演示页面
import permissionManager from '../../utils/permissionManager.js'
import getStateManager from '../../utils/stateManager.js'

Page({
  data: {
    userStatus: {},
    availablePermissions: [],
    testResults: []
  },

  onLoad() {
    this.updateUserStatus()
    this.updateAvailablePermissions()
  },

  onShow() {
    this.updateUserStatus()
    this.updateAvailablePermissions()
  },

  // 更新用户状态
  updateUserStatus() {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    
    this.setData({
      userStatus: {
        isLogin: state.isLogin,
        hasBindPhone: state.hasBindPhone,
        isHouseAuth: state.isHouseAuth,
        authStatus: state.authStatus,
        username: state.userInfo?.nickName || '未登录',
        mobile: state.userInfo?.mobile || '未绑定'
      }
    })
  },

  // 更新可用权限
  updateAvailablePermissions() {
    const available = permissionManager.getAvailablePermissions()
    this.setData({
      availablePermissions: available
    })
  },

  // 测试基础功能
  testBasicFunction() {
    permissionManager.executeWithPermission('view_notice', () => {
      this.addTestResult('查看公告', true, '基础功能，所有用户可用')
    })
  },

  // 测试需要手机号的功能
  testPhoneRequiredFunction() {
    permissionManager.executeWithPermission('submit_complaint', () => {
      this.addTestResult('提交投诉', true, '需要手机号，权限检查通过')
    }, {
      customMessage: '提交投诉需要先绑定手机号'
    })
  },

  // 测试需要房屋认证的功能
  testHouseRequiredFunction() {
    permissionManager.executeWithPermission('view_bills', () => {
      this.addTestResult('查看账单', true, '需要房屋认证，权限检查通过')
    }, {
      customMessage: '查看账单需要完成房屋认证'
    })
  },

  // 测试缴费功能
  testPaymentFunction() {
    permissionManager.executeWithPermission('pay_bills', () => {
      wx.showToast({
        title: '跳转到缴费页面',
        icon: 'success'
      })
      this.addTestResult('缴费功能', true, '房屋认证用户可以使用缴费功能')
    })
  },

  // 测试维修申请
  testRepairFunction() {
    permissionManager.executeWithPermission('submit_repair', () => {
      wx.showToast({
        title: '跳转到维修申请页面',
        icon: 'success'
      })
      this.addTestResult('维修申请', true, '房屋认证用户可以提交维修申请')
    })
  },

  // 添加测试结果
  addTestResult(functionName, success, message) {
    const testResults = this.data.testResults
    testResults.unshift({
      id: Date.now(),
      functionName,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    })
    
    // 只保留最近10条记录
    if (testResults.length > 10) {
      testResults.pop()
    }
    
    this.setData({ testResults })
  },

  // 清除测试结果
  clearTestResults() {
    this.setData({ testResults: [] })
  },

  // 模拟登录
  simulateLogin() {
    wx.showModal({
      title: '模拟登录',
      content: '这是演示功能，实际应用中请使用真实的登录流程',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/login/index'
          })
        }
      }
    })
  },

  // 模拟绑定手机号
  simulateBindPhone() {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    
    if (!state.isLogin) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '模拟绑定手机号',
      content: '这是演示功能，实际应用中需要真实的手机号授权',
      success: (res) => {
        if (res.confirm) {
          // 模拟更新状态
          stateManager.setState({
            hasBindPhone: true,
            userInfo: {
              ...state.userInfo,
              mobile: '138****8888'
            }
          })
          this.updateUserStatus()
          this.updateAvailablePermissions()
          wx.showToast({
            title: '手机号绑定成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 模拟房屋认证
  simulateHouseAuth() {
    const stateManager = getStateManager()
    const state = stateManager.getState()
    
    if (!state.hasBindPhone) {
      wx.showToast({
        title: '请先绑定手机号',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '模拟房屋认证',
      content: '这是演示功能，实际应用中需要管理员审核',
      success: (res) => {
        if (res.confirm) {
          // 模拟更新状态
          stateManager.setState({
            isHouseAuth: true,
            authStatus: 'verified',
            ownerInfo: {
              ownerId: 'demo_owner_001',
              ownerName: '演示用户',
              mobile: state.userInfo.mobile,
              communityId: 'demo_community_001',
              houseCount: 1,
              houseId: 'demo_house_001'
            },
            houseInfo: {
              houseId: 'demo_house_001',
              houseName: '1号楼101室',
              area: '120平方米'
            }
          })
          this.updateUserStatus()
          this.updateAvailablePermissions()
          wx.showToast({
            title: '房屋认证成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 重置状态
  resetStatus() {
    wx.showModal({
      title: '重置状态',
      content: '确定要重置所有认证状态吗？',
      success: (res) => {
        if (res.confirm) {
          const stateManager = getStateManager()
          stateManager.clearState()
          this.updateUserStatus()
          this.updateAvailablePermissions()
          this.clearTestResults()
          wx.showToast({
            title: '状态已重置',
            icon: 'success'
          })
        }
      }
    })
  }
})

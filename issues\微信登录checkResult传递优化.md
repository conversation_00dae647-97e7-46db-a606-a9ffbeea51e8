# 微信登录checkResult传递优化

## 问题描述

在新用户注册流程中，存在重复调用微信接口的问题：

1. 第一次调用`smartLogin`时，后端调用`checkUserWithWxInfo`获取微信用户信息
2. 如果是新用户，返回`needPhoneAuth: true`，但没有返回`checkResult`
3. 前端获取手机号授权后，再次调用`smartLogin`
4. 后端又会调用`checkUserWithWxInfo`，但微信code已经被使用过了，导致失败

## 解决方案

将第一次获取的`checkResult`传递给前端，避免重复调用微信接口。

## 修改内容

### 后端修改 (WechatAuthController.java)

1. **新增参数支持**：
   - 添加`openid`、`unionid`、`sessionKey`、`userExists`参数接收
   - 支持前端传递的checkResult数据

2. **优化调用逻辑**：
   - 如果前端传递了checkResult，直接使用，跳过`checkUserWithWxInfo`调用
   - 如果没有传递，按原流程调用微信接口

3. **返回checkResult**：
   - 当需要手机号授权时，将完整的checkResult返回给前端

### 前端修改 (login/index.js)

1. **保存checkResult**：
   - 在`pendingLoginData`中保存`openid`、`unionid`、`sessionKey`、`userExists`

2. **传递checkResult**：
   - 在第二次调用`performSmartLogin`时，传递checkResult参数
   - 后端接收到这些参数后，避免重复调用微信接口

## 预期效果

- 新用户注册流程中，微信code只使用一次
- 第一次获取的微信用户信息在第二次请求中复用
- 避免"code已使用"的错误
- 保持原有的登录流程不变

## 测试要点

1. 新用户首次登录流程
2. 手机号授权后的注册流程
3. 老用户登录流程不受影响
4. 错误处理和日志记录

## 技术细节

- 微信登录code具有一次性使用限制
- checkResult包含：userExists、openid、unionid、sessionKey
- 前端通过pendingLoginData临时存储这些信息
- 后端通过参数判断是否需要调用微信接口

## 附加优化

### 超时时间调整

同时调整了登录相关的超时时间配置：

**文件：** `miniprogram/constants/index.js`
- DEFAULT: 10秒 → 15秒
- LOGIN: 15秒 → 30秒
- UPLOAD: 30秒 → 60秒
- QUICK_CHECK: 5秒 → 8秒

**文件：** `miniprogram/pages/login/index.js`
- 登录状态检查超时：使用TIMEOUT_CONFIG.LOGIN (30秒)

**文件：** `miniprogram/pages/index/index.js`
- 认证状态检查超时：10秒 → 15秒

这些调整提升了网络环境较差时的用户体验。

## 代码优化

### executeLogin方法逻辑简化

**问题：** executeLogin方法中存在冗余的手机号判断逻辑

**分析：**
- 进入executeLogin的前提是新用户已完成手机号解密，或老用户已存在手机号
- 因此在executeLogin中，手机号肯定不为空，hasBindPhone肯定为true

**优化内容：**
1. 移除冗余的`hasBindPhone`变量和相关判断
2. 简化手机号存在性检查逻辑
3. 直接设置`hasBindPhone: true`和`needPhoneAuth: false`
4. 优化日志输出，移除冗余的手机号绑定状态

**优化效果：**
- 代码更简洁，逻辑更清晰
- 减少不必要的判断，提升性能
- 降低代码维护复杂度

### hasBindPhone和needPhoneAuth字段的设计考虑

**保留原因：**
1. **兼容性**：老用户绑定手机号、修改手机号等场景仍需要这些字段
2. **状态管理**：前端状态管理器需要这些字段来控制UI显示
3. **API一致性**：保持不同登录接口返回数据结构的一致性

**在executeLogin中的处理：**
- `hasBindPhone: true` - 因为进入executeLogin时手机号肯定已绑定
- `needPhoneAuth: false` - 因为executeLogin中不需要手机号授权
- 前端登录结果处理简化，跳过手机号授权检查

## 新用户注册流程完善

### 用户信息授权流程修复

**问题：** 新用户注册时跳过了用户信息授权，导致encryptedData和iv没有传递到后台

**修复内容：**

1. **前端修改 (login/index.js)**：
   - 修改`startNewUserRegistration`：先获取用户信息授权，再获取手机号
   - 完善`getUserInfoForRegistration`：主动获取用户信息，支持用户拒绝时的处理
   - 在`pendingLoginData`中保存`encryptedData`和`iv`
   - 在调用`performSmartLogin`时传递完整的用户信息

2. **后端处理 (WxUserServiceImpl.java)**：
   - `wxLogin`方法已正确处理`encryptedData`和`iv`解密
   - 解密成功时设置用户昵称、头像和性别
   - 解密失败时使用默认值"微信用户"

**优化后的新用户注册流程：**
1. 用户点击登录 → 第一次调用smartLogin → 返回needPhoneAuth
2. 显示注册提示 → 用户确认 → 直接显示手机号授权
3. 获取手机号成功 → 第二次调用smartLogin → 完成基本注册
4. 注册成功后 → 可选的用户信息授权（头像、昵称）
5. 用户可以选择授权完善资料，或跳过直接使用默认信息

**流程优化理由：**
- **手机号是必须的**：用于身份验证和服务通知
- **用户信息是可选的**：头像和昵称可以后续完善
- **用户体验更好**：不强制要求用户信息授权，减少注册阻力
- **符合微信规范**：避免过度获取用户信息

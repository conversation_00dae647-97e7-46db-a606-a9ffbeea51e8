package com.ehome.framework.manager.factory;

import java.util.TimerTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ehome.common.constant.Constants;
import com.ehome.common.utils.AddressUtils;
import com.ehome.common.utils.LogUtils;
import com.ehome.common.utils.ServletUtils;
import com.ehome.common.utils.ShiroUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.spring.SpringUtils;
import com.ehome.framework.shiro.session.OnlineSession;
import com.ehome.system.domain.SysLogininfor;
import com.ehome.system.domain.SysOperLog;
import com.ehome.system.domain.SysUserOnline;
import com.ehome.system.service.ISysOperLogService;
import com.ehome.system.service.ISysUserOnlineService;
import com.ehome.system.service.impl.SysLogininforServiceImpl;
import eu.bitwalker.useragentutils.UserAgent;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import java.util.Date;
import java.text.SimpleDateFormat;

/**
 * 异步工厂（产生任务用）
 * 
 * <AUTHOR>
 *
 */
public class AsyncFactory
{
    private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

    /**
     * 同步session到数据库
     * 
     * @param session 在线用户会话
     * @return 任务task
     */
    public static TimerTask syncSessionToDb(final OnlineSession session)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                SysUserOnline online = new SysUserOnline();
                online.setSessionId(String.valueOf(session.getId()));
                online.setDeptName(session.getDeptName());
                online.setLoginName(session.getLoginName());
                online.setStartTimestamp(session.getStartTimestamp());
                online.setLastAccessTime(session.getLastAccessTime());
                online.setExpireTime(session.getTimeout());
                online.setIpaddr(session.getHost());
                online.setLoginLocation(AddressUtils.getRealAddressByIP(session.getHost()));
                online.setBrowser(session.getBrowser());
                online.setOs(session.getOs());
                online.setStatus(session.getStatus());
                SpringUtils.getBean(ISysUserOnlineService.class).saveOnline(online);

            }
        };
    }

    /**
     * 操作日志记录
     * 
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final SysOperLog operLog)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                // 远程查询操作地点
                operLog.setOperLocation(AddressUtils.getRealAddressByIP(operLog.getOperIp()));
                SpringUtils.getBean(ISysOperLogService.class).insertOperlog(operLog);
            }
        };
    }

    /**
     * 记录登录信息
     * 
     * @param username 用户名
     * @param status 状态
     * @param message 消息
     * @param args 列表
     * @return 任务task
     */
    public static TimerTask recordLogininfor(final String username, final String status, final String message, final Object... args)
    {
        final UserAgent userAgent = UserAgent.parseUserAgentString(ServletUtils.getRequest().getHeader("User-Agent"));
        final String ip = ShiroUtils.getIp();
        return new TimerTask()
        {
            @Override
            public void run()
            {
                String address = AddressUtils.getRealAddressByIP(ip);
                StringBuilder s = new StringBuilder();
                s.append(LogUtils.getBlock(ip));
                s.append(address);
                s.append(LogUtils.getBlock(username));
                s.append(LogUtils.getBlock(status));
                s.append(LogUtils.getBlock(message));
                // 打印信息到日志
                sys_user_logger.info(s.toString(), args);
                // 获取客户端操作系统
                String os = userAgent.getOperatingSystem().getName();
                // 获取客户端浏览器
                String browser = userAgent.getBrowser().getName();
                // 封装对象
                SysLogininfor logininfor = new SysLogininfor();
                logininfor.setLoginName(username);
                logininfor.setIpaddr(ip);
                logininfor.setLoginLocation(address);
                logininfor.setBrowser(browser);
                logininfor.setOs(os);
                logininfor.setMsg(message);
                // 日志状态
                if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
                {
                    logininfor.setStatus(Constants.SUCCESS);
                }
                else if (Constants.LOGIN_FAIL.equals(status))
                {
                    logininfor.setStatus(Constants.FAIL);
                }
                // 插入数据
                SpringUtils.getBean(SysLogininforServiceImpl.class).insertLogininfor(logininfor);
            }
        };
    }

    /**
     * 记录菜单点击日志
     *
     * @param menuId 菜单ID
     * @param menuName 菜单名称
     * @param menuType 菜单类型
     * @param userId 用户ID
     * @param userName 用户名称
     * @param ipAddress IP地址
     * @param ownerId 业主ID
     * @param houseId 房屋ID
     * @param houseName 房屋名称
     * @param communityId 小区ID
     * @param source 来源页面
     * @return 任务task
     */
    public static TimerTask recordMenuClick(final String menuId, final String menuName, final String menuType,
                                          final String userId, final String userName, final String ipAddress,
                                          final String ownerId, final String houseId, final String houseName,
                                          final String communityId, final String source)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                try {
                    Date now = new Date();
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String clickDate = dateFormat.format(now);

                    // 使用INSERT ... ON DUPLICATE KEY UPDATE实现每天记录第一次和最后一次
                    String sql = "INSERT INTO sys_menu_click_log " +
                               "(menu_id, menu_name, menu_type, user_id, user_name, first_click_time, last_click_time, " +
                               "click_date, click_count, ip_address, owner_id, house_id, house_name, community_id, source) " +
                               "VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?) " +
                               "ON DUPLICATE KEY UPDATE " +
                               "last_click_time = VALUES(last_click_time), " +
                               "click_count = click_count + 1, " +
                               "ip_address = VALUES(ip_address)";

                    Db.update(sql, menuId, menuName, menuType, userId, userName, now, now, clickDate,
                             ipAddress, ownerId, houseId, houseName, communityId, source);

                } catch (Exception e) {
                    // 记录异常但不影响主流程
                    sys_user_logger.error("记录菜单点击日志失败: {}", e.getMessage());
                }
            }
        };
    }
}

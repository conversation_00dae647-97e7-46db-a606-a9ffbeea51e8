package com.ehome.admin.controller.file;

import com.alibaba.fastjson.JSONObject;
import com.ehome.admin.service.IEhAttachmentRelationService;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附件关联控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/attachment")
public class EhAttachmentRelationController extends BaseController {

    @Autowired
    private IEhAttachmentRelationService attachmentRelationService;
    
    /**
     * 获取业务附件列表
     */
    @PostMapping("/list")
    @ResponseBody
    public AjaxResult getAttachmentList() {
        JSONObject params = getParams();
        String businessType = params.getString("businessType");
        String businessId = params.getString("businessId");
        
        if (StringUtils.isEmpty(businessType) || StringUtils.isEmpty(businessId)) {
            return AjaxResult.error("参数不能为空");
        }
        
        List<Record> attachments = attachmentRelationService.getAttachmentDetails(businessType, businessId);
        return AjaxResult.success(attachments);
    }
    
    /**
     * 保存业务附件关联
     */
    @PostMapping("/save")
    @ResponseBody
    @Log(title = "附件关联", businessType = BusinessType.INSERT)
    public AjaxResult saveAttachments() {
        JSONObject params = getParams();
        String businessType = params.getString("businessType");
        String businessId = params.getString("businessId");
        String fileIdsStr = params.getString("fileIds");
        
        if (StringUtils.isEmpty(businessType) || StringUtils.isEmpty(businessId)) {
            return AjaxResult.error("参数不能为空");
        }
        
        String[] fileIds = null;
        if (StringUtils.isNotEmpty(fileIdsStr)) {
            fileIds = fileIdsStr.split(",");
        }
        
        boolean success = attachmentRelationService.saveBusinessAttachments(
            businessType,
            businessId,
            fileIds,
            getLoginName(),
            getSysUser().getCommunityId()
        );
        
        return success ? AjaxResult.success() : AjaxResult.error("保存失败");
    }
    
    /**
     * 删除附件关联
     */
    @PostMapping("/delete")
    @ResponseBody
    @Log(title = "附件关联", businessType = BusinessType.DELETE)
    public AjaxResult deleteAttachment() {
        JSONObject params = getParams();
        String relationId = params.getString("relationId");
        
        if (StringUtils.isEmpty(relationId)) {
            return AjaxResult.error("关联ID不能为空");
        }
        
        boolean success = attachmentRelationService.deleteAttachmentRelation(relationId);
        return success ? AjaxResult.success() : AjaxResult.error("删除失败");
    }
    
    /**
     * 更新附件排序
     */
    @PostMapping("/updateSort")
    @ResponseBody
    @Log(title = "附件关联", businessType = BusinessType.UPDATE)
    public AjaxResult updateSort() {
        JSONObject params = getParams();
        String relationId = params.getString("relationId");
        Integer sortOrder = params.getInteger("sortOrder");

        if (StringUtils.isEmpty(relationId) || sortOrder == null) {
            return AjaxResult.error("参数不能为空");
        }

        boolean success = attachmentRelationService.updateAttachmentSort(relationId, sortOrder);
        return success ? AjaxResult.success() : AjaxResult.error("更新失败");
    }

    /**
     * 测试获取附件详情（用于调试）
     */
    @GetMapping("/test/{businessType}/{businessId}")
    @ResponseBody
    public AjaxResult testGetAttachments(@PathVariable String businessType, @PathVariable String businessId) {
        List<Record> attachments = attachmentRelationService.getAttachmentDetails(businessType, businessId);
        return AjaxResult.success("查询成功", attachments);
    }
}

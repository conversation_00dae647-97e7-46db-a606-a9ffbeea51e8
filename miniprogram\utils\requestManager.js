// 网络请求优化管理器
import debounceManager from './debounceManager.js'

class RequestManager {
  constructor() {
    this.cache = new Map() // 请求缓存
    this.pendingRequests = new Map() // 进行中的请求
    this.batchQueue = new Map() // 批量请求队列
  }

  /**
   * 生成请求缓存键
   * @param {string} url 请求URL
   * @param {string} method 请求方法
   * @param {Object} data 请求数据
   * @returns {string} 缓存键
   */
  generateCacheKey(url, method, data) {
    const dataStr = data ? JSON.stringify(data) : ''
    return `${method}:${url}:${dataStr}`
  }

  /**
   * 带缓存的请求
   * @param {Object} options 请求选项
   * @param {number} cacheTime 缓存时间（毫秒），0表示不缓存
   * @returns {Promise} 请求Promise
   */
  async requestWithCache(options, cacheTime = 0) {
    const { url, method = 'GET', data } = options
    const cacheKey = this.generateCacheKey(url, method, data)

    // 检查缓存
    if (cacheTime > 0 && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)
      if (Date.now() - cached.timestamp < cacheTime) {
        console.log(`[RequestManager] 使用缓存: ${cacheKey}`)
        return Promise.resolve(cached.data)
      } else {
        // 缓存过期，删除
        this.cache.delete(cacheKey)
      }
    }

    // 检查是否有相同的请求正在进行
    if (this.pendingRequests.has(cacheKey)) {
      console.log(`[RequestManager] 复用进行中的请求: ${cacheKey}`)
      return this.pendingRequests.get(cacheKey)
    }

    // 发起新请求
    const app = getApp()
    const requestPromise = app.request(options).then(result => {
      // 缓存结果
      if (cacheTime > 0) {
        this.cache.set(cacheKey, {
          data: result,
          timestamp: Date.now()
        })
      }
      
      // 清除进行中的请求记录
      this.pendingRequests.delete(cacheKey)
      
      return result
    }).catch(error => {
      // 清除进行中的请求记录
      this.pendingRequests.delete(cacheKey)
      throw error
    })

    // 记录进行中的请求
    this.pendingRequests.set(cacheKey, requestPromise)
    
    return requestPromise
  }

  /**
   * 批量请求
   * @param {string} batchKey 批量键名
   * @param {Object} requestOptions 请求选项
   * @param {number} delay 延迟时间（毫秒）
   * @returns {Promise} 请求Promise
   */
  batchRequest(batchKey, requestOptions, delay = 50) {
    return new Promise((resolve, reject) => {
      // 添加到批量队列
      if (!this.batchQueue.has(batchKey)) {
        this.batchQueue.set(batchKey, [])
      }
      
      this.batchQueue.get(batchKey).push({
        options: requestOptions,
        resolve,
        reject
      })

      // 防抖执行批量请求
      debounceManager.debounce(`batch-${batchKey}`, () => {
        this.executeBatchRequests(batchKey)
      }, delay)
    })
  }

  /**
   * 执行批量请求
   * @param {string} batchKey 批量键名
   */
  async executeBatchRequests(batchKey) {
    const requests = this.batchQueue.get(batchKey)
    if (!requests || requests.length === 0) return

    console.log(`[RequestManager] 执行批量请求: ${batchKey}, 数量: ${requests.length}`)

    // 清空队列
    this.batchQueue.delete(batchKey)

    // 检查是否可以合并请求
    if (this.canMergeRequests(requests)) {
      try {
        const mergedResult = await this.executeMergedRequest(requests)
        // 分发结果给各个请求
        this.distributeMergedResults(requests, mergedResult)
      } catch (error) {
        // 批量请求失败，逐个执行
        console.warn(`[RequestManager] 批量请求失败，回退到逐个执行: ${error.message}`)
        this.executeIndividualRequests(requests)
      }
    } else {
      // 不能合并，并发执行
      this.executeIndividualRequests(requests)
    }
  }

  /**
   * 检查是否可以合并请求
   * @param {Array} requests 请求数组
   * @returns {boolean} 是否可以合并
   */
  canMergeRequests(requests) {
    if (requests.length <= 1) return false

    // 检查是否都是相同的URL和方法
    const firstRequest = requests[0].options
    return requests.every(req => 
      req.options.url === firstRequest.url && 
      req.options.method === firstRequest.method
    )
  }

  /**
   * 执行合并后的请求
   * @param {Array} requests 请求数组
   * @returns {Promise} 合并请求结果
   */
  async executeMergedRequest(requests) {
    // 这里可以根据具体业务逻辑实现请求合并
    // 例如：将多个查询ID合并为一个批量查询
    const firstRequest = requests[0].options
    
    // 示例：合并查询参数
    const mergedData = this.mergeRequestData(requests.map(req => req.options.data))
    
    const app = getApp()
    return app.request({
      ...firstRequest,
      data: mergedData
    })
  }

  /**
   * 合并请求数据
   * @param {Array} dataArray 数据数组
   * @returns {Object} 合并后的数据
   */
  mergeRequestData(dataArray) {
    // 简单的合并逻辑，可以根据业务需求定制
    const merged = {}
    const ids = []
    
    dataArray.forEach(data => {
      if (data) {
        Object.assign(merged, data)
        if (data.id) ids.push(data.id)
        if (data.ids) ids.push(...data.ids)
      }
    })
    
    if (ids.length > 0) {
      merged.ids = [...new Set(ids)] // 去重
    }
    
    return merged
  }

  /**
   * 分发合并请求的结果
   * @param {Array} requests 原始请求数组
   * @param {Object} mergedResult 合并请求结果
   */
  distributeMergedResults(requests, mergedResult) {
    // 根据业务逻辑分发结果
    requests.forEach((request, index) => {
      try {
        // 这里需要根据具体业务逻辑来分发结果
        // 示例：如果是批量查询，需要从结果中提取对应的数据
        const individualResult = this.extractIndividualResult(
          mergedResult, 
          request.options.data, 
          index
        )
        request.resolve(individualResult)
      } catch (error) {
        request.reject(error)
      }
    })
  }

  /**
   * 从合并结果中提取单个结果
   * @param {Object} mergedResult 合并结果
   * @param {Object} originalData 原始请求数据
   * @param {number} index 请求索引
   * @returns {Object} 单个结果
   */
  extractIndividualResult(mergedResult, originalData, index) {
    // 简单实现：如果是数组结果，按索引返回
    if (Array.isArray(mergedResult.data)) {
      return {
        ...mergedResult,
        data: mergedResult.data[index] || mergedResult.data[0]
      }
    }
    
    // 否则返回完整结果
    return mergedResult
  }

  /**
   * 逐个执行请求
   * @param {Array} requests 请求数组
   */
  async executeIndividualRequests(requests) {
    const app = getApp()
    
    // 并发执行所有请求
    const promises = requests.map(async (request) => {
      try {
        const result = await app.request(request.options)
        request.resolve(result)
      } catch (error) {
        request.reject(error)
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * 清除缓存
   * @param {string} pattern 缓存键模式（可选）
   */
  clearCache(pattern) {
    if (pattern) {
      // 清除匹配模式的缓存
      for (const [key] of this.cache) {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear()
    }
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache() {
    const now = Date.now()
    for (const [key, value] of this.cache) {
      // 假设默认缓存时间为5分钟
      if (now - value.timestamp > 5 * 60 * 1000) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 销毁管理器，清理所有资源
   */
  destroy() {
    this.clearCache()
    this.pendingRequests.clear()
    this.batchQueue.clear()

    // 清理定时器
    if (this._cleanupTimer) {
      clearInterval(this._cleanupTimer)
      this._cleanupTimer = null
    }
  }
}

// 创建单例
const requestManager = new RequestManager()

// 定期清理过期缓存，并保存定时器引用以便清理
requestManager._cleanupTimer = setInterval(() => {
  requestManager.clearExpiredCache()
}, 60000) // 每分钟清理一次

// 监听小程序生命周期，在应用销毁时清理资源
if (typeof getApp === 'function') {
  const app = getApp()
  if (app) {
    const originalOnHide = app.onHide || function() {}
    app.onHide = function() {
      // 应用进入后台时清理过期缓存
      requestManager.clearExpiredCache()
      originalOnHide.call(this)
    }
  }
}

export default requestManager

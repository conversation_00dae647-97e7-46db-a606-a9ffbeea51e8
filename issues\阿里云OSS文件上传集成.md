# 阿里云OSS文件上传集成

## 项目背景

为了提升文件存储的可靠性和访问速度，将现有的本地文件存储系统集成阿里云OSS，实现双重存储策略。

## 集成方案

### 存储策略
- **双重存储模式**：本地存储 + 阿里云OSS同步存储
- **访问优先级**：优先使用OSS URL，本地URL作为降级方案
- **兼容性保证**：保持现有API接口不变

## 实施内容

### 1. 依赖管理
- **文件**：`ehome-common/pom.xml`
- **添加**：阿里云OSS Java SDK 3.17.4
- **状态**：✅ 已完成

### 2. 配置管理
- **配置类**：`ehome-common/src/main/java/com/ehome/common/config/OssConfig.java`
- **配置文件**：`ehome-web/src/main/resources/application.yml`
- **配置项**：
  ```yaml
  aliyun:
    oss:
      enabled: true
      endpoint: oss-cn-guangzhou.aliyuncs.com
      access-key-id: LTAI5t5gNqAPbEjavmCvfaqo
      access-key-secret: ******************************
      bucket-name: ehome-storage
      domain: ehome-storage.oss-cn-guangzhou.aliyuncs.com
      path-prefix: uploads
  ```
- **状态**：✅ 已完成

### 3. 核心服务类
- **OSS工具类**：`ehome-common/src/main/java/com/ehome/common/utils/oss/OssUtils.java`
  - 文件上传到OSS
  - 文件删除
  - URL生成
  - 对象键名生成
- **OSS服务类**：`ehome-common/src/main/java/com/ehome/common/service/OssService.java`
  - 封装OSS操作
  - 提供统一接口
  - 异常处理和降级
- **状态**：✅ 已完成

### 4. 数据库表结构调整
- **表名**：`eh_file_info`
- **新增字段**：
  - `oss_url` VARCHAR(500) - OSS访问URL
  - `oss_key` VARCHAR(200) - OSS存储键名
  - `storage_type` VARCHAR(20) - 存储类型（local/oss/both）
- **状态**：✅ 已完成

### 5. 文件上传逻辑更新
- **文件**：`ehome-admin/src/main/java/com/ehome/admin/controller/common/CommonController.java`
- **更新方法**：
  - `uploadFile()` - 单文件上传
  - `uploadFiles()` - 多文件上传
- **新增逻辑**：
  1. 保持原有本地上传
  2. 同步上传到OSS
  3. 数据库记录双重URL
  4. 返回OSS URL作为主要访问地址
- **状态**：✅ 已完成

## 技术特性

### 1. 双重保障
- 本地存储作为备份
- OSS存储提供高可用性
- 任一存储失败不影响业务

### 2. 智能降级
- OSS上传失败时自动使用本地存储
- 访问时优先使用OSS，降级到本地
- 配置开关控制OSS功能启用

### 3. 性能优化
- 异步上传策略（可扩展）
- CDN加速访问
- 减少本地存储压力

### 4. 安全性
- 访问密钥通过配置文件管理
- 支持环境变量配置敏感信息
- 文件访问权限控制

## 配置说明

### 阿里云OSS信息
- **用户名**：<EMAIL>
- **Endpoint**：oss-cn-guangzhou.aliyuncs.com
- **Bucket**：ehome-storage
- **域名**：ehome-storage.oss-cn-guangzhou.aliyuncs.com
- **AccessKey ID**：LTAI5t5gNqAPbEjavmCvfaqo

### 功能开关
```yaml
aliyun:
  oss:
    enabled: true  # 设置为false可禁用OSS功能
```

## API响应变化

### 单文件上传响应
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "url": "https://ehome-storage.oss-cn-guangzhou.aliyuncs.com/uploads/2024/01/15/file_123456.jpg",
    "fileName": "/upload/2024/01/15/file_123456.jpg",
    "newFileName": "file_123456.jpg",
    "originalFilename": "original.jpg",
    "ossUrl": "https://ehome-storage.oss-cn-guangzhou.aliyuncs.com/uploads/2024/01/15/file_123456.jpg",
    "localUrl": "http://localhost:8080/upload/2024/01/15/file_123456.jpg",
    "storageType": "both"
  }
}
```

### 环境隔离

通过不同的路径前缀实现环境隔离：

- **开发环境**：`uploads/dev/2025/07/09/file.jpg`
- **测试环境**：`uploads/test/2025/07/09/file.jpg`
- **生产环境**：`uploads/prod/2025/07/09/file.jpg`

这样可以：
1. 避免不同环境的文件相互干扰
2. 便于环境数据管理和清理
3. 提供更好的数据隔离性

### 使用场景

#### 富文本编辑器图片上传
```javascript
// 上传到公共Bucket，永久访问
fetch('/common/upload', {
  method: 'POST',
  body: formData.append('bucketType', 'public')
})
```

#### 敏感文件上传
```javascript
// 上传到私有Bucket，预签名URL访问
fetch('/common/upload', {
  method: 'POST',
  body: formData.append('bucketType', 'private')
})
```

## 测试要点

### 1. 功能测试
- [ ] 单文件上传测试（私有/公共Bucket）
- [ ] 多文件上传测试（私有/公共Bucket）
- [ ] OSS功能开关测试
- [ ] 文件访问URL测试（直接访问/预签名URL）
- [ ] bucketType参数测试

### 2. 异常测试
- [ ] OSS服务不可用时的降级测试
- [ ] 网络异常时的处理测试
- [ ] 配置错误时的处理测试
- [ ] 公共Bucket配置缺失时的处理测试

### 3. 性能测试
- [ ] 大文件上传测试
- [ ] 并发上传测试
- [ ] 上传速度对比测试
- [ ] 公共/私有Bucket访问速度对比

## 部署注意事项

### 1. 环境配置
- 确保服务器能访问阿里云OSS服务
- 配置正确的访问密钥
- 检查Bucket权限设置

### 2. 监控建议
- 监控OSS上传成功率
- 监控文件访问响应时间
- 设置异常告警

### 3. 备份策略
- 定期检查本地和OSS文件一致性
- 制定数据迁移和恢复方案

## 问题修复与优化

### OSS文件访问权限问题
- **问题**：OSS Bucket设置为私有，直接访问URL返回 `AccessDenied` 错误
- **初步解决方案**：实现预签名URL生成机制
- **进一步优化**：双Bucket策略解决富文本图片长期访问问题
- **最终方案**：
  1. **私有Bucket** (`ehome-storage`)：存储敏感文件，使用预签名URL访问
  2. **公共读Bucket** (`ehome-public`)：存储公开内容（如富文本图片），直接URL访问
  3. 通过 `bucketType` 参数控制上传目标：`private`（默认）或 `public`
- **状态**：✅ 已完成

### 双Bucket配置

#### 基础配置 (application.yml)
```yaml
aliyun:
  oss:
    enabled: true
    endpoint: oss-cn-guangzhou.aliyuncs.com
    access-key-id: LTAI5t5gNqAPbEjavmCvfaqo
    access-key-secret: ******************************
    # 私有存储空间（敏感文件）
    bucket-name: ehome-storage
    domain: ehome-storage.oss-cn-guangzhou.aliyuncs.com
    # 公共读存储空间（公开内容）
    public-bucket-name: ehome-public
    public-domain: ehome-public.oss-cn-guangzhou.aliyuncs.com
    # 文件路径前缀（各环境会覆盖此配置）
    path-prefix: uploads
    # 预签名URL过期时间
    url-expiration: 604800
```

#### 环境隔离配置

**开发环境** (application-dev.yml):
```yaml
aliyun:
  oss:
    path-prefix: uploads/dev
```

**测试环境** (application-test.yml):
```yaml
aliyun:
  oss:
    path-prefix: uploads/test
```

**生产环境** (application-prod.yml):
```yaml
aliyun:
  oss:
    path-prefix: uploads/prod
```

### 新增API接口

#### 1. 文件上传（支持Bucket选择）
```http
POST /common/upload
Content-Type: multipart/form-data

file: [文件]
source: richtext
bucketType: public  # private（默认）或 public
```

#### 2. 根据OSS Key生成预签名URL
```http
POST /common/oss/generateUrl
Content-Type: application/x-www-form-urlencoded

ossKey=uploads/2025/07/09/share-cover_20250709121422A001.png
expiration=3600
```

**响应示例**：
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "url": "https://ehome-storage.oss-cn-guangzhou.aliyuncs.com/uploads/2025/07/09/share-cover_20250709121422A001.png?Expires=1752034819&OSSAccessKeyId=LTAI5t5gNqAPbEjavmCvfaqo&Signature=xxx",
    "ossKey": "uploads/2025/07/09/share-cover_20250709121422A001.png",
    "expiration": 3600
  }
}
```

## 后续优化

### 1. 异步上传
- 实现异步OSS上传，提升响应速度
- 添加上传队列和重试机制

### 2. 文件管理
- 添加文件清理和归档功能
- 实现文件使用统计和分析

### 3. CDN集成
- 配置阿里云CDN加速
- 实现智能域名切换

## 相关文件

- `ehome-common/src/main/java/com/ehome/common/config/OssConfig.java`
- `ehome-common/src/main/java/com/ehome/common/utils/oss/OssUtils.java`
- `ehome-common/src/main/java/com/ehome/common/service/OssService.java`
- `ehome-admin/src/main/java/com/ehome/admin/controller/common/CommonController.java`
- `ehome-web/src/main/resources/application.yml`

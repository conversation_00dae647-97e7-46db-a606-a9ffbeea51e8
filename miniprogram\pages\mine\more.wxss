/* pages/mine/more.wxss */
page {
  background: #f7f8fa;
  height: 100vh;
}

.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-top: 20rpx;
}

/* 分区标题 */
.section-title {
  font-size: 28rpx;
  color: #666;
  padding: 30rpx 30rpx 30rpx 30rpx;
  font-weight: 500;
}

/* 菜单列表 */
.menu-list {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.02);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #fafafa;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-item-left van-icon {
  margin-right: 20rpx;
  width: 48rpx;
  text-align: center;
}

.menu-item-left text {
  font-size: 28rpx;
  color: #333;
}

.menu-item-right {
  display: flex;
  align-items: center;
}

.menu-item-right .desc {
  font-size: 26rpx;
  color: #999;
  margin-right: 12rpx;
}

.menu-item-right .icon-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 产品建议按钮 */
.feedback-btn {
  font-weight: 400;
  width: 100% !important;
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  border-radius: 0;
  border-bottom: none; /* 最后一个菜单项无下边框 */
}

.feedback-btn::after {
  display: none;
}

-- 分享统计系统数据库表设计

-- 分享记录表
CREATE TABLE `eh_wx_share_record` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `user_id` varchar(32) DEFAULT NULL COMMENT '分享用户ID',
  `openid` varchar(64) NOT NULL COMMENT '分享用户openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '分享用户昵称',
  `share_type` varchar(20) NOT NULL COMMENT '分享类型(app_message:分享给朋友, timeline:分享到朋友圈)',
  `share_source` varchar(50) NOT NULL DEFAULT 'index' COMMENT '分享来源页面(index:首页, notice:公告, repair:报修等)',
  `source_id` varchar(32) DEFAULT NULL COMMENT '来源内容ID(如公告ID、报修ID等)',
  `source_title` varchar(200) DEFAULT NULL COMMENT '来源内容标题',
  `share_title` varchar(200) DEFAULT NULL COMMENT '分享标题',
  `share_desc` varchar(500) DEFAULT NULL COMMENT '分享描述',
  `share_path` varchar(500) DEFAULT NULL COMMENT '分享路径',
  `share_image` varchar(500) DEFAULT NULL COMMENT '分享图片',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_share_source` (`share_source`),
  KEY `idx_source_id` (`source_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享记录表';

-- 分享访问记录表
CREATE TABLE `eh_wx_share_visit` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `share_record_id` varchar(32) NOT NULL COMMENT '分享记录ID',
  `visitor_openid` varchar(64) DEFAULT NULL COMMENT '访问者openid(未登录时为空)',
  `visitor_user_id` varchar(32) DEFAULT NULL COMMENT '访问者用户ID(未登录时为空)',
  `visitor_nickname` varchar(50) DEFAULT NULL COMMENT '访问者昵称',
  `is_new_user` tinyint(1) DEFAULT '0' COMMENT '是否新用户(0:否, 1:是)',
  `visit_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
  `stay_duration` int(11) DEFAULT '0' COMMENT '停留时长(秒)',
  `page_views` int(11) DEFAULT '1' COMMENT '页面浏览数',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  PRIMARY KEY (`id`),
  KEY `idx_share_record_id` (`share_record_id`),
  KEY `idx_visitor_openid` (`visitor_openid`),
  KEY `idx_visitor_user_id` (`visitor_user_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_visit_time` (`visit_time`),
  KEY `idx_is_new_user` (`is_new_user`),
  CONSTRAINT `fk_share_visit_record` FOREIGN KEY (`share_record_id`) REFERENCES `eh_wx_share_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享访问记录表';

-- 分享统计汇总表(可选，用于提高查询性能)
CREATE TABLE `eh_wx_share_statistics` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `share_record_id` varchar(32) NOT NULL COMMENT '分享记录ID',
  `total_visits` int(11) DEFAULT '0' COMMENT '总访问次数',
  `unique_visitors` int(11) DEFAULT '0' COMMENT '独立访客数',
  `new_users` int(11) DEFAULT '0' COMMENT '新用户数',
  `total_stay_duration` int(11) DEFAULT '0' COMMENT '总停留时长(秒)',
  `avg_stay_duration` decimal(10,2) DEFAULT '0.00' COMMENT '平均停留时长(秒)',
  `last_visit_time` datetime DEFAULT NULL COMMENT '最后访问时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_record_id` (`share_record_id`),
  CONSTRAINT `fk_share_statistics_record` FOREIGN KEY (`share_record_id`) REFERENCES `eh_wx_share_record` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信分享统计汇总表';

-- 创建索引以提高查询性能
CREATE INDEX `idx_share_record_community_time` ON `eh_wx_share_record` (`community_id`, `create_time`);
CREATE INDEX `idx_share_record_source_time` ON `eh_wx_share_record` (`share_source`, `create_time`);
CREATE INDEX `idx_share_visit_time_community` ON `eh_wx_share_visit` (`visit_time`, `community_id`);

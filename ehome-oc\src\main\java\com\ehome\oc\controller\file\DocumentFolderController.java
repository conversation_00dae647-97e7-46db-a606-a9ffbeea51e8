package com.ehome.oc.controller.file;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 文档文件夹管理控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/document/folder")
public class DocumentFolderController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(DocumentFolderController.class);
    private static final String PREFIX = "document/folder";

    /**
     * 新增文件夹页面
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        String parentId = getRequest().getParameter("parentId");
        mmap.put("parentId", parentId);
        return PREFIX + "/add";
    }

    /**
     * 编辑文件夹页面
     */
    @GetMapping("/edit/{folderId}")
    public String edit(@PathVariable("folderId") String folderId, ModelMap mmap) {
        Record folder = Db.findFirst("select * from eh_file_folder where folder_id = ? and status = '0'", folderId);
        if (folder != null) {
            mmap.put("folder", folder.toMap());
        }
        return PREFIX + "/edit";
    }

    /**
     * 新增保存文件夹
     */
    @PostMapping("/add")
    @ResponseBody
    @Log(title = "新增文件夹", businessType = BusinessType.INSERT)
    public AjaxResult addSave() {
        JSONObject params = getParams();
        String folderName = params.getString("folderName");
        String parentId = params.getString("parentId");
        String remark = params.getString("remark");
        
        if (StringUtils.isEmpty(folderName)) {
            return AjaxResult.error("文件夹名称不能为空");
        }
        
        // 检查同级目录下是否存在同名文件夹
        String checkSql = "select count(*) from eh_file_folder where folder_name = ? and community_id = '0' and status = '0'";
        if (StringUtils.isNotEmpty(parentId)) {
            checkSql += " and parent_id = ?";
            Long count = Db.queryLong(checkSql, folderName, parentId);
            if (count > 0) {
                return AjaxResult.error("同级目录下已存在同名文件夹");
            }
        } else {
            checkSql += " and parent_id is null";
            Long count = Db.queryLong(checkSql, folderName);
            if (count > 0) {
                return AjaxResult.error("同级目录下已存在同名文件夹");
            }
        }
        
        try {
            String newFolderId = Seq.getId();
            String folderCode = generateFolderCode(parentId);

            Record folder = new Record();
            folder.set("folder_id", newFolderId);
            folder.set("parent_id", StringUtils.isEmpty(parentId) ? null : parentId);
            folder.set("folder_name", folderName);
            folder.set("folder_code", folderCode);
            folder.set("community_id", "0");
            folder.set("sort_order", getNextSortOrder(parentId));
            folder.set("create_time", DateUtils.getNowDate());
            folder.set("update_time", DateUtils.getNowDate());
            folder.set("create_user", getSysUser() != null ? getSysUser().getLoginName() : "anonymous");
            folder.set("status", "0");
            folder.set("remark", remark);

            Db.save("eh_file_folder", "folder_id", folder);
            return AjaxResult.success("新增文件夹成功");
        } catch (Exception e) {
            log.error("新增文件夹失败", e);
            return AjaxResult.error("新增文件夹失败：" + e.getMessage());
        }
    }

    /**
     * 修改保存文件夹
     */
    @PostMapping("/edit")
    @ResponseBody
    @Log(title = "修改文件夹", businessType = BusinessType.UPDATE)
    public AjaxResult editSave() {
        JSONObject params = getParams();
        String folderId = params.getString("folderId");
        String folderName = params.getString("folderName");
        String remark = params.getString("remark");
        
        if (StringUtils.isEmpty(folderId)) {
            return AjaxResult.error("文件夹ID不能为空");
        }
        
        if (StringUtils.isEmpty(folderName)) {
            return AjaxResult.error("文件夹名称不能为空");
        }
        
        // 检查文件夹是否存在
        Record existFolder = Db.findFirst("select * from eh_file_folder where folder_id = ? and status = '0'", folderId);
        if (existFolder == null) {
            return AjaxResult.error("文件夹不存在");
        }
        
        // 检查同级目录下是否存在同名文件夹（排除自己）
        String parentId = existFolder.getStr("parent_id");
        String checkSql = "select count(*) from eh_file_folder where folder_name = ? and community_id = '0' and status = '0' and folder_id != ?";
        if (StringUtils.isNotEmpty(parentId)) {
            checkSql += " and parent_id = ?";
            Long count = Db.queryLong(checkSql, folderName, folderId, parentId);
            if (count > 0) {
                return AjaxResult.error("同级目录下已存在同名文件夹");
            }
        } else {
            checkSql += " and parent_id is null";
            Long count = Db.queryLong(checkSql, folderName, folderId);
            if (count > 0) {
                return AjaxResult.error("同级目录下已存在同名文件夹");
            }
        }
        
        try {
            Record folder = new Record();
            folder.set("folder_id", folderId);
            folder.set("folder_name", folderName);
            folder.set("update_time", DateUtils.getNowDate());
            folder.set("remark", remark);
            
            Db.update("eh_file_folder", "folder_id", folder);
            return AjaxResult.success("修改文件夹成功");
        } catch (Exception e) {
            log.error("修改文件夹失败", e);
            return AjaxResult.error("修改文件夹失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件夹
     */
    @PostMapping("/remove")
    @ResponseBody
    @Log(title = "删除文件夹", businessType = BusinessType.DELETE)
    public AjaxResult remove() {
        JSONObject params = getParams();
        String folderIds = params.getString("ids");
        
        if (StringUtils.isEmpty(folderIds)) {
            return AjaxResult.error("请选择要删除的文件夹");
        }
        
        try {
            String[] ids = folderIds.split(",");
            for (String folderId : ids) {
                // 检查文件夹下是否有子文件夹
                Long childCount = Db.queryLong("select count(*) from eh_file_folder where parent_id = ? and status = '0'", folderId);
                if (childCount > 0) {
                    return AjaxResult.error("文件夹下存在子文件夹，无法删除");
                }
                
                // 检查文件夹下是否有文件
                Long fileCount = Db.queryLong("select count(*) from eh_file_info where folder_id = ? and status = '0'", folderId);
                if (fileCount > 0) {
                    return AjaxResult.error("文件夹下存在文件，无法删除");
                }
                
                // 逻辑删除文件夹
                Record folder = new Record();
                folder.set("folder_id", folderId);
                folder.set("status", "1");
                folder.set("update_time", DateUtils.getNowDate());
                Db.update("eh_file_folder", "folder_id", folder);
            }
            return AjaxResult.success("删除文件夹成功");
        } catch (Exception e) {
            log.error("删除文件夹失败", e);
            return AjaxResult.error("删除文件夹失败：" + e.getMessage());
        }
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSortOrder(String parentId) {
        String sql = "select max(sort_order) from eh_file_folder where community_id = '0' and status = '0'";
        if (StringUtils.isNotEmpty(parentId)) {
            sql += " and parent_id = ?";
            Integer maxOrder = Db.queryInt(sql, parentId);
            return maxOrder != null ? maxOrder + 1 : 1;
        } else {
            sql += " and parent_id is null";
            Integer maxOrder = Db.queryInt(sql);
            return maxOrder != null ? maxOrder + 1 : 1;
        }
    }

    /**
     * 移动文件夹
     */
    @PostMapping("/move")
    @ResponseBody
    @Log(title = "移动文件夹", businessType = BusinessType.UPDATE)
    public AjaxResult move() {
        JSONObject params = getParams();
        String folderId = params.getString("folderId");
        String targetParentId = params.getString("targetParentId");
        
        if (StringUtils.isEmpty(folderId)) {
            return AjaxResult.error("文件夹ID不能为空");
        }
        
        // 检查是否移动到自己的子目录下（防止循环引用）
        if (StringUtils.isNotEmpty(targetParentId) && isChildFolder(folderId, targetParentId)) {
            return AjaxResult.error("不能移动到自己的子目录下");
        }
        
        try {
            Record folder = new Record();
            folder.set("folder_id", folderId);
            folder.set("parent_id", StringUtils.isEmpty(targetParentId) ? null : targetParentId);
            folder.set("update_time", DateUtils.getNowDate());
            
            Db.update("eh_file_folder", "folder_id", folder);
            return AjaxResult.success("移动文件夹成功");
        } catch (Exception e) {
            log.error("移动文件夹失败", e);
            return AjaxResult.error("移动文件夹失败：" + e.getMessage());
        }
    }

    /**
     * 检查是否为子文件夹（防止循环引用）
     */
    private boolean isChildFolder(String parentId, String childId) {
        if (StringUtils.isEmpty(childId)) {
            return false;
        }

        if (parentId.equals(childId)) {
            return true;
        }

        Record child = Db.findFirst("select parent_id from eh_file_folder where folder_id = ? and status = '0'", childId);
        if (child != null && StringUtils.isNotEmpty(child.getStr("parent_id"))) {
            return isChildFolder(parentId, child.getStr("parent_id"));
        }

        return false;
    }

    /**
     * 生成文件夹编码
     */
    private String generateFolderCode(String parentId) {
        if (StringUtils.isEmpty(parentId)) {
            // 根级文件夹，查询最大编码
            String maxCode = Db.queryStr("select max(folder_code) from eh_file_folder where community_id = '0' and status = '0' and parent_id is null");
            if (StringUtils.isEmpty(maxCode)) {
                return "001";
            } else {
                int nextNum = Integer.parseInt(maxCode) + 1;
                return String.format("%03d", nextNum);
            }
        } else {
            // 子文件夹，基于父文件夹编码生成
            Record parent = Db.findFirst("select folder_code from eh_file_folder where folder_id = ? and status = '0'", parentId);
            if (parent == null || StringUtils.isEmpty(parent.getStr("folder_code"))) {
                throw new RuntimeException("父文件夹不存在或编码为空");
            }

            String parentCode = parent.getStr("folder_code");
            // 查询同级最大编码
            String maxCode = Db.queryStr("select max(folder_code) from eh_file_folder where community_id = '0' and status = '0' and parent_id = ?", parentId);

            if (StringUtils.isEmpty(maxCode)) {
                return parentCode + ".001";
            } else {
                // 提取最后一级编码
                String[] parts = maxCode.split("\\.");
                int lastNum = Integer.parseInt(parts[parts.length - 1]);
                int nextNum = lastNum + 1;
                return parentCode + "." + String.format("%03d", nextNum);
            }
        }
    }
}

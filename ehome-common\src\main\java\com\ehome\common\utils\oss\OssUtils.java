package com.ehome.common.utils.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.ehome.common.config.OssConfig;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.file.FileUploadUtils;
import com.ehome.common.utils.uuid.UUID;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云OSS工具类
 * 
 * <AUTHOR>
 */
@Component
public class OssUtils {
    
    private static final Logger log = LoggerFactory.getLogger(OssUtils.class);
    
    @Autowired
    private OssConfig ossConfig;


    /**
     * 上传文件到OSS
     *
     * @param file 上传的文件
     * @param folder 文件夹路径
     * @param isPublic 是否上传到公共Bucket
     * @return OSS文件键名
     * @throws IOException
     */
    public String uploadFile(MultipartFile file,String fileId, String folder, boolean isPublic) throws IOException {
        if (!ossConfig.isConfigValid()) {
            throw new IOException("OSS配置无效");
        }

        if (isPublic && !ossConfig.isPublicBucketConfigValid()) {
            throw new IOException("公共OSS Bucket配置无效");
        }

        String extractedFileName = FileUploadUtils.extractFilename(file);

        // 生成文件键名
        String objectKey = generateObjectKey(file, folder);

        // 选择Bucket
        String bucketName = isPublic ? ossConfig.getPublicBucketName() : ossConfig.getBucketName();

        OSS ossClient = null;
        try {
            // 创建OSS客户端
            ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );

            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentEncoding("UTF-8");
            metadata.setContentType(file.getContentType());
            metadata.setCacheControl("no-cache");
            String encodedFileName = URLEncoder.encode(extractedFileName, "UTF-8").replaceAll("\\+", "%20");
            metadata.setContentDisposition("inline; filename=\"" + extractedFileName + "\"; filename*=UTF-8''" + encodedFileName);

            Map<String, String> userMetadata = new HashMap<>();
            userMetadata.put("id", fileId);
            metadata.setUserMetadata(userMetadata);

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                bucketName,
                objectKey,
                file.getInputStream(),
                metadata
            );

            PutObjectResult result = ossClient.putObject(putObjectRequest);

            log.info("文件上传到OSS成功: {} -> {} ({})", file.getOriginalFilename(), objectKey, isPublic ? "公共Bucket" : "私有Bucket");
            return objectKey;

        } catch (Exception e) {
            log.error("文件上传到OSS失败: {}", e.getMessage(), e);
            throw new IOException("OSS上传失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 上传字节数组到OSS
     * 
     * @param bytes 字节数组
     * @param fileName 文件名
     * @param contentType 内容类型
     * @return OSS文件键名
     * @throws IOException
     */
    public String uploadBytes(byte[] bytes, String fileName, String contentType) throws IOException {
        if (!ossConfig.isConfigValid()) {
            throw new IOException("OSS配置无效");
        }
        
        String objectKey = generateObjectKey(fileName, null);
        
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(), 
                ossConfig.getAccessKeyId(), 
                ossConfig.getAccessKeySecret()
            );
            
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(bytes.length);
            metadata.setContentType(contentType);
            
            InputStream inputStream = new ByteArrayInputStream(bytes);
            PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(), 
                objectKey, 
                inputStream, 
                metadata
            );
            
            ossClient.putObject(putObjectRequest);
            
            log.info("字节数组上传到OSS成功: {}", objectKey);
            return objectKey;
            
        } catch (Exception e) {
            log.error("字节数组上传到OSS失败: {}", e.getMessage(), e);
            throw new IOException("OSS上传失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 删除OSS文件
     * 
     * @param objectKey 文件键名
     * @return 是否删除成功
     */
    public boolean deleteFile(String objectKey, boolean isPublic) {
        if (!ossConfig.isConfigValid() || StringUtils.isEmpty(objectKey)) {
            return false;
        }
        
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(), 
                ossConfig.getAccessKeyId(), 
                ossConfig.getAccessKeySecret()
            );

            if(isPublic){
                ossClient.deleteObject(ossConfig.getPublicBucketName(), objectKey);
            }else{
                ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
            }
            log.info("OSS文件删除成功: {}", objectKey);
            return true;
            
        } catch (Exception e) {
            log.error("OSS文件删除失败: {}", e.getMessage(), e);
            return false;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 获取文件访问URL
     *
     * @param objectKey 文件键名
     * @param isPublic 是否为公共Bucket文件
     * @return 访问URL
     */
    public String getFileUrl(String objectKey, boolean isPublic) {
        if (isPublic) {
            // 公共Bucket直接返回URL，无需签名
            if (StringUtils.isEmpty(objectKey)) {
                return null;
            }
            return ossConfig.getFullDomain(true) + "/" + objectKey;
        } else {
            // 私有Bucket返回预签名URL
            return getPresignedUrl(objectKey, ossConfig.getUrlExpiration(), false);
        }
    }

    /**
     * 获取文件预签名访问URL（默认私有Bucket）
     *
     * @param objectKey 文件键名
     * @return 预签名访问URL
     */
    public String getFileUrl(String objectKey) {
        return getPresignedUrl(objectKey, ossConfig.getUrlExpiration(), false);
    }

    /**
     * 生成预签名URL
     *
     * @param objectKey 文件键名
     * @param expiration 过期时间（秒）
     * @return 预签名URL
     */
    public String getPresignedUrl(String objectKey, long expiration) {
        return getPresignedUrl(objectKey, expiration, false);
    }

    /**
     * 生成预签名URL
     *
     * @param objectKey 文件键名
     * @param expiration 过期时间（秒）
     * @param isPublic 是否为公共Bucket
     * @return 预签名URL
     */
    public String getPresignedUrl(String objectKey, long expiration, boolean isPublic) {
        if (StringUtils.isEmpty(objectKey)) {
            return null;
        }

        if (!ossConfig.isConfigValid()) {
            log.error("OSS配置无效，无法生成预签名URL");
            return null;
        }

        if (isPublic && !ossConfig.isPublicBucketConfigValid()) {
            log.error("公共OSS Bucket配置无效，无法生成预签名URL");
            return null;
        }

        // 选择Bucket
        String bucketName = isPublic ? ossConfig.getPublicBucketName() : ossConfig.getBucketName();

        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret()
            );

            // 设置过期时间
            Date expirationDate = new Date(System.currentTimeMillis() + expiration * 1000);

            // 生成预签名URL
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                bucketName,
                objectKey
            );
            request.setExpiration(expirationDate);

            URL url = ossClient.generatePresignedUrl(request);
            String _url = url.toString();
            _url = _url.replace("http://", "https://"); // 确保使用HTTPS协议
            return _url;

        } catch (Exception e) {
            log.error("生成预签名URL失败: {}", e.getMessage(), e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }
    
    /**
     * 生成OSS对象键名
     *
     * @param file 文件
     * @param folder 文件夹
     * @return 对象键名
     */
    private String generateObjectKey(MultipartFile file, String folder) {
        String extractedFileName = FileUploadUtils.extractFilename(file);
        String extension = FilenameUtils.getExtension(extractedFileName);
        String baseName = FilenameUtils.getBaseName(extractedFileName);
        // 清理文件名，去除空格和特殊字符
        String sanitizedBaseName = FileUploadUtils.sanitizeFileName(baseName);
        return generateObjectKey(sanitizedBaseName + "." + extension, folder);
    }
    
    /**
     * 生成OSS对象键名
     *
     * @param fileName 文件名
     * @param folder 文件夹
     * @return 对象键名
     */
    private String generateObjectKey(String fileName, String folder) {
        StringBuilder keyBuilder = new StringBuilder();

        // 添加路径前缀
        if (StringUtils.isNotEmpty(ossConfig.getPathPrefix())) {
            keyBuilder.append(ossConfig.getPathPrefix()).append("/");
        }

        // 添加文件夹
        if (StringUtils.isNotEmpty(folder)) {
            keyBuilder.append(folder).append("/");
        }

        // 添加日期路径
        keyBuilder.append(DateUtils.datePath()).append("/");

        // 添加文件名（带唯一ID）
        String extension = FilenameUtils.getExtension(fileName);
        String baseName = FilenameUtils.getBaseName(fileName);
        // 清理文件名，去除空格和特殊字符
        String sanitizedBaseName = FileUploadUtils.sanitizeFileName(baseName);
        keyBuilder.append(sanitizedBaseName).append("_").append(UUID.randomUUID()).append(".").append(extension);

        return keyBuilder.toString();
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.system.mapper.SysNoticeCommentMapper">
    
    <resultMap type="SysNoticeComment" id="SysNoticeCommentResult">
        <result property="commentId"    column="comment_id"    />
        <result property="noticeId"     column="notice_id"     />
        <result property="parentId"     column="parent_id"     />
        <result property="userId"       column="user_id"       />
        <result property="userName"     column="user_name"     />
        <result property="userType"     column="user_type"     />
        <result property="content"      column="content"       />
        <result property="status"       column="status"        />
        <result property="pmsId"        column="pms_id"        />
        <result property="communityId"  column="community_id"  />
        <result property="ownerId"      column="owner_id"      />
        <result property="houseId"      column="house_id"      />
        <result property="houseName"    column="house_name"    />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
        <result property="createBy"     column="create_by"     />
        <result property="updateBy"     column="update_by"     />
        <result property="replyCount"   column="reply_count"   />
        <result property="noticeTitle"  column="notice_title"  />
    </resultMap>

    <sql id="selectSysNoticeCommentVo">
        select comment_id, notice_id, parent_id, user_id, user_name, user_type, content, status, pms_id, community_id, owner_id, house_id, house_name, create_time, update_time, create_by, update_by from sys_notice_comment
    </sql>

    <select id="selectSysNoticeCommentList" parameterType="SysNoticeComment" resultMap="SysNoticeCommentResult">
        <include refid="selectSysNoticeCommentVo"/>
        <where>  
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="pmsId != null  and pmsId != ''"> and pms_id = #{pmsId}</if>
            <if test="communityId != null  and communityId != ''"> and community_id = #{communityId}</if>
            <if test="houseName != null  and houseName != ''"> and house_name like concat('%', #{houseName}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSysNoticeCommentByCommentId" parameterType="Long" resultMap="SysNoticeCommentResult">
        <include refid="selectSysNoticeCommentVo"/>
        where comment_id = #{commentId}
    </select>

    <select id="selectCommentsByNoticeId" parameterType="Long" resultMap="SysNoticeCommentResult">
        <include refid="selectSysNoticeCommentVo"/>
        where notice_id = #{noticeId} and status = '0'
        order by parent_id asc, create_time asc
    </select>

    <select id="selectTopCommentsByNoticeId" parameterType="Long" resultMap="SysNoticeCommentResult">
        select c.*, 
               (select count(*) from sys_notice_comment r where r.parent_id = c.comment_id and r.status = '0') as reply_count
        from sys_notice_comment c
        where c.notice_id = #{noticeId} and c.parent_id is null and c.status = '0'
        order by c.create_time desc
    </select>

    <select id="selectRepliesByParentId" parameterType="Long" resultMap="SysNoticeCommentResult">
        <include refid="selectSysNoticeCommentVo"/>
        where parent_id = #{parentId} and status = '0'
        order by create_time asc
    </select>

    <select id="selectRepliesByParentIdWithUser" resultMap="SysNoticeCommentResult">
        <include refid="selectSysNoticeCommentVo"/>
        where parent_id = #{parentId}
        and (status = '0' or (status = '2' and user_id = #{userId}))
        order by create_time asc
    </select>

    <select id="countCommentsByNoticeId" parameterType="Long" resultType="int">
        select count(*) from sys_notice_comment where notice_id = #{noticeId} and status = '0'
    </select>

    <select id="countRepliesByParentId" parameterType="Long" resultType="int">
        select count(*) from sys_notice_comment where parent_id = #{parentId} and status = '0'
    </select>
        
    <insert id="insertSysNoticeComment" parameterType="SysNoticeComment" useGeneratedKeys="true" keyProperty="commentId">
        insert into sys_notice_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="status != null">status,</if>
            <if test="pmsId != null">pms_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="houseId != null">house_id,</if>
            <if test="houseName != null">house_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="status != null">#{status},</if>
            <if test="pmsId != null">#{pmsId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="houseId != null">#{houseId},</if>
            <if test="houseName != null">#{houseName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateSysNoticeComment" parameterType="SysNoticeComment">
        update sys_notice_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="status != null">status = #{status},</if>
            <if test="pmsId != null">pms_id = #{pmsId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where comment_id = #{commentId}
    </update>

    <delete id="deleteSysNoticeCommentByCommentId" parameterType="Long">
        delete from sys_notice_comment where comment_id = #{commentId}
    </delete>

    <delete id="deleteSysNoticeCommentByCommentIds" parameterType="String">
        delete from sys_notice_comment where comment_id in 
        <foreach item="commentId" collection="array" open="(" separator="," close=")">
            #{commentId}
        </foreach>
    </delete>

    <update id="softDeleteComment" parameterType="Long">
        update sys_notice_comment set status = '1', update_time = now() where comment_id = #{commentId}
    </update>

    <update id="auditComment">
        update sys_notice_comment set status = #{status}, update_time = now() where comment_id = #{commentId}
    </update>

    <select id="selectAllCommentsByCommunity" parameterType="SysNoticeComment" resultMap="SysNoticeCommentResult">
        select
            c.comment_id,
            c.notice_id,
            c.parent_id,
            c.user_id,
            c.user_name,
            c.user_type,
            c.content,
            c.status,
            c.pms_id,
            c.community_id,
            c.owner_id,
            c.house_id,
            c.house_name,
            c.create_time,
            c.update_time,
            c.create_by,
            c.update_by,
            n.notice_title
        from sys_notice_comment c
        left join sys_notice n on c.notice_id = n.notice_id
        <where>
            AND n.deleted = 0
            <if test="communityId != null and communityId != ''">
                AND c.community_id = #{communityId}
            </if>
            <if test="userName != null and userName != ''">
                AND c.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userType != null and userType != ''">
                AND c.user_type = #{userType}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            <if test="content != null and content != ''">
                AND c.content like concat('%', #{content}, '%')
            </if>
            <if test="noticeTitle != null and noticeTitle != ''">
                AND n.notice_title like concat('%', #{noticeTitle}, '%')
            </if>
            <if test="houseName != null and houseName != ''">
                AND c.house_name like concat('%', #{houseName}, '%')
            </if>
        </where>
        order by c.create_time desc
    </select>

</mapper>

<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 诊断提示 -->
  <view class="tip-section">
    <text class="tip-text">如果页面无法访问，请将诊断结果截图，并会试在线反馈或联系客服人员。</text>
  </view>

  <!-- 诊断评情 -->
  <view class="section-title">诊断评情</view>

  <!-- 基础信息 -->
  <view class="info-section">
    <view class="section-header">基础信息</view>
    <view class="info-item">
      <text class="label">时间：</text>
      <text class="value">{{basicInfo.time}}</text>
    </view>
    <view class="info-item">
      <text class="label">微信版本：</text>
      <text class="value">{{basicInfo.wechatVersion}}</text>
    </view>
    <view class="info-item">
      <text class="label">小程序：</text>
      <text class="value">{{basicInfo.miniProgramVersion}}</text>
    </view>
    <view class="info-item">
      <text class="label">openid：</text>
      <text class="value">{{basicInfo.openid}}</text>
    </view>
    <view class="info-item">
      <text class="label">品牌型号：</text>
      <text class="value">{{basicInfo.deviceModel}}</text>
    </view>
    <view class="info-item">
      <text class="label">系统：</text>
      <text class="value">{{basicInfo.system}}</text>
    </view>
  </view>

  <!-- 网络信息 -->
  <view class="info-section">
    <view class="section-header">网络信息</view>
    <view class="info-item">
      <text class="label">网络类型：</text>
      <text class="value">{{networkInfo.networkType}}</text>
    </view>
    <view class="info-item">
      <text class="label">IP：</text>
      <text class="value">{{networkInfo.ip}}</text>
    </view>
    <view class="info-item">
      <text class="label">网络信号：</text>
      <text class="value">{{networkInfo.signal}}</text>
    </view>
  </view>

  <!-- 网络测速 -->
  <view class="info-section">
    <view class="section-header">网络测速</view>
    <view wx:if="{{loading}}" class="loading-container">
      <van-loading type="spinner" size="24px" />
      <text class="loading-text">正在测试网络连接...</text>
    </view>
    <view wx:else>
      <view class="speed-item" wx:for="{{speedTestResults}}" wx:key="url">
        <view class="speed-url">{{item.url}}</view>
        <view class="speed-result">
          <text class="status-text" style="color: {{item.success ? '#07c160' : '#ee0a24'}}">
            状态码：{{item.statusCode}}
          </text>
          <text class="time-text">耗时：{{item.duration}}ms</text>
          <van-icon 
            name="{{item.success ? 'success' : 'close'}}" 
            size="20px" 
            color="{{item.success ? '#07c160' : '#ee0a24'}}" 
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-container">
    <van-button
      type="default"
      size="large"
      round
      bindtap="refreshDiagnosis"
      disabled="{{loading}}"
      style="margin-bottom: 20rpx;"
    >
      重新诊断
    </van-button>
    <van-button
      type="primary"
      size="large"
      round
      bindtap="copyToClipboard"
      disabled="{{loading}}"
      style="margin-bottom: 20rpx;"
    >
      复制到剪贴板
    </van-button>
    <van-button
      type="warning"
      size="large"
      round
      bindtap="clearAllCache"
      disabled="{{loading}}"
    >
      重置小程序缓存
    </van-button>

    <!-- 重置说明 -->
    <view class="reset-tip">
      <text class="reset-tip-text">重置缓存将清除所有用户数据和登录状态，需要重新登录</text>
    </view>
  </view>
</view>

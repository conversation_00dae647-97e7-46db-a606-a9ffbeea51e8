# 增强版Db类使用示例

## 背景说明
为了统一记录JFinal的SQL执行时间，我们创建了增强版的Db类 `com.ehome.jfinal.utils.Db`，它包装了原生JFinal的所有Db静态方法，并添加了执行时间记录功能。

## 迁移方法
只需要修改import语句，其他代码无需任何改动！

## 示例：PmsInfoController.java 迁移

### 修改前（原生Db）
```java
package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ShiroUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.framework.shiro.service.SysPasswordService;
import com.ehome.jfinal.model.OcInfoModel;
import com.ehome.jfinal.model.PmsInfoModel;
import com.ehome.oc.service.ICommunityService;
import com.ehome.oc.service.impl.DataInitServiceImpl;
import com.ehome.system.service.ISysDeptService;
import com.ehome.system.service.ISysUserService;
import com.jfinal.plugin.activerecord.Db;  // ❌ 原生Db，无法记录执行时间
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

@Controller
@RequestMapping("/oc/pmsInfo")
public class PmsInfoController extends BaseController {
    
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        // ❌ 这些Db调用无法记录执行时间
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }
    
    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        // ... 其他代码 ...
        
        // ❌ 这些Db调用无法记录执行时间
        Db.save("sys_dept","dept_id",deptRecord);
        Db.save("sys_user", "user_id", userRecord);
        Db.update("insert into sys_user_role(user_id,role_id) values(?,?)",userId,"100");
        
        return AjaxResult.success();
    }
    
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        // ... 其他代码 ...
        
        for (String id : idArr) {
            // ❌ 这些Db调用无法记录执行时间
            Db.update("update eh_pms_info set status = ?  where pms_id = ?", 1,id);
            Db.update("update eh_community set oc_state = ?  where pms_id = ?", 1,id);
        }
        return success();
    }
}
```

### 修改后（增强版Db）
```java
package com.ehome.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.ShiroUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.framework.shiro.service.SysPasswordService;
import com.ehome.jfinal.model.OcInfoModel;
import com.ehome.jfinal.model.PmsInfoModel;
import com.ehome.oc.service.ICommunityService;
import com.ehome.oc.service.impl.DataInitServiceImpl;
import com.ehome.system.service.ISysDeptService;
import com.ehome.system.service.ISysUserService;
import com.ehome.jfinal.utils.Db;  // ✅ 增强版Db，自动记录执行时间
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

@Controller
@RequestMapping("/oc/pmsInfo")
public class PmsInfoController extends BaseController {
    
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        // ✅ 自动记录执行时间到sql.log
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select *",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }
    
    @PostMapping("/addData")
    @ResponseBody
    public AjaxResult addData() {
        // ... 其他代码 ...
        
        // ✅ 自动记录执行时间到sql.log
        Db.save("sys_dept","dept_id",deptRecord);
        Db.save("sys_user", "user_id", userRecord);
        Db.update("insert into sys_user_role(user_id,role_id) values(?,?)",userId,"100");
        
        return AjaxResult.success();
    }
    
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        // ... 其他代码 ...
        
        for (String id : idArr) {
            // ✅ 自动记录执行时间到sql.log
            Db.update("update eh_pms_info set status = ?  where pms_id = ?", 1,id);
            Db.update("update eh_community set oc_state = ?  where pms_id = ?", 1,id);
        }
        return success();
    }
}
```

## 关键变化
**只有一行代码需要修改：**
```java
// 修改前
import com.jfinal.plugin.activerecord.Db;

// 修改后  
import com.ehome.jfinal.utils.Db;
```

## 日志输出效果

### 修改前（无执行时间记录）
```
2025-01-10 14:30:25.123 [http-nio-8066-exec-1] DEBUG com.jfinal.plugin.activerecord.ActiveRecordPlugin - Sql: SELECT * FROM eh_pms_info WHERE pms_name LIKE ?
```

### 修改后（自动记录执行时间）
```
2025-01-10 14:30:25.123 [http-nio-8066-exec-1] DEBUG sql-log - [JFinal-Enhanced] SQL执行 - 操作: paginate, 执行时间: 45ms, SQL: select * FROM eh_pms_info WHERE pms_name LIKE ?, 参数: %测试%, 结果: Page(第1页, 10条记录, 共25条)

2025-01-10 14:30:25.456 [http-nio-8066-exec-2] DEBUG sql-log - [JFinal-Enhanced] SQL执行 - 表: sys_dept, 操作: save, 执行时间: 12ms, 参数: Record{dept_name=测试物业, parent_id=0, ...}, 结果: 操作成功

2025-01-10 14:30:25.789 [http-nio-8066-exec-3] INFO sql-log - [JFinal-Enhanced] SQL执行 - 操作: update, 执行时间: 650ms, SQL: update eh_pms_info set status = ? where pms_id = ?, 参数: 1, PMS001, 结果: 影响行数: 1 [性能关注]
```

## 需要迁移的文件列表
根据代码扫描，以下文件使用了原生Db，建议进行迁移：

1. `ehome-oc/src/main/java/com/ehome/oc/controller/BuildingController.java`
2. `ehome-oc/src/main/java/com/ehome/oc/controller/VehicleMgrController.java`
3. `ehome-oc/src/main/java/com/ehome/oc/controller/HouseMgrController.java`
4. `ehome-common/src/main/java/com/ehome/common/core/controller/BaseController.java`
5. `ehome-oc/src/main/java/com/ehome/oc/controller/ParkingSpaceMgrController.java`
6. `ehome-oc/src/main/java/com/ehome/oc/controller/file/EhFileInfoController.java`
7. `ehome-oc/src/main/java/com/ehome/oc/controller/PmsInfoController.java`

## 迁移优势
1. **零代码修改**：只需改import语句
2. **完整覆盖**：所有JFinal Db操作都会记录执行时间
3. **性能监控**：自动识别慢SQL并分级记录
4. **统一日志**：与MyBatis SQL日志输出到同一文件
5. **向后兼容**：API完全一致，无学习成本

## 注意事项
1. 确保新的import路径正确：`com.ehome.jfinal.utils.Db`
2. 如果IDE提示冲突，检查是否有其他Db相关的import
3. 迁移后重启应用，观察sql.log文件中的JFinal-Enhanced日志

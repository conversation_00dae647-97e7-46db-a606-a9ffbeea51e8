<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.system.mapper.SysNoticeLikeMapper">
    
    <resultMap type="SysNoticeLike" id="SysNoticeLikeResult">
        <result property="likeId"       column="like_id"       />
        <result property="noticeId"     column="notice_id"     />
        <result property="userId"       column="user_id"       />
        <result property="userName"     column="user_name"     />
        <result property="userType"     column="user_type"     />
        <result property="communityId"  column="community_id"  />
        <result property="ownerId"      column="owner_id"      />
        <result property="houseId"      column="house_id"      />
        <result property="houseName"    column="house_name"    />
        <result property="status"       column="status"        />
        <result property="likeTime"     column="create_time"   />
        <result property="noticeTitle"  column="notice_title"  />
    </resultMap>
    
    <sql id="selectLikeVo">
        select like_id, notice_id, user_id, user_name, user_type, community_id, owner_id, house_id, house_name, status, create_time
        from sys_notice_like
    </sql>
    
    <select id="selectLikeById" parameterType="Long" resultMap="SysNoticeLikeResult">
        <include refid="selectLikeVo"/>
        where like_id = #{likeId}
    </select>
    
    <select id="selectLikeList" parameterType="SysNoticeLike" resultMap="SysNoticeLikeResult">
        <include refid="selectLikeVo"/>
        <where>
            <if test="noticeId != null">
                AND notice_id = #{noticeId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userType != null and userType != ''">
                AND user_type = #{userType}
            </if>
            <if test="communityId != null and communityId != ''">
                AND community_id = #{communityId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="likeTime != null">
                AND date_format(create_time,'%y%m%d') = date_format(#{likeTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectLikeByNoticeId" parameterType="Long" resultMap="SysNoticeLikeResult">
        <include refid="selectLikeVo"/>
        where notice_id = #{noticeId} and status = '0'
        order by create_time desc
    </select>
    
    <select id="selectAllLikesByCommunity" parameterType="SysNoticeLike" resultMap="SysNoticeLikeResult">
        select 
            l.like_id, 
            l.notice_id, 
            l.user_id, 
            l.user_name, 
            l.user_type,
            l.community_id,
            l.owner_id,
            l.house_id,
            l.house_name,
            l.status,
            l.create_time,
            n.notice_title
        from sys_notice_like l
        left join sys_notice n on l.notice_id = n.notice_id
        <where>
            AND n.deleted = 0
            <if test="communityId != null and communityId != ''">
                AND l.community_id = #{communityId}
            </if>
            <if test="userName != null and userName != ''">
                AND l.user_name like concat('%', #{userName}, '%')
            </if>
            <if test="userType != null and userType != ''">
                AND l.user_type = #{userType}
            </if>
            <if test="status != null and status != ''">
                AND l.status = #{status}
            </if>
            <if test="likeTime != null">
                AND date_format(l.create_time,'%y%m%d') = date_format(#{likeTime},'%y%m%d')
            </if>
            <if test="noticeTitle != null and noticeTitle != ''">
                AND n.notice_title like concat('%', #{noticeTitle}, '%')
            </if>
            <if test="houseName != null and houseName != ''">
                AND l.house_name like concat('%', #{houseName}, '%')
            </if>
        </where>
        order by l.create_time desc
    </select>
    
    <insert id="insertLike" parameterType="SysNoticeLike" useGeneratedKeys="true" keyProperty="likeId">
        insert into sys_notice_like (
            <if test="noticeId != null">notice_id, </if>
            <if test="userId != null and userId != ''">user_id, </if>
            <if test="userName != null and userName != ''">user_name, </if>
            <if test="userType != null and userType != ''">user_type, </if>
            <if test="communityId != null and communityId != ''">community_id, </if>
            <if test="ownerId != null and ownerId != ''">owner_id, </if>
            <if test="houseId != null and houseId != ''">house_id, </if>
            <if test="houseName != null and houseName != ''">house_name, </if>
            <if test="status != null and status != ''">status, </if>
            create_time
        )values(
            <if test="noticeId != null">#{noticeId}, </if>
            <if test="userId != null and userId != ''">#{userId}, </if>
            <if test="userName != null and userName != ''">#{userName}, </if>
            <if test="userType != null and userType != ''">#{userType}, </if>
            <if test="communityId != null and communityId != ''">#{communityId}, </if>
            <if test="ownerId != null and ownerId != ''">#{ownerId}, </if>
            <if test="houseId != null and houseId != ''">#{houseId}, </if>
            <if test="houseName != null and houseName != ''">#{houseName}, </if>
            <if test="status != null and status != ''">#{status}, </if>
            sysdate()
        )
    </insert>
    
    <update id="updateLike" parameterType="SysNoticeLike">
        update sys_notice_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null and status != ''">status = #{status},</if>
            update_time = sysdate()
        </trim>
        where like_id = #{likeId}
    </update>
    
    <delete id="deleteLikeById" parameterType="Long">
        delete from sys_notice_like where like_id = #{likeId}
    </delete>
    
    <delete id="deleteLikeByIds" parameterType="String">
        delete from sys_notice_like where like_id in 
        <foreach item="likeId" collection="array" open="(" separator="," close=")">
            #{likeId}
        </foreach>
    </delete>
    
    <delete id="deleteLikeByNoticeId" parameterType="Long">
        delete from sys_notice_like where notice_id = #{noticeId}
    </delete>
    
</mapper>

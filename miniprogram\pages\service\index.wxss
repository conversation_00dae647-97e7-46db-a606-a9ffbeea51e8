.container {
  min-height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
}

/* 顶部整体区域 */
.header-wrapper {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #1890ff 0%, #69c0ff 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  position: relative;
  z-index: 100;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 26rpx;
  position: relative;
  z-index: 2;
  cursor: pointer;
}

.location-wrapper {
  display: flex;
  align-items: center;
  transition: opacity 0.2s ease;
}

.navbar-content:active .location-wrapper {
  opacity: 0.7;
}

.navbar-location {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}



/* 加载状态 */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

/* 主要内容 */
.content {
  flex: 1;
  margin: 24rpx 16rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
}

/* Sidebar容器 */
.sidebar-container {
  display: flex;
  height: calc(100vh - 200rpx);
  position: relative;
}

/* Sidebar包装器 */
.sidebar-wrapper {
  width: 200rpx;
  transition: width 0.3s ease;
  overflow: hidden;
  background: #fafafa;
}

.sidebar-wrapper.collapsed {
  width: 0;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Tab容器样式 */
.tab-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 自定义Tab换行样式 */
.tab-wrapper {
  background: #fff;
  padding: 16rpx 12rpx 12rpx 12rpx;
}

.tab-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16rpx;
  gap: 16rpx;
  flex-wrap: nowrap;
}

.tab-row:last-child {
  margin-bottom: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  height: 64rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 32rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  flex: 1;
  max-width: none;
}

.tab-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.08) 0%, rgba(7, 193, 96, 0.03) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 未选中状态的悬浮效果 */
.tab-item:not(.active-tab):hover {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1rpx);
}

.tab-item.active-tab {
  background: linear-gradient(135deg, #07c160 0%, #06a84c 100%);
  border-color: #07c160;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.2);
  transform: translateY(-1rpx) scale(1.02);
}

.tab-item.active-tab::before {
  opacity: 1;
}

.tab-item:active {
  transform: scale(0.95);
  background: #e9ecef;
}

.tab-item.active-tab:active {
  transform: scale(0.95) translateY(-2rpx);
}

.tab-text {
  font-size: 26rpx;
  color: #495057;
  font-weight: 400;
  transition: all 0.3s ease;
  text-align: center;
  line-height: 1.2;
  padding: 0 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-item.active-tab .tab-text {
  color: #fff;
  font-weight: 500;
}

/* 序号样式 */
.tab-number {
  font-weight: 600;
  margin-right: 4rpx;
}

.tab-title {
  font-weight: inherit;
}

/* 选中状态下的序号样式 */
.tab-item.active-tab .tab-number {
  color: #fff;
  font-weight: 700;
}

.tab-item.active-tab .tab-title {
  color: #fff;
  font-weight: 500;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 375px) {
  .tab-item {
    min-width: 140rpx;
    height: 56rpx;
  }

  .tab-text {
    font-size: 22rpx;
    padding: 0 6rpx;
  }

  .tab-number {
    margin-right: 2rpx;
  }

  .tab-row {
    gap: 12rpx;
  }

  .tab-wrapper {
    padding: 12rpx 8rpx 8rpx 8rpx;
  }
}

/* 当tab数量较少时的居中显示 */
.tab-row:only-child {
  justify-content: center;
}

/* 悬浮效果增强 */
.tab-item.active-tab:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

/* Tab内容区域 */
.tab-content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
}

/* 一级菜单内容区域保持原有padding */
.content-area > view:not(.tab-container) {
  padding:0rpx 16rpx;
}

/* PDF列表样式 */
.pdf-list {
  flex: 1;
}

.pdf-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.pdf-item:last-child {
  border-bottom: none;
}

.pdf-item:active {
  background-color: #f5f5f5;
}

.pdf-icon {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: #fff5f5;
  border-radius: 12rpx;
}

.pdf-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.pdf-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.pdf-time {
  font-size: 24rpx;
  color: #999;
}

.pdf-arrow {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

/* 文本列表样式 */
.text-list {
  flex: 1;
}

.text-item {
  border-bottom: 2rpx solid #f5f5f5;
}

.text-item:last-child {
  border-bottom: none;
}

.text-content {
  padding: 32rpx 0;
  font-size: 28rpx;
  line-height: 1.8;
  color: #666;
  transition: background-color 0.2s;
}

.text-content:active {
  background-color: #f5f5f5;
}

/* rich-text 内容样式 */
.text-content rich-text {
  word-break: break-all;
}





/* 空状态 */
.empty-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 40rpx 0 20rpx;
  color: #999;
  font-size: 24rpx;
}

/* 覆盖sidebar默认样式 */
.van-sidebar {
  background: #fafafa !important;
  width: 200rpx !important;
  min-width: 200rpx !important;
}

.van-sidebar-item {
  font-size: 26rpx !important;
  padding: 28rpx 14rpx !important;
}

.van-sidebar-item--active {
  background: white !important;
  color: #07c160 !important;
  font-weight: 500 !important;
  border-right: 4rpx solid #07c160 !important;
}

/* 侧边栏折叠按钮 */
.sidebar-toggle-btn {
  position: fixed;
  left: 0;
  bottom: 200rpx;
  width: 140rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 0 30rpx 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
  gap: 8rpx;
}

.sidebar-toggle-btn:active {
  transform: scale(0.95);
  background: #e8e8e8;
}

.sidebar-toggle-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

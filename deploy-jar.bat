@echo off
setlocal enabledelayedexpansion

REM E-Home JAR包部署脚本 (Windows版本)
REM 使用方法: deploy-jar.bat [start|stop|restart|status|logs|config] [profile]

set APP_NAME=ehome

REM 动态获取脚本所在目录
set SCRIPT_DIR=%~dp0
REM 移除末尾的反斜杠
if "%SCRIPT_DIR:~-1%"=="\" set SCRIPT_DIR=%SCRIPT_DIR:~0,-1%
REM JAR文件路径（相对于脚本目录）
set JAR_FILE=%SCRIPT_DIR%\ehome.jar
REM 配置文件路径（相对于脚本目录）
set CONFIG_DIR=%SCRIPT_DIR%\env
set ENV_CONFIG_FILE=

REM 环境选择函数
:select_profile
if not "%~2"=="" (
    set PROFILE=%~2
) else (
    echo 请选择运行环境:
    echo 1^) 开发环境 ^(dev^)
    echo 2^) 测试环境 ^(test^)
    echo 3^) 生产环境 ^(prod^)
    set /p choice=请输入序号 [1-3]:

    if "!choice!"=="1" set PROFILE=dev
    if "!choice!"=="2" set PROFILE=test
    if "!choice!"=="3" set PROFILE=prod
    if "!choice!" gtr "3" (
        echo 无效选择，使用默认生产环境
        set PROFILE=prod
    )
    if "!choice!" lss "1" (
        echo 无效选择，使用默认生产环境
        set PROFILE=prod
    )
)

echo 选择的环境: !PROFILE!
goto :eof

REM 环境变量配置函数
:setup_env_vars
set ENV_CONFIG_FILE=%CONFIG_DIR%\env-!PROFILE!.bat

REM 如果环境配置文件存在，则加载
if exist "!ENV_CONFIG_FILE!" (
    echo 加载环境配置文件: !ENV_CONFIG_FILE!
    call "!ENV_CONFIG_FILE!"
) else (
    echo 警告: 环境配置文件不存在: !ENV_CONFIG_FILE!
    echo 使用默认配置，建议运行: %0 config !PROFILE! 生成配置文件
    echo 注意: IDEA开发环境不受此配置影响，使用application-dev.yml中的默认配置
)

REM 检查必要的环境变量是否已设置
if not defined DB_PASSWORD (
    echo 错误: 数据库密码未设置 ^(DB_PASSWORD^)
    echo 请在配置文件中设置: set DB_PASSWORD=your_password
    exit /b 1
)

if not defined TOKEN_SECRET (
    echo 错误: Token密钥未设置 ^(TOKEN_SECRET^)
    echo 请在配置文件中设置: set TOKEN_SECRET=your_secret
    exit /b 1
)
goto :eof

REM 检查环境变量配置
:check_env_vars
echo 当前环境变量配置:
echo   环境: !PROFILE!
echo   数据库: !DB_HOST!:!DB_PORT!/!DB_NAME!
echo   数据库用户: !DB_USERNAME!
echo   数据库密码: !DB_PASSWORD:~0,3!***
echo   微信AppID: !WECHAT_APPID!
echo   微信Secret: !WECHAT_SECRET:~0,10!***
echo   Token密钥: !TOKEN_SECRET:~0,10!***
echo.

REM 生产环境安全检查
if "!PROFILE!"=="prod" (
    if "!DB_PASSWORD!"=="yjwmkXWcydBsBZbX" (
        echo 警告: 生产环境使用默认数据库密码，存在安全风险！
    )
    if "!TOKEN_SECRET:~0,20!"=="ehomeProdSecretKey20" (
        echo 警告: 生产环境使用默认Token密钥，存在安全风险！
    )
    if "!WECHAT_SECRET:~0,20!"=="a62d650089f3c5ed044c" (
        echo 警告: 生产环境使用默认微信Secret，存在安全风险！
    )
)
goto :eof

REM 根据环境设置路径和端口
:setup_env
if "!PROFILE!"=="dev" (
    set BASE_PATH=!SCRIPT_DIR!\logs\dev
    set SERVER_PORT=8066
)
if "!PROFILE!"=="test" (
    set BASE_PATH=!SCRIPT_DIR!\logs\test
    set SERVER_PORT=8234
)
if "!PROFILE!"=="prod" (
    set BASE_PATH=!SCRIPT_DIR!\logs\prod
    set SERVER_PORT=8233
)

set PID_FILE=%TEMP%\%APP_NAME%-%PROFILE%.pid
set LOG_FILE=!BASE_PATH!\logs\app.log
set TOMCAT_LOG_FILE=!BASE_PATH!\logs\tomcat.log
goto :eof

REM JVM参数配置函数
:setup_jvm_opts
set JVM_OPTS=-Xms512m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m
set JVM_OPTS=!JVM_OPTS! -XX:+UseG1GC -XX:G1HeapRegionSize=16m
set JVM_OPTS=!JVM_OPTS! -XX:+UseGCOverheadLimit -XX:+ExplicitGCInvokesConcurrent
set JVM_OPTS=!JVM_OPTS! -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=!BASE_PATH!\logs\
set JVM_OPTS=!JVM_OPTS! -Djava.awt.headless=true -Dfile.encoding=UTF-8

REM Tomcat日志配置
set TOMCAT_OPTS=--logging.level.org.apache.catalina=INFO
set TOMCAT_OPTS=!TOMCAT_OPTS! --logging.level.org.apache.coyote=INFO
set TOMCAT_OPTS=!TOMCAT_OPTS! --logging.level.org.apache.tomcat=INFO
goto :eof



REM 检查JAR文件是否存在
:check_jar
if not exist "%JAR_FILE%" (
    echo 错误: JAR文件不存在: %JAR_FILE%
    echo 请先执行: mvn clean compile package -P prod
    exit /b 1
)
goto :eof

REM 检查应用是否运行
:is_running
if exist "!PID_FILE!" (
    set /p pid=<"!PID_FILE!"
    tasklist /FI "PID eq !pid!" 2>nul | find /I "java.exe" >nul
    if !errorlevel! equ 0 (
        set RUNNING=true
    ) else (
        set RUNNING=false
        del "!PID_FILE!" 2>nul
    )
) else (
    set RUNNING=false
)
goto :eof

REM 选择环境
:select_environment
echo.
echo ============================================
echo            E-Home 应用管理
echo ============================================
echo.
echo 请选择环境：
echo.
echo 1^) 开发环境 ^(dev^)   - 端口8066
echo 2^) 测试环境 ^(test^)  - 端口8234
echo 3^) 生产环境 ^(prod^)  - 端口8233
echo.
echo 0^) 退出
echo.

:env_choice_loop
set /p env_choice=请输入选项 ^(0-3^):
if "%env_choice%"=="1" set SELECTED_ENV=dev && goto :eof
if "%env_choice%"=="2" set SELECTED_ENV=test && goto :eof
if "%env_choice%"=="3" set SELECTED_ENV=prod && goto :eof
if "%env_choice%"=="0" goto end
echo 无效选择，请输入 0-3 之间的数字
goto env_choice_loop

REM 选择操作
:select_operation
echo.
echo ============================================
echo          !SELECTED_ENV! 环境操作菜单
echo ============================================
echo.
echo 请选择操作：
echo.
echo 1^) 启动应用    ^(start !SELECTED_ENV!^)
echo 2^) 停止应用    ^(stop !SELECTED_ENV!^)
echo 3^) 重启应用    ^(restart !SELECTED_ENV!^)
echo 4^) 查看状态    ^(status !SELECTED_ENV!^)
echo 5^) 查看日志    ^(logs !SELECTED_ENV!^)
echo 6^) 编辑配置    ^(notepad env\env-!SELECTED_ENV!.bat^)
echo.
echo 0^) 返回环境选择
echo.

:op_choice_loop
set /p op_choice=请输入选项 ^(0-6^):
if "%op_choice%"=="1" call :start !SELECTED_ENV! && goto :eof
if "%op_choice%"=="2" call :stop !SELECTED_ENV! && goto :eof
if "%op_choice%"=="3" call :restart !SELECTED_ENV! && goto :eof
if "%op_choice%"=="4" call :status !SELECTED_ENV! && goto :eof
if "%op_choice%"=="5" call :logs !SELECTED_ENV! && goto :eof
if "%op_choice%"=="6" notepad "%SCRIPT_DIR%\env\env-!SELECTED_ENV!.bat" && goto :eof
if "%op_choice%"=="0" goto :eof
echo 无效选择，请输入 0-6 之间的数字
goto op_choice_loop

REM 交互式模式
:interactive_mode
:main_loop
call :select_environment
call :select_operation
echo.
pause
goto main_loop

REM 主逻辑
if "%1"=="" goto interactive_mode
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
goto usage

:start
call :select_profile %*
call :setup_env_vars
call :setup_env
call :check_env_vars
call :setup_jvm_opts

call :is_running
if "!RUNNING!"=="true" (
    echo %APP_NAME% ^(!PROFILE!环境^) 已经在运行中
    goto end
)

call :check_jar
echo 启动 %APP_NAME% ^(!PROFILE!环境^)...

REM 创建日志目录
if not exist "!BASE_PATH!\logs" mkdir "!BASE_PATH!\logs"

REM 启动应用
echo JVM参数: !JVM_OPTS!
echo Tomcat参数: !TOMCAT_OPTS!

start /B java !JVM_OPTS! -jar "%JAR_FILE%" --spring.profiles.active=!PROFILE! !TOMCAT_OPTS! > "!LOG_FILE!" 2>&1

REM 获取进程ID（Windows方式）
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq java.exe" /FO CSV ^| find "java.exe"') do (
    set "pid=%%i"
    set "pid=!pid:"=!"
)
echo !pid! > "!PID_FILE!"

timeout /t 2 /nobreak >nul
call :is_running
if "!RUNNING!"=="true" (
    echo %APP_NAME% ^(!PROFILE!环境^) 启动成功
    echo 运行环境: !PROFILE!
    echo 服务端口: !SERVER_PORT!
    echo 应用日志: !LOG_FILE!
    echo 本地访问: http://localhost:!SERVER_PORT!
    if "!PROFILE!"=="prod" (
        echo 生产访问: https://ehome.getxf.cn
    )
    echo.
    echo 查看日志: type "!LOG_FILE!"
) else (
    echo %APP_NAME% 启动失败
    exit /b 1
)
goto end

:stop
call :select_profile %*
call :setup_env

call :is_running
if "!RUNNING!"=="false" (
    echo %APP_NAME% ^(!PROFILE!环境^) 未运行
    goto end
)

if exist "!PID_FILE!" (
    set /p pid=<"!PID_FILE!"
    echo 停止 %APP_NAME% ^(!PROFILE!环境^) ^(PID: !pid!^)...
    taskkill /PID !pid! /F >nul 2>&1
    del "!PID_FILE!" 2>nul
    echo %APP_NAME% 已停止
) else (
    echo 无法找到进程ID文件
)
goto end

:restart
call :stop %*
timeout /t 2 /nobreak >nul
call :start %*
goto end

:status
call :select_profile %*
call :setup_env

call :is_running
if "!RUNNING!"=="true" (
    echo %APP_NAME% ^(!PROFILE!环境^) 正在运行
    echo 运行环境: !PROFILE!
    echo 服务端口: !SERVER_PORT!
    echo 应用日志: !LOG_FILE!
) else (
    echo %APP_NAME% ^(!PROFILE!环境^) 未运行
)
goto end

:logs
call :select_profile %*
call :setup_env

if "%~3"=="tomcat" (
    echo 查看Tomcat日志 ^(!PROFILE!环境^) ^(Ctrl+C退出^):
    type "!TOMCAT_LOG_FILE!"
) else (
    echo 查看应用日志 ^(!PROFILE!环境^) ^(Ctrl+C退出^):
    type "!LOG_FILE!"
)
goto end

:usage
echo 使用方法: %0 {start^|stop^|restart^|status^|logs} [profile]
echo 或者直接运行: %0 ^(进入交互模式^)
echo.
echo 命令说明:
echo   start [profile]   - 启动应用
echo   stop [profile]    - 停止应用
echo   restart [profile] - 重启应用
echo   status [profile]  - 查看状态
echo   logs [profile]    - 查看应用日志
echo   logs [profile] tomcat - 查看Tomcat日志
echo.
echo 交互模式:
echo   直接运行 %0 进入交互式操作界面
echo.
echo 环境参数 ^(profile^):
echo   dev   - 开发环境 ^(端口8066^)
echo   test  - 测试环境 ^(端口8234^)
echo   prod  - 生产环境 ^(端口8233^)
echo   不指定则交互式选择
echo.
echo JVM配置:
echo   内存: 最小512MB, 最大2GB
echo   垃圾回收: G1GC
echo   堆转储: 启用OOM时自动转储
echo.
echo 配置管理:
echo   编辑配置: notepad env\env-prod.bat
echo   配置文件: env\env-{dev^|test^|prod}.bat
echo.
echo 安全提醒:
echo   - 生产环境务必修改默认密码和密钥
echo   - 不要将配置文件提交到版本控制系统
echo   - 定期更换密码和Token密钥
echo.
echo 部署前请先打包: mvn clean compile package -P prod
exit /b 1

:end
endlocal

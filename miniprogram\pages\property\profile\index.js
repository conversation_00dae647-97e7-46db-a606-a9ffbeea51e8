// 物业个人中心页面
import { getStateManager } from '../../../utils/stateManager.js'
import { handlePropertyPageShow } from '../../../utils/pageUtils.js'

const stateManager = getStateManager()

Page({
  data: {
    title: '我的',
    userInfo: null,
    userType: '2'
  },

  onLoad() {
    console.log('物业个人中心页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, 3, this.loadUserInfo)
  },

  // 加载用户信息
  loadUserInfo() {
    const state = stateManager.getState()
    this.setData({
      userInfo: state.tokenUser || state.userInfo,
      userType: state.userType
    })
  },

  // 身份切换
  switchIdentity() {
    wx.showActionSheet({
      itemList: ['切换到业主身份'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // TODO: 实现身份切换逻辑
          console.log('切换到业主身份')
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          stateManager.clearState(true)
          wx.reLaunch({
            url: '/pages/login/index'
          })
        }
      }
    })
  }
})

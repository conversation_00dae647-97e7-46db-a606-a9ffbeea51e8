package com.ehome.common.utils;

import com.google.zxing .BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码生成工具类
 * 
 * <AUTHOR>
 */
public class QrCodeUtils {

    private static final int DEFAULT_WIDTH = 300;
    private static final int DEFAULT_HEIGHT = 300;
    private static final String DEFAULT_FORMAT = "PNG";

    /**
     * 生成二维码图片
     * 
     * @param content 二维码内容
     * @param width 图片宽度
     * @param height 图片高度
     * @return 二维码图片的字节数组
     * @throws WriterException
     * @throws IOException
     */
    public static byte[] generateQrCode(String content, int width, int height) throws WriterException, IOException {
        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.MARGIN, 1);

        // 生成二维码
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 创建图片
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }

        // 转换为字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, DEFAULT_FORMAT, outputStream);
        return outputStream.toByteArray();
    }

    /**
     * 生成二维码图片（使用默认尺寸）
     * 
     * @param content 二维码内容
     * @return 二维码图片的字节数组
     * @throws WriterException
     * @throws IOException
     */
    public static byte[] generateQrCode(String content) throws WriterException, IOException {
        return generateQrCode(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码并保存到文件
     *
     * @param content 二维码内容
     * @param filePath 文件路径
     * @param width 图片宽度
     * @param height 图片高度
     * @throws WriterException
     * @throws IOException
     */
    public static void generateQrCodeToFile(String content, String filePath, int width, int height)
            throws WriterException, IOException {
        // 设置二维码参数
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
        hints.put(EncodeHintType.MARGIN, 1);

        // 生成二维码
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

        // 创建图片
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? Color.BLACK.getRGB() : Color.WHITE.getRGB());
            }
        }

        // 保存到文件
        File file = new File(filePath);
        // 确保目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        ImageIO.write(image, DEFAULT_FORMAT, file);
    }

    /**
     * 生成二维码并保存到文件（使用默认尺寸）
     * 
     * @param content 二维码内容
     * @param filePath 文件路径
     * @throws WriterException
     * @throws IOException
     */
    public static void generateQrCodeToFile(String content, String filePath) throws WriterException, IOException {
        generateQrCodeToFile(content, filePath, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成带Logo的二维码
     * 
     * @param content 二维码内容
     * @param logoPath Logo图片路径
     * @param width 图片宽度
     * @param height 图片高度
     * @return 二维码图片的字节数组
     * @throws WriterException
     * @throws IOException
     */
    public static byte[] generateQrCodeWithLogo(String content, String logoPath, int width, int height) 
            throws WriterException, IOException {
        // 生成基础二维码
        byte[] qrCodeBytes = generateQrCode(content, width, height);
        
        // 如果没有Logo路径，直接返回基础二维码
        if (logoPath == null || logoPath.trim().isEmpty()) {
            return qrCodeBytes;
        }
        
        // TODO: 实现Logo叠加逻辑
        // 这里可以添加Logo叠加的实现
        
        return qrCodeBytes;
    }
}

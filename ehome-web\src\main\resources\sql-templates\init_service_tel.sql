-- 初始化小区服务电话数据
-- 使用方法：将 {community_id} 替换为实际的小区ID

-- ========== 外部服务电话 ==========

-- 1. 紧急服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '紧急服务', '', 1, '0', NOW());

-- 紧急服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '110', 'external', '报警电话', '公安部门', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '119', 'external', '火警电话', '消防部门', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '120', 'external', '急救电话', '医疗急救', 3, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '122', 'external', '交通事故', '交警部门', 4, '0', NOW());

-- 2. 公共服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '公共服务', '', 2, '0', NOW());

-- 公共服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '95598', 'external', '供电服务', '国家电网', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96777', 'external', '燃气服务', '燃气公司', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '96116', 'external', '供水服务', '自来水公司', 3, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '10086', 'external', '移动客服', '中国移动', 4, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '10010', 'external', '联通客服', '中国联通', 5, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '10000', 'external', '电信客服', '中国电信', 6, '0', NOW());

-- 3. 便民服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'external', '便民服务', '', 3, '0', NOW());

-- 便民服务子项
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '114', 'external', '查号台', '中国电信', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '12315', 'external', '消费者投诉', '工商部门', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '12345', 'external', '政府热线', '政府服务', 3, '0', NOW());

-- ========== 内部服务电话 ==========

-- 1. 物业服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '物业服务', '', 1, '0', NOW());

-- 物业服务子项（请根据实际情况修改电话号码）
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '8001', 'internal', '物业客服', '物业管理处', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8002', 'internal', '物业经理', '物业管理处', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8003', 'internal', '收费处', '物业管理处', 3, '0', NOW());

-- 2. 安保服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '安保服务', '', 2, '0', NOW());

-- 安保服务子项（请根据实际情况修改电话号码）
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '8010', 'internal', '门岗值班', '安保部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8011', 'internal', '巡逻队', '安保部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8012', 'internal', '监控中心', '安保部', 3, '0', NOW());

-- 3. 维修服务分类
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES (0, '{community_id}', '', 'internal', '维修服务', '', 3, '0', NOW());

-- 维修服务子项（请根据实际情况修改电话号码）
INSERT INTO eh_service_tel (parent_id, community_id, tel_number, tel_type, service_name, company_name, sort_order, status, create_time)
VALUES
(LAST_INSERT_ID(), '{community_id}', '8020', 'internal', '水电维修', '维修部', 1, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8021', 'internal', '电梯维修', '维修部', 2, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8022', 'internal', '绿化养护', '维修部', 3, '0', NOW()),
(LAST_INSERT_ID(), '{community_id}', '8023', 'internal', '保洁服务', '保洁部', 4, '0', NOW());

-- 注意：
-- 1. 内部电话号码（8001-8023）为示例，请根据实际小区情况修改
-- 2. 可以根据需要添加或删除服务项目
-- 3. sort_order 字段用于排序显示
-- 4. status 字段：'0'表示正常，'1'表示停用

package com.ehome.framework.config;

import com.ehome.framework.filter.RequestResponseLoggingFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CharacterEncodingFilter;

import javax.servlet.DispatcherType;

/**
 * 日志过滤器配置
 *
 * <AUTHOR>
 */
@Configuration
public class LoggingFilterConfig {

    @Autowired
    private RequestResponseLoggingFilter requestResponseLoggingFilter;

    /**
     * 注册字符编码过滤器
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Bean
    public FilterRegistrationBean characterEncodingFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter();
        characterEncodingFilter.setEncoding("UTF-8");
        characterEncodingFilter.setForceEncoding(true);

        registration.setFilter(characterEncodingFilter);
        registration.addUrlPatterns("/*");
        registration.setName("characterEncodingFilter");
        // 设置最高优先级，确保在其他过滤器之前执行
        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
        return registration;
    }

    /**
     * 注册HTTP请求响应日志过滤器
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Bean
    public FilterRegistrationBean requestResponseLoggingFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(requestResponseLoggingFilter);
        registration.addUrlPatterns("/*");
        registration.setName("requestResponseLoggingFilter");
        // 设置在字符编码过滤器之后执行
        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE + 10);
        return registration;
    }
}

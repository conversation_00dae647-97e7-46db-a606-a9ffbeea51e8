-- 为eh_service_tel表添加parent_id字段，支持二级目录功能
-- 用于实现服务电话的父子关系，0表示顶级分类

ALTER TABLE `eh_service_tel` 
ADD COLUMN `parent_id` varchar(32) DEFAULT '0' COMMENT '父级ID(0表示顶级分类)' 
AFTER `service_tel_id`;

-- 为parent_id字段添加索引，提高查询性能
ALTER TABLE `eh_service_tel` 
ADD INDEX `idx_parent_id` (`parent_id`);

-- 验证字段添加结果的查询语句
-- SELECT service_tel_id, service_name, parent_id, sort_order 
-- FROM eh_service_tel 
-- WHERE community_id = 'your_community_id' 
-- ORDER BY parent_id ASC, sort_order ASC;

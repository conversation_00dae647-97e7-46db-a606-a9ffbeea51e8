<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('团队成员管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>团队：</label>
                                <select name="team_id" id="teamSelect"><option value="">全部团队</option></select>
                            </li>
                            <li>
                                <label>昵称：</label>
                                <input type="text" name="nickname" placeholder="请输入昵称"/>
                            </li>
                            <li>
                                <label>手机号：</label>
                                <input type="text" name="phone" placeholder="请输入手机号"/>
                            </li>
                            <li>
                                <label>成员类型：</label>
                                <select name="member_type">
                                    <option value="">所有</option>
                                    <option value="1">已注册主管理员</option>
                                    <option value="2">已注册管理员</option>
                                    <option value="3">已注册普通成员</option>
                                    <option value="4">未注册普通成员</option>
                                    <option value="5">未注册管理员</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="syncMemberData()" shiro:hasPermission="oc:markicam:sync">
                    <i class="fa fa-refresh"></i> 同步成员数据
                </a>
                <a class="btn btn-info" onclick="syncByTeam()" shiro:hasPermission="oc:markicam:sync">
                    <i class="fa fa-users"></i> 按团队同步
                </a>
                <a class="btn btn-warning" onclick="exportData()" shiro:hasPermission="oc:markicam:export">
                    <i class="fa fa-download"></i> 导出数据
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 按团队同步模态框 -->
    <div class="modal fade" id="syncTeamModal" tabindex="-1" role="dialog" aria-labelledby="syncTeamModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="syncTeamModalLabel">按团队同步成员</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="syncTeamForm">
                        <div class="form-group">
                            <label for="teamId">团队ID <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="teamId" name="team_id" required>
                            <small class="form-text text-muted">请输入要同步的团队ID</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="doSyncByTeam()">开始同步</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";

        $(function() {
            var options = {
                url: prefix + "/member/list",
                createUrl: prefix + "/member/add",
                updateUrl: prefix + "/member/edit/{id}",
                removeUrl: prefix + "/member/remove",
                exportUrl: prefix + "/member/export",
                modalName: "团队成员",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'uid',
                    title: '用户ID',
                    width: 80
                },
                {
                    field: 'team_id',
                    title: '团队ID',
                    width: 100
                },
                {
                    field: 'team_name',
                    title: '团队名称',
                    width: 120
                },
                {
                    field: 'nickname',
                    title: '昵称',
                    width: 120
                },
                {
                    field: 'phone',
                    title: '手机号',
                    width: 120,
                    formatter: function(value, row, index) {
                        if (value && value.length > 7) {
                            return value.substring(0, 3) + '****' + value.substring(7);
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'member_type',
                    title: '成员类型',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        var typeMap = {
                            1: '<span class="badge badge-danger">已注册主管理员</span>',
                            2: '<span class="badge badge-warning">已注册管理员</span>',
                            3: '<span class="badge badge-success">已注册普通成员</span>',
                            4: '<span class="badge badge-secondary">未注册普通成员</span>',
                            5: '<span class="badge badge-info">未注册管理员</span>'
                        };
                        return typeMap[value] || value;
                    }
                },
                {
                    field: 'join_time_str',
                    title: '加入时间',
                    width: 150
                },
                {
                    field: 'sync_time',
                    title: '同步时间',
                    width: 150
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 100,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 如果URL带team_id参数，自动按团队筛选
            var params = new URLSearchParams(window.location.search);
            if (params.has('team_id')) {
                $("input[name='team_id']").val(params.get('team_id'));
                $.table.search();
            }

            // 加载团队下拉
            $.post(prefix + "/team/list", { pageNum: 1, pageSize: 1000 }, function(res){
                if (res.total && res.rows) {
                    var opts = ['<option value="">全部团队</option>'];
                    res.rows.forEach(function(r){ opts.push('<option value="'+ r.team_id +'">'+ (r.team_name || r.team_id) +'</option>'); });
                    $('#teamSelect').html(opts.join(''));

                    // 如果URL带team_id参数，自动按团队筛选
                    var params = new URLSearchParams(window.location.search);
                    if (params.has('team_id')) {
                        $("select[name='team_id']").val(params.get('team_id'));
                        $.table.search();
                    }
                }
            });

        });

        function syncMemberData() {
            $.modal.confirm("确定要同步所有团队的成员数据吗？", function() {
                $.modal.loading("正在同步数据...");
                $.post(prefix + "/sync/member", {}, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("同步成功");
                        $.table.refresh();
                    } else {
                        $.modal.alertError("同步失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("同步失败");
                });
            });
        }

        function syncByTeam() {
            $('#syncTeamForm')[0].reset();
            $('#syncTeamModal').modal('show');
        }

        function doSyncByTeam() {
            var teamId = $('#teamId').val();
            if (!teamId) {
                $.modal.alertWarning("请输入团队ID");
                return;
            }

            $('#syncTeamModal').modal('hide');
            $.modal.loading("正在同步团队成员数据...");
            $.post(prefix + "/sync/member", { team_id: teamId }, function(result) {
                $.modal.closeLoading();
                if (result.code == 0) {
                    $.modal.alertSuccess("同步成功");
                    $.table.refresh();
                } else {
                    $.modal.alertError("同步失败：" + result.msg);
                }
            }).fail(function() {
                $.modal.closeLoading();
                $.modal.alertError("同步失败");
            });
        }

        function viewDetail(id) {
            $.get(prefix + "/member/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>用户ID:</strong> ' + (data.uid || '-') + '</p>';
                    content += '<p><strong>团队ID:</strong> ' + (data.team_id || '-') + '</p>';
                    content += '<p><strong>团队名称:</strong> ' + (data.team_name || '-') + '</p>';
                    content += '<p><strong>昵称:</strong> ' + (data.nickname || '-') + '</p>';
                    content += '<p><strong>手机号:</strong> ' + (data.phone || '-') + '</p>';
                    content += '</div>';
                    content += '<div class="col-md-6">';

                    var memberTypeText = '';
                    switch(data.member_type) {
                        case 1: memberTypeText = '已注册主管理员'; break;
                        case 2: memberTypeText = '已注册管理员'; break;
                        case 3: memberTypeText = '已注册普通成员'; break;
                        case 4: memberTypeText = '未注册普通成员'; break;
                        case 5: memberTypeText = '未注册管理员'; break;
                        default: memberTypeText = data.member_type;
                    }
                    content += '<p><strong>成员类型:</strong> ' + memberTypeText + '</p>';
                    content += '<p><strong>加入时间:</strong> ' + (data.join_time_str || '-') + '</p>';
                    content += '<p><strong>同步时间:</strong> ' + (data.sync_time || '-') + '</p>';
                    content += '<p><strong>创建时间:</strong> ' + (data.created_at || '-') + '</p>';
                    content += '<p><strong>更新时间:</strong> ' + (data.updated_at || '-') + '</p>';
                    content += '</div>';
                    content += '</div>';

                    $.modal.open("成员详情", content);
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function exportData() {
            $.modal.confirm("确定要导出成员数据吗？", function() {
                var formData = $("#formId").serialize();
                window.location.href = prefix + "/member/export?" + formData;
            });
        }
    </script>
</body>
</html>

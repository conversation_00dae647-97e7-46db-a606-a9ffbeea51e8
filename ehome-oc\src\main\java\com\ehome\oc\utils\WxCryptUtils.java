package com.ehome.oc.utils;

import com.ehome.common.utils.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 微信数据解密工具类
 * 统一处理微信小程序的数据解密逻辑
 * 
 * <AUTHOR>
 */
public class WxCryptUtils {
    
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final String KEY_ALGORITHM = "AES";
    
    /**
     * 解密微信数据
     * 
     * @param sessionKey 会话密钥
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @return 解密后的数据
     * @throws Exception 解密异常
     */
    public static String decrypt(String sessionKey, String encryptedData, String iv) throws Exception {
        if (StringUtils.isEmpty(sessionKey) || StringUtils.isEmpty(encryptedData) || StringUtils.isEmpty(iv)) {
            throw new IllegalArgumentException("解密参数不能为空");
        }
        
        try {
            byte[] keyBytes = Base64.getDecoder().decode(sessionKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedData);

            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decrypted = cipher.doFinal(encryptedBytes);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new Exception("微信数据解密失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 安全地解密微信数据，捕获异常并返回null
     * 
     * @param sessionKey 会话密钥
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @return 解密后的数据，失败时返回null
     */
    public static String decryptSafely(String sessionKey, String encryptedData, String iv) {
        try {
            return decrypt(sessionKey, encryptedData, iv);
        } catch (Exception e) {
            // 记录日志但不抛出异常
            System.err.println("微信数据解密失败: " + e.getMessage());
            return null;
        }
    }
}

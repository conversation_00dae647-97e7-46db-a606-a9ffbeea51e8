<view class="profile-container">
  <view class="header">
    <view class="user-info">
      <view class="avatar">
        <text>物</text>
      </view>
      <view class="user-details">
        <view class="username">{{userInfo.username || '物业用户'}}</view>
        <view class="user-type">物业人员</view>
      </view>
    </view>
  </view>
  
  <view class="menu-section">
    <view class="menu-item" bindtap="switchIdentity">
      <text class="menu-text">身份切换</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item">
      <text class="menu-text">设置</text>
      <text class="menu-arrow">></text>
    </view>
    
    <view class="menu-item">
      <text class="menu-text">帮助与反馈</text>
      <text class="menu-arrow">></text>
    </view>
  </view>
  
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>

# 附件关联功能测试指南

## 测试前准备

### 1. 执行数据库迁移
```sql
-- 执行以下SQL添加业务关联字段
ALTER TABLE `eh_file_info` 
ADD COLUMN `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型(bx-报修,complaint-投诉建议)' AFTER `upload_user`;

ALTER TABLE `eh_file_info` 
ADD COLUMN `business_id` varchar(32) DEFAULT NULL COMMENT '业务ID' AFTER `business_type`;

ALTER TABLE `eh_file_info` 
ADD INDEX `idx_business`(`business_type`, `business_id`) USING BTREE;
```

### 2. 重启应用
确保前后端代码更新生效

## 测试步骤

### 测试1：报修功能附件关联

1. **打开报修页面**
   - 进入小程序报修页面
   - 检查页面是否正常加载

2. **上传文件**
   - 选择1-2个图片文件上传
   - 观察上传过程是否正常
   - 检查上传成功提示

3. **填写报修信息**
   - 选择报修类型
   - 填写报修内容
   - 填写地址等信息

4. **提交报修**
   - 点击提交按钮
   - 观察提交是否成功
   - 检查成功提示

5. **数据库验证**
   ```sql
   -- 查看报修记录
   SELECT * FROM eh_wx_bx ORDER BY create_time DESC LIMIT 1;
   
   -- 查看关联的文件记录
   SELECT * FROM eh_file_info 
   WHERE business_type = 'bx' 
   AND business_id = '刚才创建的报修记录ID'
   ORDER BY create_time DESC;
   ```

### 测试2：投诉建议功能附件关联

1. **打开投诉建议页面**
   - 进入小程序投诉建议页面
   - 检查页面是否正常加载

2. **上传文件**
   - 选择1-2个图片文件上传
   - 观察上传过程是否正常
   - 检查上传成功提示

3. **填写投诉信息**
   - 选择投诉类型
   - 填写投诉内容
   - 填写地址等信息

4. **提交投诉**
   - 点击提交按钮
   - 观察提交是否成功
   - 检查成功提示

5. **数据库验证**
   ```sql
   -- 查看投诉记录
   SELECT * FROM eh_wx_complaint ORDER BY create_time DESC LIMIT 1;
   
   -- 查看关联的文件记录
   SELECT * FROM eh_file_info 
   WHERE business_type = 'complaint' 
   AND business_id = '刚才创建的投诉记录ID'
   ORDER BY create_time DESC;
   ```

### 测试3：附件查询接口

1. **测试报修附件查询**
   ```javascript
   // 在小程序中调用
   const result = await app.request({
     url: '/api/wx/bx/getAttachments',
     method: 'POST',
     data: { businessId: '报修记录ID' }
   })
   console.log('报修附件:', result.data)
   ```

2. **测试投诉附件查询**
   ```javascript
   // 在小程序中调用
   const result = await app.request({
     url: '/api/wx/complaint/getAttachments',
     method: 'POST',
     data: { businessId: '投诉记录ID' }
   })
   console.log('投诉附件:', result.data)
   ```

## 预期结果

### 1. 文件上传
- 文件成功上传到服务器
- `eh_file_info` 表中创建文件记录
- 文件记录包含正确的 `business_type` 和临时 `business_id`
- 前端收到包含 `fileId` 的响应

### 2. 业务数据提交
- 业务记录成功创建（eh_wx_bx 或 eh_wx_complaint）
- `media_urls` 字段包含完整的文件信息（包括fileId）
- 文件记录的 `business_id` 更新为正式的业务记录ID

### 3. 数据关联验证
```sql
-- 应该能查到关联的文件
SELECT 
    b.id as business_id,
    b.type,
    b.content,
    f.file_id,
    f.original_name,
    f.file_url,
    f.business_type,
    f.business_id as file_business_id
FROM eh_wx_bx b
LEFT JOIN eh_file_info f ON b.id = f.business_id AND f.business_type = 'bx'
WHERE b.id = '业务记录ID';
```

## 常见问题排查

### 1. 文件上传失败
- 检查文件大小是否超限
- 检查文件类型是否支持
- 检查服务器磁盘空间
- 查看后端日志

### 2. 文件关联失败
- 检查 `media_urls` 字段是否包含 `fileId`
- 查看后端日志中的文件关联更新信息
- 检查数据库表结构是否正确

### 3. 查询接口异常
- 检查业务ID是否正确
- 验证数据库中的关联数据
- 查看接口返回的错误信息

## 成功标志

✅ 文件上传成功并保存到数据库  
✅ 业务数据提交成功  
✅ 文件与业务数据建立正确关联  
✅ 可以通过业务ID查询到相关附件  
✅ `media_urls` 字段和数据库关联双重保障生效

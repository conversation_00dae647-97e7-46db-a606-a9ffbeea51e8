# 文件上传Controller优化完成

## 任务背景

用户反馈两个优化需求：
1. 两个controller获取绝对路径太复杂了，直接file对象得到岂不是更好
2. 默认情况如果无论是否开启oss上传，都要上传到本地，就是both类型
3. last_download_time 应该使用dateUtils.getTime()
4. file_size 后面还要加个file_size_str 100K 20M 100G 动态识别

## 实施内容

### 1. 简化绝对路径获取逻辑

#### 1.1 修改FileUploadResult类
- **文件**：`ehome-admin/src/main/java/com/ehome/admin/domain/FileUploadResult.java`
- **修改内容**：
  - 添加`absolutePath`字段
  - 修改构造函数包含absolutePath参数
  - 添加getter/setter方法

#### 1.2 优化FileController
- **文件**：`ehome-admin/src/main/java/com/ehome/admin/controller/file/FileController.java`
- **修改内容**：
  - 使用`FileUploadUtils.getAbsoluteFile()`直接获取File对象
  - 调用`file.getAbsolutePath()`获取绝对路径
  - 移除复杂的字符串拼接逻辑
  - 在`saveFileInfoToDatabase`方法中直接使用`result.getAbsolutePath()`

#### 1.3 优化WxFileController
- **文件**：`ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxFileController.java`
- **修改内容**：
  - 应用相同的绝对路径获取优化
  - 修改`saveWxFileInfoToDatabase`方法签名，直接传入absolutePath参数

### 2. 调整存储类型默认逻辑

#### 2.1 统一存储类型逻辑
- **默认行为**：总是先上传到本地（确保本地文件存在）
- **存储类型设置**：
  - 默认`storageType = "local"`
  - OSS上传成功后改为`storageType = "both"`
- **优势**：确保文件总是有本地备份，提高可靠性

### 3. 时间格式优化

#### 3.1 使用DateUtils.getTime()
- **修改位置**：
  - `FileController.updateDownloadStats()`方法
  - `WxFileController.updateWxDownloadStats()`方法
- **变更**：将`DateUtils.dateTimeNow()`改为`DateUtils.getTime()`
- **格式**：统一使用`yyyy-MM-dd HH:mm:ss`格式

### 4. 文件大小格式化

#### 4.1 添加formatFileSize工具方法
- **文件**：`ehome-common/src/main/java/com/ehome/common/utils/file/FileUtils.java`
- **方法**：`formatFileSize(long size)`
- **功能**：将字节数转换为可读的文件大小格式（B、KB、MB、GB、TB）

#### 4.2 数据库结构调整
- **表名**：`eh_file_info`
- **新增字段**：`file_size_str VARCHAR(20) DEFAULT '' COMMENT '格式化的文件大小'`
- **位置**：在`file_size`字段后面

#### 4.3 更新文件上传逻辑
- **FileController**：在保存文件信息时添加`file_size_str`字段
- **WxFileController**：同样添加格式化文件大小字段
- **数据更新**：为现有112条记录更新了file_size_str字段

### 5. 修复附件下载链接问题

#### 5.1 修复notice/view.html
- **问题**：jsrender模板中`[[${ctx}]]`无法正确解析
- **解决方案**：
  - 将`[[${ctx}]]/common/download/{{:file_id}}`改为`{{:~getDownloadUrl(file_id)}}`
  - 在JavaScript中添加`getDownloadUrl`函数：`ctx + "common/download/" + fileId`

## 技术要点

1. **兼容性处理**：保持原有逻辑不变，新增优化功能
2. **异常处理**：数据库操作失败不影响文件上传的主流程
3. **代码简化**：移除复杂的字符串处理，使用File对象直接获取路径
4. **数据一致性**：统一时间格式和文件大小显示格式

## 测试建议

1. 测试文件上传是否正确保存绝对路径和格式化文件大小
2. 测试存储类型是否按预期设置（默认both类型）
3. 测试文件下载统计是否使用正确的时间格式
4. 测试附件下载链接是否正常工作
5. 测试异常情况下的容错处理

## 完成状态

✅ FileUploadResult类修改完成
✅ FileController绝对路径优化完成
✅ WxFileController绝对路径优化完成
✅ 存储类型逻辑调整完成
✅ 时间格式优化完成
✅ 文件大小格式化功能完成
✅ 数据库结构调整完成
✅ 现有数据更新完成
✅ 附件下载链接修复完成

## 相关文件

- `ehome-admin/src/main/java/com/ehome/admin/domain/FileUploadResult.java`
- `ehome-admin/src/main/java/com/ehome/admin/controller/file/FileController.java`
- `ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxFileController.java`
- `ehome-common/src/main/java/com/ehome/common/utils/file/FileUtils.java`
- `ehome-page/src/main/resources/templates/system/notice/view.html`
- `scripts/update_file_size_str.sql`

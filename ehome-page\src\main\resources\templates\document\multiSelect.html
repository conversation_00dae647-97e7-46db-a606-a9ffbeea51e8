<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('选择公共文档库文件')" />
    <th:block th:include="include :: layout-latest-css" />
    <th:block th:include="include :: ztree-css" />
    <style type="text/css">
        #treeSearchInput{width: 150px;}
        .selected-files-info {
            max-height: 100px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            background-color: #f9f9f9;
        }
        .selected-file-item {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            background-color: #d9edf7;
            border: 1px solid #bce8f1;
            border-radius: 3px;
            font-size: 12px;
        }
        .selected-file-item .remove-btn {
            margin-left: 5px;
            color: #31708f;
            cursor: pointer;
        }
        .selected-file-item .remove-btn:hover {
            color: #245269;
        }
        /* 表格行选择样式 */
        .bootstrap-table .fixed-table-body .table tbody tr.selected {
            background-color: #d9edf7 !important;
        }
        .bootstrap-table .fixed-table-body .table tbody tr:hover {
            background-color: #f5f5f5 !important;
            cursor: pointer;
        }
        .footer-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: #f8f9fa;
            border-top: 1px solid #e5e5e5;
            display: flex;
            justify-content: center;
            gap: 12px;
            z-index: 1000;
        }
        .footer-actions .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .footer-actions .btn-primary {
            background: #007bff;
            color: white;
        }
        .footer-actions .btn-primary:hover {
            background: #0056b3;
        }
        .footer-actions .btn-primary:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .footer-actions .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .footer-actions .btn-secondary:hover {
            background: #545b62;
        }
        /* 为页面内容添加底部边距，避免被固定按钮遮挡 */
        .ui-layout-center {
            padding-bottom: 80px;
        }
        /* 左侧树容器间距 */
        .ui-layout-west {
            padding: 10px;
        }
        .ui-layout-west .box {
            margin: 0;
        }
    </style>
</head>
<body class="gray-bg">   
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa fa-folder-open"></i> 文档分类
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新目录"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div style="padding: 0px;padding-bottom: 5px;">
                    <div class="input-group" style="margin-bottom: 0px;">
                        <input type="text" id="treeSearchInput" placeholder="搜索文件夹" class="form-control form-control-sm" onkeypress="if(event.keyCode==13) searchTree()"/>
                        <span class="input-group-btn">
                            <button class="btn btn-sm btn-default" type="button" onclick="searchTree()"><i class="fa fa-search"></i></button>
                            <button class="btn btn-sm btn-default" type="button" onclick="clearTreeSearch()"><i class="fa fa-times"></i></button>
                        </span>
                    </div>
                </div>
                <div id="folderTree" class="ztree"></div>
            </div>
        </div>
    </div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row" style="margin-left: -20px;">
                <!-- 搜索区域 -->
                <div class="col-sm-12 search-collapse">
                    <form id="formId">
                        <input type="hidden" name="folderId" id="folderId"/>
                        <input type="hidden" name="fileType" id="fileType" th:value="${fileType != null ? fileType : ''}"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label>当前位置：</label>
                                    <input type="text" name="currentPath" placeholder="根目录" readonly style="background-color: #f5f5f5;"/>
                                </li>
                                <li>
                                    <label>文件名：</label>
                                    <input type="text" name="fileName" placeholder="请输入文件名" onkeypress="if(event.keyCode==13) $.table.search()"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm ml-10" onclick="resetSearch()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <!-- 工具栏和表格区域 -->
                <div class="btn-group-sm" id="toolbar" role="group" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: nowrap;">
                    <div style="flex: 1; min-width: 0;">
                        <span class="text-info">
                            <i class="fa fa-info-circle"></i>
                            <span th:if="${fileType != null and fileType != ''}">
                                当前筛选文件类型：<strong th:text="${fileType}"></strong>
                            </span>
                            <span th:if="${fileType == null or fileType == ''}">
                                显示所有文件类型
                            </span>
                            &nbsp;&nbsp;
                            <span class="text-muted">可多选文件，点击行选择/取消选择</span>
                        </span>
                    </div>

                </div>
                <div class="col-sm-12 select-table table-striped">
                    <!-- 已选择文件信息 -->
                    <div style="padding: 8px 0; margin-bottom: 10px; text-align: left; border-bottom: 1px solid #e5e5e5;">
                        <label class="control-label">已选择文件：</label>
                        <span id="selectedFilesInfo" style="margin-left: 10px;">
                            <span class="text-muted">尚未选择任何文件</span>
                        </span>
                    </div>
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer-actions">
        <button type="button" class="btn btn-primary" onclick="confirmSelection()" disabled id="confirmBtn">
            <i class="fa fa-check"></i> 确定选择 (<span id="selectedCount">0</span>)
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeDialog()">
            <i class="fa fa-close"></i> 取消
        </button>
    </div>



    <th:block th:include="include :: footer" />
    <th:block th:include="include :: layout-latest-js" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        var prefix = ctx + "document";
        var currentFolderId = "";
        var currentFolderName = "根目录";
        var selectedFiles = [];
        var fileTypeFilter = /*[[${fileType}]]*/ '';

        $(function() {
            var panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({ initClosed: panehHidden, west__size: 250, resizeWithWindow: false });

            loadFolderTree();
            
            var options = {
                url: prefix + "/fileList",
                modalName: "文件",
                limit: 20,
                sidePagination: "server",
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                clickToSelect: false,
                singleSelect: false,
                onCheck: function(row, $element) {
                    toggleFileSelection(row, true);
                },
                onUncheck: function(row, $element) {
                    toggleFileSelection(row, false);
                },
                onClickRow: function(row, $element) {
                    var index = $element.data('index');
                    var isChecked = $element.hasClass('selected'); // bootstrap-table 会给选中行加类
                    if (isChecked) {
                        $('#bootstrap-table').bootstrapTable('uncheck', index);
                    } else {
                        $('#bootstrap-table').bootstrapTable('check', index);
                    }
                },
                columns: [{
                    checkbox: true
                },
                {
                    field: 'file_id',
                    title: '文件ID',
                    visible: false
                },
                {
                    field: 'original_name',
                    title: '文件名',
                    formatter: function(value, row, index) {
                        return '<span class="file-name" title="' + value + '">' + value + '</span>';
                    }
                },
                {
                    field: 'file_type',
                    title: '文件类型',
                    formatter: function(value, row, index) {
                        return value ? value.toUpperCase() : '-';
                    }
                },
                {
                    field: 'file_size',
                    title: '文件大小',
                    formatter: function(value, row, index) {
                        return formatFileSize(value);
                    }
                }]
            };
            $.table.init(options);
        });

        // 切换文件选择状态
        function toggleFileSelection(row, isSelected) {
            var fileId = row.file_id;
            var existingIndex = selectedFiles.findIndex(function(file) {
                return file.fileId === fileId;
            });

            if (isSelected === undefined) {
                // 自动切换模式
                isSelected = existingIndex < 0;
            }

            if (isSelected && existingIndex < 0) {
                // 添加文件
                var fileObj = {
                    fileId: row.file_id,
                    fileName: row.original_name,
                    fileType: row.file_type,
                    fileSize: row.file_size,
                    fileUrl: row.access_url || '',
                    mimeType: row.mime_type || '',
                    isUpload: false
                };
                selectedFiles.push(fileObj);
            } else if (!isSelected && existingIndex >= 0) {
                // 移除文件
                selectedFiles.splice(existingIndex, 1);
            }

            updateSelectedFilesDisplay();
        }

        // 更新已选择文件显示
        function updateSelectedFilesDisplay() {
            var $container = $('#selectedFilesInfo');
            var $countSpan = $('#selectedCount');

            $countSpan.text(selectedFiles.length);

            if (selectedFiles.length === 0) {
                $container.html('<span class="text-muted">尚未选择任何文件</span>');
                $('#confirmBtn').prop('disabled', true);
            } else {
                var fileNames = selectedFiles.map(function(file) {
                    return file.fileName;
                });
                var displayText = fileNames.join(', ');
                if (displayText.length > 100) {
                    displayText = displayText.substring(0, 97) + '...';
                }
                $container.html('<span class="text-info">' + displayText + '</span>');
                $('#confirmBtn').prop('disabled', false);
            }
        }



        // 确认选择
        function confirmSelection() {
            if (selectedFiles.length === 0) {
                $.modal.alertWarning("请先选择文件");
                return;
            }
            
            // 调用父页面的回调函数
            if (parent && parent.setSelectedAttachments) {
                parent.setSelectedAttachments(selectedFiles);
            }
            
            closeDialog();
        }

        // 关闭对话框
        function closeDialog() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            var k = 1024;
            var sizes = ['B', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 重置搜索
        function resetSearch() {
            $("#formId")[0].reset();
            $("#folderId").val(currentFolderId);
            $("#fileType").val(fileTypeFilter);
            $("input[name='currentPath']").val(currentFolderName);
            $.table.search();
        }

        // 加载文件夹树
        function loadFolderTree() {
            $.ajax({
                url: prefix + "/folderTree",
                type: "POST",
                success: function(res) {
                    if (res.code == 0) {
                        var setting = {
                            data: {
                                simpleData: {
                                    enable: true,
                                    idKey: "id",
                                    pIdKey: "pId",
                                    rootPId: null
                                }
                            },
                            view: {
                                showIcon: true,
                                selectedMulti: false
                            },
                            callback: {
                                onClick: function(event, treeId, treeNode) {
                                    currentFolderId = treeNode.id;
                                    currentFolderName = treeNode.name;
                                    $("#folderId").val(currentFolderId);
                                    $("input[name='currentPath']").val(getFolderPath(treeNode));
                                    $.table.search();
                                }
                            }
                        };

                        // 添加根目录节点
                        var treeData = [{
                            id: "",
                            pId: null,
                            name: "根目录",
                            type: "folder",
                            open: true
                        }];

                        if (res.data && res.data.length > 0) {
                            // 设置所有节点默认展开
                            res.data.forEach(function(node) {
                                node.open = true;
                            });
                            treeData = treeData.concat(res.data);
                        }

                        var treeObj = $.fn.zTree.init($("#folderTree"), setting, treeData);
                        // 展开所有节点
                        treeObj.expandAll(true);
                    } else {
                        $.modal.alertError("加载文件夹树失败：" + res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.alertError("加载文件夹树失败：" + error);
                }
            });
        }

        // 获取文件夹路径
        function getFolderPath(treeNode) {
            if (!treeNode || treeNode.id === "") {
                return "根目录";
            }

            var path = treeNode.name;
            var parent = treeNode.getParentNode();
            while (parent && parent.id !== "") {
                path = parent.name + " / " + path;
                parent = parent.getParentNode();
            }
            return "根目录 / " + path;
        }

        // 展开所有节点
        $('#btnExpand').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            treeObj.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        // 折叠所有节点
        $('#btnCollapse').click(function() {
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            treeObj.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        // 刷新树
        $('#btnRefresh').click(function() {
            loadFolderTree();
        });

        // 搜索树节点
        function searchTree() {
            var keyword = $("#treeSearchInput").val();
            var treeObj = $.fn.zTree.getZTreeObj("folderTree");
            if (!treeObj) return;

            if (keyword) {
                var allNodes = treeObj.getNodes();
                var nodesToShow = [];

                function searchNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        var node = nodes[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            nodesToShow.push(node);
                            var parent = node.getParentNode();
                            while (parent) {
                                if (nodesToShow.indexOf(parent) === -1) {
                                    nodesToShow.push(parent);
                                }
                                parent = parent.getParentNode();
                            }
                        }
                        if (node.children && node.children.length > 0) {
                            searchNodes(node.children);
                        }
                    }
                }

                searchNodes(allNodes);

                if (nodesToShow.length > 0) {
                    function hideAllNodes(nodes) {
                        for (var i = 0; i < nodes.length; i++) {
                            treeObj.hideNode(nodes[i]);
                            if (nodes[i].children && nodes[i].children.length > 0) {
                                hideAllNodes(nodes[i].children);
                            }
                        }
                    }
                    hideAllNodes(allNodes);

                    for (var i = 0; i < nodesToShow.length; i++) {
                        treeObj.showNode(nodesToShow[i]);
                    }

                    var firstMatchNode = null;
                    for (var i = 0; i < nodesToShow.length; i++) {
                        var node = nodesToShow[i];
                        if (node.name && node.name.indexOf(keyword) !== -1) {
                            if (!firstMatchNode) {
                                firstMatchNode = node;
                            }
                            var parent = node.getParentNode();
                            while (parent) {
                                treeObj.expandNode(parent, true, false, false);
                                parent = parent.getParentNode();
                            }
                        }
                    }

                    if (firstMatchNode) {
                        treeObj.selectNode(firstMatchNode, true);
                    }
                } else {
                    $.modal.alertWarning("未找到匹配的文件夹");
                }
            } else {
                var allNodes = treeObj.getNodes();
                function showAllNodes(nodes) {
                    for (var i = 0; i < nodes.length; i++) {
                        treeObj.showNode(nodes[i]);
                        if (nodes[i].children && nodes[i].children.length > 0) {
                            showAllNodes(nodes[i].children);
                        }
                    }
                }
                showAllNodes(allNodes);
                treeObj.expandAll(false);
            }
        }

        // 清空树搜索
        function clearTreeSearch() {
            $("#treeSearchInput").val("");
            searchTree();
        }
    </script>
</body>
</html>

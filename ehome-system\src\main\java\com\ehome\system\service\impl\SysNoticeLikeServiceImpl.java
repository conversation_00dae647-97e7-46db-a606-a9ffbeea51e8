package com.ehome.system.service.impl;

import com.ehome.common.core.text.Convert;
import com.ehome.system.domain.SysNoticeLike;
import com.ehome.system.mapper.SysNoticeLikeMapper;
import com.ehome.system.service.ISysNoticeLikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公告点赞记录 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysNoticeLikeServiceImpl implements ISysNoticeLikeService
{
    @Autowired
    private SysNoticeLikeMapper likeMapper;

    /**
     * 查询点赞记录信息
     * 
     * @param likeId 点赞记录ID
     * @return 点赞记录信息
     */
    @Override
    public SysNoticeLike selectLikeById(Long likeId)
    {
        return likeMapper.selectLikeById(likeId);
    }

    /**
     * 查询点赞记录列表
     * 
     * @param like 点赞记录信息
     * @return 点赞记录集合
     */
    @Override
    public List<SysNoticeLike> selectLikeList(SysNoticeLike like)
    {
        return likeMapper.selectLikeList(like);
    }

    /**
     * 根据公告ID查询点赞记录列表
     * 
     * @param noticeId 公告ID
     * @return 点赞记录集合
     */
    @Override
    public List<SysNoticeLike> selectLikeByNoticeId(Long noticeId)
    {
        return likeMapper.selectLikeByNoticeId(noticeId);
    }

    /**
     * 查询指定小区所有公告的点赞记录列表（包含公告标题）
     * 
     * @param like 点赞记录信息（包含小区ID等查询条件）
     * @return 点赞记录集合
     */
    @Override
    public List<SysNoticeLike> selectAllLikesByCommunity(SysNoticeLike like)
    {
        return likeMapper.selectAllLikesByCommunity(like);
    }

    /**
     * 新增点赞记录
     * 
     * @param like 点赞记录信息
     * @return 结果
     */
    @Override
    public int insertLike(SysNoticeLike like)
    {
        return likeMapper.insertLike(like);
    }

    /**
     * 修改点赞记录
     * 
     * @param like 点赞记录信息
     * @return 结果
     */
    @Override
    public int updateLike(SysNoticeLike like)
    {
        return likeMapper.updateLike(like);
    }

    /**
     * 删除点赞记录信息
     * 
     * @param likeIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteLikeByIds(String likeIds)
    {
        return likeMapper.deleteLikeByIds(Convert.toStrArray(likeIds));
    }

    /**
     * 根据公告ID删除点赞记录
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteLikeByNoticeId(Long noticeId)
    {
        return likeMapper.deleteLikeByNoticeId(noticeId);
    }
}

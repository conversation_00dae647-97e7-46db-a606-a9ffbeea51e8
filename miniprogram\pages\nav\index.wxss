/**nav.wxss**/
page {
  background: #f7f8fa;
}

/* 页面loading状态 */
.page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f8fa;
  z-index: 9999;
}

.container {
  padding: 24rpx 24rpx;
  min-height: calc(100vh - 80rpx);
  box-sizing: border-box;
}

/* 菜单分类容器 */
.menu-sections {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
  min-height: calc(100vh - 60rpx);
  box-sizing: border-box;
}

/* 分类区域 */
.category-section {
  margin-bottom: 20rpx;
}

.category-section:last-child {
  margin-bottom: 30rpx;
}

/* 分类标题 */
.category-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  padding-left: 20rpx;
  position: relative;
}

.category-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #1890ff, #69c0ff);
  border-radius: 4rpx;
}

/* 功能网格样式 */
.function-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0 20rpx;
}

/* 确保每行4个项目的布局 */
.function-grid::after {
  content: '';
  flex: auto;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(25% - 16rpx);
  height: 140rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  margin-bottom: 40rpx;
  margin-right: 20rpx;
}

/* 每行第4个项目不需要右边距 */
.grid-item:nth-child(4n) {
  margin-right: 0;
}

.grid-item:active {
  background: rgba(24,144,255,0.05);
  transform: scale(0.92);
}

.grid-item:hover {
  transform: translateY(-4rpx);
}

.grid-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.12);
  transition: all 0.3s ease;
}

.grid-item:active .grid-icon {
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
}

.grid-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 24rpx;
}

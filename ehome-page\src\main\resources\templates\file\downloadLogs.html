<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: saHeader('文件下载记录')" />
</head>
<body>

<div class="vue-box" style="display: none;" :style="'display: block;'">
    <div class="c-panel">
        <div class="c-title">文件下载记录</div>
        <el-form>
            <div class="c-item">
                <label class="c-label">用户名称：</label>
                <el-input v-model="p.userName" placeholder="模糊查询用户名"></el-input>
            </div>
            <div class="c-item">
                <label class="c-label">下载时间：</label>
                <el-date-picker v-model="p.downloadTimeStart" type="date" value-format="yyyy-MM-dd" placeholder="开始日期"></el-date-picker> -
                <el-date-picker v-model="p.downloadTimeEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期"></el-date-picker>
            </div>
            <div class="c-item" style="min-width: 0px;">
                <el-button type="primary" icon="el-icon-search" @click="p.pageNum = 1; f5()">查询</el-button>
            </div>
        </el-form>
        <!-- ------------- 快捷按钮 ------------- -->
        <div class="fast-btn">
            <el-button type="success" icon="el-icon-view" @click="getBySelect()">查看详情</el-button>
            <el-button type="warning" icon="el-icon-download" @click="sa.exportExcel()">导出</el-button>
            <el-button type="info"  icon="el-icon-refresh"  @click="f5()">刷新</el-button>
        </div>
        <!-- ------------- 数据列表 ------------- -->
        <el-table class="data-table" ref="data-table" :data="dataList" size="small">
            <el-table-column type="selection" width="45px"></el-table-column>
            <el-table-column label="日志ID" prop="log_id" width="80px"></el-table-column>
            <el-table-column label="文件名" prop="file_name" width="200px" show-overflow-tooltip></el-table-column>
            <el-table-column label="用户名" prop="user_name" width="120px"></el-table-column>
            <el-table-column label="用户类型" prop="user_type_name" width="100px"></el-table-column>
            <el-table-column label="下载IP" prop="download_ip" width="120px"></el-table-column>
            <el-table-column label="房屋名称" prop="house_name" width="150px" show-overflow-tooltip></el-table-column>
            <el-table-column label="下载时间" prop="download_time" width="160px"></el-table-column>
            <el-table-column label="操作" width="100px">
                <template slot-scope="s">
                    <el-button class="c-btn" type="success" icon="el-icon-view" @click="get(s.row)">详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- ------------- 分页 ------------- -->
        <div class="page-box">
            <el-pagination background
                           layout="total, prev, pager, next, sizes, jumper"
                           :current-page.sync="p.pageNum"
                           :page-size.sync="p.pageSize"
                           :total="dataCount"
                           :page-sizes="[10, 20, 30, 40, 50, 100]"
                           @current-change="f5(true)"
                           @size-change="f5(true)">
            </el-pagination>
        </div>
    </div>
</div>

<th:block th:include="include :: saFooter" />
<script type="text/javascript">
    var app = new Vue({
        el: '.vue-box',
        data: {
            p: {
                userName: '',
                downloadTimeStart: '',
                downloadTimeEnd: '',
                fileId: '[[${fileId}]]' || '',
                pageNum: 1,
                pageSize: 10,
            },
            dataCount: 0,
            dataList: [],
            fileName: '[[${fileName}]]' || ''
        },
        methods: {
            f5: function() {
                sa.ajax('/file/downloadLogsList', this.p, function(res){
                    this.dataList = res.rows;
                    this.dataCount = res.total;
                    sa.f5TableHeight();
                }.bind(this));
            },
            get: function(data) {
                var str = '<div>';
                str += '<p>日志ID：' + data.log_id + '</p>';
                str += '<p>文件名：' + (data.file_name || '未知') + '</p>';
                str += '<p>用户名：' + (data.user_name || '未知') + '</p>';
                str += '<p>用户类型：' + (data.user_type_name || '未知') + '</p>';
                str += '<p>下载IP：' + (data.download_ip || '未知') + '</p>';
                str += '<p>房屋名称：' + (data.house_name || '无') + '</p>';
                str += '<p>下载时间：' + (data.download_time || '未知') + '</p>';
                str += '</div>';
                sa.alert(str);
            },
            getBySelect: function(data) {
                var selection = this.$refs['data-table'].selection;
                if(selection.length == 0) {
                    return sa.msg('请选择一条数据')
                }
                this.get(selection[0]);
            },
        },
        created: function(){
            if(this.fileName) {
                document.title = '文件下载记录 - ' + this.fileName;
            }
            this.f5();
            sa.onInputEnter();
        }
    })

</script>
</body>
</html>

# 留言功能优化

## 任务描述
1. 将留言的发送按钮调整到最右边位置
2. 实现表情选择功能，提供常用emoji表情

## 实现内容

### 1. 按钮布局调整
- 修改 `floating-input-tools` 样式，添加 `flex: 1` 确保工具栏占据剩余空间
- 发送按钮自然位于最右边

### 2. 表情选择功能
- 添加了64个常用emoji表情
- 点击表情图标显示/隐藏表情面板
- 点击表情直接插入到输入框末尾
- 选择表情后自动关闭面板

### 3. 修改的文件

#### miniprogram/pages/notice/detail.wxml
- 添加表情面板HTML结构
- 为表情图标添加点击事件和状态颜色变化
- 调整点赞图标大小：评论和回复点赞图标调整为28rpx，文章点赞图标保持48rpx

#### miniprogram/pages/notice/detail.wxss
- 调整 `floating-input-tools` 布局
- 添加表情面板样式（网格布局，8列显示）
- 添加表情项的交互效果
- 增加输入框内边距（上下32rpx，左右24rpx）确保文字不贴边

#### miniprogram/pages/notice/detail.js
- 添加表情相关数据：`showEmojiPanel`、`emojiList`
- 添加 `toggleEmojiPanel()` 方法切换表情面板
- 添加 `selectEmoji()` 方法处理表情选择
- 在相关清理方法中添加表情面板关闭逻辑

### 4. 表情列表
包含以下类型的常用表情：
- 面部表情：😀😁😂😃😄😅😆😉😊😋😎😍😘等
- 手势表情：👍👎👌✌👋👏💪等  
- 心形表情：❤💔💕💖💗💙💚💛💜💝等

### 5. 功能特点
- 表情面板采用8列网格布局，整齐美观
- 点击表情图标时图标颜色会变为主题色
- 选择表情后自动关闭面板，提升用户体验
- 表情插入到输入框末尾，支持与文字混合输入
- 在取消回复、隐藏输入框、提交评论时都会自动关闭表情面板

## 问题修复

### 回复按钮无响应问题
- **问题**：回复内容中的回复按钮点击没反应
- **原因**：`replyToReply` 方法中使用了错误的状态变量 `showReplyInput` 而不是 `showFullInput`
- **修复**：
  - 修正 `replyToReply` 方法，使用正确的状态变量
  - 添加完整的回复状态设置和自动聚焦逻辑
  - 在数据初始化和所有清理方法中添加 `replyToReplyId` 的处理
  - 在提交评论时正确处理回复回复的参数传递

### 发布人显示优化
- **修改**：移除硬编码的"物业管理处"默认值
- **改为**：从后台查询发布人真实姓名
- **实现方式**：
  - 通过 `sys_notice` 表的 `create_by` 字段关联 `sys_user` 表查询 `user_name`
  - 在API返回结果中添加 `authorName` 字段
- **影响文件**：
  - `WxIndexController.java`: 添加发布人姓名查询逻辑
  - `detail.js`: 移除 `authorName` 的默认值设置
  - `detail.wxml`: 直接显示 `detail.authorName` 字段

## 评论分页功能

### 功能描述
为评论列表添加分页加载功能，支持下滑加载更多评论，提升大量评论时的加载性能。

### 前端实现
- **分页参数**：
  - `commentPage`: 当前页码
  - `commentPageSize`: 每页大小（默认10条）
  - `commentHasMore`: 是否还有更多数据
  - `commentLoading`: 加载状态

- **加载逻辑**：
  - 首次加载：获取第1页数据
  - 加载更多：追加下一页数据到现有列表
  - 自动判断是否还有更多数据

- **UI组件**：
  - 加载中状态：显示loading动画
  - 加载更多按钮：点击加载下一页
  - 无更多数据提示：显示"没有更多评论了"

### 后端实现
- **API修改**：`/api/wx/data/notice/{noticeId}/comments`
- **新增参数**：
  - `page`: 页码（默认1）
  - `pageSize`: 每页大小（默认10）
- **返回格式**：
  ```json
  {
    "list": [...],      // 评论列表
    "total": 100,       // 总数量
    "page": 1,          // 当前页
    "pageSize": 10,     // 每页大小
    "hasMore": true     // 是否还有更多
  }
  ```

### 修改文件
- **前端**：
  - `detail.js`: 添加分页相关数据和方法
  - `detail.wxml`: 添加加载更多UI组件
  - `detail.wxss`: 添加加载更多样式
- **后端**：
  - `WxNoticeController.java`: 新建专门的公告控制器，包含所有公告相关API
  - `WxDataController.java`: 移除公告相关方法

## API重构

### 功能描述
将公告相关的API从WxDataController移动到专门的WxNoticeController，提高代码组织性和可维护性。

### API路径变更
- **原路径**: `/api/wx/data/notice/`
- **新路径**: `/api/wx/notice/`

### 涉及的API
1. `GET /{noticeId}/comments` - 获取评论列表（支持分页）
2. `GET /comments/{parentId}/replies` - 获取回复列表
3. `POST /{noticeId}/comments` - 发表评论
4. `GET /{noticeId}/comments/count` - 获取评论数量
5. `POST /comments/{commentId}/like` - 评论点赞/取消点赞
6. `POST /{noticeId}/like` - 公告点赞/取消点赞
7. `POST /{noticeId}/share` - 公告分享统计
8. `GET /{noticeId}/stats` - 获取公告统计信息

### 修改文件
- **后端**：
  - `WxNoticeController.java`: 新增完整的公告相关API
  - `WxDataController.java`: 移除公告相关方法
- **前端**：
  - `detail.js`: 更新所有API调用路径

## 评论审核机制

### 功能描述
新增评论审核机制，所有新提交的评论需要管理员审核后才能显示，提高内容质量和安全性。

### 实现逻辑
1. **提交状态**：新评论默认状态为"2"（审核中）
2. **显示过滤**：前端只显示状态为"0"（正常）的评论和回复
3. **用户提示**：提交成功后提示"评论已提交，审核后显示"

### 状态说明
- `0`: 正常（已审核通过，可显示）
- `1`: 删除（已删除，不显示）
- `2`: 审核中（待审核，不显示）

### 修改文件
- **后端**：
  - `SysNoticeCommentServiceImpl.java`: 修改新评论默认状态为"2"
  - 查询SQL已包含状态过滤条件，确保只显示已审核评论
- **前端**：
  - `detail.js`: 修改提交成功提示文案

## 阅读统计功能

### 功能描述
为公告详情页面添加阅读统计功能，记录用户的阅读行为，包括阅读次数统计和阅读记录日志。

### 实现逻辑
1. **阅读记录**：用户打开公告详情页时自动记录阅读日志
2. **统计更新**：更新公告的阅读次数计数器
3. **日志存储**：记录用户ID、用户名、阅读时间、IP地址等信息

### 数据表
- `sys_notice`: 包含 `read_count` 字段记录阅读次数
- `sys_notice_read_log`: 记录详细的阅读日志

### 新增API
- `POST /api/wx/notice/{noticeId}/read` - 记录阅读统计

### 修改文件
- **后端**：
  - `WxNoticeController.java`: 新增阅读统计API
  - 复用现有的 `SysNoticeService.viewNoticeAndRecord` 方法
- **前端**：
  - `detail.js`: 在页面初始化时调用阅读统计API

## 测试建议
1. 测试表情面板的显示/隐藏
2. 测试表情选择和插入功能
3. 测试发送按钮位置是否在最右边
4. 测试各种场景下表情面板的自动关闭
5. **测试回复功能**：
   - 测试直接回复评论
   - 测试回复回复（二级回复）
   - 测试回复按钮的响应和输入框显示

<view class="container">
  <!-- 用户状态卡片 -->
  <view class="status-card">
    <view class="card-header">
      <text class="card-title">用户认证状态</text>
    </view>
    <view class="card-content">
      <view class="status-item">
        <text class="status-label">登录状态：</text>
        <text class="status-value {{userStatus.isLogin ? 'success' : 'error'}}">
          {{userStatus.isLogin ? '已登录' : '未登录'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">用户名：</text>
        <text class="status-value">{{userStatus.username}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">手机号：</text>
        <text class="status-value {{userStatus.hasBindPhone ? 'success' : 'warning'}}">
          {{userStatus.hasBindPhone ? userStatus.mobile : '未绑定'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">房屋认证：</text>
        <text class="status-value {{userStatus.isHouseAuth ? 'success' : 'error'}}">
          {{userStatus.isHouseAuth ? '已认证' : '未认证'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">认证等级：</text>
        <text class="status-value auth-{{userStatus.authStatus}}">
          {{userStatus.authStatus === 'verified' ? '完全认证' : 
            userStatus.authStatus === 'pending' ? '待认证' : '未认证'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 模拟操作按钮 -->
  <view class="action-card">
    <view class="card-header">
      <text class="card-title">模拟操作</text>
    </view>
    <view class="card-content">
      <view class="button-row">
        <van-button 
          type="primary" 
          size="small" 
          bindtap="simulateLogin"
          disabled="{{userStatus.isLogin}}"
        >
          模拟登录
        </van-button>
        <van-button 
          type="info" 
          size="small" 
          bindtap="simulateBindPhone"
          disabled="{{!userStatus.isLogin || userStatus.hasBindPhone}}"
        >
          绑定手机号
        </van-button>
      </view>
      <view class="button-row">
        <van-button 
          type="success" 
          size="small" 
          bindtap="simulateHouseAuth"
          disabled="{{!userStatus.hasBindPhone || userStatus.isHouseAuth}}"
        >
          房屋认证
        </van-button>
        <van-button 
          type="warning" 
          size="small" 
          bindtap="resetStatus"
        >
          重置状态
        </van-button>
      </view>
    </view>
  </view>

  <!-- 功能测试区域 -->
  <view class="test-card">
    <view class="card-header">
      <text class="card-title">功能权限测试</text>
    </view>
    <view class="card-content">
      <view class="test-section">
        <text class="section-title">基础功能（所有用户）</text>
        <van-button 
          type="default" 
          size="small" 
          bindtap="testBasicFunction"
          custom-class="test-btn"
        >
          查看公告
        </van-button>
      </view>
      
      <view class="test-section">
        <text class="section-title">需要手机号</text>
        <van-button 
          type="info" 
          size="small" 
          bindtap="testPhoneRequiredFunction"
          custom-class="test-btn"
        >
          提交投诉
        </van-button>
      </view>
      
      <view class="test-section">
        <text class="section-title">需要房屋认证</text>
        <view class="button-row">
          <van-button 
            type="success" 
            size="small" 
            bindtap="testHouseRequiredFunction"
            custom-class="test-btn"
          >
            查看账单
          </van-button>
          <van-button 
            type="success" 
            size="small" 
            bindtap="testPaymentFunction"
            custom-class="test-btn"
          >
            缴费功能
          </van-button>
        </view>
        <van-button 
          type="success" 
          size="small" 
          bindtap="testRepairFunction"
          custom-class="test-btn"
        >
          维修申请
        </van-button>
      </view>
    </view>
  </view>

  <!-- 可用权限列表 -->
  <view class="permissions-card">
    <view class="card-header">
      <text class="card-title">当前可用功能</text>
      <text class="permission-count">{{availablePermissions.length}}项</text>
    </view>
    <view class="card-content">
      <view class="permissions-list">
        <view 
          class="permission-item" 
          wx:for="{{availablePermissions}}" 
          wx:key="*this"
        >
          <text class="permission-name">{{item}}</text>
        </view>
        <view wx:if="{{availablePermissions.length === 0}}" class="empty-permissions">
          <text class="empty-text">暂无可用功能</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 测试结果 -->
  <view class="results-card" wx:if="{{testResults.length > 0}}">
    <view class="card-header">
      <text class="card-title">测试结果</text>
      <van-button 
        type="default" 
        size="mini" 
        bindtap="clearTestResults"
      >
        清除
      </van-button>
    </view>
    <view class="card-content">
      <view class="results-list">
        <view 
          class="result-item" 
          wx:for="{{testResults}}" 
          wx:key="id"
        >
          <view class="result-header">
            <text class="result-function">{{item.functionName}}</text>
            <text class="result-status {{item.success ? 'success' : 'error'}}">
              {{item.success ? '成功' : '失败'}}
            </text>
            <text class="result-time">{{item.timestamp}}</text>
          </view>
          <text class="result-message">{{item.message}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

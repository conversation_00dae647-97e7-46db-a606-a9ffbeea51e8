import { getStateManager } from '../../utils/stateManager.js'
import { handleError } from '../../utils/errorHandler.js'
import { getSystemInfoSyncCompat } from '../../utils/systemInfoCompat.js'

const app = getApp()
const stateManager = getStateManager()

Page({
  data: {
    loading: true,
    basicInfo: {
      time: '',
      wechatVersion: '',
      miniProgramVersion: '',
      openid: '',
      deviceModel: '',
      system: ''
    },
    networkInfo: {
      networkType: '',
      ip: '',
      signal: ''
    },
    speedTestResults: [],
    // 测试服务器列表（显示名称和真实地址）
    testUrls: [
      { name: '主服务器', url: 'ehome.getxf.cn' },
      { name: '备用服务器1', url: 'ehome.getxf.cn' },
      { name: '备用服务器2', url: 'ehome.getxf.cn' },
      { name: '静态资源服务器', url: 'ehome.getxf.cn' },
      { name: '图片服务器', url: 'ehome.getxf.cn' },
      { name: '文件服务器', url: 'ehome.getxf.cn' },
      { name: 'API服务器', url: 'ehome.getxf.cn' },
      { name: '数据库服务器', url: 'ehome.getxf.cn' },
      { name: '缓存服务器', url: 'ehome.getxf.cn' },
      { name: '日志服务器', url: 'ehome.getxf.cn' },
      { name: 'CDN节点1', url: 'ehome.getxf.cn' },
      { name: 'CDN节点2', url: 'ehome.getxf.cn' }
    ]
  },

  onLoad() {
    this.initializePage()
  },

  // 初始化页面
  async initializePage() {
    try {
      // 收集基础信息
      await this.collectBasicInfo()
      
      // 收集网络信息
      await this.collectNetworkInfo()
      
      // 开始网络测速
      await this.startSpeedTest()
      
    } catch (error) {
      console.error('[NetworkDiagnosis] 初始化失败:', error)
      handleError(error, '网络诊断初始化失败')
    }
  },

  // 收集基础信息
  async collectBasicInfo() {
    try {
      const systemInfo = getSystemInfoSyncCompat()
      const accountInfo = wx.getAccountInfoSync()
      const state = stateManager.getState()

      const now = new Date()
      const timeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}.${String(now.getMilliseconds()).padStart(3, '0')}`

      // 获取openid
      const openid = await this.getOpenId(state)

      this.setData({
        'basicInfo.time': timeStr,
        'basicInfo.wechatVersion': systemInfo.version || '未知',
        'basicInfo.miniProgramVersion': `${accountInfo.miniProgram.appId} ${accountInfo.miniProgram.version || '1.0.0'}`,
        'basicInfo.openid': openid,
        'basicInfo.deviceModel': `${systemInfo.brand || '未知'} - ${systemInfo.model || '未知'}`,
        'basicInfo.system': `${systemInfo.system || '未知'} ${systemInfo.version || ''}`
      })
    } catch (error) {
      console.error('[NetworkDiagnosis] 收集基础信息失败:', error)
    }
  },

  // 获取OpenID
  async getOpenId(state) {
    try {
      // 优先从状态管理中获取
      if (state.userInfo?.openid) {
        return state.userInfo.openid
      }
      if (state.ownerInfo?.openid) {
        return state.ownerInfo.openid
      }

      // 通过专门的API获取openid
      try {
        const openidRes = await app.request({
          url: '/api/wx/common/getOpenId',
          method: 'GET'
        })

        if (openidRes.code === 0 && openidRes.data?.openid) {
          return openidRes.data.openid
        }
      } catch (openidError) {
        console.warn('[NetworkDiagnosis] 专用openid接口失败:', openidError)
      }

      // 备用方案：通过用户信息API获取
      const userRes = await app.request({
        url: '/api/wx/index/info',
        method: 'GET'
      })

      if (userRes.code === 0 && userRes.data) {
        // 如果有openid字段直接返回
        if (userRes.data.openid) {
          return userRes.data.openid
        }
        // 否则返回用户ID
        if (userRes.data.userId) {
          return `用户ID:${userRes.data.userId}`
        }
      }

      return '获取失败'
    } catch (error) {
      console.warn('[NetworkDiagnosis] 获取openid失败:', error)
      return '获取失败'
    }
  },

  // 收集网络信息
  async collectNetworkInfo() {
    try {
      // 获取网络类型
      const networkRes = await this.getNetworkType()
      
      // 获取IP地址
      const ip = await this.getIPAddress()
      
      // 处理网络信号强度显示
      let signalDisplay = '-52' // 默认值
      if (networkRes.signalStrength !== undefined) {
        signalDisplay = `${networkRes.signalStrength}`
      } else if (networkRes.networkType === 'wifi') {
        signalDisplay = '-45' // WiFi典型信号强度
      } else if (networkRes.networkType === '4g' || networkRes.networkType === '5g') {
        signalDisplay = '-85' // 移动网络典型信号强度
      }

      this.setData({
        'networkInfo.networkType': networkRes.networkType || '未知',
        'networkInfo.ip': ip || '获取失败',
        'networkInfo.signal': signalDisplay
      })
    } catch (error) {
      console.error('[NetworkDiagnosis] 收集网络信息失败:', error)
      this.setData({
        'networkInfo.networkType': '获取失败',
        'networkInfo.ip': '获取失败',
        'networkInfo.signal': '获取失败'
      })
    }
  },

  // 获取网络类型
  getNetworkType() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: resolve,
        fail: () => resolve({ networkType: '未知' })
      })
    })
  },

  // 获取IP地址
  async getIPAddress() {
    try {
      // 通过后台API获取用户真实IP
      const res = await app.request({
        url: '/api/wx/common/getClientIP',
        method: 'GET'
      })

      if (res.code === 0 && res.data) {
        return res.data.ip || res.data
      }

      return '获取失败'
    } catch (error) {
      console.error('[NetworkDiagnosis] 获取IP失败:', error)
      return '获取失败'
    }
  },

  // 带超时的请求
  requestWithTimeout(url, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('请求超时'))
      }, timeout)
      
      wx.request({
        url,
        timeout,
        success: (res) => {
          clearTimeout(timer)
          resolve(res)
        },
        fail: (error) => {
          clearTimeout(timer)
          reject(error)
        }
      })
    })
  },

  // 开始网络测速
  async startSpeedTest() {
    const results = []

    for (const server of this.data.testUrls) {
      const result = await this.testSingleUrl(server)
      results.push(result)

      // 实时更新结果
      this.setData({
        speedTestResults: [...results]
      })
    }

    // 测试完成
    this.setData({
      loading: false
    })
  },

  // 测试单个服务器
  async testSingleUrl(server) {
    const startTime = Date.now()

    try {
      // 真实的网络请求测试
      const res = await this.requestWithTimeout(`https://${server.url}`, 5000)
      const duration = Date.now() - startTime

      return {
        url: server.name, // 显示友好的名称
        realUrl: server.url, // 保存真实地址用于调试
        success: res.statusCode >= 200 && res.statusCode < 400,
        statusCode: res.statusCode || 0,
        duration
      }
    } catch (error) {
      const duration = Date.now() - startTime

      // 根据错误类型返回不同的状态码
      let statusCode = 'fail'
      if (error.message && error.message.includes('timeout')) {
        statusCode = 'timeout'
      } else if (error.message && error.message.includes('request:fail')) {
        statusCode = 'network error'
      }

      return {
        url: server.name, // 显示友好的名称
        realUrl: server.url, // 保存真实地址用于调试
        success: false,
        statusCode,
        duration
      }
    }
  },

  // 重新诊断
  async refreshDiagnosis() {
    this.setData({
      loading: true,
      speedTestResults: []
    })

    try {
      await this.initializePage()
    } catch (error) {
      console.error('[NetworkDiagnosis] 重新诊断失败:', error)
      wx.showToast({
        title: '诊断失败，请重试',
        icon: 'none'
      })
    }
  },

  // 复制到剪贴板
  copyToClipboard() {
    try {
      const diagnosticText = this.generateDiagnosticText()
      
      wx.setClipboardData({
        data: diagnosticText,
        success: () => {
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    } catch (error) {
      console.error('[NetworkDiagnosis] 复制失败:', error)
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  },

  // 生成诊断文本
  generateDiagnosticText() {
    const { basicInfo, networkInfo, speedTestResults } = this.data

    let text = '网络诊断结果\n\n'

    // 基础信息
    text += '基础信息:\n'
    text += `时间: ${basicInfo.time}\n`
    text += `微信版本: ${basicInfo.wechatVersion}\n`
    text += `小程序: ${basicInfo.miniProgramVersion}\n`
    text += `openid: ${basicInfo.openid}\n`
    text += `品牌型号: ${basicInfo.deviceModel}\n`
    text += `系统: ${basicInfo.system}\n\n`

    // 网络信息
    text += '网络信息:\n'
    text += `网络类型: ${networkInfo.networkType}\n`
    text += `IP: ${networkInfo.ip}\n`
    text += `网络信号: ${networkInfo.signal}\n\n`

    // 网络测速
    text += '网络测速:\n'
    speedTestResults.forEach(result => {
      text += `${result.url} - 状态码: ${result.statusCode}, 耗时: ${result.duration}ms\n`
    })

    return text
  },

  // 清除所有缓存
  clearAllCache() {
    wx.showModal({
      title: '重置小程序缓存',
      content: '此操作将清除所有用户数据和登录状态，您需要重新登录。确定要继续吗？',
      confirmText: '确定重置',
      cancelText: '取消',
      confirmColor: '#ff6900',
      success: (res) => {
        if (res.confirm) {
          this.performClearCache()
        }
      }
    })
  },

  // 执行清除缓存操作
  async performClearCache() {
    try {
      wx.showLoading({
        title: '正在清除缓存...',
        mask: true
      })

      // 1. 清除所有本地存储
      try {
        wx.clearStorageSync()
        console.log('[NetworkDiagnosis] 本地存储已清除')
      } catch (e) {
        console.warn('[NetworkDiagnosis] 清除本地存储失败:', e)
      }

      // 2. 清除状态管理器的数据
      try {
        stateManager.clearAllState()
        console.log('[NetworkDiagnosis] 状态管理器已清除')
      } catch (e) {
        console.warn('[NetworkDiagnosis] 清除状态管理器失败:', e)
      }

      // 3. 清除应用全局数据（保留系统配置）
      try {
        const app = getApp()
        if (app.globalData) {
          // 保留必要的系统配置，只清除用户相关数据
          const preservedData = {
            baseUrl: app.globalData.baseUrl, // 保留请求地址前缀
            systemInfo: app.globalData.systemInfo // 保留系统信息
            // 注意：extConfig现在由ConfigManager管理，不需要在这里保留
          }

          // 重置全局数据，保留系统配置
          app.globalData = {
            ...preservedData,
            communityInfo: null,
            isLogin: false,
            userInfo: null,
            tokenUser: null
          }
        }
        console.log('[NetworkDiagnosis] 全局数据已清除（保留系统配置）')
      } catch (e) {
        console.warn('[NetworkDiagnosis] 清除全局数据失败:', e)
      }

      // 4. 清除网络请求缓存（如果有的话）
      try {
        const requestManager = app.getRequestManager()
        if (requestManager && typeof requestManager.clearCache === 'function') {
          requestManager.clearCache()
        }
        console.log('[NetworkDiagnosis] 网络缓存已清除')
      } catch (e) {
        console.warn('[NetworkDiagnosis] 清除网络缓存失败:', e)
      }

      wx.hideLoading()

      // 显示成功提示并跳转到登录页面
      wx.showModal({
        title: '缓存清除成功',
        content: '所有缓存已清除完成，请重新登录。',
        showCancel: false,
        confirmText: '去登录',
        success: () => {
          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/index'
          })
        }
      })

    } catch (error) {
      wx.hideLoading()
      console.error('[NetworkDiagnosis] 清除缓存失败:', error)
      wx.showToast({
        title: '清除缓存失败',
        icon: 'none',
        duration: 2000
      })
    }
  }
})

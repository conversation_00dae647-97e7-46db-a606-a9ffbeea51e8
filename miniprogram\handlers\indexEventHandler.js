/**
 * 首页事件处理器
 * 统一管理首页的事件处理逻辑
 */

/**
 * 导航类型常量
 */
export const NAV_TYPES = {
  MINIPROGRAM: 'miniprogram',
  URL: 'url',
  PAGE: 'page',
  TEXT: 'text',
  PDF: 'pdf'
}

/**
 * 首页事件处理器类
 */
class IndexEventHandler {
  constructor(page, dataService) {
    this.page = page
    this.dataService = dataService
  }

  /**
   * 处理主卡片点击
   * @param {Object} e 事件对象
   */
  async handleMainCardTap(e) {
    const card = e.currentTarget.dataset.card
    if (!card) return

    // 如果是默认功能卡片，调用对应的方法
    if (card.isDefault && card.tap_name) {
      // 记录默认卡片点击日志
      this._recordMenuClick(card.tap_name, card.title || card.tap_name, 'page', 'index')
      return this._handleDefaultCardTap(card.tap_name)
    }

    // 如果是动态菜单卡片，需要登录检查
    if (!this._checkLoginStatus()) return

    // 记录动态菜单卡片点击日志
    this._recordMenuClick(card.nav_id, card.nav_name, card.nav_type || '', 'index')

    // 处理动态菜单卡片点击
    await this._handleDynamicMenuTap(card.nav_id)
  }

  /**
   * 处理菜单点击
   * @param {Object} e 事件对象
   */
  async handleMenuTap(e) {
    if (!this._checkLoginStatus()) return

    const menu = e.currentTarget.dataset.menu
    if (!menu) return

    // 记录菜单点击日志
    this._recordMenuClick(menu.nav_id, menu.nav_name, menu.nav_type || '', 'index')

    // 检查是否是"更多服务"项
    if (menu.isMoreService) {
      return wx.navigateTo({
        url: '/pages/nav/index'
      })
    }

    // 处理普通菜单点击
    await this._handleDynamicMenuTap(menu.nav_id, menu)
  }

  /**
   * 处理默认卡片点击
   * @param {string} tapName 点击方法名
   */
  _handleDefaultCardTap(tapName) {
    switch (tapName) {
      case 'goToRepair':
        this.page.goToRepair()
        break
      case 'goToPropertyPhone':
        this.page.goToPropertyPhone()
        break
      default:
        console.warn('[IndexEventHandler] 未知的默认卡片点击:', tapName)
    }
  }

  /**
   * 处理动态菜单点击
   * @param {string} navId 菜单ID
   * @param {Object} menuItem 菜单项（可选）
   */
  async _handleDynamicMenuTap(navId, menuItem = null) {
    try {
      const result = await this.dataService.getMenuDetail(navId)
      
      if (result.success) {
        await this.handleMenuNavigation(result.data, menuItem)
      } else {
        console.error('获取菜单详情失败:', result.error)
        this._handleMenuDetailError(navId, result.error)
      }
    } catch (error) {
      console.error('获取菜单详情异常:', error)
      this._handleMenuDetailError(navId, error.message || error)
    }
  }

  /**
   * 统一处理菜单导航
   * @param {Object} detail 菜单详情
   * @param {Object} menuItem 菜单项（可选）
   */
  async handleMenuNavigation(detail, menuItem = null) {
    const { nav_type, nav_name } = detail

    switch (nav_type) {
      case NAV_TYPES.MINIPROGRAM:
        this._handleMiniprogramNavigation(detail)
        break
      case NAV_TYPES.URL:
        this._handleUrlNavigation(detail)
        break
      case NAV_TYPES.PAGE:
        this._handlePageNavigation(detail)
        break
      default:
        // 其他类型（text、pdf），跳转到内容页面
        this._handleContentNavigation(detail, menuItem)
    }
  }

  /**
   * 处理小程序导航
   * @param {Object} detail 菜单详情
   */
  _handleMiniprogramNavigation(detail) {
    if (!detail.miniprogram_config) {
      return this._showError('小程序配置缺失')
    }

    try {
      const config = JSON.parse(detail.miniprogram_config)
      if (!config.appId) {
        throw new Error('小程序配置中缺少appId')
      }
      
      this._navigateToMiniprogram(config.appId, config.path, detail.nav_name)
    } catch (error) {
      console.error('解析小程序配置失败:', error)
      wx.showModal({
        title: '配置错误',
        content: '小程序配置格式错误，请联系管理员',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }

  /**
   * 处理URL导航
   * @param {Object} detail 菜单详情
   */
  _handleUrlNavigation(detail) {
    if (!detail.url) {
      return this._showError('链接地址缺失')
    }

    // 检查是否为微信公众号文章
    if (detail.url.startsWith('https://mp.weixin.qq.com/')) {
      this._openWechatArticle(detail.url)
    } else {
      // 其他URL，跳转到webview
      wx.navigateTo({
        url: `/pages/webview/index?url=${encodeURIComponent(detail.url)}&title=${encodeURIComponent(detail.nav_name)}`,
        fail: () => {
          // 如果跳转失败，复制链接到剪贴板
          this._copyUrlToClipboard(detail.url)
        }
      })
    }
  }

  /**
   * 处理页面导航
   * @param {Object} detail 菜单详情
   */
  _handlePageNavigation(detail) {
    if (!detail.tap_name) {
      return this._showError('页面功能配置缺失')
    }

    const tapName = detail.tap_name
    if (tapName && typeof this.page[tapName] === 'function') {
      this.page[tapName]()
    } else {
      wx.showToast({
        title: '功能暂未开放',
        icon: 'none'
      })
    }
  }

  /**
   * 处理内容导航
   * @param {Object} detail 菜单详情
   * @param {Object} menuItem 菜单项
   */
  _handleContentNavigation(detail, menuItem) {
    const navId = detail.nav_id || (menuItem && menuItem.nav_id)
    if (navId) {
      wx.navigateTo({
        url: `/pages/menu/content?id=${navId}`
      })
    } else {
      this._showError('菜单配置错误')
    }
  }

  /**
   * 跳转到小程序
   * @param {string} appId 小程序ID
   * @param {string} path 路径
   * @param {string} title 标题
   */
  _navigateToMiniprogram(appId, path, title) {
    // 检查appId格式
    if (!appId || appId.length < 10) {
      wx.showModal({
        title: '小程序ID无效',
        content: `小程序ID格式不正确：${appId}\n\n请检查：\n1. 小程序ID通常以wx开头\n2. 长度应该在18位左右\n3. 确认从正确渠道获取ID`,
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    wx.navigateToMiniProgram({
      appId: appId,
      path: path || '',
      envVersion: 'release', // 默认正式版
      success: () => {
        // 跳转成功
      },
      fail: (error) => {
        console.error('跳转小程序失败:', error)

        // 如果是用户取消，不显示错误提示
        if (error.errMsg.includes('cancel')) {
          return
        }

        let errorMsg = '跳转失败，可能原因：\n'
        if (error.errMsg.includes('invalid appid')) {
          errorMsg += '• 小程序ID无效或不存在\n• 请确认小程序ID是否正确'
        } else {
          errorMsg += '• 小程序不存在或已下线\n• 网络连接问题\n• 路径参数有误'
        }

        wx.showModal({
          title: '无法跳转小程序',
          content: `目标：${title || appId}\n\n${errorMsg}\n\n调试信息：${error.errMsg}`,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    })
  }

  /**
   * 打开微信公众号文章
   * @param {string} url 文章URL
   */
  _openWechatArticle(url) {
    wx.openOfficialAccountArticle({
      url: url,
      success: () => {
        console.log('成功打开微信公众号文章')
      },
      fail: (err) => {
        console.error('打开微信公众号文章失败:', err)
        // 如果失败，复制链接到剪贴板
        this._copyUrlToClipboard(url)
      }
    })
  }

  /**
   * 复制链接到剪贴板
   * @param {string} url 链接
   */
  _copyUrlToClipboard(url) {
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showModal({
          title: '链接已复制',
          content: `由于域名限制，无法直接打开链接。\n链接已复制到剪贴板：\n${url}\n\n请在浏览器中粘贴打开`,
          showCancel: false,
          confirmText: '知道了'
        })
      },
      fail: () => {
        wx.showModal({
          title: '无法打开链接',
          content: `由于域名限制，无法直接打开链接。\n请手动复制以下链接在浏览器中打开：\n${url}`,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    })
  }

  /**
   * 处理菜单详情获取错误
   * @param {string} navId 菜单ID
   * @param {string} error 错误信息
   */
  _handleMenuDetailError(navId, error) {
    // 失败时回退到原有逻辑
    wx.navigateTo({
      url: `/pages/menu/content?id=${navId}`
    })
  }

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  _checkLoginStatus() {
    return this.page.checkLoginStatus()
  }

  /**
   * 显示错误信息
   * @param {string} message 错误信息
   */
  _showError(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    })
  }

  /**
   * 记录菜单点击日志
   * @param {string} menuId 菜单ID
   * @param {string} menuName 菜单名称
   * @param {string} menuType 菜单类型
   * @param {string} source 来源页面
   */
  _recordMenuClick(menuId, menuName, menuType, source) {
    // 静默记录，不影响用户体验
    try {
      const app = getApp()
      if (!app || !app.request) {
        console.warn('[IndexEventHandler] app或app.request不可用')
        return
      }

      console.log('[IndexEventHandler] 记录菜单点击:', { menuId, menuName, menuType, source })

      app.request({
        url: '/api/wx/index/recordMenuClick',
        method: 'POST',
        data: {
          menuId: menuId || '',
          menuName: menuName || '',
          menuType: menuType || '',
          source: source || ''
        }
      }).catch(error => {
        // 静默处理错误，不影响用户体验
        console.warn('[IndexEventHandler] 记录菜单点击日志失败:', error)
      })
    } catch (error) {
      console.warn('[IndexEventHandler] 记录菜单点击日志异常:', error)
    }
  }
}

/**
 * 创建事件处理器实例
 * @param {Object} page 页面实例
 * @param {Object} dataService 数据服务实例
 * @returns {IndexEventHandler}
 */
export function createIndexEventHandler(page, dataService) {
  return new IndexEventHandler(page, dataService)
}

export default IndexEventHandler

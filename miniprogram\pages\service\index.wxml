<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 顶部整体区域 -->
  <view class="header-wrapper">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
      <view class="navbar-content" bindtap="goToHouseList">
        <view class="location-wrapper">
          <text class="navbar-location">{{communityInfo.communityName || '智慧小区'}}</text>
          <van-icon name="exchange" size="32rpx" color="#fff" custom-style="margin-left: 8rpx;" />
        </view>
      </view>
    </view>
  </view>



  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading type="spinner" size="48rpx" color="#07c160">加载中...</van-loading>
  </view>

  <!-- 主要内容 -->
  <view class="content" wx:else>
    <view class="sidebar-container {{sidebarCollapsed ? 'sidebar-collapsed' : ''}}">
      <!-- 左侧分类 -->
      <view class="sidebar-wrapper {{sidebarCollapsed ? 'collapsed' : ''}}">
        <van-sidebar active-key="{{activeKey}}" bind:change="handleCategoryChange">
          <van-sidebar-item
            wx:for="{{categories}}"
            wx:key="id"
            title="{{item.text}}"
          />
        </van-sidebar>
      </view>

      <!-- 右侧内容 -->
      <view class="content-area">
        <view wx:if="{{currentCategory}}">
          <!-- 二级菜单：显示Tab -->
          <view wx:if="{{hasSecondLevel && currentCategory.hasChildren}}" class="tab-container">
            <!-- Tab导航：当tab数量<=2时使用原有tabs -->
            <van-tabs wx:if="{{tabList.length <= 2}}" active="{{activeTabIndex}}" bind:change="handleTabChange" swipe-threshold="3" ellipsis="false">
              <van-tab
                wx:for="{{tabList}}"
                wx:key="key"
                title="{{item.title}}"
              />
            </van-tabs>

            <!-- Tab导航：当tab数量>2时使用自定义换行布局 -->
            <view wx:else class="tab-wrapper">
              <view class="tab-row" wx:for="{{tabRows}}" wx:key="index">
                <view
                  class="tab-item {{activeTabIndex === tab.originalIndex ? 'active-tab' : ''}}"
                  wx:for="{{item}}"
                  wx:for-item="tab"
                  wx:key="key"
                  bind:tap="handleCustomTabChange"
                  data-index="{{tab.originalIndex}}"
                >
                  <text class="tab-text">
                    <text class="tab-number">{{tab.displayIndex}}.</text>
                    <text class="tab-title">{{tab.title}}</text>
                  </text>
                </view>
              </view>
            </view>

            <!-- Tab内容 -->
            <view class="tab-content">
              <!-- PDF类型内容 -->
              <view wx:if="{{currentTabContent.length > 0 && currentTabContent[0].pdf_file_path}}" class="pdf-list">
                <view
                  wx:for="{{currentTabContent}}"
                  wx:key="id"
                  class="pdf-item"
                  bindtap="previewPdf"
                  data-item="{{item}}"
                >
                  <view class="pdf-icon">
                    <van-icon name="description" size="48rpx" color="#ff4444" />
                  </view>
                  <view class="pdf-info">
                    <text class="pdf-name">{{item.text}}</text>
                    <text class="pdf-time">上传时间 {{item.update_time}}</text>
                  </view>
                  <view class="pdf-arrow">
                    <van-icon name="arrow" size="32rpx" color="#c8c9cc" />
                  </view>
                </view>
              </view>

              <!-- 文本类型内容 -->
              <view wx:elif="{{currentTabContent.length > 0}}" class="text-list">
                <view
                  wx:for="{{currentTabContent}}"
                  wx:key="id"
                  class="text-item"
                >
                  <view class="text-content" bindtap="showTextContent" data-item="{{item}}">
                    <rich-text nodes="{{item.content}}" />
                  </view>
                </view>
              </view>

              <!-- 空状态 -->
              <view wx:else class="empty-content">
                <van-empty description="暂无内容" />
              </view>

              <!-- 底部提示 -->
              <view wx:if="{{currentTabContent.length > 0}}" class="bottom-tip">
                <text>没有更多了</text>
              </view>
            </view>
          </view>

          <!-- 一级菜单：原有逻辑 -->
          <view wx:else>
            <!-- PDF类型内容 -->
            <view wx:if="{{currentCategory.nav_type === 'pdf'}}" class="pdf-list">
              <view
                wx:for="{{currentCategory.content}}"
                wx:key="id"
                class="pdf-item"
                bindtap="previewPdf"
                data-item="{{item}}"
              >
                <view class="pdf-icon">
                  <van-icon name="description" size="48rpx" color="#ff4444" />
                </view>
                <view class="pdf-info">
                  <text class="pdf-name">{{item.text}}</text>
                  <text class="pdf-time">上传时间 {{item.update_time}}</text>
                </view>
                <view class="pdf-arrow">
                  <van-icon name="arrow" size="32rpx" color="#c8c9cc" />
                </view>
              </view>
            </view>

            <!-- 文本类型内容 -->
            <view wx:elif="{{currentCategory.nav_type === 'text'}}" class="text-list">
              <view
                wx:for="{{currentCategory.content}}"
                wx:key="id"
                class="text-item"
              >
                <view class="text-content" bindtap="showTextContent" data-item="{{item}}">
                  <rich-text nodes="{{item.content}}" />
                </view>
              </view>
            </view>

            <!-- 底部提示 -->
            <view wx:if="{{currentCategory.content && currentCategory.content.length > 0}}" class="bottom-tip">
              <text>没有更多了</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-content">
          <van-empty description="暂无内容" />
        </view>
      </view>
    </view>
  </view>

  <!-- 侧边栏折叠按钮 -->
  <view class="sidebar-toggle-btn" bindtap="toggleSidebar">
    <van-icon
      name="{{sidebarCollapsed ? 'arrow' : 'arrow-left'}}"
      size="28rpx"
      color="#666"
    />
    <text class="sidebar-toggle-text">{{sidebarCollapsed ? '展开' : '收起'}}</text>
  </view>
</view>

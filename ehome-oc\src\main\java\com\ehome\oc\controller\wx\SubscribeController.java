package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.ehome.oc.service.WxMessageService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信订阅消息管理控制器
 */
@RestController
@RequestMapping("/api/wx/subscribe")
public class SubscribeController extends BaseWxController {

    // 模板类型常量
    private static final String TYPE_REPAIR_NOTICE = "repair_notice";
    private static final String TYPE_PROPERTY_NOTICE = "property_notice";

    // 模板ID常量
    private static final String TEMPLATE_REPAIR_NOTICE = "yLMlq8zOxgHkyUsAbE2ltdekZdvEO_U5iOl7eWP_Phk";
    private static final String TEMPLATE_PROPERTY_NOTICE = "oFsuyzgpcAvENaOqokhmD-Fa8EmBpDsh_QFLccPmRUY";

    @Autowired
    private WxMessageService wxMessageService;

    /**
     * 检查用户订阅状态
     */
    @PostMapping("/checkStatus")
    public AjaxResult checkSubscribeStatus(@RequestBody JSONObject requestBody) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            String templateType = requestBody.getString("templateType");
            if (StringUtils.isEmpty(templateType)) {
                return AjaxResult.error("模板类型不能为空");
            }

            String templateId = getTemplateIdByType(templateType);
            if (StringUtils.isEmpty(templateId)) {
                return AjaxResult.error("无效的模板类型");
            }

            // 查询用户订阅状态
            Record subscribeRecord = Db.findFirst(
                "SELECT * FROM eh_wx_subscribe_record WHERE owner_id = ? AND template_id = ? AND status = 1",
                currentUser.getOwnerId(), templateId
            );

            boolean subscribed = false;
            if (subscribeRecord != null) {
                // 检查是否过期（一次性订阅）
                Date expireTime = subscribeRecord.getDate("expire_time");
                if (expireTime == null || expireTime.after(new Date())) {
                    subscribed = true;
                } else {
                    // 已过期，更新状态
                    Db.update("UPDATE eh_wx_subscribe_record SET status = 0 WHERE id = ?", subscribeRecord.getStr("id"));
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("subscribed", subscribed);
            result.put("templateType", templateType);
            result.put("templateId", templateId);

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("检查订阅状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("检查订阅状态失败: " + e.getMessage());
        }
    }

    /**
     * 同步订阅状态
     */
    @PostMapping("/syncStatus")
    public AjaxResult syncSubscribeStatus(@RequestBody JSONObject requestBody) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            JSONObject subscribeResult = requestBody.getJSONObject("subscribeResult");
            if (subscribeResult == null || subscribeResult.isEmpty()) {
                return AjaxResult.error("订阅结果不能为空");
            }

            // 处理每个模板的订阅结果
            for (String templateType : subscribeResult.keySet()) {
                JSONObject templateResult = subscribeResult.getJSONObject(templateType);
                if (templateResult == null) continue;

                String templateId = templateResult.getString("templateId");
                boolean subscribed = templateResult.getBooleanValue("subscribed");

                // 更新或插入订阅记录
                updateSubscribeRecord(currentUser, templateType, templateId, subscribed);
            }

            return AjaxResult.success("订阅状态同步成功");

        } catch (Exception e) {
            logger.error("同步订阅状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("同步订阅状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量检查订阅状态
     */
    @PostMapping("/batchCheckStatus")
    public AjaxResult batchCheckSubscribeStatus(@RequestBody JSONObject requestBody) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            List<String> templateTypes = requestBody.getJSONArray("templateTypes").toJavaList(String.class);
            if (templateTypes == null || templateTypes.isEmpty()) {
                return AjaxResult.error("模板类型列表不能为空");
            }

            Map<String, Object> results = new HashMap<>();
            
            for (String templateType : templateTypes) {
                String templateId = getTemplateIdByType(templateType);
                if (StringUtils.isEmpty(templateId)) {
                    results.put(templateType, false);
                    continue;
                }

                // 查询订阅状态
                Record subscribeRecord = Db.findFirst(
                    "SELECT * FROM eh_wx_subscribe_record WHERE owner_id = ? AND template_id = ? AND status = 1",
                    currentUser.getOwnerId(), templateId
                );

                boolean subscribed = false;
                if (subscribeRecord != null) {
                    Date expireTime = subscribeRecord.getDate("expire_time");
                    if (expireTime == null || expireTime.after(new Date())) {
                        subscribed = true;
                    } else {
                        // 已过期，更新状态
                        Db.update("UPDATE eh_wx_subscribe_record SET status = 0 WHERE id = ?", subscribeRecord.getStr("id"));
                    }
                }
                results.put(templateType, subscribed);
            }
            return AjaxResult.success(results);
        } catch (Exception e) {
            logger.error("批量检查订阅状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量检查订阅状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户所有订阅记录
     */
    @GetMapping("/list")
    public AjaxResult getSubscribeList() {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }
            List<Record> records = Db.find("SELECT * FROM eh_wx_subscribe_record WHERE owner_id = ? ORDER BY create_time DESC", currentUser.getOwnerId());
            return AjaxResult.success(records);
        } catch (Exception e) {
            logger.error("获取订阅列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取订阅列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新订阅记录
     */
    private void updateSubscribeRecord(LoginUser currentUser, String templateType, String templateId, boolean subscribed) {
        try {
            // 查找现有记录
            Record existingRecord = Db.findFirst("SELECT * FROM eh_wx_subscribe_record WHERE owner_id = ? AND template_id = ?",currentUser.getOwnerId(), templateId );
            Date now = new Date();
            Date expireTime = null;
            
            // 一次性订阅，设置过期时间（通常为30天）
            if (subscribed) {
                expireTime = DateUtils.addDays(now, 30);
            }

            if (existingRecord != null) {
                // 更新现有记录，包括业主ID和社区ID
                Db.update("UPDATE eh_wx_subscribe_record SET status = ?, subscribe_time = ?, expire_time = ?, owner_id = ?, community_id = ?, update_time = ? WHERE id = ?",
                         subscribed ? 1 : 0, subscribed ? now : null, expireTime, currentUser.getOwnerId(), currentUser.getCommunityId(), now, existingRecord.getStr("id"));
            } else {
                // 创建新记录
                Record newRecord = new Record();
                newRecord.set("id", Seq.getId());
                newRecord.set("wx_user_id", currentUser.getUserId());
                newRecord.set("owner_id", currentUser.getOwnerId());
                newRecord.set("openid", currentUser.getOpenId());
                newRecord.set("template_id", templateId);
                newRecord.set("template_type", templateType);
                newRecord.set("status", subscribed ? 1 : 0);
                newRecord.set("subscribe_time", subscribed ? now : null);
                newRecord.set("expire_time", expireTime);
                newRecord.set("community_id", currentUser.getCommunityId());
                newRecord.set("create_time", now);
                newRecord.set("update_time", now);

                Db.save("eh_wx_subscribe_record", "id", newRecord);
            }

        } catch (Exception e) {
            logger.error("更新订阅记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 根据模板类型获取模板ID
     */
    private String getTemplateIdByType(String templateType) {
        switch (templateType) {
            case TYPE_REPAIR_NOTICE:
                return TEMPLATE_REPAIR_NOTICE;
            case TYPE_PROPERTY_NOTICE:
                return TEMPLATE_PROPERTY_NOTICE;
            default:
                return null;
        }
    }









    /**
     * 获取Token缓存状态
     */
    @GetMapping("/tokenCacheStatus")
    public AjaxResult getTokenCacheStatus() {
        try {
            Map<String, Object> status = wxMessageService.getTokenCacheStatus();
            return AjaxResult.success(status);
        } catch (Exception e) {
            logger.error("获取Token缓存状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取Token缓存状态失败: " + e.getMessage());
        }
    }

    /**
     * 清除Token缓存
     */
    @PostMapping("/clearTokenCache")
    public AjaxResult clearTokenCache() {
        try {
            wxMessageService.clearTokenCache();
            return AjaxResult.success("Token缓存已清除");
        } catch (Exception e) {
            logger.error("清除Token缓存失败: {}", e.getMessage(), e);
            return AjaxResult.error("清除Token缓存失败: " + e.getMessage());
        }
    }

    /**
     * 预热Token缓存
     */
    @PostMapping("/warmupTokenCache")
    public AjaxResult warmupTokenCache() {
        try {
            String token = wxMessageService.getAccessToken();
            if (StringUtils.isNotEmpty(token)) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("tokenLength", token.length());
                result.put("cacheStatus", wxMessageService.getTokenCacheStatus());
                return AjaxResult.success("Token缓存预热成功", result);
            } else {
                return AjaxResult.error("Token获取失败");
            }
        } catch (Exception e) {
            logger.error("预热Token缓存失败: {}", e.getMessage(), e);
            return AjaxResult.error("预热Token缓存失败: " + e.getMessage());
        }
    }
}

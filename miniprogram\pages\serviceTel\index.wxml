<!--pages/serviceTel/index.wxml-->
<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24px" text-size="14px">加载中...</van-loading>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <van-empty description="{{error}}">
      <van-button round type="primary" class="retry-btn" bind:click="onRetry">重新加载</van-button>
    </van-empty>
  </view>

  <!-- 标签卡导航 -->
  <view wx:else>
    <!-- 搜索框 -->
    <view class="search-container">
      <van-search
        value="{{ searchKeyword }}"
        placeholder="搜索联系人或电话号码"
        bind:search="onSearch"
        bind:input="onSearchInput"
        bind:clear="onSearchClear"
        show-action="{{ searchKeyword.length > 0 }}"
        action-text="取消"
        bind:cancel="onSearchCancel"
      />
    </view>

    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
      <van-tab title="外部号码" name="external">
        <!-- 外部号码列表 -->
        <view wx:if="{{filteredExternalContacts.length > 0}}" class="contacts-list">
          <view class="contact-section" wx:for="{{filteredExternalContacts}}" wx:key="index">
            <!-- 分类标题 -->
            <view wx:if="{{item.title}}" class="section-title">{{item.title}}</view>

            <!-- 联系人列表 -->
            <view class="contact-item"
                  wx:for="{{item.items}}"
                  wx:for-item="contact"
                  wx:key="service_tel_id">

              <!-- 左侧头像/图标 -->
              <view class="contact-avatar" style="background-color: {{contact.avatarColor}}">
                <text class="avatar-text">{{contact.avatarText}}</text>
              </view>

              <!-- 中间信息（可点击拨打） -->
              <view class="contact-info"
                    data-tel-number="{{contact.tel_number}}"
                    data-service-name="{{contact.service_name}}"
                    data-company-name="{{contact.company_name}}"
                    bind:tap="makePhoneCall">
                <view class="contact-name">{{contact.service_name}}</view>
                <view wx:if="{{contact.company_name}}" class="contact-company">{{contact.company_name}}</view>
                <view class="contact-phone">{{contact.tel_number}}</view>
              </view>

              <!-- 右侧操作按钮 -->
              <view class="contact-actions">
                <view class="action-btn call-btn"
                      data-tel-number="{{contact.tel_number}}"
                      data-service-name="{{contact.service_name}}"
                      bind:tap="makePhoneCall">
                  <van-icon name="phone-o" size="18px" color="#1989fa" />
                </view>
                <view class="action-btn more-btn"
                      data-tel-number="{{contact.tel_number}}"
                      data-service-name="{{contact.service_name}}"
                      data-company-name="{{contact.company_name}}"
                      bind:tap="showMoreActions">
                  <van-icon name="ellipsis" size="18px" color="#969799" />
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-container">
          <van-empty description="暂无外部号码">
            <van-button round type="primary" bind:click="onRetry">刷新</van-button>
          </van-empty>
        </view>
      </van-tab>

      <van-tab title="内部号码" name="internal">
        <!-- 内部号码列表 -->
        <view wx:if="{{filteredInternalContacts.length > 0}}" class="contacts-list">
          <view class="contact-section" wx:for="{{filteredInternalContacts}}" wx:key="index">
            <!-- 分类标题 -->
            <view wx:if="{{item.title}}" class="section-title">{{item.title}}</view>

            <!-- 联系人列表 -->
            <view class="contact-item"
                  wx:for="{{item.items}}"
                  wx:for-item="contact"
                  wx:key="service_tel_id">

              <!-- 左侧头像/图标 -->
              <view class="contact-avatar" style="background-color: {{contact.avatarColor}}">
                <text class="avatar-text">{{contact.avatarText}}</text>
              </view>

              <!-- 中间信息（可点击拨打） -->
              <view class="contact-info"
                    data-tel-number="{{contact.tel_number}}"
                    data-service-name="{{contact.service_name}}"
                    data-company-name="{{contact.company_name}}"
                    bind:tap="makePhoneCall">
                <view class="contact-name">{{contact.service_name}}</view>
                <view wx:if="{{contact.company_name}}" class="contact-company">{{contact.company_name}}</view>
                <view class="contact-phone">{{contact.tel_number}}</view>
              </view>

              <!-- 右侧操作按钮 -->
              <view class="contact-actions">
                <view class="action-btn call-btn"
                      data-tel-number="{{contact.tel_number}}"
                      data-service-name="{{contact.service_name}}"
                      bind:tap="makePhoneCall">
                  <van-icon name="phone-o" size="18px" color="#1989fa" />
                </view>
                <view class="action-btn more-btn"
                      data-tel-number="{{contact.tel_number}}"
                      data-service-name="{{contact.service_name}}"
                      data-company-name="{{contact.company_name}}"
                      bind:tap="showMoreActions">
                  <van-icon name="ellipsis" size="18px" color="#969799" />
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-container">
          <van-empty description="暂无内部号码">
            <van-button round type="primary" bind:click="onRetry">刷新</van-button>
          </van-empty>
        </view>
      </van-tab>
    </van-tabs>
  </view>
</view>

<!-- 更多操作弹窗 -->
<van-action-sheet
  show="{{ showActionSheet }}"
  actions="{{ actionSheetActions }}"
  bind:close="onActionSheetClose"
  bind:select="onActionSheetSelect"
  cancel-text="取消"
/>

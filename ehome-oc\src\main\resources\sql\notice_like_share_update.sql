-- 为sys_notice表添加点赞和分享字段
ALTER TABLE `sys_notice` 
ADD COLUMN `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
ADD COLUMN `share_count` int(11) DEFAULT '0' COMMENT '分享次数';

-- 为sys_notice_comment表添加点赞字段
ALTER TABLE `sys_notice_comment` 
ADD COLUMN `like_count` int(11) DEFAULT '0' COMMENT '点赞次数';

-- 创建公告点赞记录表
CREATE TABLE `sys_notice_like` (
  `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `notice_id` int(4) NOT NULL COMMENT '公告ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
  `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
  `status` char(1) DEFAULT '0' COMMENT '状态（0有效 1已取消）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`like_id`),
  UNIQUE KEY `uk_notice_user` (`notice_id`, `user_id`),
  KEY `idx_notice_id` (`notice_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告点赞记录表';

-- 创建公告分享记录表
CREATE TABLE `sys_notice_share` (
  `share_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分享ID',
  `notice_id` int(4) NOT NULL COMMENT '公告ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
  `community_id` varchar(50) DEFAULT '' COMMENT '关联小区ID',
  `share_platform` varchar(20) DEFAULT 'wechat' COMMENT '分享平台（wechat/timeline/other）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`share_id`),
  KEY `idx_notice_id` (`notice_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告分享记录表';

-- 创建评论点赞记录表
CREATE TABLE `sys_comment_like` (
  `like_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `comment_id` bigint(20) NOT NULL COMMENT '评论ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户名',
  `user_type` varchar(20) NOT NULL DEFAULT 'wx_user' COMMENT '用户类型（wx_user/sys_user）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0有效 1已取消）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`like_id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞记录表';

-- 为现有数据初始化字段值
UPDATE `sys_notice` SET `like_count` = 0, `share_count` = 0 WHERE `like_count` IS NULL OR `share_count` IS NULL;
UPDATE `sys_notice_comment` SET `like_count` = 0 WHERE `like_count` IS NULL;

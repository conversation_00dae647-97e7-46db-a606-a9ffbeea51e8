<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<!-- 邀请管理页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <van-icon name="friends-o" size="60rpx" color="#1890ff" />
    <text class="page-title">邀请管理</text>
    <text class="page-desc">查看我发出的邀请记录</text>
  </view>

  <!-- 邀请列表 -->
  <view class="invite-section">
    <!-- 我发出的邀请列表 -->
    <view class="invite-list" wx:if="{{sentInvites.length > 0}}">
        <view class="invite-item" 
              wx:for="{{sentInvites}}" 
              wx:key="invite_id">
          <view class="invite-header">
            <view class="house-info">
              <text class="house-name">{{item.house_name}}</text>
              <text class="invite-relation">{{item.rel_type_name}}</text>
            </view>
            <view class="invite-status status-{{item.status}}">
              {{item.status_name}}
            </view>
          </view>
          
          <view class="invite-details">
            <view class="detail-row">
              <text class="detail-label">邀请对象：</text>
              <text class="detail-value">{{item.invite_phone}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">邀请类型：</text>
              <text class="detail-value">小程序卡片邀请</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">创建时间：</text>
              <text class="detail-value">{{item.create_time_formatted}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">有效期至：</text>
              <text class="detail-value">{{item.expire_time_formatted}}</text>
            </view>
            <view class="detail-row" wx:if="{{item.remark}}">
              <text class="detail-label">备注：</text>
              <text class="detail-value">{{item.remark}}</text>
            </view>
            <view class="detail-row" wx:if="{{item.status == 1 && item.used_time}}">
              <text class="detail-label">使用时间：</text>
              <text class="detail-value">{{item.used_time_formatted}}</text>
            </view>
          </view>

          <!-- 过期标识 -->
          <view class="expired-tag" wx:if="{{item.is_expired}}">
            <text>已过期</text>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:else>
        <van-icon name="info-o" size="80rpx" color="#ccc" />
        <text class="empty-text">暂无发出的邀请</text>
        <van-button 
          type="primary" 
          size="small" 
          bind:click="goInvite"
          custom-style="margin-top: 20rpx;"
        >
          去邀请住户
        </van-button>
      </view>
  </view>
</view>

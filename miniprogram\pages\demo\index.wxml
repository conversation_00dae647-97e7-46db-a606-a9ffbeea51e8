<!--demo.wxml-->

<!-- 页面内容 -->
<view class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
      <view class="navbar-content">
        <view class="location-wrapper" bindtap="showDemoTip">
          <text class="navbar-location">演示小区</text>
          <van-icon name="exchange" size="32rpx" color="#fff" custom-style="margin-left: 8rpx;" />
        </view>
      </view>
    </view>

    <!-- 功能网格容器 -->
    <view class="function-container">
      <!-- 服务标题 -->
      <view class="service-title">
        <text class="title-text">物业服务</text>
      </view>

      <!-- 功能网格 -->
      <view class="function-grid">
        <view class="grid-row">
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);">
              <van-icon name="gold-coin-o" size="48rpx" color="#1890ff" />
            </view>
            <text class="grid-text">缴费</text>
          </view>
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);">
              <van-icon name="gold-coin-o" size="48rpx" color="#52c41a" />
            </view>
            <text class="grid-text">预存款充值</text>
          </view>
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);">
              <van-icon name="service" size="48rpx" color="#fa8c16" />
            </view>
            <text class="grid-text">报事报修</text>
          </view>
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #fff0f6 0%, #ffadd2 100%);">
              <van-icon name="chat-o" size="48rpx" color="#eb2f96" />
            </view>
            <text class="grid-text">投诉建议</text>
          </view>
        </view>
        <view class="grid-row">
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #f9f0ff 0%, #d3adf7 100%);">
              <van-icon name="star-o" size="48rpx" color="#722ed1" />
            </view>
            <text class="grid-text">满意度调查</text>
          </view>
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #e6fffb 0%, #87e8de 100%);">
              <van-icon name="friends-o" size="48rpx" color="#13c2c2" />
            </view>
            <text class="grid-text">邀请访客</text>
          </view>
          <view class="grid-item" bindtap="goServiceTel">
            <view class="grid-icon" style="background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);">
              <van-icon name="phone-o" size="48rpx" color="#faad14" />
            </view>
            <text class="grid-text">服务电话</text>
          </view>
          <view class="grid-item" bindtap="showLoginTip">
            <view class="grid-icon" style="background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);">
              <van-icon name="apps-o" size="48rpx" color="#f5222d" />
            </view>
            <text class="grid-text">更多服务</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 物业公告区域 -->
    <view class="news-section">
      <view class="section-header">
        <text class="section-title">物业公告</text>
        <view class="section-more" bindtap="showLoginTip">
          <text class="more-text">查看全部公告</text>
          <van-icon name="arrow" size="24rpx" color="#999" />
        </view>
      </view>

      <view class="activity-content">
        <view class="activity-item" bindtap="showLoginTip">
          <view class="activity-text">
            <text class="activity-title">演示小区公告</text>
            <text class="activity-time">2025-07-15 15:38:48</text>
          </view>
        </view>
        <view class="activity-item" bindtap="showLoginTip">
          <view class="activity-text">
            <text class="activity-title">物业服务通知</text>
            <text class="activity-time">2025-07-10 10:20:30</text>
          </view>
        </view>
        <view class="activity-item" bindtap="showLoginTip">
          <view class="activity-text">
            <text class="activity-title">小区活动公告</text>
            <text class="activity-time">2025-07-05 14:15:20</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部登录按钮 -->
    <view class="login-section">
      <van-button
        type="info"
        size="large"
        round
        block
        bindtap="showPrivacyDialog"
        custom-class="login-btn"
      >
        登录
      </van-button>
      <view class="login-tips">
        <text class="tips-text">仅小区业主可使用，登录到小区物业处手机号登录</text>
      </view>
    </view>
</view>

<!-- 登录提醒弹窗 -->
<van-dialog
  id="login-dialog"
  title="登录提醒"
  message="为了更好的体验请进行登录认证"
  show="{{ showLoginDialog }}"
  show-cancel-button
  cancel-button-text="取消"
  confirm-button-text="认证"
  bind:confirm="showPrivacyDialog"
  bind:cancel="cancelLogin"
/>

<!-- 隐私保护提示弹窗 -->
<van-popup
  show="{{ showPrivacyDialog }}"
  position="bottom"
  round
  closeable
  bind:close="rejectPrivacy"
  custom-style=""
>
  <view class="privacy-popup">
    <view class="privacy-title">用户隐私保护提示</view>
    <view class="privacy-content">
      <text>在您使用睦邻共治服务之前，请仔细阅读</text>
      <text class="privacy-link" bindtap="openPrivacyGuide">《用户隐私保护指引》</text>
      <text>。如您同意该指引，请点击"同意"开始使用本小程序。</text>
    </view>
    <view class="privacy-buttons">
      <button
        class="privacy-btn privacy-btn-cancel"
        bindtap="rejectPrivacy"
      >
        拒绝
      </button>
      <button
        class="privacy-btn privacy-btn-confirm"
        bindtap="agreePrivacy"
      >
        同意
      </button>
    </view>
  </view>
</van-popup>

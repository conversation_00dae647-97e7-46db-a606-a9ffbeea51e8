# Summernote富文本编辑器OSS公共Bucket配置

## 问题背景

在实现阿里云OSS双Bucket存储方案后，发现项目中使用Summernote富文本编辑器的页面仍然使用默认的私有Bucket上传图片，导致富文本中的图片URL会在7天后过期，影响用户体验。

## 解决方案

为所有使用Summernote富文本编辑器的页面的`sendFile`函数添加`bucketType=public`参数，确保富文本图片上传到公共读Bucket，实现永久访问。

## 修改内容

### 1. 公告管理页面

#### system/notice/add.html
- **位置**：第159-184行
- **修改**：在sendFile函数中添加：
  ```javascript
  data.append("source", "notice");
  data.append("bucketType", "public");
  ```

#### system/notice/edit.html  
- **位置**：第187-212行
- **修改**：在sendFile函数中添加：
  ```javascript
  data.append("source", "notice");
  data.append("bucketType", "public");
  ```

### 2. 微信导航配置页面

#### oc/wx/config/nav/add.html
- **位置**：第446-471行
- **修改**：在sendFile函数中添加：
  ```javascript
  data.append("bucketType", "public");
  ```
- **说明**：该页面已有`source`参数，只需添加`bucketType`

#### oc/wx/config/nav/edit.html
- **位置**：第525-550行  
- **修改**：在sendFile函数中添加：
  ```javascript
  data.append("bucketType", "public");
  ```
- **说明**：该页面已有`source`参数，只需添加`bucketType`

## 修改前后对比

### 修改前
```javascript
function sendFile(file, obj) {
    var data = new FormData();
    data.append("file", file);
    // 部分页面有source参数
    $.ajax({
        type: "POST",
        url: ctx + "common/upload",
        data: data,
        // ... 其他配置
    });
}
```

### 修改后
```javascript
function sendFile(file, obj) {
    var data = new FormData();
    data.append("file", file);
    data.append("source", "notice"); // 或 "wxNav"
    data.append("bucketType", "public");
    $.ajax({
        type: "POST",
        url: ctx + "common/upload",
        data: data,
        // ... 其他配置
    });
}
```

## 技术效果

### 1. 图片永久访问
- 富文本编辑器中的图片上传到公共读Bucket
- 返回的URL可以永久访问，不会过期
- 解决了7天过期的问题

### 2. 环境隔离
- 通过`source`参数实现文件分类管理
- 结合环境配置实现不同环境的路径隔离
- 便于文件管理和维护

### 3. 用户体验提升
- 富文本内容中的图片始终可以正常显示
- 避免了图片链接失效的问题
- 提供了稳定的内容展示

## 影响范围

### 直接影响
- 公告管理：添加和编辑公告时的富文本图片
- 微信导航：添加和编辑导航内容时的富文本图片

### 间接影响
- 数据库中存储的图片URL将指向公共Bucket
- OSS存储成本略有增加（公共Bucket存储）
- 提升了系统的稳定性和用户体验

## 注意事项

### 1. 代码生成器模板
- 按照用户要求，代码生成器模板文件保持不变
- 新生成的页面如果使用Summernote，需要手动添加相关参数

### 2. 现有数据
- 已上传的图片仍在私有Bucket中
- 新上传的图片将存储在公共Bucket中
- 可以考虑后续迁移历史数据

### 3. 安全考虑
- 公共Bucket中的图片可以被直接访问
- 确保上传的图片内容符合安全要求
- 建议定期检查和清理不必要的文件

## 测试验证

### 1. 功能测试
- [ ] 公告添加页面富文本图片上传测试
- [ ] 公告编辑页面富文本图片上传测试
- [ ] 微信导航添加页面富文本图片上传测试
- [ ] 微信导航编辑页面富文本图片上传测试

### 2. 访问测试
- [ ] 验证上传后的图片URL可以直接访问
- [ ] 验证图片在富文本中正常显示
- [ ] 验证图片URL不会过期

### 3. 环境测试
- [ ] 开发环境测试
- [ ] 测试环境测试
- [ ] 生产环境测试

## 相关文件

- `ehome-page/src/main/resources/templates/system/notice/add.html`
- `ehome-page/src/main/resources/templates/system/notice/edit.html`
- `ehome-page/src/main/resources/templates/oc/wx/config/nav/add.html`
- `ehome-page/src/main/resources/templates/oc/wx/config/nav/edit.html`

## 后续优化

### 1. 统一管理
- 考虑将sendFile函数提取为公共函数
- 统一管理富文本图片上传逻辑

### 2. 配置化
- 可以考虑通过配置控制是否使用公共Bucket
- 提供更灵活的存储策略选择

### 3. 监控告警
- 监控公共Bucket的使用情况
- 设置存储容量和访问量告警

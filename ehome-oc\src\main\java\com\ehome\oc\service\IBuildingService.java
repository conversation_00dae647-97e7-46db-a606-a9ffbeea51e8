package com.ehome.oc.service;

import com.ehome.oc.domain.Building;

import java.util.List;
import java.util.Map;

/**
 * 楼栋信息Service接口
 */
public interface IBuildingService {

    /**
     * 获取楼栋单元树
     */
    List<Map<String, Object>> getBuildingUnitTree(String communityId);

    /**
     * 查询楼栋信息
     */
    public Building selectBuildingById(Integer buildingId);

    /**
     * 查询楼栋信息列表
     */
    public List<Building> selectBuildingList(Building building);

    /**
     * 新增楼栋信息
     */
    public int insertBuilding(Building building);

    /**
     * 修改楼栋信息
     */
    public int updateBuilding(Building building);

    /**
     * 删除楼栋信息
     */
    public int deleteBuildingById(Integer buildingId);

    /**
     * 批量删除楼栋信息
     */
    public int deleteBuildingByIds(Integer[] buildingIds);

    /**
     * 更新楼栋单元数
     */
    public int updateBuildingTotalUnits(String buildingId);
} 
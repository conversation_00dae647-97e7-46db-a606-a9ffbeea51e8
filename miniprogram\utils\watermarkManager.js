// 水印管理器
import { getStateManager } from './stateManager.js'
import { getSystemInfoSyncCompat } from './systemInfoCompat.js'
import { FeatureConfig } from './configHelper.js'

class WatermarkManager {
  constructor() {
    this.watermarkElement = null
    this.isVisible = false
  }

  // 检查是否应该显示水印
  shouldShowWatermark() {
    // 检查enable_miniprogram_mark参数
    return FeatureConfig.isMarkEnabled()
  }

  // 获取水印配置
  getWatermarkConfig() {
    const app = getApp()
    const userInfo = app.globalData.userInfo

    if (!userInfo) {
      return null
    }

    // 获取用户信息
    const stateManager = getStateManager()
    const state = stateManager.getState()
    const ownerInfo = state.ownerInfo

    const userName = ownerInfo?.ownerName || userInfo?.nickName || '用户'
    const phoneNumber = ownerInfo?.mobile || userInfo?.phoneNumber || ''

    // 生成水印文本
    const watermarkText = `${userName} ${phoneNumber}`

    // 生成水印位置
    const watermarkItems = this.generateWatermarkPositions(watermarkText)

    return {
      watermarkItems
    }
  }

  // 生成水印位置
  generateWatermarkPositions(text) {
    const items = []

    // 获取系统信息
    const systemInfo = getSystemInfoSyncCompat()
    const screenWidth = systemInfo.screenWidth
    const screenHeight = systemInfo.screenHeight

    const spacing = 150 // 水印间距(px)
    const rows = Math.ceil(screenHeight / spacing) + 2 // 多加几行确保覆盖
    const cols = Math.ceil(screenWidth / spacing) + 2 // 多加几列确保覆盖

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        items.push({
          text,
          left: col * spacing - 50, // 偏移一些避免边缘
          top: row * spacing - 50
        })
      }
    }

    return items
  }

  // 创建水印元素
  createWatermark(page) {
    if (!this.shouldShowWatermark() || !page) {
      return
    }

    const config = this.getWatermarkConfig()
    if (!config) {
      return
    }

    // 更新页面数据以显示水印
    page.setData({
      watermarkConfig: config,
      showWatermark: true
    })

    this.isVisible = true
  }

  // 移除水印
  removeWatermark(page) {
    if (!page) {
      return
    }

    page.setData({
      watermarkConfig: null,
      showWatermark: false
    })

    this.isVisible = false
  }

  // 更新水印
  updateWatermark(page) {
    if (!page) {
      return
    }

    if (this.shouldShowWatermark()) {
      this.createWatermark(page)
    } else {
      this.removeWatermark(page)
    }
  }

  // 获取水印样式
  getWatermarkStyle() {
    return {
      position: 'fixed',
      top: '20rpx',
      left: '20rpx',
      right: '20rpx',
      zIndex: 1000,
      pointerEvents: 'none',
      opacity: 0.8
    }
  }
}

// 创建单例
let watermarkManager = null

export function getWatermarkManager() {
  if (!watermarkManager) {
    watermarkManager = new WatermarkManager()
  }
  return watermarkManager
}

export default getWatermarkManager

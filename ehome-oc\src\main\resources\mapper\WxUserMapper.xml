<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.WxUserMapper">

    <resultMap type="com.ehome.oc.domain.WxUser" id="WxUserResult">
        <id     property="userId"       column="user_id"/>
        <result property="openId"       column="openid"/>
        <result property="unionId"      column="unionid"/>
        <result property="sessionKey"   column="session_key"/>
        <result property="communityId"  column="community_id"/>
        <result property="ownerId"      column="owner_id"/>
        <result property="nickName"     column="nick_name"/>
        <result property="avatarUrl"    column="avatar_url"/>
        <result property="mobile"       column="mobile"/>
        <result property="gender"       column="gender"/>
        <result property="status"       column="status"/>
        <result property="loginIp"      column="login_ip"/>
        <result property="loginDate"    column="login_date"/>
        <result property="userRoles"    column="user_roles"/>
        <result property="createTime"   column="create_time"/>
        <result property="updateTime"   column="update_time"/>
    </resultMap>

    <sql id="selectWxUserVo">
        select user_id, openid, unionid, session_key, owner_id, community_id, nick_name, avatar_url, mobile, gender, status,
        login_ip, login_date, user_roles, create_time, update_time
        from eh_wx_user
    </sql>

    <select id="selectWxUserById" parameterType="Long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectWxUserByOpenid" parameterType="String" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where openid = #{openId}
    </select>

    <insert id="insertWxUser" parameterType="com.ehome.oc.domain.WxUser" useGeneratedKeys="true" keyProperty="userId">
        insert into eh_wx_user (
            openid, unionid, session_key, owner_id, nick_name, avatar_url, mobile, gender, status,
            login_ip, login_date, user_roles, create_time
        ) values (
            #{openId}, #{unionId}, #{sessionKey}, #{ownerId}, #{nickName}, #{avatarUrl}, #{mobile}, #{gender}, #{status},
            #{loginIp}, #{loginDate}, #{userRoles}, sysdate()
        )
    </insert>

    <update id="updateWxUser" parameterType="com.ehome.oc.domain.WxUser">
        update eh_wx_user
        <set>
            <if test="unionId != null">unionid = #{unionId},</if>
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="status != null">status = #{status},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="userRoles != null">user_roles = #{userRoles},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

</mapper> 
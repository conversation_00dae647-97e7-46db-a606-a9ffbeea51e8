<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<!-- 页面loading状态 -->
<view class="page-loading" wx:if="{{pageLoading}}">
  <van-loading type="spinner" size="48rpx" color="#1890ff" />
</view>

<!-- 页面内容 -->
<view class="container" wx:if="{{!pageLoading}}">
  <!-- 菜单分类展示 -->
  <view class="menu-sections" wx:if="{{menuSections.length > 0}}">
    <view class="category-section" wx:for="{{menuSections}}" wx:key="index">
      <!-- 分类标题 -->
      <view class="category-title">
        {{item.title}}
      </view>

      <!-- 菜单网格 -->
      <view class="function-grid">
        <view class="grid-item"
              wx:for="{{item.items}}"
              wx:for-item="menu"
              wx:key="nav_id"
              bindtap="handleMenuTap"
              data-menu="{{menu}}">
          <view class="grid-icon" style="background: {{menu.iconBg}};">
            <van-icon name="{{menu.icon_name || 'apps-o'}}" size="48rpx" color="#fff" />
          </view>
          <text class="grid-text">{{menu.nav_name}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{menuSections.length === 0}}">
    <van-icon name="apps-o" size="120rpx" color="#ddd" />
    <text class="empty-text">暂无服务</text>
  </view>
</view>

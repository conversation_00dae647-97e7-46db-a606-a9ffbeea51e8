package com.ehome.jfinal.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 配置数据库表
 * <AUTHOR>
 * @date 2020-08-18
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.TYPE })
public @interface Table {

	String DEFAULT_PRIMARY_KEY="id";
	/**
	 * 数据表名称
	 * @return
	 */
	String tableName();
	
	/**
	 * 主键，默认为id
	 * @return
	 */
	String primaryKey() default DEFAULT_PRIMARY_KEY;

	/**
	 * 数据源名称
	 * @return
	 */
	String configName() default "";
	
}

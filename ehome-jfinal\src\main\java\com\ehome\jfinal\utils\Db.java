package com.ehome.jfinal.utils;

import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 增强的JFinal数据库操作类
 * 替代原生Db类，添加SQL执行时间记录
 * 
 * 使用方式：将 import com.jfinal.plugin.activerecord.Db; 
 *         改为 import com.ehome.jfinal.utils.Db;
 * 
 * <AUTHOR>
 */
public class Db {
    
    private static final Logger sqlLogger = LoggerFactory.getLogger("sql-log");
    
    /**
     * 保存记录
     */
    public static boolean save(String tableName, String primaryKey, Record record) {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.save(tableName, primaryKey, record);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("save", tableName, executionTime, record, result, exception);
        }
    }
    
    /**
     * 保存记录（无主键）
     */
    public static boolean save(String tableName, Record record) {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.save(tableName, record);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("save", tableName, executionTime, record, result, exception);
        }
    }
    
    /**
     * 更新记录
     */
    public static boolean update(String tableName, String primaryKey, Record record) {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.update(tableName, primaryKey, record);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("update", tableName, executionTime, record, result, exception);
        }
    }
    
    /**
     * 更新记录（无主键）
     */
    public static boolean update(String tableName, Record record) {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.update(tableName, record);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("update", tableName, executionTime, record, result, exception);
        }
    }
    
    /**
     * 删除记录
     */
    public static boolean deleteById(String tableName, String primaryKey, Object idValue) {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.deleteById(tableName, primaryKey, idValue);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("deleteById", tableName, executionTime, idValue, result, exception);
        }
    }
    
    /**
     * 根据ID查找记录
     */
    public static Record findById(String tableName, String primaryKey, Object idValue) {
        long startTime = System.currentTimeMillis();
        Record result = null;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.findById(tableName, primaryKey, idValue);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logDbOperation("findById", tableName, executionTime, idValue, result, exception);
        }
    }
    
    /**
     * 执行SQL更新
     */
    public static int update(String sql, Object... paras) {
        long startTime = System.currentTimeMillis();
        int result = 0;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.update(sql, paras);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logSqlOperation("update", sql, executionTime, paras, result, exception);
        }
    }
    
    /**
     * 查找记录
     */
    public static List<Record> find(String sql, Object... paras) {
        long startTime = System.currentTimeMillis();
        List<Record> result = null;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.find(sql, paras);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logSqlOperation("find", sql, executionTime, paras, result, exception);
        }
    }
    
    /**
     * 查找第一条记录
     */
    public static Record findFirst(String sql, Object... paras) {
        long startTime = System.currentTimeMillis();
        Record result = null;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.findFirst(sql, paras);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logSqlOperation("findFirst", sql, executionTime, paras, result, exception);
        }
    }
    
    /**
     * 分页查询
     */
    public static Page<Record> paginate(int pageNumber, int pageSize, String select, String sqlExceptSelect, Object... paras) {
        long startTime = System.currentTimeMillis();
        Page<Record> result = null;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.paginate(pageNumber, pageSize, select, sqlExceptSelect, paras);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            String fullSql = select + " " + sqlExceptSelect;
            logSqlOperation("paginate", fullSql, executionTime, paras, result, exception);
        }
    }
    
    /**
     * 批量更新
     */
    public static int[] batch(String sql, Object[][] paras, int batchSize) {
        long startTime = System.currentTimeMillis();
        int[] result = null;
        Throwable exception = null;
        
        try {
            result = com.jfinal.plugin.activerecord.Db.batch(sql, paras, batchSize);
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logSqlOperation("batch", sql, executionTime, paras, result, exception);
        }
    }
    
    /**
     * 批量更新（默认批次大小）
     */
    public static int[] batch(String sql, Object[][] paras) {
        return batch(sql, paras, 512);
    }
    
    /**
     * 记录数据库操作日志
     */
    private static void logDbOperation(String operation, String tableName, long executionTime, 
                                     Object params, Object result, Throwable exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[JFinal-Enhanced] ");
        logMessage.append("SQL执行 - ");
        logMessage.append("表: ").append(tableName).append(", ");
        logMessage.append("操作: ").append(operation).append(", ");
        logMessage.append("执行时间: ").append(executionTime).append("ms");
        
        if (params != null) {
            logMessage.append(", 参数: ").append(formatParameter(params));
        }
        
        if (result != null) {
            logMessage.append(", 结果: ").append(formatResult(result));
        }
        
        logWithLevel(logMessage.toString(), executionTime, exception);
    }
    
    /**
     * 记录SQL操作日志
     */
    private static void logSqlOperation(String operation, String sql, long executionTime,
                                      Object[] params, Object result, Throwable exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[JFinal-Enhanced] ");
        logMessage.append("SQL执行 - ");
        logMessage.append("操作: ").append(operation).append(", ");
        logMessage.append("执行时间: ").append(executionTime).append("ms");
        logMessage.append(", SQL: ").append(formatSql(sql));
        
        if (params != null && params.length > 0) {
            logMessage.append(", 参数: ").append(formatParameters(params));
        }
        
        if (result != null) {
            logMessage.append(", 结果: ").append(formatResult(result));
        }
        
        logWithLevel(logMessage.toString(), executionTime, exception);
    }
    
    /**
     * 记录批量操作日志
     */
    private static void logSqlOperation(String operation, String sql, long executionTime,
                                      Object[][] params, Object result, Throwable exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[JFinal-Enhanced] ");
        logMessage.append("SQL执行 - ");
        logMessage.append("操作: ").append(operation).append(", ");
        logMessage.append("执行时间: ").append(executionTime).append("ms");
        logMessage.append(", SQL: ").append(formatSql(sql));
        
        if (params != null && params.length > 0) {
            logMessage.append(", 批量参数: ").append(params.length).append("组");
        }
        
        if (result != null) {
            logMessage.append(", 结果: ").append(formatResult(result));
        }
        
        logWithLevel(logMessage.toString(), executionTime, exception);
    }
    
    /**
     * 根据执行时间选择日志级别
     */
    private static void logWithLevel(String message, long executionTime, Throwable exception) {
        if (exception != null) {
            sqlLogger.error(message + ", 异常: " + exception.getMessage(), exception);
        } else if (executionTime > 1000) {
            sqlLogger.warn(message + " [慢SQL警告]");
        } else if (executionTime > 500) {
            sqlLogger.info(message + " [性能关注]");
        } else {
            sqlLogger.debug(message);
        }
    }
    
    /**
     * 格式化参数
     */
    private static String formatParameter(Object param) {
        if (param == null) return "null";
        String str = param.toString();
        return str.length() > 100 ? str.substring(0, 100) + "..." : str;
    }
    
    /**
     * 格式化参数数组
     */
    private static String formatParameters(Object[] params) {
        if (params == null || params.length == 0) return "无";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < params.length && i < 5; i++) {
            if (i > 0) sb.append(", ");
            sb.append(formatParameter(params[i]));
        }
        if (params.length > 5) {
            sb.append("...(").append(params.length).append("个参数)");
        }
        return sb.toString();
    }
    
    /**
     * 格式化SQL
     */
    private static String formatSql(String sql) {
        if (sql == null) return "null";
        String cleanSql = sql.replaceAll("\\s+", " ").trim();
        return cleanSql.length() > 200 ? cleanSql.substring(0, 200) + "..." : cleanSql;
    }
    
    /**
     * 格式化结果
     */
    private static String formatResult(Object result) {
        if (result == null) return "null";
        
        if (result instanceof List) {
            return "List(" + ((List<?>) result).size() + "条记录)";
        } else if (result instanceof Page) {
            Page<?> page = (Page<?>) result;
            return "Page(第" + page.getPageNumber() + "页, " + page.getList().size() + "条记录, 共" + page.getTotalRow() + "条)";
        } else if (result instanceof Record) {
            return "Record(1条记录)";
        } else if (result instanceof Boolean) {
            return "操作" + (((Boolean) result) ? "成功" : "失败");
        } else if (result instanceof Number) {
            return "影响行数: " + result;
        } else if (result instanceof int[]) {
            int[] arr = (int[]) result;
            return "批量操作(" + arr.length + "条)";
        } else {
            return result.getClass().getSimpleName();
        }
    }
}

# FileAccessUtils 文件处理工具类使用指南

## 概述

FileAccessUtils 是一个统一的文件处理工具类，整合了文件上传、选择、回显等功能，提供简洁的API接口供页面调用。

## 主要功能

### 1. 文件访问功能
- `getFileAccessUrl(fileId, expiration)` - 通过文件ID获取访问URL
- `getSmartFileUrl(fileIdOrUrl, expiration)` - 智能识别文件ID或直接URL
- `downloadFile(fileIdOrUrl, fileName, expiration)` - 下载文件
- `previewFile(fileIdOrUrl, expiration, fileName)` - 预览文件

### 2. 文件上传功能
- `uploadFiles(options, callback)` - 简化的文件上传
- `openFileUploader(options, callback)` - 打开文件上传器
- `handleSummernoteUpload(file, editor, source, bucketType)` - 处理富文本编辑器上传
- `setupSummernoteUpload(selector, source, bucketType)` - 设置富文本编辑器上传

### 3. 文件选择功能
- `selectSingleFile(options, callback)` - 简化的单文件选择
- `selectMultipleFiles(options, callback)` - 简化的多文件选择
- `openFileSelector(options, callback)` - 打开文件选择器

### 4. 文件处理工具
- `normalizeFileObject(fileData)` - 标准化文件对象格式
- `isFileExists(newFile, existingFiles)` - 检查文件是否已存在（优先使用fileId判重）
- `createFileDisplayHtml(files, options)` - 创建文件显示HTML（支持基于fileId的删除）
- `updateFileIds(files, fieldSelector)` - 更新文件ID隐藏字段
- `removeFileById(files, fileId)` - 根据文件ID从数组中移除文件
- `findFileIndexById(files, fileId)` - 根据文件ID查找文件索引
- `removeFile(files, fileId, callback)` - 智能删除文件（根据isUpload字段自动判断是否调用删除接口）

## 使用示例

### 基本用法

#### 1. 单文件选择
```javascript
FileAccessUtils.selectSingleFile({
    fileType: 'pdf',
    title: '选择PDF文件'
}, function(selectedFile) {
    console.log('选中的文件:', selectedFile);
    $('#selectedFileName').text(selectedFile.fileName);
    $('#selectedFileId').val(selectedFile.fileId);
});
```

#### 2. 多文件选择
```javascript
FileAccessUtils.selectMultipleFiles({
    fileType: 'image',
    title: '选择图片文件'
}, function(selectedFiles) {
    console.log('选中的文件:', selectedFiles);
    // 更新文件列表显示
    var html = FileAccessUtils.createFileDisplayHtml(selectedFiles, {
        showRemove: true,
        removeCallback: 'removeSelectedFile'
    });
    $('#fileList').html(html);
    // 更新隐藏字段
    FileAccessUtils.updateFileIds(selectedFiles, '#fileIds');
});
```

#### 3. 文件上传
```javascript
FileAccessUtils.uploadFiles({
    fileType: 'all',
    source: 'example',
    bucketType: 'public'
}, function(uploadedFiles) {
    console.log('上传的文件:', uploadedFiles);
    // 处理上传的文件
    var normalizedFiles = uploadedFiles.map(function(file) {
        return FileAccessUtils.normalizeFileObject(file);
    });
    
    // 更新显示
    var html = FileAccessUtils.createFileDisplayHtml(normalizedFiles);
    $('#uploadedFileList').html(html);
});
```

#### 4. 设置富文本编辑器上传
```javascript
FileAccessUtils.setupSummernoteUpload('#summernote', 'article', 'public');
```

### 完整的文件管理示例

```javascript
var FileManager = {
    selectedFiles: [],
    
    // 初始化
    init: function() {
        this.loadExistingFiles();
    },
    
    // 选择文件（叠加逻辑）
    selectFiles: function() {
        FileAccessUtils.selectMultipleFiles({
            fileType: 'pdf',
            title: '选择PDF文件'
        }, function(files) {
            // 合并文件，避免重复
            var addedCount = 0;
            files.forEach(function(file) {
                if (!FileAccessUtils.isFileExists(file, FileManager.selectedFiles)) {
                    FileManager.selectedFiles.push(file);
                    addedCount++;
                }
            });
            FileManager.updateDisplay();

            if (addedCount > 0) {
                $.modal.msgSuccess('成功添加 ' + addedCount + ' 个文件');
            } else {
                $.modal.msgWarning('所有文件都已存在，未添加新文件');
            }
        });
    },
    
    // 上传文件
    uploadFiles: function() {
        FileAccessUtils.uploadFiles({
            fileType: 'pdf',
            source: 'document',
            bucketType: 'public'
        }, function(uploadedFiles) {
            // 添加上传的文件
            uploadedFiles.forEach(function(file) {
                var normalizedFile = FileAccessUtils.normalizeFileObject(file);
                if (!FileAccessUtils.isFileExists(normalizedFile, FileManager.selectedFiles)) {
                    FileManager.selectedFiles.push(normalizedFile);
                }
            });
            FileManager.updateDisplay();
        });
    },
    
    // 删除文件（通过fileId删除）
    removeFile: function(fileId) {
        FileManager.selectedFiles = FileAccessUtils.removeFileById(FileManager.selectedFiles, fileId);
        FileManager.updateDisplay();
    },
    
    // 更新显示
    updateDisplay: function() {
        var html = FileAccessUtils.createFileDisplayHtml(this.selectedFiles, {
            showRemove: true,
            removeCallback: 'FileManager.removeFile'
        });
        $('#fileList').html(html);
        
        // 更新隐藏字段
        FileAccessUtils.updateFileIds(this.selectedFiles, '#fileIds');
    }
};
```

## 参数说明

### 文件选择选项 (options)
- `mode`: 选择模式 ('single' | 'multiple')
- `fileType`: 文件类型限制 ('pdf', 'image', 'all' 等)
- `title`: 对话框标题

### 文件上传选项 (options)
- `fileType`: 文件类型限制
- `source`: 来源标识
- `bucketType`: 存储桶类型 (默认: 'public')
- `businessId`: 业务ID (可选)

### 文件显示选项 (options)
- `showRemove`: 是否显示删除按钮 (默认: true)
- `removeCallback`: 删除回调函数名

## 最佳实践

### 删除回调函数实现
`createFileDisplayHtml` 会根据文件的 `isUpload` 字段自动生成正确的删除回调参数：

```javascript
function removeFile(fileId, needDeleteServer) {
    selectedFiles = FileAccessUtils.removeFile(selectedFiles, fileId, needDeleteServer, function(updatedFiles, success, errorMsg) {
        selectedFiles = updatedFiles;
        updateDisplay();

        if (success) {
            $.modal.msgSuccess('文件删除成功');
        } else {
            $.modal.msgWarning('文件已从列表移除' + (errorMsg ? '，但服务器删除失败：' + errorMsg : ''));
        }
    });
}
```

#### 文件类型标识和删除逻辑
- **上传文件**：文件对象中 `isUpload` 字段为 `1` 或 `'1'`
  - `createFileDisplayHtml` 生成的删除按钮：`removeFile('fileId')`
  - 删除时会调用 `/file/delete` 接口删除服务器文件
- **选择文件**：文件对象中 `isUpload` 字段不为 `1`
  - `createFileDisplayHtml` 生成的删除按钮：`removeFile('fileId', false)`
  - 删除时只从数组中移除，不调用删除接口

#### 简单删除（仅从数组移除）
如果确定只需要从数组中移除文件，不需要删除服务器文件：

```javascript
function removeFile(fileId) {
    selectedFiles = FileAccessUtils.removeFileById(selectedFiles, fileId);
    updateDisplay();
}
```

### 文件选择逻辑
#### 单选模式
单选模式应该替换现有选择：

```javascript
FileAccessUtils.selectSingleFile({...}, function(file) {
    selectedFiles = [file]; // 替换逻辑
    updateDisplay();
});
```

#### 多选模式
多选模式应该叠加到现有文件：

```javascript
FileAccessUtils.selectMultipleFiles({...}, function(files) {
    // 叠加逻辑，避免重复
    var addedCount = 0;
    files.forEach(function(file) {
        if (!FileAccessUtils.isFileExists(file, selectedFiles)) {
            selectedFiles.push(file);
            addedCount++;
        }
    });
    updateDisplay();

    if (addedCount > 0) {
        $.modal.msgSuccess('成功添加 ' + addedCount + ' 个文件');
    } else {
        $.modal.msgWarning('所有文件都已存在，未添加新文件');
    }
});
```

### 文件判重
判重逻辑使用 fileId，简单可靠：

```javascript
// 添加文件前检查重复
files.forEach(function(file) {
    if (!FileAccessUtils.isFileExists(file, selectedFiles)) {
        selectedFiles.push(file);
    }
});
```

## 兼容性

工具类提供了完整的向后兼容性：

### 全局函数兼容
```javascript
// 旧的调用方式仍然有效
window.getFileDownloadUrl(fileId);
window.formatFileSize(bytes);
window.getFileIconHtml(fileType);
window.normalizeFileObject(fileData);
window.sendFile(file, obj, source, bucketType);
```

### 现有页面兼容
现有的 `setSelectedFile` 和 `setSelectedAttachments` 回调函数会自动处理，无需修改现有页面代码。

## 迁移指南

### 从现有代码迁移

#### 1. 替换文件选择逻辑
**原来的代码:**
```javascript
function selectPdfFiles() {
    layer.open({
        title: '选择PDF文件',
        type: 2,
        content: ctx + 'file/multiSelect?fileType=pdf'
    });
}
```

**新的代码:**
```javascript
function selectPdfFiles() {
    FileAccessUtils.selectMultipleFiles({
        fileType: 'pdf',
        title: '选择PDF文件'
    }, function(files) {
        // 处理选中的文件
        selectedFiles = files;
        updateDisplay();
    });
}
```

#### 2. 替换文件上传逻辑
**原来的代码:**
```javascript
function openUploadFiles() {
    layer.open({
        type: 2,
        title: '上传文件',
        content: ctx + 'file/uploader?fileType=pdf&source=example'
    });
}
```

**新的代码:**
```javascript
function openUploadFiles() {
    FileAccessUtils.uploadFiles({
        fileType: 'pdf',
        source: 'example'
    }, function(uploadedFiles) {
        // 处理上传的文件
        handleUploadedFiles(uploadedFiles);
    });
}
```

## 样式要求

页面需要包含以下CSS样式以正确显示文件列表：

```css
.attachment-list {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    min-height: 50px;
    background-color: #f9f9f9;
}

.attachment-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.attachment-item:last-child {
    border-bottom: none;
}

.file-icon {
    margin-right: 8px;
    width: 20px;
    text-align: center;
}

.file-name {
    flex: 1;
    margin-right: 10px;
}

.remove-btn {
    color: #d9534f;
    cursor: pointer;
    padding: 2px 5px;
}

.remove-btn:hover {
    background-color: #f5f5f5;
    border-radius: 3px;
}

.attachment-empty {
    text-align: center;
    color: #999;
    padding: 20px;
}
```

## BusinessId 处理

### 编辑页面
在编辑页面中，可以直接使用现有的业务ID：
```javascript
FileAccessUtils.uploadFiles({
    source: 'notice',
    bucketType: 'public',
    businessId: $('#noticeId').val() // 使用现有的业务ID
}, callback);
```

### 新增页面
在新增页面中，由于还没有生成业务ID，需要使用临时ID：
```javascript
// 生成临时业务ID
var tempBusinessId = generateTempBusinessId();

function generateTempBusinessId() {
    return 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 使用临时ID上传文件
FileAccessUtils.uploadFiles({
    source: 'notice',
    bucketType: 'public',
    businessId: tempBusinessId
}, callback);
```

### 后端处理建议
1. **临时文件关联**：后端可以根据临时businessId来关联文件
2. **保存时更新**：在保存业务数据成功后，将临时businessId更新为真实的业务ID
3. **清理机制**：定期清理未关联的临时文件

## 注意事项

1. 确保页面引入了 `fileAccessUtils.js` 文件
2. 文件选择和上传功能依赖于 layer 弹窗组件
3. 富文本编辑器功能需要引入 summernote 相关文件
4. 所有回调函数都会自动处理错误情况
5. 文件对象会自动标准化为统一格式，同时保持向后兼容
6. 新增页面需要生成临时businessId用于文件关联

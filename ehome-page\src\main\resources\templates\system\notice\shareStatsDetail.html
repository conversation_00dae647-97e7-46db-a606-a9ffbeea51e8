<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('公告分享统计详情')" />
    <style>
        .container-div{
            padding: 10px;
        }
        .stats-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stats-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .notice-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>公告分享统计详情</h5>
                        <div class="ibox-tools">
                            <a class="btn btn-primary btn-xs" onclick="refreshStats()">
                                <i class="fa fa-refresh"></i> 刷新
                            </a>
                            <a class="btn btn-default btn-xs" onclick="goBack()">
                                <i class="fa fa-arrow-left"></i> 返回
                            </a>
                        </div>
                    </div>
                    <div class="ibox-content">
                        <!-- 公告信息 -->
                        <div class="notice-info" th:if="${notice}">
                            <h4 th:text="${notice.noticeTitle}">公告标题</h4>
                            <p class="text-muted">
                                发布时间：<span th:text="${#dates.format(notice.createTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                | 发布人：<span th:text="${notice.createBy}"></span>
                            </p>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card text-center">
                                    <div class="stats-number" id="shareCount">0</div>
                                    <div class="stats-label">分享次数</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card text-center">
                                    <div class="stats-number" id="totalVisits">0</div>
                                    <div class="stats-label">总访问次数</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card text-center">
                                    <div class="stats-number" id="uniqueVisitors">0</div>
                                    <div class="stats-label">独立访问者</div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <div class="stats-card text-center">
                                    <div class="stats-number" id="newVisitors">0</div>
                                    <div class="stats-label">新访问者</div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近分享记录 -->
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="stats-card">
                                    <h4>最近分享记录</h4>
                                    <table class="table table-striped" id="recentSharesTable">
                                        <thead>
                                            <tr>
                                                <th>分享者</th>
                                                <th>分享时间</th>
                                                <th>访问次数</th>
                                                <th>最后访问</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="4" class="text-center">暂无数据</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="stats-card">
                                    <h4>最近访问记录</h4>
                                    <table class="table table-striped" id="recentVisitsTable">
                                        <thead>
                                            <tr>
                                                <th>访问者</th>
                                                <th>访问时间</th>
                                                <th>新访问者</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="3" class="text-center">暂无数据</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var ctx = [[@{/}]];
        var prefix = ctx + "system/notice";
        var noticeId = [[${noticeId}]] || null;

        $(function() {
            if (noticeId && noticeId !== 'undefined') {
                loadStatsData();
            } else {
                $.modal.msgError("公告ID无效");
            }
        });

        // 加载统计数据
        function loadStatsData() {
            $.get(prefix + "/shareStats/data/" + noticeId, function(result) {
                if (result.code == 0) {
                    updateStatsCards(result.data);
                    updateRecentShares(result.data.recentShares || []);
                    updateRecentVisits(result.data.recentVisits || []);
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        }

        // 更新统计卡片
        function updateStatsCards(data) {
            $("#shareCount").text(data.shareCount || 0);
            $("#totalVisits").text(data.totalVisits || 0);
            $("#uniqueVisitors").text(data.uniqueVisitors || 0);
            $("#newVisitors").text(data.newVisitors || 0);
        }

        // 更新最近分享记录
        function updateRecentShares(shares) {
            var tbody = $("#recentSharesTable tbody");
            tbody.empty();
            
            if (shares.length === 0) {
                tbody.append('<tr><td colspan="4" class="text-center">暂无数据</td></tr>');
                return;
            }
            
            shares.forEach(function(share) {
                var row = '<tr>' +
                    '<td>' + (share.user_name || '未知用户') + '</td>' +
                    '<td>' + formatDateTime(share.create_time) + '</td>' +
                    '<td>' + (share.visit_count || 0) + '</td>' +
                    '<td>' + (share.last_visit_time ? formatDateTime(share.last_visit_time) : '无') + '</td>' +
                '</tr>';
                tbody.append(row);
            });
        }

        // 更新最近访问记录
        function updateRecentVisits(visits) {
            var tbody = $("#recentVisitsTable tbody");
            tbody.empty();
            
            if (visits.length === 0) {
                tbody.append('<tr><td colspan="3" class="text-center">暂无数据</td></tr>');
                return;
            }
            
            visits.forEach(function(visit) {
                var isNew = visit.is_new_visitor == 1;
                var row = '<tr>' +
                    '<td>' + (visit.visitor_name || '匿名访问者') + '</td>' +
                    '<td>' + formatDateTime(visit.visit_time) + '</td>' +
                    '<td>' + (isNew ? '<span class="badge badge-success">是</span>' : '<span class="badge badge-default">否</span>') + '</td>' +
                '</tr>';
                tbody.append(row);
            });
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            var date = new Date(dateStr);
            return date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   String(date.getHours()).padStart(2, '0') + ':' + 
                   String(date.getMinutes()).padStart(2, '0') + ':' + 
                   String(date.getSeconds()).padStart(2, '0');
        }

        // 刷新统计
        function refreshStats() {
            loadStatsData();
            $.modal.msgSuccess("统计数据已刷新");
        }

        // 返回
        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>

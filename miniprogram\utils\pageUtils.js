/**
 * 页面工具类 - 提供公共的页面功能
 */

/**
 * 更新TabBar选中状态
 * @param {number} selectedIndex 选中的tab索引
 */
export function updateTabBarSelected(selectedIndex) {
  if (typeof getCurrentPages === 'function') {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    
    if (typeof currentPage.getTabBar === 'function' && currentPage.getTabBar()) {
      currentPage.getTabBar().setSelected(selectedIndex)
    }
  }
}

/**
 * 物业页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {number} tabIndex tab索引
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handlePropertyPageShow(pageInstance, tabIndex, loadDataFn) {
  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
  
  // 更新TabBar选中状态
  updateTabBarSelected(tabIndex)
}

/**
 * 业主页面通用的onShow处理
 * @param {Object} pageInstance 页面实例
 * @param {number} tabIndex tab索引
 * @param {Function} loadDataFn 加载数据的函数
 */
export function handleOwnerPageShow(pageInstance, tabIndex, loadDataFn) {
  // 加载页面数据
  if (typeof loadDataFn === 'function') {
    loadDataFn.call(pageInstance)
  }
  
  // 更新TabBar选中状态
  updateTabBarSelected(tabIndex)
}

/**
 * 获取当前用户类型
 * @returns {string} 用户类型 '1'=业主, '2'=物业
 */
export function getCurrentUserType() {
  try {
    const app = getApp()
    if (app && app.globalData && app.globalData.stateManager) {
      const state = app.globalData.stateManager.getState()
      return state.userType || '1'
    }
  } catch (error) {
    console.error('获取用户类型失败:', error)
  }
  return '1' // 默认业主
}

/**
 * 检查是否为物业用户
 * @returns {boolean}
 */
export function isPropertyUser() {
  return getCurrentUserType() === '2'
}

/**
 * 检查是否为业主用户
 * @returns {boolean}
 */
export function isOwnerUser() {
  return getCurrentUserType() === '1'
}

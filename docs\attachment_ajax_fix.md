# 公告附件Ajax加载修复

## 问题描述
用户反馈：公告页面的附件还是渲染不出来，需要改为Ajax请求单独查询attachments。

## 解决方案
将原来通过Thymeleaf模板直接渲染附件数据的方式改为Ajax异步加载。

## 修改内容

### 1. 后端修改

#### SysNoticeController.java
- **新增Ajax接口**：`/system/notice/attachments/{noticeId}`
  ```java
  @GetMapping("/attachments/{noticeId}")
  @ResponseBody
  public AjaxResult getAttachments(@PathVariable("noticeId") Long noticeId)
  ```

- **简化edit方法**：移除原来的附件数据传递
- **简化view方法**：移除原来的附件数据传递
- **移除未使用的import**：`IpUtils`

### 2. 前端修改

#### edit.html
- **修改loadExistingAttachments()函数**：
  - 原来：从Thymeleaf模板变量中获取附件数据
  - 现在：通过Ajax请求 `/system/notice/attachments/{noticeId}` 获取附件数据

#### view.html  
- **简化HTML结构**：移除Thymeleaf条件判断，使用固定的容器结构
- **新增loadAttachments()函数**：通过Ajax加载附件列表
- **修改页面初始化**：调用loadAttachments()而不是处理已有DOM元素

## 技术细节

### Ajax请求格式
```javascript
$.ajax({
    url: prefix + "/attachments/" + noticeId,
    type: "GET",
    dataType: "json",
    success: function(result) {
        if (result.code === 0 && result.data) {
            // 处理附件数据
        }
    }
});
```

### 返回数据格式
```json
{
    "code": 0,
    "msg": "操作成功",
    "data": [
        {
            "file_id": "文件ID",
            "original_name": "原始文件名",
            "file_type": "文件类型",
            "file_url": "文件URL",
            "file_size": "文件大小"
        }
    ]
}
```

### 3. 使用jsrender模板替代字符串拼接

#### edit.html
- **添加jsrender模板**：`attachmentListTmpl`
  ```html
  <script id="attachmentListTmpl" type="text/x-jsrender">
      {{if length > 0}}
          {{for #data}}
              <div class="attachment-item">
                  <span class="file-icon">{{:~getFileIcon(file_type)}}</span>
                  <span title="{{:original_name}}">...</span>
                  <span class="remove-btn" onclick="removeAttachment({{:#index}})" title="删除">
                      <i class="fa fa-times"></i>
                  </span>
              </div>
          {{/for}}
      {{else}}
          <div class="attachment-empty">暂无附件</div>
      {{/if}}
  </script>
  ```

- **修改updateAttachmentDisplay()函数**：
  ```javascript
  function updateAttachmentDisplay() {
      var tmpl = $.templates("#attachmentListTmpl");
      var html = tmpl.render(selectedAttachments, {
          getFileIcon: function(fileType) {
              return getFileIcon(fileType);
          }
      });
      $('#attachmentList').html(html);
  }
  ```

#### view.html
- **添加jsrender模板**：`attachmentViewTmpl`
  ```html
  <script id="attachmentViewTmpl" type="text/x-jsrender">
      {{if length > 0}}
          {{for #data}}
              <a href="{{:file_url}}" target="_blank" class="attachment-item">
                  <span class="file-icon">{{:~getFileIconHtml(file_type)}}</span>
                  <span>{{:original_name}}</span>
                  <span class="file-size">({{:~formatFileSize(file_size)}})</span>
              </a>
          {{/for}}
      {{else}}
          <div class="no-attachments">暂无附件</div>
      {{/if}}
  </script>
  ```

- **修改loadAttachments()函数**：使用jsrender模板渲染

## 优势
1. **解决渲染问题**：避免Thymeleaf模板渲染复杂数据时的问题
2. **提高灵活性**：可以动态刷新附件列表，无需重新加载页面
3. **错误处理**：提供更好的错误处理和用户反馈
4. **性能优化**：页面初始加载更快，附件数据异步加载
5. **代码维护性**：使用jsrender模板替代字符串拼接，代码更清晰易维护
6. **模板复用**：jsrender模板可以在多个地方复用

## 测试建议
1. 测试编辑页面附件加载
2. 测试详情页面附件显示
3. 测试网络异常情况下的错误处理
4. 验证附件文件类型图标显示
5. 验证附件下载功能
6. 验证jsrender模板渲染效果

/**
 * 小程序文件访问管理器
 * 统一处理文件ID和URL的访问逻辑，支持OSS和本地文件
 */

const app = getApp()

class FileAccessManager {
  constructor() {
    this.baseUrl = app.globalData.baseUrl
    this.accessCache = new Map() // 访问URL缓存
  }

  /**
   * 通过文件ID获取完整文件信息
   * @param {string} fileId 文件ID
   * @returns {Promise<object>} 文件信息对象
   */
  async getFileInfo(fileId) {
    if (!fileId) {
      throw new Error('文件ID不能为空')
    }

    // 检查缓存
    const cacheKey = `fileInfo_${fileId}`
    if (this.accessCache.has(cacheKey)) {
      const cached = this.accessCache.get(cacheKey)
      if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
        return cached.data
      }
    }

    try {
      const token = wx.getStorageSync('token')
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {}

      const res = await new Promise((resolve, reject) => {
        wx.request({
          url: `${this.baseUrl}/api/wx/file/access`,
          method: 'POST',
          data: {
            fileId: fileId
          },
          header: headers,
          success: resolve,
          fail: reject
        })
      })

      if (res.data && res.data.code === 0) {
        const fileInfo = {
          fileId: res.data.fileId,
          fileName: res.data.fileName,
          fileType: res.data.fileType,
          mimeType: res.data.mimeType,
          fileSize: res.data.fileSize,
          url: res.data.url,
          storageType: res.data.storageType,
          previewType: res.data.previewType,
          canPreview: res.data.canPreview
        }

        // 缓存结果
        this.accessCache.set(cacheKey, {
          data: fileInfo,
          timestamp: Date.now()
        })

        return fileInfo
      } else {
        throw new Error(res.data?.msg || '获取文件信息失败')
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 通过文件ID获取访问URL
   * @param {string} fileId 文件ID
   * @returns {Promise<string>} 访问URL
   */
  async getFileAccessUrl(fileId) {
    const fileInfo = await this.getFileInfo(fileId)
    return fileInfo.url
  }

  /**
   * 智能文件访问 - 自动识别文件ID或直接URL
   * @param {string} fileIdOrUrl 文件ID或直接URL
   * @returns {Promise<string>} 访问URL
   */
  async getSmartFileUrl(fileIdOrUrl) {
    if (!fileIdOrUrl) {
      throw new Error('文件标识不能为空')
    }

    const identifier = String(fileIdOrUrl).trim()

    // 如果已经是完整的HTTP URL，直接返回
    if (identifier.startsWith('http://') || identifier.startsWith('https://')) {
      return identifier
    }

    // 如果是相对路径URL，补全为完整URL
    if (identifier.startsWith('/')) {
      return this.baseUrl + identifier
    }

    // 检查是否看起来像文件ID（通常是字母数字组合，长度合理）
    const isLikelyFileId = /^[a-zA-Z0-9]{8,32}$/.test(identifier)

    if (isLikelyFileId) {
      // 当作文件ID处理
      try {
        return await this.getFileAccessUrl(identifier)
      } catch (error) {
        return this.baseUrl + '/' + identifier
      }
    } else {
      // 不像文件ID，直接作为相对路径处理
      return this.baseUrl + '/' + identifier
    }
  }

  /**
   * 下载文件
   * @param {string} fileIdOrUrl 文件ID或URL
   * @param {Function} onProgress 下载进度回调（可选）
   * @returns {Promise<object>} 下载结果
   */
  async downloadFile(fileIdOrUrl, onProgress = null) {
    try {
      const url = await this.getSmartFileUrl(fileIdOrUrl)

      return new Promise((resolve, reject) => {
        const downloadTask = wx.downloadFile({
          url: url,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res)
            } else {
              reject(new Error(`下载失败，状态码: ${res.statusCode}`))
            }
          },
          fail: (error) => {
            // 检查是否为域名校验错误
            const errMsg = error.errMsg || ''
            if (errMsg.includes('合法域名') || errMsg.includes('domain')) {
              const domain = this.extractDomain(url)
              const errorMessage = `下载失败: 域名校验错误，请确保 ${domain} 已添加到小程序合法域名列表中，或在开发工具中勾选"不校验合法域名"`
              reject(new Error(errorMessage))
            } else {
              reject(new Error(`下载失败: ${errMsg || '未知错误'}`))
            }
          }
        })

        // 如果提供了进度回调，监听下载进度
        if (onProgress && typeof onProgress === 'function') {
          downloadTask.onProgressUpdate(onProgress)
        }
      })
    } catch (error) {
      throw error
    }
  }

  /**
   * 预览文档（向后兼容方法）
   * @param {string} fileId 文件ID（优先）
   * @param {string} fallbackUrl 备用URL（可选）
   * @param {string} fileName 文件名（可选）
   * @returns {Promise<void>}
   */
  async previewDocument(fileId, fallbackUrl = null, fileName = null) {
    // 参数兼容性处理
    // 如果第一个参数不像文件ID，可能是旧版调用方式
    if (fileId && (fileId.startsWith('http://') || fileId.startsWith('https://') || fileId.startsWith('/'))) {
      // 旧版调用: previewDocument(url, fileName)
      return this.previewFile(null, fileId, fallbackUrl)
    }

    // 新版调用: previewDocument(fileId, fallbackUrl, fileName)
    return this.previewFile(fileId, fallbackUrl, fileName)
  }

  /**
   * 预览图片
   * @param {string|Array} fileIdOrUrls 文件ID/URL或数组
   * @param {number} current 当前显示图片的索引（仅数组时有效）
   * @returns {Promise<void>}
   */
  async previewImage(fileIdOrUrls, current = 0) {
    try {
      let urls = []

      if (Array.isArray(fileIdOrUrls)) {
        // 批量处理
        for (const item of fileIdOrUrls) {
          const url = await this.getSmartFileUrl(item)
          urls.push(url)
        }
      } else {
        // 单个处理
        const url = await this.getSmartFileUrl(fileIdOrUrls)
        urls = [url]
      }

      return new Promise((resolve, reject) => {
        wx.previewImage({
          urls: urls,
          current: urls[current] || urls[0],
          success: resolve,
          fail: (error) => {
            reject(new Error(`图片预览失败: ${error.errMsg || '未知错误'}`))
          }
        })
      })
    } catch (error) {
      throw error
    }
  }



  /**
   * 检查是否为图片文件
   * @param {string} fileName 文件名
   * @returns {boolean}
   */
  isImageFile(fileName) {
    if (!fileName || typeof fileName !== 'string') return false
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'ico', 'tiff', 'tif']
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    return imageTypes.includes(fileExtension)
  }

  /**
   * 检查是否为文档文件
   * @param {string} fileName 文件名
   * @returns {boolean}
   */
  isDocumentFile(fileName) {
    if (!fileName || typeof fileName !== 'string') return false
    const docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp']
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    return docTypes.includes(fileExtension)
  }

  /**
   * 检查是否为视频文件
   * @param {string} fileName 文件名
   * @returns {boolean}
   */
  isVideoFile(fileName) {
    if (!fileName || typeof fileName !== 'string') return false
    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp']
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    return videoTypes.includes(fileExtension)
  }

  /**
   * 检查是否为音频文件
   * @param {string} fileName 文件名
   * @returns {boolean}
   */
  isAudioFile(fileName) {
    if (!fileName || typeof fileName !== 'string') return false
    const audioTypes = ['mp3', 'wav', 'aac', 'flac', 'ogg', 'wma', 'm4a']
    const fileExtension = fileName.split('.').pop()?.toLowerCase()
    return audioTypes.includes(fileExtension)
  }

  /**
   * 获取文件类型
   * @param {string} fileName 文件名
   * @returns {string} 文件类型：'image', 'document', 'video', 'audio', 'other'
   */
  getFileType(fileName) {
    if (this.isImageFile(fileName)) return 'image'
    if (this.isDocumentFile(fileName)) return 'document'
    if (this.isVideoFile(fileName)) return 'video'
    if (this.isAudioFile(fileName)) return 'audio'
    return 'other'
  }

  /**
   * 智能预览文件
   * @param {string} fileId 文件ID
   * @param {string} fallbackUrl 备用URL（当文件ID无效时使用）
   * @param {string} fileName 文件名（可选，用于显示）
   * @returns {Promise<void>}
   */
  async previewFile(fileId, fallbackUrl = null, fileName = null) {
    try {
      // 如果有有效的文件ID，优先使用文件ID获取完整信息
      if (fileId && fileId !== 'undefined' && fileId !== 'null' && fileId.trim() !== '') {
        try {
          const fileInfo = await this.getFileInfo(fileId)

          // 根据后端返回的预览类型选择预览方式
          switch (fileInfo.previewType) {
            case 'image':
              return await this.previewImage(fileInfo.url)

            case 'document':
              return await this.downloadAndPreviewDocument(fileInfo.url, fileName || fileInfo.fileName)

            case 'video':
              wx.showModal({
                title: '提示',
                content: '视频文件暂不支持在线预览，请下载后查看',
                showCancel: false
              })
              return

            case 'audio':
              wx.showModal({
                title: '提示',
                content: '音频文件暂不支持在线预览，请下载后查看',
                showCancel: false
              })
              return

            default:
              return await this.downloadAndPreviewDocument(fileInfo.url, fileName || fileInfo.fileName)
          }
        } catch (fileIdError) {
          // 如果文件ID失败且有备用URL，使用备用URL
          if (fallbackUrl) {
            return await this.previewByUrl(fallbackUrl, fileName)
          } else {
            throw fileIdError
          }
        }
      } else if (fallbackUrl) {
        // 没有有效的文件ID，直接使用备用URL
        return await this.previewByUrl(fallbackUrl, fileName)
      } else {
        throw new Error('文件ID和备用URL都不能为空')
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 通过URL预览文件
   * @param {string} url 文件URL
   * @param {string} fileName 文件名
   * @returns {Promise<void>}
   */
  async previewByUrl(url, fileName = null) {
    try {
      // 根据文件名判断类型
      const fileType = this.getFileType(fileName)

      switch (fileType) {
        case 'image':
          return await this.previewImage(url)
        case 'document':
          return await this.downloadAndPreviewDocument(url, fileName)
        default:
          // 默认尝试文档预览
          return await this.downloadAndPreviewDocument(url, fileName)
      }
    } catch (error) {
      throw error
    }
  }

  /**
   * 下载并预览文档
   * @param {string} url 文件URL
   * @param {string} fileName 文件名
   * @returns {Promise<void>}
   */
  async downloadAndPreviewDocument(url, fileName = null) {
    // 提取域名
    const domain = this.extractDomain(url)

    try {
      // 验证URL格式
      if (!url || typeof url !== 'string') {
        throw new Error('文件URL无效')
      }

      // 下载文件
      const downloadRes = await this.downloadFile(url)

      // 验证下载结果
      if (!downloadRes.tempFilePath) {
        throw new Error('下载失败：未获取到临时文件路径')
      }

      // 预览文档
      return new Promise((resolve, reject) => {
        wx.openDocument({
          filePath: downloadRes.tempFilePath,
          fileName: fileName,
          success: resolve,
          fail: (error) => {
            // 错误分析
            const errMsg = error.errMsg || ''
            let userFriendlyMessage = '文档预览失败'

            if (errMsg.includes('file not exist') || errMsg.includes('文件不存在')) {
              userFriendlyMessage = '文件不存在或已被删除'
            } else if (errMsg.includes('file format') || errMsg.includes('格式')) {
              userFriendlyMessage = 'PDF文件格式不支持或文件已损坏'
            } else if (errMsg.includes('permission') || errMsg.includes('权限')) {
              userFriendlyMessage = '没有权限访问该文件'
            } else if (errMsg.includes('network') || errMsg.includes('网络')) {
              userFriendlyMessage = '网络连接异常，请检查网络后重试'
            }

            // 显示错误提示
            wx.showModal({
              title: '预览失败',
              content: `${userFriendlyMessage}\n\n技术信息: ${errMsg}`,
              showCancel: true,
              cancelText: '取消',
              confirmText: '重试',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 用户选择重试
                  this.downloadAndPreviewDocument(url, fileName)
                    .then(resolve)
                    .catch(reject)
                } else {
                  reject(new Error(userFriendlyMessage))
                }
              }
            })
          }
        })
      })
    } catch (error) {
      // 错误处理
      const errorMessage = error.message || ''
      if (errorMessage.includes('下载失败')) {
        // 域名校验错误处理
        if (errorMessage.includes('域名校验') || errorMessage.includes('合法域名')) {
          wx.showModal({
            title: '下载失败',
            content: `域名校验失败，请联系管理员将 ${domain} 添加到小程序合法域名列表中。`,
            showCancel: false,
            confirmText: '知道了'
          })
        } else {
          wx.showModal({
            title: '下载失败',
            content: `文件下载失败: ${errorMessage}\n\n请检查网络连接或联系管理员。`,
            showCancel: false,
            confirmText: '知道了'
          })
        }
      } else {
        wx.showModal({
          title: '操作失败',
          content: `操作失败: ${errorMessage}`,
          showCancel: false,
          confirmText: '知道了'
        })
      }

      throw error
    }
  }

  /**
   * 清理访问URL缓存
   */
  clearCache() {
    this.accessCache.clear()
  }

  /**
   * 从URL中提取域名
   * @param {string} url URL地址
   * @returns {string} 域名
   */
  extractDomain(url) {
    try {
      if (!url || typeof url !== 'string') return 'unknown'

      // 处理相对路径
      if (url.startsWith('/')) {
        return 'localhost'
      }

      // 提取域名
      const match = url.match(/^https?:\/\/([^\/]+)/)
      return match ? match[1] : 'unknown'
    } catch (error) {
      return 'unknown'
    }
  }

}

// 创建单例实例
const fileAccessManager = new FileAccessManager()

export default fileAccessManager

// 安全日志工具 - 防止敏感信息泄露
class SecureLogger {
  // 敏感字段列表
  static SENSITIVE_FIELDS = [
    'token', 'accessToken', 'refreshToken',
    'openid', 'openId', 'unionid', 'unionId',
    'sessionKey', 'session_key',
    'code', 'phoneCode', 'phone_code',
    'encryptedData', 'encrypted_data',
    'iv', 'password', 'pwd',
    'mobile', 'phone', 'phoneNumber',
    'idCard', 'id_card', 'realName', 'real_name'
  ]

  // 完全隐藏的字段（不显示任何信息）
  static HIDDEN_FIELDS = [
    'password', 'pwd', 'sessionKey', 'session_key',
    'encryptedData', 'encrypted_data', 'iv'
  ]

  /**
   * 安全地记录日志，自动脱敏敏感信息
   * @param {string} tag 日志标签
   * @param {string} message 日志消息
   * @param {*} data 要记录的数据
   */
  static log(tag, message, data = null) {
    if (data) {
      const sanitizedData = this.sanitizeData(data)
      console.log(`[${tag}] ${message}`, sanitizedData)
    } else {
      console.log(`[${tag}] ${message}`)
    }
  }

  /**
   * 安全地记录错误日志
   * @param {string} tag 日志标签
   * @param {string} message 错误消息
   * @param {*} error 错误对象或数据
   */
  static error(tag, message, error = null) {
    if (error) {
      const sanitizedError = this.sanitizeData(error)
      console.error(`[${tag}] ${message}`, sanitizedError)
    } else {
      console.error(`[${tag}] ${message}`)
    }
  }

  /**
   * 安全地记录警告日志
   * @param {string} tag 日志标签
   * @param {string} message 警告消息
   * @param {*} data 要记录的数据
   */
  static warn(tag, message, data = null) {
    if (data) {
      const sanitizedData = this.sanitizeData(data)
      console.warn(`[${tag}] ${message}`, sanitizedData)
    } else {
      console.warn(`[${tag}] ${message}`)
    }
  }

  /**
   * 数据脱敏处理
   * @param {*} data 原始数据
   * @returns {*} 脱敏后的数据
   */
  static sanitizeData(data) {
    if (data === null || data === undefined) {
      return data
    }

    if (typeof data === 'string') {
      return this.sanitizeString(data)
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item))
    }

    if (typeof data === 'object') {
      return this.sanitizeObject(data)
    }

    return data
  }

  /**
   * 对象脱敏处理
   * @param {Object} obj 原始对象
   * @returns {Object} 脱敏后的对象
   */
  static sanitizeObject(obj) {
    const sanitized = {}

    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase()

      if (this.HIDDEN_FIELDS.some(field => lowerKey.includes(field.toLowerCase()))) {
        // 完全隐藏的字段
        sanitized[key] = '[HIDDEN]'
      } else if (this.SENSITIVE_FIELDS.some(field => lowerKey.includes(field.toLowerCase()))) {
        // 敏感字段，部分脱敏
        sanitized[key] = this.maskSensitiveValue(value)
      } else {
        // 普通字段，递归处理
        sanitized[key] = this.sanitizeData(value)
      }
    }

    return sanitized
  }

  /**
   * 字符串脱敏处理
   * @param {string} str 原始字符串
   * @returns {string} 脱敏后的字符串
   */
  static sanitizeString(str) {
    // 检查是否包含敏感信息的模式
    if (this.containsSensitivePattern(str)) {
      return this.maskString(str)
    }
    return str
  }

  /**
   * 检查字符串是否包含敏感信息模式
   * @param {string} str 字符串
   * @returns {boolean} 是否包含敏感信息
   */
  static containsSensitivePattern(str) {
    // 检查是否像token（长度>20的字母数字组合）
    if (str.length > 20 && /^[a-zA-Z0-9_-]+$/.test(str)) {
      return true
    }

    // 检查是否像手机号
    if (/^1[3-9]\d{9}$/.test(str)) {
      return true
    }

    // 检查是否像身份证号
    if (/^\d{17}[\dXx]$/.test(str)) {
      return true
    }

    return false
  }

  /**
   * 敏感值脱敏
   * @param {*} value 原始值
   * @returns {string} 脱敏后的值
   */
  static maskSensitiveValue(value) {
    if (value === null || value === undefined) {
      return value
    }

    const str = String(value)
    return this.maskString(str)
  }

  /**
   * 字符串掩码处理
   * @param {string} str 原始字符串
   * @returns {string} 掩码后的字符串
   */
  static maskString(str) {
    if (!str || str.length === 0) {
      return str
    }

    if (str.length <= 4) {
      return '***'
    }

    if (str.length <= 8) {
      return str.substring(0, 2) + '***'
    }

    // 显示前3位和后2位，中间用***代替
    return str.substring(0, 3) + '***' + str.substring(str.length - 2)
  }

  /**
   * 创建安全的请求日志
   * @param {Object} requestData 请求数据
   * @returns {Object} 安全的请求日志对象
   */
  static createSafeRequestLog(requestData) {
    const safeLog = {}

    // 安全地记录请求基本信息
    if (requestData.url) {
      safeLog.url = requestData.url
    }

    if (requestData.method) {
      safeLog.method = requestData.method
    }

    // 脱敏处理请求数据
    if (requestData.data) {
      safeLog.dataSize = JSON.stringify(requestData.data).length + ' bytes'
      safeLog.hasCode = !!requestData.data.code
      safeLog.hasPhoneCode = !!requestData.data.phoneCode
      safeLog.hasEncryptedData = !!(requestData.data.encryptedData && requestData.data.iv)
      safeLog.hasUserInfo = !!requestData.data.userInfo
    }

    return safeLog
  }
}

export default SecureLogger

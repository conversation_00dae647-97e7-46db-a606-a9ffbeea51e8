package com.ehome.oc.domain;

import com.ehome.common.core.domain.BaseEntity;

import java.util.Date;

public class WxUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    private String communityId;

    /** 用户昵称 */
    private String nickName;

    /** 用户头像 */
    private String avatarUrl;

    /** 手机号码 */
    private String mobile;

    /** 用户性别（0未知 1男 2女） */
    private String gender;

    /** 用户状态（0正常 1停用） */
    private String status;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 用户openId */
    private String openId;

    /** 用户unionId */
    private String unionId;

    /** 会话密钥 */
    private String sessionKey;

    private String ownerId;

    /** 临时字段：是否首次登录（不存储到数据库） */
    private Boolean isFirstLogin;

    /** 用户角色：owner=业主,property=物业，多个用逗号分隔 */
    private String userRoles;

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }


    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getCommunityId() {
		return communityId;
	}

    public void setCommunityId(String communityId) {
		this.communityId = communityId;
	}

    public Boolean getIsFirstLogin() {
        return isFirstLogin;
    }

    public void setIsFirstLogin(Boolean isFirstLogin) {
        this.isFirstLogin = isFirstLogin;
    }

    public String getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(String userRoles) {
        this.userRoles = userRoles;
    }

    /**
     * 检查是否有指定角色
     */
    public boolean hasRole(String role) {
        if (userRoles == null || userRoles.isEmpty()) {
            return false;
        }
        return userRoles.contains(role);
    }

    /**
     * 添加角色
     */
    public void addRole(String role) {
        if (userRoles == null || userRoles.isEmpty()) {
            userRoles = role;
        } else if (!hasRole(role)) {
            userRoles = userRoles + "," + role;
        }
    }
}
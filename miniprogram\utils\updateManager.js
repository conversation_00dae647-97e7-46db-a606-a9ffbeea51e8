// 版本更新管理器
import SecureLogger from './secureLogger.js'
import { getConfigManager } from './configManager.js'

class AppUpdateManager {
  constructor() {
    this.updateManager = null
    this.configManager = getConfigManager()
    this.isChecking = false
    this.hasCheckedOnLaunch = false
    this.logTag = 'UpdateManager'
  }

  /**
   * 初始化更新管理器
   */
  init() {
    try {
      // 检查是否支持UpdateManager
      if (!wx.getUpdateManager) {
        SecureLogger.log(this.logTag, '当前微信版本过低，无法使用UpdateManager')
        return false
      }

      this.updateManager = wx.getUpdateManager()
      this.setupEventListeners()
      SecureLogger.log(this.logTag, '版本更新管理器初始化成功')
      return true
    } catch (error) {
      SecureLogger.log(this.logTag, '版本更新管理器初始化失败:', error)
      return false
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.updateManager) return

    // 监听检查更新结果
    this.updateManager.onCheckForUpdate((res) => {
      this.handleCheckForUpdate(res)
    })

    // 监听更新就绪
    this.updateManager.onUpdateReady(() => {
      this.handleUpdateReady()
    })

    // 监听更新失败
    this.updateManager.onUpdateFailed(() => {
      this.handleUpdateFailed()
    })
  }

  /**
   * 处理检查更新结果
   * @param {Object} res 检查结果
   */
  handleCheckForUpdate(res) {
    this.isChecking = false
    
    if (res.hasUpdate) {
      SecureLogger.log(this.logTag, '发现新版本，开始下载')
      wx.showToast({
        title: '发现新版本',
        icon: 'none',
        duration: 2000
      })
    } else {
      SecureLogger.log(this.logTag, '当前已是最新版本')
      
      // 如果是手动检查，提示用户
      if (this.isManualCheck) {
        wx.showToast({
          title: '当前已是最新版本',
          icon: 'success',
          duration: 2000
        })
        this.isManualCheck = false
      }
    }
  }

  /**
   * 处理更新就绪
   */
  handleUpdateReady() {
    SecureLogger.log(this.logTag, '新版本下载完成，准备更新')
    
    const config = this.configManager.getAll()
    const forceUpdate = config.force_update_mode === '1'
    
    if (forceUpdate) {
      this.showForceUpdateDialog()
    } else {
      this.showOptionalUpdateDialog()
    }
  }

  /**
   * 处理更新失败
   */
  handleUpdateFailed() {
    SecureLogger.log(this.logTag, '新版本下载失败')
    
    wx.showToast({
      title: '更新失败，请稍后重试',
      icon: 'none',
      duration: 3000
    })
  }

  /**
   * 显示强制更新对话框
   */
  showForceUpdateDialog() {
    wx.showModal({
      title: '版本更新',
      content: '发现新版本，需要更新后才能继续使用',
      showCancel: false,
      confirmText: '立即更新',
      confirmColor: '#1890ff',
      success: (res) => {
        if (res.confirm) {
          this.applyUpdate()
        }
      }
    })
  }

  /**
   * 显示可选更新对话框
   */
  showOptionalUpdateDialog() {
    wx.showModal({
      title: '版本更新',
      content: '发现新版本，是否立即更新？\n更新后将获得更好的使用体验',
      cancelText: '稍后更新',
      confirmText: '立即更新',
      confirmColor: '#1890ff',
      success: (res) => {
        if (res.confirm) {
          this.applyUpdate()
        } else {
          SecureLogger.log(this.logTag, '用户选择稍后更新')
        }
      }
    })
  }

  /**
   * 应用更新并重启
   */
  applyUpdate() {
    try {
      SecureLogger.log(this.logTag, '开始应用更新')
      
      wx.showLoading({
        title: '更新中...',
        mask: true
      })
      
      // 延迟一下让用户看到loading
      setTimeout(() => {
        wx.hideLoading()
        this.updateManager.applyUpdate()
      }, 1000)
      
    } catch (error) {
      wx.hideLoading()
      SecureLogger.log(this.logTag, '应用更新失败:', error)
      
      wx.showToast({
        title: '更新失败，请重启小程序',
        icon: 'none',
        duration: 3000
      })
    }
  }

  /**
   * 手动检查更新
   */
  checkForUpdate() {
    if (!this.updateManager) {
      wx.showToast({
        title: '当前微信版本不支持检查更新',
        icon: 'none',
        duration: 3000
      })
      return
    }

    if (this.isChecking) {
      wx.showToast({
        title: '正在检查更新中...',
        icon: 'none',
        duration: 2000
      })
      return
    }

    this.isManualCheck = true
    this.isChecking = true
    
    wx.showLoading({
      title: '检查更新中...',
      mask: true
    })

    // 设置超时 - 开发环境下微信不会真正检查更新
    setTimeout(() => {
      if (this.isChecking) {
        wx.hideLoading()
        this.isChecking = false

        // 在开发环境下给出友好提示
        if (wx.getSystemInfoSync().platform === 'devtools') {
          wx.showToast({
            title: '开发环境无法检查更新',
            icon: 'none',
            duration: 3000
          })
        } else {
          wx.showToast({
            title: '当前已是最新版本',
            icon: 'success',
            duration: 2000
          })
        }
      }
    }, 5000) // 缩短超时时间到5秒

    SecureLogger.log(this.logTag, '手动检查更新')
  }

  /**
   * 自动检查更新（应用启动时）
   */
  autoCheckForUpdate() {
    if (this.hasCheckedOnLaunch) {
      return
    }

    const config = this.configManager.getAll()
    const enableAutoUpdate = config.enable_auto_update !== '0' // 默认启用

    if (!enableAutoUpdate) {
      SecureLogger.log(this.logTag, '自动更新检查已禁用')
      return
    }

    if (!this.updateManager) {
      SecureLogger.log(this.logTag, 'UpdateManager不可用，跳过自动检查')
      return
    }

    this.hasCheckedOnLaunch = true
    SecureLogger.log(this.logTag, '应用启动时自动检查更新')
    
    // 微信会自动检查更新，我们只需要监听结果即可
    // 这里不需要主动调用检查，因为微信在小程序启动时会自动检查
  }

  /**
   * 获取更新管理器状态
   */
  getStatus() {
    return {
      isSupported: !!this.updateManager,
      isChecking: this.isChecking,
      hasCheckedOnLaunch: this.hasCheckedOnLaunch
    }
  }
}

// 单例模式
let updateManagerInstance = null

/**
 * 获取更新管理器实例
 * @returns {AppUpdateManager}
 */
export function getUpdateManager() {
  if (!updateManagerInstance) {
    updateManagerInstance = new AppUpdateManager()
  }
  return updateManagerInstance
}

export default AppUpdateManager

<!-- 接受邀请页面 -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <van-loading size="40rpx" />
    <text class="loading-text">加载邀请信息...</text>
  </view>

  <!-- 邀请信息 -->
  <view class="invite-section" wx:if="{{inviteInfo && !loading}}">
    <!-- 邀请卡片 -->
    <view class="invite-card">
      <view class="card-header">
        <van-icon name="home-o" size="60rpx" color="#1890ff" />
        <view class="header-text">
          <text class="invite-title">邀请你加入房屋</text>
          <text class="invite-subtitle">invited you to join the house</text>
        </view>
      </view>

      <view class="card-content">
        <!-- 房屋信息 -->
        <view class="house-info">
          <view class="house-name">{{inviteInfo.building_name}}{{inviteInfo.unit_name}}{{inviteInfo.room}}</view>
          <view class="divider"></view>
          <view class="house-meta">
            <view class="meta-item">
              <van-icon name="user-o" size="24rpx" />
              <text>邀请人：{{inviteInfo.inviter_name}}</text>
            </view>
            <view class="meta-item" wx:if="{{inviteInfo.inviter_phone}}">
              <van-icon name="phone-o" size="24rpx" />
              <text>联系方式：{{inviteInfo.inviter_phone}}</text>
            </view>
            <view class="meta-item">
              <van-icon name="label-o" size="24rpx" />
              <text>关系：{{inviteInfo.rel_type_name}}</text>
            </view>
            <view class="meta-item" wx:if="{{inviteInfo.remark}}">
              <van-icon name="chat-o" size="24rpx" />
              <text>备注：{{inviteInfo.remark}}</text>
            </view>
            <view class="meta-item">
              <van-icon name="clock-o" size="24rpx" />
              <text>有效期至：{{inviteInfo.expire_time}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="card-actions">
        <van-button
          type="primary"
          size="large"
          loading="{{confirming}}"
          bind:click="confirmInvite"
          block
          custom-style="margin-bottom: 20rpx;"
        >
          {{confirming ? '确认中...' : '确认接受'}}
        </van-button>

        <van-button
          type="default"
          size="large"
          bind:click="rejectInvite"
          block
          custom-style="color: #999;"
        >
          拒绝邀请
        </van-button>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-title">
        <van-icon name="info-o" size="28rpx" />
        <text>温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">确认后，您将注册为该房屋的{{inviteInfo.rel_type_name}}</text>
        <text class="tip-item">需要登录小程序后才能使用相关功能</text>
        <text class="tip-item">如有疑问，请联系邀请人确认</text>
        <text class="tip-item">每个邀请只能使用一次</text>
      </view>
    </view>
  </view>
</view>

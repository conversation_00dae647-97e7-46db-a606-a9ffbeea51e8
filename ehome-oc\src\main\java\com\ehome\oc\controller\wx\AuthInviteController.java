package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.UUID;

/**
 * 邀请认证控制器（无需登录）
 * 处理邀请相关的无需用户登录的接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/wx/auth/invite")
public class AuthInviteController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(AuthInviteController.class);

    /**
     * 通过邀请令牌查询卡片邀请信息（无需登录）
     */
    @GetMapping("/card-info/{inviteToken}")
    public AjaxResult getCardInviteInfo(@PathVariable String inviteToken) {
        try {
            if (StringUtils.isEmpty(inviteToken)) {
                return AjaxResult.error("邀请令牌不能为空");
            }
            
            Record invite = Db.findFirst(
                "SELECT i.*, h.room, h.building_id, h.unit_id, " +
                "b.name as building_name, u.name as unit_name, " +
                "o.owner_name as inviter_name, o.mobile as inviter_phone " +
                "FROM eh_house_invite i " +
                "LEFT JOIN eh_house_info h ON i.house_id = h.house_id " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "LEFT JOIN eh_owner o ON i.inviter_id = o.owner_id " +
                "WHERE i.invite_token = ? AND i.invite_type = 1",
                inviteToken
            );
            
            if (invite == null) {
                return AjaxResult.error("邀请不存在");
            }
            
            // 检查邀请状态
            if (invite.getInt("status") != 0) {
                String statusMsg = getStatusMessage(invite.getInt("status"));
                return AjaxResult.error("邀请已" + statusMsg);
            }
            
            // 检查是否过期
            if (isExpired(invite.getDate("expire_time"))) {
                // 更新邀请状态为过期
                Db.update("UPDATE eh_house_invite SET status = 3 WHERE invite_id = ?", invite.getStr("invite_id"));
                return AjaxResult.error("邀请已过期");
            }
            
            // 格式化关系类型
            String relTypeName = getRelTypeName(invite.getInt("rel_type"));
            invite.set("rel_type_name", relTypeName);
            
            return AjaxResult.success(invite.toMap());
            
        } catch (Exception e) {
            logger.error("查询卡片邀请信息失败", e);
            return AjaxResult.error("查询邀请信息失败");
        }
    }
    
    /**
     * 确认接受卡片邀请（注册用户到系统，无需登录）
     */
    @Log(title = "确认接受卡片邀请", businessType = BusinessType.INSERT)
    @PostMapping("/card-confirm")
    public AjaxResult confirmCardInvite(@RequestBody JSONObject params) {
        try {
            String inviteToken = params.getString("inviteToken");

            if (StringUtils.isEmpty(inviteToken)) {
                return AjaxResult.error("邀请令牌不能为空");
            }

            // 查询邀请信息
            Record invite = Db.findFirst(
                "SELECT * FROM eh_house_invite WHERE invite_token = ? AND invite_type = 1",
                inviteToken
            );

            if (invite == null) {
                return AjaxResult.error("邀请不存在");
            }

            // 检查邀请状态
            if (invite.getInt("status") != 0) {
                String statusMsg = getStatusMessage(invite.getInt("status"));
                return AjaxResult.error("邀请已" + statusMsg);
            }

            // 检查是否过期
            if (isExpired(invite.getDate("expire_time"))) {
                // 更新邀请状态为过期
                Db.update("UPDATE eh_house_invite SET status = 3 WHERE invite_id = ?", invite.getStr("invite_id"));
                return AjaxResult.error("邀请已过期");
            }

            // 检查接收人手机号是否已注册
            String invitePhone = invite.getStr("invite_phone");
            if (StringUtils.isEmpty(invitePhone)) {
                return AjaxResult.error("邀请信息不完整，缺少接收人手机号");
            }

            Record existingOwner = Db.findFirst(
                "SELECT owner_id FROM eh_owner WHERE mobile = ?",
                invitePhone
            );

            String acceptUserId;
            if (existingOwner != null) {
                // 用户已存在，直接使用现有用户ID
                acceptUserId = existingOwner.getStr("owner_id");
                logger.info("用户已存在，使用现有用户ID: {}", acceptUserId);
            } else {
                // 用户不存在，创建新用户
                acceptUserId = UUID.randomUUID().toString().replace("-", "");
                
                Record newOwner = new Record();
                newOwner.set("owner_id", acceptUserId);
                newOwner.set("mobile", invitePhone);
                newOwner.set("owner_name", invitePhone); // 默认使用手机号作为用户名
                newOwner.set("create_time", DateUtils.getTime());
                newOwner.set("creator", invite.getStr("inviter_id"));
                
                boolean insertResult = Db.save("eh_owner", "owner_id", newOwner);
                if (!insertResult) {
                    return AjaxResult.error("创建用户失败");
                }
                logger.info("创建新用户成功，用户ID: {}", acceptUserId);
            }

            // 检查是否已经存在相同的房屋关系
            Record existingRelation = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ? AND rel_type = ? AND check_status = 1",
                invite.getStr("house_id"), acceptUserId, invite.getInt("rel_type")
            );

            if (existingRelation != null) {
                // 更新邀请状态为已接受
                Db.update(
                    "UPDATE eh_house_invite SET status = 1, accept_user_id = ?, accept_time = ? WHERE invite_id = ?",
                    acceptUserId, new Date(), invite.getStr("invite_id")
                );
                return AjaxResult.error("您已经是该房屋的" + getRelTypeName(invite.getInt("rel_type")) + "了");
            }

            // 创建房屋关系
            Record houseOwnerRel = new Record();
            houseOwnerRel.set("rel_id", UUID.randomUUID().toString().replace("-", ""));
            houseOwnerRel.set("house_id", invite.getStr("house_id"));
            houseOwnerRel.set("owner_id", acceptUserId);
            houseOwnerRel.set("rel_type", invite.getInt("rel_type"));
            houseOwnerRel.set("is_default", 0); // 非默认房屋
            houseOwnerRel.set("check_status", 1); // 直接审核通过
            houseOwnerRel.set("remark", "通过卡片邀请加入");
            houseOwnerRel.set("create_time", DateUtils.getTime());
            houseOwnerRel.set("create_by", invite.getStr("inviter_id"));

            boolean relationResult = Db.save("eh_house_owner_rel", "rel_id", houseOwnerRel);
            if (!relationResult) {
                return AjaxResult.error("创建房屋关系失败");
            }

            // 更新邀请状态为已接受
            Db.update(
                "UPDATE eh_house_invite SET status = 1, accept_user_id = ?, accept_time = ? WHERE invite_id = ?",
                acceptUserId, new Date(), invite.getStr("invite_id")
            );

            logger.info("邀请确认成功，邀请ID: {}, 接受用户ID: {}", invite.getStr("invite_id"), acceptUserId);
            return AjaxResult.success("邀请确认成功，您已注册到系统");

        } catch (Exception e) {
            logger.error("确认卡片邀请失败", e);
            return AjaxResult.error("确认邀请失败，请稍后重试");
        }
    }

    /**
     * 检查邀请是否过期
     */
    private boolean isExpired(Date expireTime) {
        return expireTime != null && expireTime.before(new Date());
    }

    /**
     * 获取关系类型名称
     */
    private String getRelTypeName(Integer relType) {
        if (relType == null) return "未知";
        switch (relType) {
            case 1: return "业主";
            case 2: return "家庭成员";
            case 3: return "租户";
            default: return "未知";
        }
    }

    /**
     * 获取状态消息
     */
    private String getStatusMessage(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "接受";
            case 2: return "拒绝";
            case 3: return "过期";
            default: return "失效";
        }
    }
}

# 小程序logo图片上传功能实现

## 任务描述
为小区信息维护页面的小程序logo字段实现图片上传功能，替换原有的文本输入框。

## 实现方案
使用bootstrap-fileinput插件实现图片上传，与项目现有上传功能保持一致。

## 技术实现

### 1. HTML结构修改
- 文件：`ehome-page/src/main/resources/templates/oc/info/maintain.html`
- 修改内容：
  - 将文本输入框改为隐藏字段存储图片URL
  - 添加文件上传组件div
  - 添加图片预览区域和删除按钮

### 2. JavaScript功能实现
- 添加`initLogoUpload()`函数初始化fileinput组件
- 配置上传参数：
  - 上传URL：`ctx + 'common/upload'`
  - 支持格式：jpg, jpeg, png, gif, bmp
  - 文件大小限制：5MB
  - 单文件上传
- 添加`showLogoPreview()`和`hideLogoPreview()`函数处理图片预览
- 添加`removeLogo()`函数删除图片
- 在表单数据回填时处理logo图片显示

### 3. 功能特性
- 支持拖拽上传
- 图片预览功能
- 文件类型和大小验证
- 上传进度显示
- 中文界面提示
- 与现有表单数据保存集成

## 使用说明
1. 点击"选择图片"按钮选择logo图片文件
2. 支持jpg、png、gif等常见图片格式
3. 文件大小不超过5MB
4. 上传成功后显示图片预览
5. 可点击"删除图片"按钮移除已上传的图片
6. 表单提交时图片URL会自动保存到数据库

## 技术依赖
- bootstrap-fileinput插件（项目已集成）
- 现有的通用上传接口 `/common/upload`
- jQuery和Bootstrap样式框架

## 实现完成情况

### ✅ 已完成的功能
1. **HTML结构修改**
   - 将文本输入框改为隐藏字段存储图片URL
   - 添加文件上传组件div (`miniprogram_logo_upload`)
   - 添加图片预览区域 (`miniprogram_logo_preview`)
   - 添加删除按钮

2. **JavaScript功能实现**
   - `initLogoUpload()` - 初始化fileinput组件
   - `showLogoPreview()` - 显示图片预览
   - `hideLogoPreview()` - 隐藏图片预览
   - `removeLogo()` - 删除图片功能
   - 表单数据回填时处理logo图片显示

3. **资源文件引入**
   - 引入bootstrap-fileinput CSS文件
   - 引入bootstrap-fileinput JS文件

### 🔧 配置参数
- 上传URL: `ctx + 'common/upload'`
- 支持格式: jpg, jpeg, png, gif, bmp
- 文件大小限制: 5MB
- 单文件上传模式
- 中文界面

## 测试要点
- [ ] 图片上传功能正常
- [ ] 图片预览显示正确
- [ ] 文件类型限制生效
- [ ] 文件大小限制生效
- [ ] 删除图片功能正常
- [ ] 表单数据保存包含图片URL
- [ ] 页面刷新后图片正确显示

## 部署说明
功能已完成开发，可以直接部署测试。无需额外的数据库修改或配置变更。

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改车辆')" />
    <style>
       .bind-btn{
        line-height: 31px;
       }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-vehicle-edit">
            <input name="vehicle_id" type="hidden" th:value="${vehicle.vehicle_id}">
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">车牌号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="plate_no" required>
                </div>
                <label class="col-sm-2 control-label">车辆类型：</label>
                <div class="col-sm-4">
                    <select class="form-control" name="vehicle_type">
                        <option value="">请选择车辆类型</option>
                        <option value="业主车辆">业主车辆</option>
                        <option value="非业主车辆">非业主车辆</option>
                       
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">车主姓名：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_real_name">
                </div>
                <label class="col-sm-2 control-label">车主手机号：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" name="owner_phone">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">绑定信息：</label>
                <div class="col-sm-10">
                    <!-- 绑定信息展示区域 -->
                    <div id="bindingInfoDisplay" class="binding-info-display">
                        <div id="ownerBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定住户：</span>
                            <span class="binding-content">暂无绑定住户</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="houseBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定房屋：</span>
                            <span class="binding-content">暂无绑定房屋</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                        <div id="parkingBindingInfo" class="binding-text-item">
                            <span class="binding-label">绑定车位：</span>
                            <span class="binding-content">暂无绑定车位</span>
                            <a href="javascript:void(0);" onclick="manageBindings();" class="binding-manage-link">去修改</a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />

    <style>
        .binding-info-display {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .binding-text-item {
            margin-bottom: 10px;
            padding: 8px 0;
            display: flex;
            align-items: center;
        }

        .binding-text-item:last-child {
            margin-bottom: 0;
        }

        .binding-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
            display: inline-block;
        }

        .binding-content {
            flex: 1;
            color: #666;
            margin-right: 10px;
        }

        .binding-manage-link {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            padding: 2px 8px;
            border: 1px solid #007bff;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .binding-manage-link:hover {
            background-color: #007bff;
            color: white;
            text-decoration: none;
        }
    </style>
   
    <script type="text/javascript">
        var prefix = ctx + "oc/vehicle";
        
        $("#form-vehicle-edit").validate({
            focusCleanup: true
        });

        $(function() {
            $('#form-vehicle-edit').renderForm({url:prefix+'/record'});
            // 加载绑定信息
            loadBindingInfo();
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-vehicle-edit').serialize());
            }
        }

        function manageBindings() {
            var vehicleId = $("input[name='vehicle_id']").val();
            if (!vehicleId) {
                $.modal.alertWarning("请先保存车辆信息");
                return;
            }
            $.modal.popupRight({title:"管理绑定关系", url:prefix + "/bindings/" + vehicleId, end:function() {
                // 弹窗关闭后刷新绑定信息
                loadBindingInfo();
            }});
        }

        // 加载绑定信息
        function loadBindingInfo() {
            var vehicleId = $("input[name='vehicle_id']").val();
            if (!vehicleId) {
                return;
            }

            // 加载住户绑定信息
            loadOwnerBindingInfo(vehicleId);
            // 加载房屋绑定信息
            loadHouseBindingInfo(vehicleId);
            // 加载车位绑定信息
            loadParkingBindingInfo(vehicleId);
        }

        // 加载住户绑定信息
        function loadOwnerBindingInfo(vehicleId) {
            $.ajax({
                url: prefix + "/ownerBindings",
                type: "GET",
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderOwnerBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载住户绑定信息失败");
                }
            });
        }

        // 加载房屋绑定信息
        function loadHouseBindingInfo(vehicleId) {
            $.ajax({
                url: prefix + "/houseBindings",
                type: "GET",
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderHouseBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载房屋绑定信息失败");
                }
            });
        }

        // 加载车位绑定信息
        function loadParkingBindingInfo(vehicleId) {
            $.ajax({
                url: prefix + "/parkingBindings",
                type: "GET",
                data: { vehicleId: vehicleId },
                success: function(res) {
                    if (res.code === 0) {
                        renderParkingBindingInfo(res.data);
                    }
                },
                error: function() {
                    console.error("加载车位绑定信息失败");
                }
            });
        }

        // 渲染住户绑定信息
        function renderOwnerBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var owner = data[i];
                    var ownerText = owner.owner_name;
                    if (owner.mobile) {
                        ownerText += '(' + owner.mobile + ')';
                    }
                    displayItems.push(ownerText);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定住户';
            }
            $('#ownerBindingInfo .binding-content').text(contentText);
        }

        // 渲染房屋绑定信息
        function renderHouseBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var house = data[i];
                    displayItems.push(house.house_name || '房屋');
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定房屋';
            }
            $('#houseBindingInfo .binding-content').text(contentText);
        }

        // 渲染车位绑定信息
        function renderParkingBindingInfo(data) {
            var contentText = '';
            if (data && data.length > 0) {
                var displayItems = [];
                for (var i = 0; i < data.length; i++) {
                    var parking = data[i];
                    displayItems.push(parking.parking_no);
                }
                contentText = displayItems.join('、');
            } else {
                contentText = '暂无绑定车位';
            }
            $('#parkingBindingInfo .binding-content').text(contentText);
        }

        // 获取车位状态文本
        function getParkingStatusText(status) {
            switch(status) {
                case '0': return '空闲';
                case '1': return '占用';
                case '2': return '维修';
                default: return '未知';
            }
        }
    </script>
</body>
</html>
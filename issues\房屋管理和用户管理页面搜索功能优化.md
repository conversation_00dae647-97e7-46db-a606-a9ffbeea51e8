# 房屋管理和用户管理页面搜索功能优化

## 任务描述
为房屋管理页面和用户管理页面添加搜索输入框和回车搜索功能。

## 修改内容

### 1. 用户管理页面 (list-owner.html)
- 为姓名和手机号码输入框添加回车搜索功能
- 修改位置：第14行和第17行
- 添加 `onkeypress="if(event.keyCode==13) $.table.search()"` 属性

### 2. 房屋管理页面 (list.html)
- 在搜索区域顶部添加通用搜索输入框（搜索楼栋号或单元号）
- 为房间号输入框添加回车搜索功能
- 修改位置：第38-51行
- 新增搜索输入框：`<input type="text" name="searchKeyword" placeholder="请输入楼栋号或单元号" onkeypress="if(event.keyCode==13) $.table.search()"/>`

### 3. 后端控制器 (HouseMgrController.java)
- 在 `buildListQuery` 方法中添加对 `searchKeyword` 参数的支持
- 修改位置：第478-483行
- 支持搜索楼栋号(t2.name)或单元号(t3.name)

### 4. 楼栋树搜索功能 (list.html)
- 在楼栋树上方添加搜索输入框组合（包含搜索和清空按钮）
- 修改位置：第25-33行
- 添加 `searchTree()` 函数实现树节点过滤搜索功能
- 修改位置：第361-442行
- 添加 `clearTreeSearch()` 函数实现搜索重置功能
- 修改位置：第439-442行
- 支持模糊搜索楼栋或单元名称，智能显示匹配节点及其完整层级结构（父节点+子节点），隐藏不匹配的节点

### 5. 房屋信息链接优化 (list.html)
- 将房屋列表第一列的房屋信息改为蓝色链接
- 修改位置：第157-166行
- 修改 houseDetail 函数使用 popupRight 方式打开房屋详情
- 修改位置：第250-254行

### 6. 操作按钮优化 (list.html)
- 去掉"更绑住户"按钮，保留编辑和删除按钮
- 修改位置：第230-235行

### 7. 树形右键菜单功能 (list.html)
- 添加右键菜单HTML结构和样式
- 修改位置：第105-121行
- 为zTree添加右键点击事件处理
- 修改位置：第262-301行
- 添加右键菜单显示逻辑和相关操作函数
- 修改位置：第519-612行
- 单元右键菜单：编辑单元、新增单元、新增房屋
- 楼栋右键菜单：编辑楼宇、新增楼宇、新增单元
- 完善树节点数据结构，添加buildingId字段
- 修改位置：第339-346行
- 优化参数传递：新增房屋时自动传递楼栋ID和单元ID
- 修改位置：第586-604行

### 8. 树形样式优化 (list.html)
- 去掉树形的文件夹图标和展开/折叠按钮
- 调整字体大小为14px，增加行高和间距
- 修改位置：第116-172行（CSS样式）
- 修改zTree配置，关闭图标和连线显示
- 修改位置：第332-346行

## 功能说明
1. 用户可以在搜索输入框中输入关键字后按回车键触发搜索
2. 房屋管理页面的搜索输入框支持搜索楼栋号或单元号
3. 楼栋树支持智能过滤搜索功能，当匹配到节点时显示其完整层级结构（包含所有父节点和子节点），隐藏不匹配的节点
4. 楼栋树搜索支持搜索按钮和清空按钮，方便用户操作
5. 房屋信息列显示为蓝色链接，点击以右侧弹窗方式打开房屋详情
6. 楼栋树支持右键菜单功能，根据节点类型显示不同的操作选项
7. 精简操作按钮，去掉不必要的"更绑住户"按钮
8. 优化树形样式，去掉文件夹图标，增大字体和间距，提升视觉效果
9. 保持原有的点击搜索按钮功能不变
10. 所有搜索条件可以组合使用

## 测试要点
1. 验证回车键搜索功能是否正常工作
2. 验证搜索结果是否正确
3. 验证不影响原有的点击搜索功能
4. 验证搜索条件组合使用是否正常
5. 验证楼栋树搜索功能是否正常工作
6. 验证树节点搜索后的展开和选中效果
7. **重点测试**：验证搜索父节点时是否正确显示所有子节点
8. **重点测试**：验证搜索子节点时是否正确显示父节点路径
9. 验证房屋信息链接是否为蓝色且可点击
10. 验证点击房屋链接是否以右侧弹窗方式打开房屋详情页面
11. **重点测试**：验证楼栋树右键菜单功能是否正常
12. **重点测试**：验证单元右键菜单显示：编辑单元、新增单元、新增房屋
13. **重点测试**：验证楼栋右键菜单显示：编辑楼宇、新增楼宇、新增单元
14. 验证右键菜单的样式和交互效果
15. **重点测试**：验证单元右键"新增房屋"是否正确传递楼栋ID和单元ID参数
16. **重点测试**：验证楼栋/单元右键"新增单元"是否正确传递楼栋ID参数
17. 验证树形样式优化效果：无文件夹图标、字体大小、间距等

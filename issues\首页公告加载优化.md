# 首页公告加载逻辑优化

## 任务背景
首页公告加载逻辑需要优化，要求：
1. 最开始加载全部公告（限制5条）
2. 点击不同类型时，重新请求该类型的数据
3. 每次最多返回5条数据
4. 如果当前类型总数大于5条，显示"查看更多"按钮
5. 点击"查看更多"跳转到公告列表页面

## 实施计划

### 1. 后端接口优化 ✅
**文件：** `ehome-oc\src\main\java\com\ehome\oc\controller\wx\WxIndexController.java`
- 修改 `/api/wx/index/notices` 接口
- 添加分页参数：`page`（页码，默认1）、`pageSize`（每页数量，默认5）
- 添加类型筛选参数：`noticeType`（公告类型，可选）
- 返回总数信息和是否还有更多数据的标识
- 使用 `Db.paginate` 进行分页查询

### 2. 公告列表页面优化 ✅
**文件：** `miniprogram/pages/notice/list.js`
- 支持从页面参数获取公告类型
- 在请求中添加类型筛选参数

### 3. 首页前端逻辑重构 ✅
**文件：** `miniprogram/pages/index/index.js`
- 重构 `getNewsList()` 方法，支持类型参数
- 修改 `switchNoticeType()` 方法，切换时重新请求对应类型数据
- 添加 `goToNoticeList()` 方法跳转到公告列表页
- 优化缓存策略，按类型分别缓存
- 添加 `hasMoreNews` 字段管理"查看更多"按钮显示

### 4. 首页界面更新 ✅
**文件：** `miniprogram/pages/index/index.wxml`
- 在公告列表底部添加"查看更多"按钮
- 当 `hasMoreNews` 为true且有数据时显示按钮

### 5. 样式调整 ✅
**文件：** `miniprogram/pages/index/index.wxss`
- 添加"查看更多"按钮样式
- 包含点击效果和图标样式

## 主要变更

### 后端变更
1. 接口支持分页和类型筛选
2. 返回数据包含总数和是否还有更多的标识
3. 使用 `Db.paginate` 替代简单的 `limit` 查询

### 前端变更
1. 按类型缓存公告数据
2. 切换类型时重新请求数据而非前端过滤
3. 添加"查看更多"功能
4. 优化数据加载流程

## 问题修复

### 参数传递问题 ✅
**问题：** 前端使用GET请求但参数放在data中，导致Content-Type为application/json，后端ParamsKit无法正确解析参数
**解决：** 改为POST请求，参数放在请求体中
- 修改后端接口：`@GetMapping` 改为 `@PostMapping`
- 修改前端请求：使用POST方法，参数放在data中
- 这样ParamsKit可以正确从JSON请求体中解析参数

## 预期效果
- 首页按类型加载对应的5条数据 ✅
- 切换类型时实时获取该类型数据 ✅
- 当某类型数据超过5条时显示"查看更多"按钮 ✅
- 点击"查看更多"跳转到专门的列表页面 ✅
- 保持良好的缓存机制和用户体验 ✅

## 技术要点
1. **后端参数解析：** ParamsKit根据Content-Type决定参数解析方式
2. **POST请求优势：** POST请求可以在请求体中传递JSON数据，避免URL参数问题
3. **分页查询：** 使用 `Db.paginate` 进行高效分页
4. **缓存策略：** 按类型分别缓存，提高用户体验
5. **接口一致性：** 统一使用POST请求处理复杂查询参数

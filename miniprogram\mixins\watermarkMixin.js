// 水印页面混入器
import { getWatermarkManager } from '../utils/watermarkManager.js'

const watermarkManager = getWatermarkManager()

// 水印混入对象
export const watermarkMixin = {
  data: {
    // 水印相关数据
    showWatermark: false,
    watermarkConfig: null
  },

  // 页面生命周期混入
  onLoad() {
    // 初始化水印
    this.initWatermark()
  },

  onShow() {
    // 页面显示时更新水印
    this.updateWatermark()
  },

  onReady() {
    // 页面准备完毕时更新水印
    this.updateWatermark()
    // 动态添加水印组件到页面
    this.injectWatermarkComponent()
  },

  // 水印相关方法
  initWatermark() {
    try {
      // 延迟初始化，确保页面数据已加载
      setTimeout(() => {
        this.updateWatermark()
      }, 500)
    } catch (error) {
      console.warn('[WatermarkMixin] 初始化水印失败:', error)
    }
  },

  updateWatermark() {
    try {
      watermarkManager.updateWatermark(this)
    } catch (error) {
      console.warn('[WatermarkMixin] 更新水印失败:', error)
    }
  },

  removeWatermark() {
    try {
      watermarkManager.removeWatermark(this)
    } catch (error) {
      console.warn('[WatermarkMixin] 移除水印失败:', error)
    }
  },

  // 动态注入水印组件
  injectWatermarkComponent() {
    try {
      // 检查页面是否已经有水印组件
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      if (!currentPage) return

      // 通过选择器检查是否已存在水印组件
      const query = wx.createSelectorQuery().in(currentPage)
      query.select('.global-watermark-container').boundingClientRect()
      query.exec((res) => {
        // 如果没有找到水印组件，说明页面没有手动添加水印
        if (!res[0]) {
          console.log('[WatermarkMixin] 页面未包含水印组件，将通过数据驱动显示水印')
          // 通过数据更新来显示水印，依赖页面模板中的条件渲染
        }
      })
    } catch (error) {
      console.warn('[WatermarkMixin] 注入水印组件失败:', error)
    }
  }
}

// 自动混入函数
export function mixinWatermark(pageOptions) {
  // 保存原始生命周期方法
  const originalOnLoad = pageOptions.onLoad
  const originalOnShow = pageOptions.onShow
  const originalOnReady = pageOptions.onReady

  // 合并数据
  pageOptions.data = {
    ...watermarkMixin.data,
    ...pageOptions.data
  }

  // 混入生命周期方法
  pageOptions.onLoad = function(options) {
    // 先执行混入的方法
    watermarkMixin.onLoad.call(this)
    // 再执行原始方法
    if (originalOnLoad) {
      originalOnLoad.call(this, options)
    }
  }

  pageOptions.onShow = function() {
    // 先执行混入的方法
    watermarkMixin.onShow.call(this)
    // 再执行原始方法
    if (originalOnShow) {
      originalOnShow.call(this)
    }
  }

  pageOptions.onReady = function() {
    // 先执行混入的方法
    watermarkMixin.onReady.call(this)
    // 再执行原始方法
    if (originalOnReady) {
      originalOnReady.call(this)
    }
  }

  // 混入水印方法
  pageOptions.initWatermark = watermarkMixin.initWatermark
  pageOptions.updateWatermark = watermarkMixin.updateWatermark
  pageOptions.removeWatermark = watermarkMixin.removeWatermark
  pageOptions.injectWatermarkComponent = watermarkMixin.injectWatermarkComponent

  return pageOptions
}

export default watermarkMixin

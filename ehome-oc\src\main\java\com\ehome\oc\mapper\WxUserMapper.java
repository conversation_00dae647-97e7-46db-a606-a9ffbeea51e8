package com.ehome.oc.mapper;

import com.ehome.oc.domain.WxUser;
import org.apache.ibatis.annotations.Param;

public interface WxUserMapper {
    /**
     * 新增用户
     */
    int insertWxUser(WxUser user);

    /**
     * 修改用户
     */
    int updateWxUser(WxUser user);

    /**
     * 根据用户ID查询用户
     */
    WxUser selectWxUserById(@Param("userId") Long userId);

    /**
     * 根据openid查询用户
     */
    WxUser selectWxUserByOpenid(@Param("openId") String openid);
} 
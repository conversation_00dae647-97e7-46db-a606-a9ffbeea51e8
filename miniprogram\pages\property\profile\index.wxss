.profile-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background: white;
  padding: 40rpx 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: #1890ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
}

.avatar text {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-type {
  font-size: 24rpx;
  color: #1890ff;
  background: #e6f7ff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.menu-section {
  background: white;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.menu-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #999;
}

.logout-section {
  padding: 40rpx 0;
}

.logout-btn {
  width: 100%;
  background: white;
  color: #f5222d;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

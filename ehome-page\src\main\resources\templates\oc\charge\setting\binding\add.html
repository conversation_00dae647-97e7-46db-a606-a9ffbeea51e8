<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('添加收费绑定')" />
    <th:block th:include="include :: ztree-css" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-binding-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">资产类型：</label>
            <div class="col-sm-8">
                <div class="radio-box" th:each="dict,iterStat : ${@dict.getType('asset_type')}">
                    <input type="radio" th:id="'assetType' + ${dict.dictValue}" name="assetType" th:value="${dict.dictValue}" class="asset-type-radio" th:checked="${iterStat.first}" />
                    <label th:for="'assetType' + ${dict.dictValue}" th:text="${dict.dictLabel}"></label>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">选择资产：</label>
            <div class="col-sm-8">
                <!-- 房屋选择 -->
                <div id="houseSelector" style="display: none;">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="input-group">
                                <input type="text" id="houseInput" class="form-control" placeholder="点击选择房屋" readonly>
                                <div class="input-group-btn">
                                    <button class="btn btn-default" type="button" onclick="showHouseTree()">选择</button>
                                </div>
                            </div>
                            <div id="houseTreeContainer" style="display: none; border: 1px solid #ddd; margin-top: 5px; max-height: 300px; overflow-y: auto;">
                                <div id="buildingTree" class="ztree" style="padding: 10px;"></div>
                                <div style="text-align: center; padding: 10px; border-top: 1px solid #ddd;">
                                    <button type="button" class="btn btn-primary btn-sm" onclick="confirmHouseSelection()">确定</button>
                                    <button type="button" class="btn btn-default btn-sm" onclick="cancelHouseSelection()">取消</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 车位选择 -->
                <div id="parkingSelector" style="display: none;">
                    <div class="row">
                        <div class="col-sm-12">
                            <select id="parkingSelect" multiple="multiple" style="width: 100%; height: 200px;">
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 车辆选择 -->
                <div id="vehicleSelector" style="display: none;">
                    <div class="row">
                        <div class="col-sm-12">
                            <select id="vehicleSelect" multiple="multiple" style="width: 100%; height: 200px;">
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 其他资产类型 -->
                <div id="otherSelector" style="display: none;">
                    <div class="row">
                        <div class="col-sm-12">
                            <textarea id="otherAssets" placeholder="请输入资产ID，多个用逗号分隔" rows="5" style="width: 100%;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">绑定收费标准：</label>
            <div class="col-sm-8">
                <select name="chargeStandardId" class="form-control" required>
                    <option value="">请选择收费标准</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">收费开始日期：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="startTimeStr" class="form-control time-input" placeholder="yyyy-MM-dd" type="text" required  data-callback="checkStartDate">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 从选择的日期开始，生成每个月的账单，且未来每个月也会定期生成账单</span>

                <!-- 自然月账单选项 -->
                <div id="naturalPeriodOption" style="margin-top: 10px;">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="naturalPeriodCheck" name="naturalPeriod" value="1"> 按照自然月生成账单
                            <i class="fa fa-question-circle" style="color: #999; cursor: pointer; margin-left: 5px;" onmouseover="showNaturalPeriodTip(this)" onmouseout="layer.closeAll('tips')"></i>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">结束时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="endTimeStr" class="form-control time-input" placeholder="yyyy-MM-dd，留空表示无结束时间" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="noEndTime" onchange="toggleEndTime()"> 无结束时间
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">收费周期：</label>
            <div class="col-sm-8">
                <div class="input-group">
                    <select name="periodNum" class="form-control">
                        <option value="1" selected>1个月</option>
                        <option value="2">2个月</option>
                        <option value="3">3个月</option>
                        <option value="4">4个月</option>
                        <option value="5">5个月</option>
                        <option value="6">6个月</option>
                        <option value="7">7个月</option>
                        <option value="8">8个月</option>
                        <option value="9">9个月</option>
                        <option value="10">10个月</option>
                        <option value="11">11个月</option>
                        <option value="12">12个月</option>
                    </select>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 每X个月作为一个周期进行收费</span>
            </div>
        </div>




    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: ztree-js" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">
    var prefix = ctx + "oc/charge/setting/binding";
    var zTree;
    var selectedHouses = [];
    var setting = {
        check: {
            enable: true,
            chkboxType: {"Y": "ps", "N": "ps"}
        },
        data: {
            simpleData: {
                enable: true,
                idKey: "id",
                pIdKey: "pId",
                rootPId: 0
            }
        }
    };



    $("#form-binding-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            var assetType = $("input[name='assetType']:checked").val();
            if (!assetType) {
                $.modal.alertWarning("请选择资产类型");
                return;
            }

            var assetIds = getSelectedAssetIds(assetType);
            if (!assetIds) {
                $.modal.alertWarning("请选择要绑定的资产");
                return;
            }

            // 临时启用被禁用的naturalPeriodCheck以确保其值能被提交
            var naturalPeriodCheck = $("#naturalPeriodCheck");
            var wasDisabled = naturalPeriodCheck.prop('disabled');
            if (wasDisabled && naturalPeriodCheck.prop('checked')) {
                naturalPeriodCheck.prop('disabled', false);
            }

            var formData = $("#form-binding-add").serializeArray();
            var data = {};
            $.each(formData, function(i, field) {
                data[field.name] = field.value;
            });

            // 恢复naturalPeriodCheck的禁用状态
            if (wasDisabled) {
                naturalPeriodCheck.prop('disabled', true);
            }

            // 添加资产相关数据
            data.assetType = assetType;
            data.assetIds = assetIds;

            // 处理时间转换
            if (data.startTimeStr) {
                data.startTime = new Date(data.startTimeStr).getTime() / 1000;
            }
            if (data.endTimeStr && !$("#noEndTime").is(":checked")) {
                data.endTime = new Date(data.endTimeStr).getTime() / 1000;
            } else {
                data.endTime = 0;
            }

            $.operate.save(prefix+"/add", data);
        }
    }

    function changeAssetType(assetType) {
        console.log("changeAssetType called with:", assetType);
        // 隐藏所有选择器
        $("#houseSelector, #parkingSelector, #vehicleSelector, #otherSelector").hide();

        // 转换为数字进行比较
        assetType = parseInt(assetType);

        // 显示对应的选择器
        switch(assetType) {
            case 1: // 房屋
                $("#houseSelector").show();
                break;
            case 2: // 车位
                $("#parkingSelector").show();
                loadAssets(assetType, "#parkingSelect");
                break;
            case 3: // 车辆
                $("#vehicleSelector").show();
                loadAssets(assetType, "#vehicleSelect");
                break;
            default: // 其他
                $("#otherSelector").show();
                break;
        }
    }



    function loadAssets(assetType, selector) {
        $.post(prefix + "/getAssets", {assetType: assetType}, function(result) {
            if (result.code == 0) {
                var select = $(selector);
                select.empty();
                $.each(result.data, function(index, item) {
                    select.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
                // 初始化select2
                select.select2({
                    placeholder: "请选择资产",
                    allowClear: true,
                    width: '100%'
                });
            }
        });
    }

    // 显示房屋树形选择器
    function showHouseTree() {
        if (!zTree) {
            loadBuildingTree();
        }
        $("#houseTreeContainer").show();
    }

    // 确认房屋选择
    function confirmHouseSelection() {
        if (zTree) {
            var nodes = zTree.getCheckedNodes(true);
            selectedHouses = [];
            var houseNames = [];
            $.each(nodes, function(i, node) {
                if (node.assetId) {
                    selectedHouses.push(node.assetId);
                    // 构建完整的房屋名称：楼栋/单元/房号
                    var parentUnit = zTree.getNodeByTId(node.parentTId);
                    var parentBuilding = parentUnit ? zTree.getNodeByTId(parentUnit.parentTId) : null;
                    var fullName = "";
                    if (parentBuilding && parentUnit) {
                        fullName = parentBuilding.name + "/" + parentUnit.name + "/" + node.name;
                    } else {
                        fullName = node.name;
                    }
                    houseNames.push(fullName);
                }
            });
            $("#houseInput").val(houseNames.join(", "));
        }
        $("#houseTreeContainer").hide();
    }

    // 取消房屋选择
    function cancelHouseSelection() {
        $("#houseTreeContainer").hide();
    }

    // 加载楼栋树
    function loadBuildingTree() {
        $.post(prefix + "/getBuildingTreeSimple", {}, function(result) {
            if (result.code == 0) {
                zTree = $.fn.zTree.init($("#buildingTree"), setting, result.data);
                zTree.expandAll(false);
            }
        });
    }

    function getSelectedAssetIds(assetType) {
        var assetIds = "";
        switch(assetType) {
            case "1": // 房屋
                assetIds = selectedHouses.join(",");
                break;
            case "2": // 车位
                var parkingVals = $("#parkingSelect").val();
                assetIds = parkingVals && parkingVals.length > 0 ? parkingVals.join(",") : "";
                break;
            case "3": // 车辆
                var vehicleVals = $("#vehicleSelect").val();
                assetIds = vehicleVals && vehicleVals.length > 0 ? vehicleVals.join(",") : "";
                break;
            default: // 其他
                assetIds = $("#otherAssets").val();
                break;
        }
        return assetIds;
    }

    function toggleEndTime() {
        var noEndTime = $("#noEndTime").is(":checked");
        $("input[name='endTimeStr']").prop("disabled", noEndTime);
        if (noEndTime) {
            $("input[name='endTimeStr']").val("");
        }
    }

    $(function() {
        // 加载收费标准下拉选项
        $.post(prefix + "/getChargeStandards", {}, function(result) {
            if (result.code == 0) {
                var select = $("select[name='chargeStandardId']");
                select.empty();
                select.append('<option value="">请选择收费标准</option>');
                $.each(result.data, function(index, item) {
                    select.append('<option value="' + item.id + '">' + item.name + '</option>');
                });
            }
        });

        // 绑定资产类型选择事件
        $("input[name='assetType']").change(function() {
            if (this.checked) {
                changeAssetType(this.value);
            }
        });

        // 页面加载时自动选择第一个资产类型
        var firstAssetType = $("input[name='assetType']:first");
        if (firstAssetType.length > 0) {
            firstAssetType.prop('checked', true);
            changeAssetType(firstAssetType.val());
        }
    });

    // 检查开始日期
    function checkStartDate() {
        var startDate = $("input[name='startTimeStr']").val();
        if (startDate) {
            var naturalPeriodCheck = $("#naturalPeriodCheck");

            // 检查日期是否以01结尾（即1号）
            if (startDate.endsWith('-01')) {
                // 如果是1号，自动勾选并禁用
                naturalPeriodCheck.prop('checked', true);
                naturalPeriodCheck.prop('disabled', true);
            } else {
                // 如果不是1号，启用选择并默认不勾选
                naturalPeriodCheck.prop('disabled', false);
                naturalPeriodCheck.prop('checked', false);
            }
        }
    }

    // 显示自然月账单说明
    function showNaturalPeriodTip(element) {
        var tipContent = '<div style="text-align: left; line-height: 1.4; font-size: 12px;">';
        tipContent += '<strong>以开始时间2023.03.08为例：</strong><br><br>';
        tipContent += '<table style="width: 100%; border-collapse: collapse; font-size: 11px;">';
        tipContent += '<tr>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">账单类型</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第1期</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第2期</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第3期</th>';
        tipContent += '</tr>';
        tipContent += '<tr>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; font-weight: bold;">按自然月生成账单</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">03.08~03.31</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">04.01~04.30</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">05.01~05.31</td>';
        tipContent += '</tr>';
        tipContent += '<tr>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; font-weight: bold;">按非自然月生成账单</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">03.08~04.07</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">04.08~05.07</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">05.08~06.07</td>';
        tipContent += '</tr>';
        tipContent += '</table>';
        tipContent += '</div>';

        layer.tips(tipContent, element, {
            tips: [1, '#3595CC'],
            time: 0,
            area: ['400px', 'auto']
        });
    }
</script>
</body>
</html>

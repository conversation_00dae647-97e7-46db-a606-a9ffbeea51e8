/* 
	更新于2022-2-16 优化 el-alert 样式
 */

/** 公共css */
*{margin: 0px;padding: 0px;}
html{font-size: 10px; height: 100%;}
body{font-size: 1.4rem; height: 100%;/* background-color: #eeeeee; */  color: #333;} 
body{font-family: "Helvetica Neue", Helvetica, "PingFang SC", Tahoma, Arial, sans-serif;}
a{text-decoration: none;}
a:hover{}
/* h1,h2,h3,h4,h5,h6{font-weight: 400;} */
hr{background-color : #ddd; height: 1px; border: none;}
input,select{outline: 0;}

/* input type=number时不显示按钮 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none !important;
	margin: 0;
}

/* 居中形式的img */
.cover-img{object-fit: cover; object-position: 50% 30%;}

/* ajax2加载时的转圈圈样式 */
.ajax-layer-load.layui-layer-dialog{min-width: 0px !important; background-color: rgba(0,0,0,0.85);}
.ajax-layer-load.layui-layer-dialog .layui-layer-content{padding: 10px 20px 10px 40px; color: #FFF;}
.ajax-layer-load.layui-layer-dialog .layui-layer-content .layui-layer-ico{width: 20px; height: 20px; background-size: 20px 20px; top: 12px; }


/* layer图片预览时, 左右键永远显示 */
.layui-layer-imgbar, .layui-layer-imguide{display: block !important;}
.layui-layer-iconext.layui-layer-imgprev{position: fixed; left: 50;}
.layui-layer-iconext.layui-layer-imgnext{position: fixed; right: 50;}


/* ===================== 整体面板 ===================== */
/* vue盒子 */
.vue-box{padding: 14px; height: calc(100vh - 28px); overflow: auto; font-size: 14px; background-color: #eeeeee;}

/* 内容-面板 */
.c-panel{/* margin: 0.7em 1em; */ margin-bottom: 10px; padding: 1em 1.5em; padding-bottom: 5px; background-color: #fff; color: #333; /* box-shadow: 0 0 5px #eee; */}
.c-title{font-size: 14px; font-weight: bold; line-height: 2em; margin-bottom: 3px;}
.c-title span{font-weight: 400; font-size: 0.85em; padding-left: 1em; color: #888;}

/* ===================== 表单相关 ===================== */
.c-panel .el-form{/* padding-top: 10px; */ /* padding-bottom: 15px; */}
.c-panel .el-form-item{min-width: 278px;}
.c-panel .el-form-item__label{width: 100px;}
.c-panel .el-form .el-input{width: 178px;}
.c-remark{color: #888; margin-left: 0.5em; font-size: 0.9em;}

/* 标签 */
.c-panel .c-tag{padding: 0px 15px; height: 22px; line-height: 22px; border-radius: 0px; border: 0px;}
/* 复选框 */
.c-panel .el-checkbox,.c-panel .el-radio{margin-right: 20px;}
/* 禁用input的样式 */
.c-panel .el-input.is-disabled .el-input__inner{color: #999;}

/* 表格的表头颜色深一点 */
.c-panel .el-table__header tr th{background-color: #F5F5F5; color: #555; padding: 8px 0;}

/* 调整圆角大小: 输入框、文本域、按钮、 */
.vue-box .el-input__inner,
.vue-box .el-textarea__inner,
.vue-box .el-button,
.s-foot .el-button{border-radius: 2px !important;}
.vue-box .el-alert{/* padding: 1em 0.5em; */border-radius: 0px !important;}

/* 多个按钮并列时的距离 */
.vue-box .el-button+.el-button{margin-left: 2px;}
.vue-box .el-button+.el-button.el-button--text{margin-left: 10px;}

/* 单选button,圆角限制2px */
.el-radio-button:first-child .el-radio-button__inner{border-radius: 2px 0 0 2px !important;}
.el-radio-button:last-child .el-radio-button__inner{border-radius: 0 2px 2px 0 !important;}

/* 单选按钮, 文字版 */
.s-radio-text{}
.s-radio-text .el-radio__input{display: none;}
.s-radio-text .el-radio__input.is-checked+.el-radio__label{font-weight: 700;}
.s-radio-text .el-radio__label{padding-left: 0px; }
.s-radio-text .el-radio__label:hover{text-decoration:underline;}
.s-radio-text .hover-line:hover{text-decoration: underline; cursor: pointer;}
.s-radio-text .el-form-item__content{position: relative; top: -2px;}


/* 按钮的样式调整 */
.el-button--primary{background-color: #1890ff;}
.el-button--success{background-color: #57C22A;}
.el-button--danger{background-color: #ee4949; border-color: #de4949;}
.el-button--warning{background-color: #FF8a10; border-color: #FF8a10; color: #FFF;}
.el-button--primary:hover,.el-button--primary:focus{background-color: #066CF3; border-color: #066CF3;}
.el-button--success:hover,.el-button--success:focus{background-color: #2B9939; border-color: #2B9939;}
.el-button--danger:hover,.el-button--danger:focus{background-color: #d02C2C; border-color: #d02C2C;}
.el-button--warning:hover,.el-button--warning:focus{background-color: #dd6300; border-color: #dd6300;}

.el-button--info:hover,.el-button--info:focus{background-color: #707379; border-color: #707379;}

/* .c-btn 加强 */
.el-table .c-btn{padding: 4px 6px; font-size: 12px !important; border-radius: 0px !important;}
.c-btn.el-button--danger{background-color: #ca4242; border-color: #ca4242;}
.c-btn.el-button--danger:hover{background-color: #A00C0C; border-color: #A00C0C;}

/* 表格上面的按钮 */
.fast-btn{padding-top: 15px; padding-bottom: 10px;} 
/* .el-table{padding-top: 10px;} */
/* 表格里的img */
.td-img{width: 3em; height: 3em; border-radius: 2px; cursor: pointer;}
.info-img{width: 3em; height: 3em; cursor: pointer; margin-right: 5px; vertical-align: middle;}
.c-price{color: red;}

/* 分页盒子调整一下间距 */
.page-box{padding: 2em 0 25px 0; }

/* 流体表单 */
.cj-form.vue-box{padding-bottom: 0px; background-color: #FFF;}
.cj-form .c-panel{box-shadow: 0 0 0; margin-top: 0px; margin-bottom: 0px; padding-top: 2.5em; padding-bottom: 0px;}
.cj-form .c-panel .el-form .el-input{width: 100%;}


/* 底部按钮式风格 */
.sbot{padding: 0px; height: 100vh; background-color: rgba(0,0,0,0); display: flex !important; flex-direction: column;}
.s-body{/* height: calc(100vh - 50px); */ flex: 1; overflow: auto; /* background-color: #009688s; */ }
.s-foot{height: 49px !important; line-height: 49px; text-align: right; background-color: #f5f5f5; border-top: 1px #e5e5e5 solid;}
.sbot .s-foot .el-button{margin-left: -10px; margin-right: 15px; padding: 7px 18px;}
.sbot .s-body .c-panel{margin-bottom: 0px; padding-top: 14px; padding-bottom: 0px;}

.vue-box .s-foot{display: none;}
.sbot .s-ok{display: none !important;}
.sbot .s-foot{display: block;}
.sbot .c-title{display: none;}

/* alert */
.vue-box .el-alert--success.is-light{border: 1px #13ce66 solid;}
.vue-box .el-alert--blue.is-light{border: 1px #28a0ff solid; color: #28a0ff; background-color: #e8f4ff;}
.vue-box .el-alert.is-light .el-alert__closebtn{color: 666;}

/* ===================== 表单相关 ===================== */

/* 内容-item */
.c-item {min-width: 270px; min-height: 32px; line-height: 32px; padding-right: 10px; display: inline-block; margin: 0.5em 0;}
.c-item.br{display: block; margin: 14px 0;}
/* label样式 */
.c-item .c-label{width: 6em; color: #333; padding-right: 4px; display: inline-block; text-align: right; vertical-align: top;}
/* input宽度等样式调整 */
.c-item .el-input{width: 178px;}
/* 禁用input的样式 */
.c-item .el-input.is-disabled .el-input__inner{color: #999;}
/* 链接 行高设置 */
.c-item .el-link{line-height: 1.6em;}

/* 表格动画相关 */
.data-table .el-table__body-wrapper{min-height: 0px; max-height: 60px;}
.data-table .el-table__body-wrapper{transition: all 0.3s;}
.el-table .el-table__body-wrapper table td .cell{word-break: normal;}


/* 防止下拉框被富文本覆盖 */
.el-select-dropdown,.el-picker-panel{z-index: 99999 !important;}

/* ===================== sp 新增 ===================== */

/* ==== 图集照片样式 ==== */
.c-item .image-box{max-width: 700px; padding-left: 0px;}
.c-item .image-box-2{width: 90px; height: 125px; cursor: pointer; float: left;}
.c-item .image-box-2 img{width: 90px; height: 90px; border-radius: 2px;}
.c-item .image-box-info .image-box-2{height: 90px;}
.c-item .image-box-2{display: inline-block; margin-right: 5px; margin-bottom: 5px;}
.c-item .image-box-2 p{text-align: center; color: #999; margin-top: -10px;}
.c-item .up_img{text-align: center; background-color: #f8f8f8; height: 90px;}
.c-item .up_img img{width: 40px; height: 40px; margin-top: 25px;}
.c-item .up_img{border: 1px #eee solid;}

/* ==== wang富文本编辑器 ==== */
.c-item .editor-box{width: 800px; margin-top: 0px; transition: all 0.2s;} 
.c-item .content-box-info{border: 1px #ddd solid; padding: 1em; overflow: hidden; box-sizing: border-box;}
.editor-box #editor{min-height: 300px; background-color: #FFF;}
.editor-box .w-e-toolbar{padding-top: 5px !important;}
/* 富文本内的编辑器尽量小点 */
.editor-box img{max-width: 300px !important;}
.content-box-info img{max-width: 100% !important;}

/* 仿移动端样式兼容 */
/* .editor-item .editor-box{float: left; width: 400px;} 
.editor-item .w-e-toolbar{width: 400px; flex-wrap: wrap; } */
.c-item .fold{height: 100px !important; overflow: hidden;}
.c-item .el-select-dropdown{z-index: 9999999 !important;}

/* 多行内容的样式 */
.c-item-mline{display: inline-block; width: calc(100% - 10em);}
.del-rr{color: red !important; cursor: pointer; margin-left: 0.5em; vertical-align: middle;}

/* ---- 你可以在此处定制全局的字段风格 ---- */
/* .tc-num{font-weight: bold;} */
.tc-num{color: blue;}
/* .tc-date{color: blue;} */
.data-table .el-link--inner{font-weight: bold;}


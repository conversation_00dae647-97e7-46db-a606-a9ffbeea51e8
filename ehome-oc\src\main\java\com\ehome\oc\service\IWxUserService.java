package com.ehome.oc.service;

import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;

import java.util.Map;

public interface IWxUserService {
    /**
     * 微信登录
     */
    WxUser wxLogin(WxLoginDTO wxLoginDTO);

    /**
     * 根据用户ID查询用户
     */
    WxUser selectWxUserById(Long userId);

    /**
     * 更新用户信息
     */
    int updateWxUser(WxUser user);

    /**
     * 根据openid查询用户
     */
    WxUser selectWxUserByOpenid(String openid);

    /**
     * 更新用户最后登录时间和IP
     */
    void updateUserLoginInfo(Long userId, String loginIp);

    /**
     * 根据code检查用户是否存在
     */
    WxUser checkUserByCode(String code);

    /**
     * 检查用户是否存在，同时返回微信接口数据
     */
    Map<String, Object> checkUserWithWxInfo(String code);

    /**
     * 根据手机号查询微信用户
     */
    WxUser selectWxUserByMobile(String mobile);

    /**
     * 根据手机号检查用户身份（业主+物业）
     */
    Map<String, Object> checkUserIdentityByPhone(String phoneNumber);

    /**
     * 根据手机号检查用户身份（业主+物业）- 带缓存优化
     */
    Map<String, Object> checkUserIdentityByPhoneWithCache(String phoneNumber, WxUser wxUser);
}
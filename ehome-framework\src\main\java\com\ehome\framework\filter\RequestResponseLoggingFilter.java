package com.ehome.framework.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * HTTP请求响应日志过滤器
 * 记录所有HTTP请求的详细信息，包括请求参数、响应结果、执行时间等
 * 
 * <AUTHOR>
 */
@Component
public class RequestResponseLoggingFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger("sys-request");
    
    /** 请求开始时间 */
    private static final String START_TIME = "startTime";
    
    /** 请求ID */
    private static final String REQUEST_ID = "requestId";
    
    /** 排除记录的参数名 */
    private static final String[] EXCLUDE_PARAMS = {"password", "oldPassword", "newPassword", "confirmPassword"};
    
    /** 排除记录的路径 */
    private static final String[] EXCLUDE_PATHS = {
        "/static/", "/css/", "/js/", "/images/", "/fonts/", "/favicon.ico", "/druid/",
        "/ajax/", "/libs/", ".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg"
    };

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // 检查是否需要排除
        if (shouldExclude(request)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 生成请求ID
        String requestId = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        MDC.put(REQUEST_ID, requestId);
        
        // 包装请求和响应以便捕获内容
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);
        
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        
        try {
            // 记录请求信息
            logRequest(wrappedRequest, requestId);
            
            // 继续执行过滤器链
            filterChain.doFilter(wrappedRequest, wrappedResponse);
            
            // 记录响应信息
            logResponse(wrappedRequest, wrappedResponse, requestId, System.currentTimeMillis() - startTime, null);
            
        } catch (Exception ex) {
            // 记录异常响应信息
            logResponse(wrappedRequest, wrappedResponse, requestId, System.currentTimeMillis() - startTime, ex);
            throw ex;
        } finally {
            // 复制响应内容到原始响应
            wrappedResponse.copyBodyToResponse();
            // 清理MDC
            MDC.clear();
        }
    }

    /**
     * 判断是否需要排除记录
     */
    private boolean shouldExclude(HttpServletRequest request) {
        String uri = request.getRequestURI();
        for (String excludePath : EXCLUDE_PATHS) {
            if (uri.contains(excludePath) || uri.endsWith(excludePath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为Ajax请求
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String xRequestedWith = request.getHeader("X-Requested-With");
        String contentType = request.getContentType();
        String accept = request.getHeader("Accept");

        return "XMLHttpRequest".equals(xRequestedWith) ||
               (contentType != null && contentType.contains("application/json")) ||
               (accept != null && accept.contains("application/json"));
    }

    /**
     * 记录请求信息
     */
    private void logRequest(ContentCachingRequestWrapper request, String requestId) {
        try {
            boolean isAjax = isAjaxRequest(request);

            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("requestId", requestId);
            requestInfo.put("method", request.getMethod());
            requestInfo.put("url", request.getRequestURL().toString());
            requestInfo.put("uri", request.getRequestURI());
            requestInfo.put("queryString", request.getQueryString());
            requestInfo.put("remoteAddr", IpUtils.getIpAddr((HttpServletRequest)request));
//          requestInfo.put("userAgent", request.getHeader("User-Agent"));
//          requestInfo.put("contentType", request.getContentType());
            requestInfo.put("isAjax", isAjax);

            // 记录请求头（排除敏感信息）
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                if (!headerName.toLowerCase().contains("authorization") &&
                    !headerName.toLowerCase().contains("cookie")) {
                    headers.put(headerName, request.getHeader(headerName));
                }
            }
//          requestInfo.put("headers", headers);

            // 记录请求参数
            Map<String, Object> params = getRequestParams(request);
            requestInfo.put("params", params);

            // 记录请求体（如果是POST/PUT等）
            String requestBody = getRequestBody(request);
            if (StringUtils.isNotEmpty(requestBody)) {
                requestInfo.put("body", requestBody);
            }

            String logType = isAjax ? "Ajax请求开始" : "HTTP请求开始";
            logger.info("{}: {}", logType, JSON.toJSONString(requestInfo, SerializerFeature.DisableCircularReferenceDetect));
        } catch (Exception e) {
            logger.error("记录请求日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录响应信息
     */
    private void logResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response,
                           String requestId, long costTime, Exception ex) {
        try {
            boolean isAjax = isAjaxRequest(request);

            Map<String, Object> responseInfo = new HashMap<>();
            responseInfo.put("requestId", requestId);
            responseInfo.put("method", request.getMethod());
            responseInfo.put("uri", request.getRequestURI());
            responseInfo.put("status", response.getStatus());
            responseInfo.put("costTime", costTime + "ms");
            responseInfo.put("isAjax", isAjax);

            // 只对Ajax请求记录响应内容，避免记录页面HTML等大量内容
            if (isAjax) {
                String responseBody = getResponseBody(response);
                if (StringUtils.isNotEmpty(responseBody)) {
                    responseInfo.put("responseBody", responseBody);
                }
            }

            if (ex != null) {
                responseInfo.put("exception", ex.getClass().getSimpleName());
                responseInfo.put("errorMessage", StringUtils.substring(ex.getMessage(), 0, 500));
                String logType = isAjax ? "Ajax请求异常" : "HTTP请求异常";
                logger.error("{}: {}", logType, JSON.toJSONString(responseInfo, SerializerFeature.DisableCircularReferenceDetect), ex);
            } else {
                String logType = isAjax ? "Ajax请求完成" : "HTTP请求完成";
                logger.info("{}: {}", logType, JSON.toJSONString(responseInfo, SerializerFeature.DisableCircularReferenceDetect));
            }
        } catch (Exception e) {
            logger.error("记录响应日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取请求参数，排除敏感信息
     */
    private Map<String, Object> getRequestParams(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        try {
            Enumeration<String> paramNames = request.getParameterNames();
            while (paramNames.hasMoreElements()) {
                String paramName = paramNames.nextElement();
                
                // 检查是否为敏感参数
                boolean isSensitive = false;
                for (String excludeParam : EXCLUDE_PARAMS) {
                    if (paramName.toLowerCase().contains(excludeParam.toLowerCase())) {
                        isSensitive = true;
                        break;
                    }
                }
                
                if (isSensitive) {
                    params.put(paramName, "***");
                } else {
                    String[] paramValues = request.getParameterValues(paramName);
                    if (paramValues.length == 1) {
                        params.put(paramName, paramValues[0]);
                    } else {
                        params.put(paramName, paramValues);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取请求参数失败: {}", e.getMessage());
        }
        return params;
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(ContentCachingRequestWrapper request) {
        try {
            byte[] content = request.getContentAsByteArray();
            if (content.length > 0) {
                // 强制使用UTF-8编码，避免编码问题
                String body = new String(content, "UTF-8");
                // 限制请求体长度，避免日志过大
                return StringUtils.substring(body, 0, 2000);
            }
        } catch (Exception e) {
            logger.error("获取请求体失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取响应体内容
     */
    private String getResponseBody(ContentCachingResponseWrapper response) {
        try {
            byte[] content = response.getContentAsByteArray();
            if (content.length > 0) {
                // 检查Content-Type，只处理文本类型的响应
                String contentType = response.getContentType();
                if (contentType != null && (contentType.contains("application/json") ||
                    contentType.contains("text/") || contentType.contains("application/xml"))) {

                    // 强制使用UTF-8编码，避免编码问题
                    String body = new String(content, "UTF-8");
                    // 限制响应体长度，避免日志过大
                    return StringUtils.substring(body, 0, 2000);
                }
            }
        } catch (Exception e) {
            logger.error("获取响应体失败: {}", e.getMessage());
        }
        return null;
    }
}

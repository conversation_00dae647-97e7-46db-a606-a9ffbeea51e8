package com.ehome.oc.service.impl;

import com.ehome.common.utils.sql.SqlExecutorUtils;
import com.ehome.common.utils.sql.SqlTemplateUtils;
import com.ehome.oc.service.IDataInitService;
import com.jfinal.plugin.activerecord.Db;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据初始化服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DataInitServiceImpl implements IDataInitService {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitServiceImpl.class);
    
    /**
     * 模板文件映射
     */
    private static final Map<String, String> TEMPLATE_MAPPING = new HashMap<>();
    
    static {
        TEMPLATE_MAPPING.put("service_tel", "init_service_tel.sql");
        TEMPLATE_MAPPING.put("wx_nav", "init_wx_nav.sql");
        TEMPLATE_MAPPING.put("notice_types", "init_notice_types.sql");
    }
    
    @Override
    public SqlExecutorUtils.ExecutionResult initServiceTelData(String communityId) {
        logger.info("开始初始化服务电话数据，小区ID: {}", communityId);
        
        // 检查是否已初始化
        if (isDataInitialized(communityId, "service_tel")) {
            SqlExecutorUtils.ExecutionResult result = new SqlExecutorUtils.ExecutionResult();
            result.setSuccess(true);
            result.setMessage("服务电话数据已存在，跳过初始化");
            logger.info("服务电话数据已存在，小区ID: {}", communityId);
            return result;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("community_id", communityId);
        
        return SqlExecutorUtils.executeTemplate("init_service_tel.sql", params);
    }
    
    @Override
    public SqlExecutorUtils.ExecutionResult initWxNavData(String communityId) {
        logger.info("开始初始化微信导航数据，小区ID: {}", communityId);
        
        if (isDataInitialized(communityId, "wx_nav")) {
            SqlExecutorUtils.ExecutionResult result = new SqlExecutorUtils.ExecutionResult();
            result.setSuccess(true);
            result.setMessage("微信导航数据已存在，跳过初始化");
            logger.info("微信导航数据已存在，小区ID: {}", communityId);
            return result;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("community_id", communityId);
        
        return SqlExecutorUtils.executeTemplate("init_wx_nav.sql", params);
    }
    
    @Override
    public SqlExecutorUtils.ExecutionResult initNoticeTypesData(String communityId) {
        logger.info("开始初始化公告类型数据，小区ID: {}", communityId);
        
        if (isDataInitialized(communityId, "notice_types")) {
            SqlExecutorUtils.ExecutionResult result = new SqlExecutorUtils.ExecutionResult();
            result.setSuccess(true);
            result.setMessage("公告类型数据已存在，跳过初始化");
            logger.info("公告类型数据已存在，小区ID: {}", communityId);
            return result;
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("community_id", communityId);
        
        return SqlExecutorUtils.executeTemplate("init_notice_types.sql", params);
    }
    
    @Override
    public List<SqlExecutorUtils.ExecutionResult> initAllData(String communityId, List<String> templateNames) {
        logger.info("开始批量初始化数据，小区ID: {}, 模板: {}", communityId, templateNames);
        
        List<SqlExecutorUtils.ExecutionResult> results = new ArrayList<>();
        
        // 如果未指定模板，则使用所有可用模板
        if (templateNames == null || templateNames.isEmpty()) {
            templateNames = getAvailableTemplates();
        }
        
        for (String templateName : templateNames) {
            try {
                SqlExecutorUtils.ExecutionResult result;
                
                // 根据模板名称调用相应的初始化方法
                switch (templateName) {
                    case "init_service_tel.sql":
                        result = initServiceTelData(communityId);
                        break;
                    case "init_wx_nav.sql":
                        result = initWxNavData(communityId);
                        break;
                    case "init_notice_types.sql":
                        result = initNoticeTypesData(communityId);
                        break;
                    default:
                        // 通用模板执行
                        Map<String, Object> params = new HashMap<>();
                        params.put("community_id", communityId);
                        result = SqlExecutorUtils.executeTemplate(templateName, params);
                        break;
                }
                
                results.add(result);
                
            } catch (Exception e) {
                logger.error("初始化模板失败: {}, 小区ID: {}", templateName, communityId, e);
                SqlExecutorUtils.ExecutionResult errorResult = new SqlExecutorUtils.ExecutionResult();
                errorResult.setSuccess(false);
                errorResult.setMessage("模板执行异常: " + e.getMessage());
                errorResult.addError(e.getMessage());
                results.add(errorResult);
            }
        }
        
        return results;
    }
    
    @Override
    public boolean isDataInitialized(String communityId, String dataType) {
        try {
            String checkSql;

            switch (dataType) {
                case "service_tel":
                    checkSql = "SELECT COUNT(*) FROM eh_service_tel WHERE community_id = '" + communityId + "'";
                    break;
                case "wx_nav":
                    checkSql = "SELECT COUNT(*) FROM eh_wx_nav WHERE community_id = '" + communityId + "'";
                    break;
                case "notice_types":
                    // 检查 eh_community 表的 ext_json 字段中是否包含 notice_types
                    checkSql = "SELECT COUNT(*) FROM eh_community WHERE oc_id = '" + communityId + "' AND ext_json IS NOT NULL AND JSON_EXTRACT(ext_json, '$.notice_types') IS NOT NULL";
                    break;
                default:
                    logger.warn("未知的数据类型: {}", dataType);
                    return false;
            }

            return SqlExecutorUtils.dataExists(checkSql);

        } catch (Exception e) {
            logger.error("检查数据初始化状态失败，数据类型: {}, 小区ID: {}", dataType, communityId, e);
            return false;
        }
    }
    
    @Override
    public List<String> getAvailableTemplates() {
        List<String> templates = new ArrayList<>();
        
        // 检查模板文件是否存在
        for (String templateFile : TEMPLATE_MAPPING.values()) {
            if (SqlTemplateUtils.templateExists(templateFile)) {
                templates.add(templateFile);
            }
        }
        
        return templates;
    }
    
    @Override
    public Map<String, Boolean> getInitializationStatus(String communityId) {
        Map<String, Boolean> status = new HashMap<>();

        for (String dataType : TEMPLATE_MAPPING.keySet()) {
            status.put(dataType, isDataInitialized(communityId, dataType));
        }

        return status;
    }

    @Override
    public SqlExecutorUtils.ExecutionResult deleteCommunityData(String communityId, boolean includeBasicInfo) {
        logger.warn("开始删除小区数据，小区ID: {}, 包含基本信息: {}", communityId, includeBasicInfo);

        Map<String, Object> params = new HashMap<>();
        params.put("community_id", communityId);

        // 执行删除SQL模板
        SqlExecutorUtils.ExecutionResult result = SqlExecutorUtils.executeTemplate("delete_community_data.sql", params);

        // 如果需要删除小区基本信息
        if (includeBasicInfo && result.isSuccess()) {
            String deleteCommunitySQL = "DELETE FROM eh_community WHERE oc_id = '" + communityId + "'";
            SqlExecutorUtils.ExecutionResult communityResult = SqlExecutorUtils.executeSingle(deleteCommunitySQL);

            if (!communityResult.isSuccess()) {
                result.setSuccess(false);
                result.setMessage(result.getMessage() + "; 删除小区基本信息失败: " + communityResult.getMessage());
                result.getErrors().addAll(communityResult.getErrors());
            } else {
                result.setMessage(result.getMessage() + "; 已删除小区基本信息");
            }
        }

        logger.warn("小区数据删除完成，小区ID: {}, 结果: {}", communityId, result.isSuccess());
        return result;
    }

    @Override
    public Map<String, Long> getCommunityDataStatistics(String communityId) {
        Map<String, Long> statistics = new HashMap<>();

        try {
            // 定义需要统计的表和对应的查询SQL
            Map<String, String> tableQueries = new HashMap<>();
            tableQueries.put("房屋信息", "SELECT COUNT(*) FROM eh_house_info WHERE community_id = '" + communityId + "'");
            tableQueries.put("业主信息", "SELECT COUNT(*) FROM eh_owner WHERE community_id = '" + communityId + "'");
            tableQueries.put("车辆信息", "SELECT COUNT(*) FROM eh_vehicle WHERE community_id = '" + communityId + "'");
            tableQueries.put("楼栋信息", "SELECT COUNT(*) FROM eh_building WHERE community_id = '" + communityId + "'");
            tableQueries.put("停车位信息", "SELECT COUNT(*) FROM eh_parking_space WHERE community_id = '" + communityId + "'");
            tableQueries.put("服务电话", "SELECT COUNT(*) FROM eh_service_tel WHERE community_id = '" + communityId + "'");
            tableQueries.put("微信用户", "SELECT COUNT(*) FROM eh_wx_user WHERE community_id = '" + communityId + "'");
            tableQueries.put("微信导航", "SELECT COUNT(*) FROM eh_wx_nav WHERE community_id = '" + communityId + "'");
            tableQueries.put("投诉建议", "SELECT COUNT(*) FROM eh_complaint WHERE community_id = '" + communityId + "'");
            tableQueries.put("公告通知", "SELECT COUNT(*) FROM eh_announcement WHERE community_id = '" + communityId + "'");
            tableQueries.put("文件信息", "SELECT COUNT(*) FROM eh_file_info WHERE community_id = '" + communityId + "'");

            // 执行统计查询
            for (Map.Entry<String, String> entry : tableQueries.entrySet()) {
                try {
                    Long count = Db.queryLong(entry.getValue());
                    statistics.put(entry.getKey(), count != null ? count : 0L);
                } catch (Exception e) {
                    logger.error("统计表数据失败: {}", entry.getKey(), e);
                    statistics.put(entry.getKey(), -1L); // -1表示统计失败
                }
            }

        } catch (Exception e) {
            logger.error("获取小区数据统计失败，小区ID: {}", communityId, e);
        }

        return statistics;
    }
}

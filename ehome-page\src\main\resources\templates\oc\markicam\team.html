<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('团队管理')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>团队ID：</label>
                            <input type="text" name="team_id" placeholder="请输入团队ID"/>
                        </li>
                        <li>
                            <label>团队名称：</label>
                            <input type="text" name="team_name" placeholder="请输入团队名称"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="syncTeam()" shiro:hasPermission="oc:markicam:sync">
                <i class="fa fa-refresh"></i> 同步团队
            </a>
            <a class="btn btn-info" onclick="viewDetails()" shiro:hasPermission="oc:markicam:view">
                <i class="fa fa-eye"></i> 查看详情
            </a>
            <a class="btn btn-warning" onclick="exportData()" shiro:hasPermission="oc:markicam:export">
                <i class="fa fa-download"></i> 导出数据
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer" />
<script th:inline="javascript">
    var prefix = ctx + "oc/markicam";

    $(function() {
        var options = {
            url: prefix + "/team/list",
            createUrl: prefix + "/team/add",
            updateUrl: prefix + "/team/edit/{id}",
            removeUrl: prefix + "/team/remove",
            exportUrl: prefix + "/team/export",
            modalName: "团队",
            columns: [{
                checkbox: true
            },
            {
                field: 'id',
                title: 'ID',
                visible: false
            },
            {
                field: 'team_id',
                title: '团队ID',
                width: 100
            },
            {
                field: 'team_name',
                title: '团队名称',
                width: 180
            },
            {
                field: 'member_count',
                title: '成员数量',
                width: 100,
                align: 'center'
            },
            {
                field: 'reg_member_count',
                title: '已注册',
                width: 100,
                align: 'center'
            },
            {
                field: 'unreg_member_count',
                title: '未注册',
                width: 100,
                align: 'center'
            },
            {
                field: 'sync_time',
                title: '同步时间',
                width: 160
            },
            {
                title: '操作',
                align: 'center',
                width: 160,
                formatter: function(value, row, index) {
                    var actions = [];
                    actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a> ');
                    actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="toMembers(\'' + row.team_id + '\')"><i class="fa fa-users"></i>成员</a> ');
                    actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="syncMembers(\'' + row.team_id + '\')"><i class="fa fa-refresh"></i>同步成员</a>');
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    });

    function syncTeam() {
        $.modal.confirm("确定要同步团队数据吗？", function() {
            $.modal.loading("正在同步团队数据...");
            $.post(prefix + "/sync/team", {}, function(result) {
                $.modal.closeLoading();
                if (result.code == 0) {
                    $.modal.alertSuccess("同步成功");
                    $.table.refresh();
                } else {
                    $.modal.alertError("同步失败：" + result.msg);
                }
            }).fail(function() {
                $.modal.closeLoading();
                $.modal.alertError("同步失败");
            });
        });
    }

    function viewDetails() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请选择一条记录");
            return;
        }
        viewDetail(rows[0].id);
    }

    function viewDetail(id) {
        $.get(prefix + "/team/detail/" + id, function(result) {
            if (result.code == 0) {
                var data = result.data;
                var content = '<div class="row">';
                content += '<div class="col-md-6">';
                content += '<p><strong>团队ID:</strong> ' + (data.team_id || '-') + '</p>';
                content += '<p><strong>团队名称:</strong> ' + (data.team_name || '-') + '</p>';
                content += '<p><strong>成员数量:</strong> ' + (data.member_count || 0) + '</p>';
                content += '</div>';
                content += '<div class="col-md-6">';
                content += '<p><strong>已注册:</strong> ' + (data.reg_member_count || 0) + '</p>';
                content += '<p><strong>未注册:</strong> ' + (data.unreg_member_count || 0) + '</p>';
                content += '<p><strong>同步时间:</strong> ' + (data.sync_time || '-') + '</p>';
                content += '</div>';
                content += '</div>';
                
                $.modal.open("团队详情", content);
            } else {
                $.modal.alertError(result.msg);
            }
        });
    }

    function toMembers(teamId) {
        // 跳转到成员页面并带上team_id筛选
        window.location.href = prefix + "/member?team_id=" + teamId;
    }

    function syncMembers(teamId) {
        $.modal.loading("正在同步成员数据...");
        $.post(prefix + "/sync/member", { team_id: teamId }, function(result) {
            $.modal.closeLoading();
            if (result.code == 0) {
                $.modal.alertSuccess("成员同步成功");
            } else {
                $.modal.alertError("同步失败：" + result.msg);
            }
        }).fail(function() {
            $.modal.closeLoading();
            $.modal.alertError("同步失败");
        });
    }

    function exportData() {
        $.modal.confirm("确定要导出团队数据吗？", function() {
            var formData = $("#formId").serialize();
            window.location.href = prefix + "/team/export?" + formData;
        });
    }
</script>
</body>
</html>

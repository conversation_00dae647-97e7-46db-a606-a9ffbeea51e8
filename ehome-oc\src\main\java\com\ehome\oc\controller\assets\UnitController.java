package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.Unit;
import com.ehome.oc.service.IUnitService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/oc/unit")
public class UnitController extends BaseController {
    private String prefix = "oc/unit";

    @Autowired
    private IUnitService unitService;

    @GetMapping("/mgr")
    public String mgr() {
        return prefix + "/list";
    }

    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    @GetMapping("/list/{buildingId}")
    public String list(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        mmap.put("buildingId", buildingId);
        return prefix + "/list";
    }

    @GetMapping("/add/{buildingId}")
    public String add(@PathVariable("buildingId") String buildingId, ModelMap mmap) {
        mmap.put("buildingId", buildingId);
        return prefix + "/add";
    }

    @GetMapping("/edit/{unitId}")
    public String edit(@PathVariable("unitId") String unitId, ModelMap mmap) {
        Unit unit = unitService.selectUnitById(unitId);
        mmap.put("unit", unit);
        return prefix + "/edit";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Unit unit) {
        startPage();
        List<Unit> list = unitService.selectUnitList(unit);
        return getDataTable(list);
    }

    @Log(title = "单元管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Unit unit) {
        String communityId = getSysUser().getCommunityId();

        // 检查单元名称是否重复（同一小区同一楼栋内）
        if (!StringUtils.isEmpty(unit.getName()) && !StringUtils.isEmpty(unit.getBuildingId())) {
            Record existingUnit = Db.findFirst("SELECT * FROM eh_unit WHERE name = ? AND building_id = ? AND community_id = ?", unit.getName(), unit.getBuildingId(), communityId);
            if (existingUnit != null) {
                return AjaxResult.error("该楼栋内单元名称已存在");
            }
        }

        // 设置小区ID
        unit.setCommunityId(communityId);
        unit.setCreateBy(getSysUser().getLoginName());
        unit.setCreateTime(DateUtils.getTime());
        return toAjax(unitService.insertUnit(unit));
    }

    @Log(title = "单元管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Unit unit) {
        String communityId = getSysUser().getCommunityId();

        // 检查单元名称是否重复（排除自己，同一小区同一楼栋内）
        if (!StringUtils.isEmpty(unit.getName()) && !StringUtils.isEmpty(unit.getBuildingId()) && !StringUtils.isEmpty(unit.getUnitId())) {
            Record existingUnit = Db.findFirst("SELECT * FROM eh_unit WHERE name = ? AND building_id = ? AND community_id = ? AND unit_id != ?", unit.getName(), unit.getBuildingId(), communityId, unit.getUnitId());
            if (existingUnit != null) {
                return AjaxResult.error("该楼栋内单元名称已存在");
            }
        }

        // 设置小区ID
        unit.setCommunityId(communityId);
        unit.setUpdateBy(getSysUser().getLoginName());
        unit.setUpdateTime(DateUtils.getTime());
        return toAjax(unitService.updateUnit(unit));
    }

    @Log(title = "单元管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return AjaxResult.error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            // 检查单元下是否有房屋数据
            Record houseCount = Db.findFirst("SELECT COUNT(*) as count FROM eh_house_info WHERE unit_id = ?", id);
            if (houseCount != null && houseCount.getInt("count") > 0) {
                // 获取单元名称用于错误提示
                Record unit = Db.findFirst("SELECT name FROM eh_unit WHERE unit_id = ?", id);
                String unitName = unit != null ? unit.getStr("name") : "未知单元";
                return AjaxResult.error("单元【" + unitName + "】下存在房屋数据，无法删除");
            }
        }
        return toAjax(unitService.deleteUnitByIds(idArr));
    }

    /**
     * 校验单元名称是否唯一（同一小区同一楼栋内）
     */
    @PostMapping("/checkName")
    @ResponseBody
    public boolean checkName() {
        JSONObject params = getParams();
        String name = params.getString("name");
        String buildingId = params.getString("buildingId");
        String unitId = params.getString("unitId");
        String communityId = getSysUser().getCommunityId();

        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(buildingId)) {
            return false;
        }

        // 查询同一小区同一楼栋内是否存在相同名称的单元
        String sql = "SELECT * FROM eh_unit WHERE name = ? AND building_id = ? AND community_id = ?";
        if (!StringUtils.isEmpty(unitId)) {
            sql += " AND unit_id != ?";
            Record existingUnit = Db.findFirst(sql, name, buildingId, communityId, unitId);
            return existingUnit == null;
        } else {
            Record existingUnit = Db.findFirst(sql, name, buildingId, communityId);
            return existingUnit == null;
        }
    }

    @PostMapping("/queryBuilding")
    @ResponseBody
    public AjaxResult queryBuilding() {
        List<Record> list = Db.find("select building_id, name from eh_building where community_id = ?", getSysUser().getCommunityId());
        Map<String, String> map = new HashMap<>();
        list.forEach(record -> {
            map.put(record.getStr("building_id"), record.getStr("name"));
        });
        return AjaxResult.success(map);
    }

    @Log(title = "批量导入单元", businessType = BusinessType.INSERT)
    @PostMapping("/batchImport")
    @ResponseBody
    public AjaxResult batchImport(@RequestBody Map<String, Object> requestData) {
        @SuppressWarnings("unchecked")
        List<Map<String, String>> importData = (List<Map<String, String>>) requestData.get("importData");
        String buildingId = (String) requestData.get("buildingId");

        if (importData == null || importData.isEmpty()) {
            return AjaxResult.error("导入数据不能为空");
        }

        if (StringUtils.isEmpty(buildingId)) {
            return AjaxResult.error("楼栋ID不能为空");
        }

        String communityId = getSysUser().getCommunityId();
        String loginName = getSysUser().getLoginName();
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);

        int successCount = 0;
        int skipCount = 0;

        try {
            for (Map<String, String> item : importData) {
                String unitName = item.get("unitName");

                if (StringUtils.isEmpty(unitName)) {
                    skipCount++;
                    continue;
                }

                // 检查单元是否已存在（同一小区同一楼栋内）
                Record existingUnit = Db.findFirst("SELECT * FROM eh_unit WHERE name = ? AND building_id = ? AND community_id = ?", unitName, buildingId, communityId);
                if (existingUnit != null) {
                    skipCount++;
                    continue; // 单元已存在，跳过
                }

                // 创建新单元记录
                Record unit = new Record();
                unit.set("building_id", buildingId);
                unit.set("community_id", communityId);
                unit.set("name", unitName);
                unit.set("house_count", 0);
                unit.set("house_area", 0.00);
                unit.set("create_time", now);
                unit.set("update_time", now);
                unit.set("create_by", loginName);
                unit.set("update_by", loginName);
                unit.set("remark", "批量导入");

                Db.save("eh_unit", "unit_id", unit);
                successCount++;
            }

            // 更新楼栋的单元总数
            updateBuildingTotalUnits(buildingId);

            String message = String.format("导入完成！成功导入 %d 个单元", successCount);
            if (skipCount > 0) {
                message += String.format("，跳过重复 %d 个", skipCount);
            }

            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("批量导入单元失败: " + e.getMessage(), e);
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 更新楼栋的单元总数
     */
    private void updateBuildingTotalUnits(String buildingId) {
        try {
            // 统计该楼栋的单元数量
            Record stats = Db.findFirst("SELECT COUNT(*) as unit_count FROM eh_unit WHERE building_id = ?", buildingId);

            if (stats != null) {
                // 更新楼栋单元总数
                Db.update("UPDATE eh_building SET total_units = ?, update_time = ?, update_by = ? WHERE building_id = ?",
                        stats.getInt("unit_count"),
                        DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS),
                        getSysUser().getLoginName(),
                        buildingId);
            }
        } catch (Exception e) {
            logger.error("更新楼栋单元总数失败: " + e.getMessage(), e);
        }
    }
}
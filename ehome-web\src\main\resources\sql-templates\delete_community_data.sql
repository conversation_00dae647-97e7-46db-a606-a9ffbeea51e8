-- 删除房屋业主关联关系
DELETE FROM eh_house_owner_rel WHERE community_id = '{community_id}';

-- 删除车辆业主关联关系
DELETE FROM eh_vehicle_owner_rel WHERE vehicle_id IN (
    SELECT vehicle_id FROM eh_vehicle WHERE community_id = '{community_id}'
);

-- 删除车辆房屋关联关系
DELETE FROM eh_vehicle_house_rel WHERE house_id IN (
    SELECT house_id FROM eh_house_info WHERE community_id = '{community_id}'
);

-- 删除停车位业主关联关系
DELETE FROM eh_parking_owner_rel WHERE parking_id IN (
    SELECT parking_id FROM eh_parking_space WHERE community_id = '{community_id}'
);

-- 删除附件关联关系
DELETE FROM eh_attachment_relation WHERE community_id = '{community_id}';

-- ========== 第二步：删除业务数据表 ==========

-- 删除微信相关数据
DELETE FROM eh_wx_user WHERE community_id = '{community_id}';
DELETE FROM eh_wx_nav WHERE community_id = '{community_id}';
DELETE FROM eh_wx_config WHERE community_id = '{community_id}';
DELETE FROM eh_wx_message_log WHERE community_id = '{community_id}';
DELETE FROM eh_wx_bx WHERE community_id = '{community_id}';
DELETE FROM eh_wx_complaint WHERE community_id = '{community_id}';
DELETE FROM eh_wx_subscribe_record WHERE community_id = '{community_id}';
DELETE FROM eh_wx_share_record WHERE community_id = '{community_id}';
DELETE FROM eh_wx_share_visit WHERE community_id = '{community_id}';

-- 删除投诉建议
DELETE FROM eh_complaint WHERE community_id = '{community_id}';

-- 删除维修记录
DELETE FROM eh_maintenance WHERE house_id IN (
    SELECT house_id FROM eh_house_info WHERE community_id = '{community_id}'
);

-- 删除房屋邀请记录
DELETE FROM eh_house_invite WHERE house_id IN (
    SELECT house_id FROM eh_house_info WHERE community_id = '{community_id}'
);

-- 删除费用记录
DELETE FROM eh_fee WHERE community_id = '{community_id}';

-- 删除支付记录
DELETE FROM eh_payment WHERE house_id IN (
    SELECT house_id FROM eh_house_info WHERE community_id = '{community_id}'
);

-- 删除交易记录
DELETE FROM eh_tran_record WHERE community_id = '{community_id}';

-- 删除公告通知
DELETE FROM eh_announcement WHERE community_id = '{community_id}';

-- 删除服务电话
DELETE FROM eh_service_tel WHERE community_id = '{community_id}';

-- 删除文件下载日志
DELETE FROM eh_file_download_log WHERE community_id = '{community_id}';

-- 删除文件信息
DELETE FROM eh_file_info WHERE community_id = '{community_id}';

-- 删除账户类型
DELETE FROM eh_account_type WHERE community_id = '{community_id}';

-- ========== 第三步：删除基础数据表 ==========

-- 删除车辆信息
DELETE FROM eh_vehicle WHERE community_id = '{community_id}';

-- 删除停车位信息
DELETE FROM eh_parking_space WHERE community_id = '{community_id}';

-- 删除业主信息
DELETE FROM eh_owner WHERE community_id = '{community_id}';

-- 删除房屋信息
DELETE FROM eh_house_info WHERE community_id = '{community_id}';

-- 删除单元信息
DELETE FROM eh_unit WHERE building_id IN (
    SELECT building_id FROM eh_building WHERE community_id = '{community_id}'
);

-- 删除楼栋信息
DELETE FROM eh_building WHERE community_id = '{community_id}';

-- 删除物业银行账户
DELETE FROM eh_pms_bank_account WHERE community_id = '{community_id}';

-- ========== 第四步：删除员工信息（可选） ==========
-- 注意：员工可能服务多个小区，请谨慎删除
-- DELETE FROM eh_employee WHERE community_id = '{community_id}';

-- ========== 第五步：删除小区基本信息 ==========
-- 警告：这是最后一步，删除后小区将完全消失
DELETE FROM eh_community WHERE oc_id = '{community_id}';

-- ========== 删除系统相关数据 ==========
-- 删除该小区相关的系统通知
DELETE FROM sys_notice WHERE community_id = '{community_id}';

-- 删除该小区相关的操作日志（可选）
DELETE FROM sys_oper_log WHERE community_id = '{community_id}';

-- ========== 执行完成提示 ==========
-- 数据删除完成，请检查是否还有遗漏的数据
-- 建议执行以下查询检查残留数据：
-- SELECT table_name, COUNT(*) as count FROM (
--     SELECT 'eh_house_info' as table_name, COUNT(*) as count FROM eh_house_info WHERE community_id = '{community_id}'
--     UNION ALL
--     SELECT 'eh_owner' as table_name, COUNT(*) as count FROM eh_owner WHERE community_id = '{community_id}'
--     UNION ALL
--     SELECT 'eh_vehicle' as table_name, COUNT(*) as count FROM eh_vehicle WHERE community_id = '{community_id}'
--     -- 添加其他需要检查的表...
-- ) t WHERE count > 0;

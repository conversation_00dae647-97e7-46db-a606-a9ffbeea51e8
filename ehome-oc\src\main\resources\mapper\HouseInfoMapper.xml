<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ehome.oc.mapper.HouseInfoMapper">

    <resultMap type="com.ehome.oc.domain.HouseInfo" id="HouseInfoResult">
        <id     property="houseId"      column="house_id"/>
        <result property="communityId"  column="community_id"/>
        <result property="ownerStr"     column="owner_str"/>
        <result property="communityName" column="community_name"/>
        <result property="buildingId"   column="building_id"/>
        <result property="building"     column="building_name"/>
        <result property="unit"         column="unit"/>
        <result property="room"         column="room"/>
        <result property="area"         column="area"/>
        <result property="ownerName"    column="owner_name"/>
        <result property="ownerPhone"   column="owner_phone"/>
        <result property="idCard"       column="id_card"/>
        <result property="checkStatus"  column="check_status"/>
        <result property="remark"       column="remark"/>
        <result property="createTime"   column="create_time"/>
        <result property="updateTime"   column="update_time"/>
    </resultMap>

    <sql id="selectHouseVo">
        select house_id, community_id, owner_str, community_name, building_id, building_name, unit, room,
        area, owner_name, owner_phone, id_card, check_status, remark, create_time, update_time
        from eh_house_info
    </sql>

    <select id="selectHouseById" parameterType="Long" resultMap="HouseInfoResult">
        <include refid="selectHouseVo"/>
        where house_id = #{houseId}
    </select>

    <insert id="insertHouse" parameterType="com.ehome.oc.domain.HouseInfo" useGeneratedKeys="true" keyProperty="houseId">
        insert into eh_house_info (
             community_id, owner_id, community_name, building_id, building_name, unit, room, area,
            owner_name, owner_phone, id_card, status, remark, create_time
        ) values (
            #{communityId}, #{ownerId}, #{communityName}, #{buildingId}, #{building}, #{unit}, #{room}, #{area},
            #{ownerName}, #{ownerPhone}, #{idCard}, #{status}, # #{remark}, sysdate()
        )
    </insert>

    <delete id="deleteHouseById" parameterType="Long">
        delete from eh_house_info where house_id = #{houseId}
    </delete>

</mapper>
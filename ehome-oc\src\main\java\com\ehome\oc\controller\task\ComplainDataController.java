package com.ehome.oc.controller.task;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.service.WxMessageService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping("/oc/complain")
public class ComplainDataController extends BaseController {
    private static final String PREFIX = "oc/complaint";

    @Autowired
    private WxMessageService wxMessageService;

    @RequestMapping("/compl")
    public String complaint() {
        return PREFIX+"/compl-list";
    }

    @RequestMapping("/bx")
    public String bx() {
        return PREFIX+"/bx-list";
    }

    @ResponseBody
    @PostMapping("/complaintData")
    public TableDataInfo complaintData(){
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_complaint t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("status"),"and t1.status = ?");
        sql.append("order by t1.create_time desc");
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @ResponseBody
    @PostMapping("/updateComplaintStatus")
    public AjaxResult updateComplaintStatus() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        String status = params.getString("status");
        String feedback = params.getString("feedback");

        // 构建更新SQL
        StringBuilder sql = new StringBuilder("update eh_wx_complaint set status = ?, update_time = ?");
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(status);
        sqlParams.add(new Date());

        // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
        if ("1".equals(status) || "2".equals(status)) {
            sql.append(", handler = ?, handling_time = ?");
            sqlParams.add(getSysUser().getUserName());
            sqlParams.add(new Date());
        }

        // 当状态为已完成(2)且有反馈内容时，记录处理反馈
        if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
            sql.append(", feedback = ?");
            sqlParams.add(feedback);
        }

        sql.append(" where id = ?");
        sqlParams.add(id);

        Db.update(sql.toString(), sqlParams.toArray());
        return success();
    }

    @ResponseBody
    @PostMapping("/bxData")
    public TableDataInfo bxData(){
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_wx_bx t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(),"and community_id = ?");
        sql.append(params.getString("status"),"and t1.status = ?");
        sql.append("order by t1.create_time desc");
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select t1.*",
                sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @ResponseBody
    @PostMapping("/updateBxStatus")
    public AjaxResult updateBxStatus() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        String status = params.getString("status");
        String feedback = params.getString("feedback");

        // 构建更新SQL
        StringBuilder sql = new StringBuilder("update eh_wx_bx set status = ?, update_time = ?");
        List<Object> sqlParams = new ArrayList<>();
        sqlParams.add(status);
        sqlParams.add(DateUtils.getTime());

        // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
        if ("1".equals(status) || "2".equals(status)) {
            sql.append(", handler = ?, handling_time = ?");
            sqlParams.add(getSysUser().getUserName());
            sqlParams.add(new Date());
        }

        // 当状态为已完成(2)且有反馈内容时，记录处理反馈
        if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
            sql.append(", feedback = ?");
            sqlParams.add(feedback);
        }

        sql.append(" where id = ?");
        sqlParams.add(id);

        Db.update(sql.toString(), sqlParams.toArray());

        // 发送消息通知
        sendRepairStatusMessage(id, status, feedback);

        return success();
    }

    @ResponseBody
    @PostMapping("/complaintDetail")
    public AjaxResult complaintDetail() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        Record record = Db.findFirst("select * from eh_wx_complaint where id = ?", id);
        if (record == null) {
            return error("未找到投诉记录");
        }
        return success(record.toMap());
    }

    @ResponseBody
    @PostMapping("/bxDetail")
    public AjaxResult bxDetail() {
        JSONObject params = getParams();
        String id = params.getString("id");
        if (StringUtils.isEmpty(id)) {
            return error("参数错误");
        }
        Record record = Db.findFirst("select * from eh_wx_bx where id = ?", id);
        if (record == null) {
            return error("未找到报修记录");
        }
        return success(record.toMap());
    }

    @ResponseBody
    @PostMapping("/batchUpdateComplaintStatus")
    public AjaxResult batchUpdateComplaintStatus() {
        JSONObject params = getParams();
        String ids = params.getString("ids"); // 逗号分隔
        String status = params.getString("status");
        String feedback = params.getString("feedback");

        if (StringUtils.isEmpty(ids) || StringUtils.isEmpty(status)) {
            return error("参数错误");
        }

        // 当状态为已完成(2)且没有反馈内容时，提示错误
        if ("2".equals(status) && StringUtils.isEmpty(feedback)) {
            return error("完成状态时必须填写处理反馈");
        }

        String[] idArr = ids.split(",");
        for (String id : idArr) {
            // 构建更新SQL
            StringBuilder sql = new StringBuilder("update eh_wx_complaint set status = ?, update_time = ?");
            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(status);
            sqlParams.add(DateUtils.getTime());

            // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
            if ("1".equals(status) || "2".equals(status)) {
                sql.append(", handler = ?, handling_time = ?");
                sqlParams.add(getSysUser().getUserName());
                sqlParams.add(DateUtils.getTime());
            }

            // 当状态为已完成(2)且有反馈内容时，记录处理反馈
            if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
                sql.append(", feedback = ?");
                sqlParams.add(feedback);
            }

            sql.append(" where id = ?");
            sqlParams.add(id);

            Db.update(sql.toString(), sqlParams.toArray());
        }
        return success();
    }

    @ResponseBody
    @PostMapping("/batchUpdateBxStatus")
    public AjaxResult batchUpdateBxStatus() {
        JSONObject params = getParams();
        String ids = params.getString("ids"); // 逗号分隔
        String status = params.getString("status");
        String feedback = params.getString("feedback");

        if (StringUtils.isEmpty(ids) || StringUtils.isEmpty(status)) {
            return error("参数错误");
        }

        // 当状态为已完成(2)且没有反馈内容时，提示错误
        if ("2".equals(status) && StringUtils.isEmpty(feedback)) {
            return error("完成状态时必须填写处理反馈");
        }

        String[] idArr = ids.split(",");
        for (String id : idArr) {
            // 构建更新SQL
            StringBuilder sql = new StringBuilder("update eh_wx_bx set status = ?, update_time = ?");
            List<Object> sqlParams = new ArrayList<>();
            sqlParams.add(status);
            sqlParams.add(new Date());

            // 当状态为处理中(1)或已完成(2)时，记录处理人和处理时间
            if ("1".equals(status) || "2".equals(status)) {
                sql.append(", handler = ?, handling_time = ?");
                sqlParams.add(getSysUser().getUserName());
                sqlParams.add(new Date());
            }

            // 当状态为已完成(2)且有反馈内容时，记录处理反馈
            if ("2".equals(status) && StringUtils.isNotEmpty(feedback)) {
                sql.append(", feedback = ?");
                sqlParams.add(feedback);
            }

            sql.append(" where id = ?");
            sqlParams.add(id);

            Db.update(sql.toString(), sqlParams.toArray());

            // 发送消息通知
            sendRepairStatusMessage(id, status, feedback);
        }
        return success();
    }

    /**
     * 发送报修状态更新消息
     */
    private void sendRepairStatusMessage(String repairId, String status, String feedback) {
        try {
            // 查询报修记录
            Record repairRecord = Db.findFirst("SELECT * FROM eh_wx_bx WHERE id = ?", repairId);
            if (repairRecord == null) {
                return;
            }

            // 查询用户信息
            String wxUserId = repairRecord.getStr("wx_user_id");
            Record userRecord = Db.findFirst("SELECT openid FROM eh_wx_user WHERE user_id = ?", wxUserId);
            if (userRecord == null || StringUtils.isEmpty(userRecord.getStr("openid"))) {
                return;
            }

            String openId = userRecord.getStr("openid");
            String statusText = getStatusText(status);

            // 构建消息内容
            String location = repairRecord.getStr("address") != null ? repairRecord.getStr("address") : "未指定位置";
            String reporter = repairRecord.getStr("name") != null ? repairRecord.getStr("name") : "业主";
            String phone = repairRecord.getStr("phone") != null ? repairRecord.getStr("phone") : "";
            String description = StringUtils.isNotEmpty(feedback) ? feedback : statusText;
            // 使用实际的报修时间，而不是当前时间
            String reportTime = repairRecord.getDate("create_time") != null ?
                DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, repairRecord.getDate("create_time")) :
                DateUtils.dateTimeNow();

            // 发送消息
            boolean success = wxMessageService.sendRepairNotice(
                openId, location, reporter, phone, description, reportTime, repairId
            );

            if (success) {
                logger.info("报修状态更新消息发送成功: repairId={}, status={}", repairId, status);
            } else {
                logger.warn("报修状态更新消息发送失败: repairId={}, status={}", repairId, status);
            }

        } catch (Exception e) {
            logger.error("发送报修状态更新消息异常: repairId={}, error={}", repairId, e.getMessage(), e);
        }
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(String status) {
        switch (status) {
            case "0":
                return "待处理";
            case "1":
                return "处理中";
            case "2":
                return "已完成";
            default:
                return "未知状态";
        }
    }

}

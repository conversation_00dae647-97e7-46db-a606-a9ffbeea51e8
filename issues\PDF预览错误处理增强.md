# PDF预览错误处理增强

## 问题背景

用户反馈PDF文件预览失败，从日志分析发现：
1. 文件信息获取成功
2. 下载URL获取成功  
3. 但在 `wx.downloadFile` 或 `wx.openDocument` 阶段失败
4. 缺少详细的错误信息和用户友好的提示

## 解决方案

### 1. 增强 `downloadAndPreviewDocument` 函数

**文件**: `miniprogram/utils/fileAccessManager.js`

**主要改进**:

#### 1.1 详细的日志记录
```javascript
// 添加时间戳和详细的执行步骤日志
console.log('开始下载并预览文档:', { url, fileName, timestamp: new Date().toISOString() })
console.log('文件域名:', domain)
console.log('开始下载文件...')
console.log(`文件下载成功，耗时: ${downloadTime}ms, 临时文件路径:`, downloadRes.tempFilePath)
```

#### 1.2 URL和域名验证
```javascript
// 验证URL格式
if (!url || typeof url !== 'string') {
  throw new Error('文件URL无效')
}

// 检查域名是否可能存在校验问题
const domain = this.extractDomain(url)
const isDevEnv = __wxConfig && __wxConfig.envVersion === 'develop'
if (isDevEnv && this.isLocalDomain(url)) {
  console.warn('⚠️ 开发环境提示: 检测到本地域名，请确保已在开发工具中勾选"不校验合法域名"')
}
```

#### 1.3 智能错误分析
```javascript
// 根据错误信息提供具体的解决建议
if (errMsg.includes('file not exist') || errMsg.includes('文件不存在')) {
  userFriendlyMessage = '文件不存在或已被删除'
} else if (errMsg.includes('file format') || errMsg.includes('格式')) {
  userFriendlyMessage = 'PDF文件格式不支持或文件已损坏'
} else if (errMsg.includes('permission') || errMsg.includes('权限')) {
  userFriendlyMessage = '没有权限访问该文件'
} else if (errMsg.includes('network') || errMsg.includes('网络')) {
  userFriendlyMessage = '网络连接异常，请检查网络后重试'
}
```

#### 1.4 用户友好的错误提示
```javascript
// 显示详细错误信息并提供重试选项
wx.showModal({
  title: '预览失败',
  content: `${userFriendlyMessage}\n\n技术信息: ${errMsg}`,
  showCancel: true,
  cancelText: '取消',
  confirmText: '重试',
  success: (modalRes) => {
    if (modalRes.confirm) {
      // 用户选择重试
      this.downloadAndPreviewDocument(url, fileName)
        .then(resolve)
        .catch(reject)
    }
  }
})
```

### 2. 新增辅助方法

#### 2.1 域名提取方法
```javascript
extractDomain(url) {
  // 处理相对路径
  if (url.startsWith('/')) {
    return 'localhost'
  }
  
  // 提取域名
  const match = url.match(/^https?:\/\/([^\/]+)/)
  return match ? match[1] : 'unknown'
}
```

#### 2.2 本地域名检测
```javascript
isLocalDomain(url) {
  const localPatterns = [
    'localhost', '127.0.0.1', '192.168.', '10.0.', '172.16.' // 等
  ]
  return localPatterns.some(pattern => url.includes(pattern))
}
```

## 功能特点

### 1. 详细的错误诊断
- **时间统计**: 记录下载和预览的耗时
- **阶段识别**: 区分下载失败和预览失败
- **错误分类**: 根据错误信息自动分类问题类型

### 2. 开发友好
- **开发环境提示**: 自动检测本地域名并提供配置建议
- **详细日志**: 便于开发者调试问题
- **技术信息**: 在错误提示中包含原始错误信息

### 3. 用户友好
- **中文提示**: 将技术错误转换为用户易懂的描述
- **重试机制**: 提供一键重试功能
- **解决建议**: 针对不同错误类型提供具体建议

## 常见问题解决

### 1. 域名校验错误
**现象**: 下载失败，提示域名校验错误
**解决**: 
- 开发环境：勾选"不校验合法域名"
- 生产环境：在小程序管理后台添加合法域名

### 2. 文件格式不支持
**现象**: 下载成功但预览失败，提示格式错误
**解决**: 
- 检查PDF文件是否损坏
- 确认文件确实是PDF格式
- 尝试重新生成PDF文件

### 3. 网络连接问题
**现象**: 下载超时或网络错误
**解决**: 
- 检查网络连接
- 确认OSS服务可用性
- 检查URL是否过期

## 使用方式

现有代码无需修改，直接享受增强的错误处理：

```javascript
// 正常调用，现在会有更详细的错误信息
await fileAccessManager.previewFile(fileId, fallbackUrl, fileName)

// 或者直接预览文档
await fileAccessManager.downloadAndPreviewDocument(url, fileName)
```

## 向后兼容

- ✅ 保持原有API接口不变
- ✅ 现有调用代码无需修改
- ✅ 只是增强了错误处理和日志记录
- ✅ 不影响正常的预览流程

## 使用建议

1. **开发阶段**: 关注控制台日志，根据提示配置域名
2. **测试阶段**: 验证各种错误场景的用户体验
3. **生产环境**: 确保所有域名已正确配置

## 总结

本次增强主要解决了PDF预览失败时缺少详细错误信息的问题：

- ✅ **详细诊断**: 精确定位失败原因和阶段
- ✅ **智能提示**: 根据错误类型提供针对性建议  
- ✅ **用户体验**: 友好的错误提示和重试机制
- ✅ **开发体验**: 丰富的日志和开发环境提示

通过这些改进，开发者可以快速定位PDF预览问题，用户也能获得更好的错误反馈体验。

-- 分享统计汇总功能
-- 用于维护 eh_wx_share_statistics 表的数据

-- 1. 创建存储过程：更新分享统计数据
DELIMITER $$

CREATE PROCEDURE UpdateShareStatistics()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE share_id VARCHAR(32);
    DECLARE total_visits INT DEFAULT 0;
    DECLARE unique_visitors INT DEFAULT 0;
    DECLARE new_users INT DEFAULT 0;
    DECLARE total_stay_duration INT DEFAULT 0;
    DECLARE avg_stay_duration DECIMAL(10,2) DEFAULT 0.00;
    DECLARE last_visit_time DATETIME DEFAULT NULL;
    
    -- 声明游标
    DECLARE share_cursor CURSOR FOR 
        SELECT DISTINCT share_record_id 
        FROM eh_wx_share_visit 
        WHERE share_record_id NOT LIKE 'unknown_%';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始处理
    OPEN share_cursor;
    
    read_loop: LOOP
        FETCH share_cursor INTO share_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 计算统计数据
        SELECT 
            COUNT(*) as visits,
            COUNT(DISTINCT visitor_openid) as visitors,
            SUM(is_new_user) as new_user_count,
            SUM(stay_duration) as total_duration,
            MAX(visit_time) as last_visit
        INTO total_visits, unique_visitors, new_users, total_stay_duration, last_visit_time
        FROM eh_wx_share_visit 
        WHERE share_record_id = share_id;
        
        -- 计算平均停留时长
        IF total_visits > 0 THEN
            SET avg_stay_duration = total_stay_duration / total_visits;
        ELSE
            SET avg_stay_duration = 0.00;
        END IF;
        
        -- 插入或更新统计记录
        INSERT INTO eh_wx_share_statistics (
            id, share_record_id, total_visits, unique_visitors, new_users,
            total_stay_duration, avg_stay_duration, last_visit_time, update_time
        ) VALUES (
            UUID(), share_id, total_visits, unique_visitors, new_users,
            total_stay_duration, avg_stay_duration, last_visit_time, NOW()
        ) ON DUPLICATE KEY UPDATE
            total_visits = VALUES(total_visits),
            unique_visitors = VALUES(unique_visitors),
            new_users = VALUES(new_users),
            total_stay_duration = VALUES(total_stay_duration),
            avg_stay_duration = VALUES(avg_stay_duration),
            last_visit_time = VALUES(last_visit_time),
            update_time = NOW();
            
    END LOOP;
    
    CLOSE share_cursor;
    
END$$

DELIMITER ;

-- 2. 创建触发器：当有新的访问记录时自动更新统计
DELIMITER $$

CREATE TRIGGER tr_share_visit_insert 
AFTER INSERT ON eh_wx_share_visit
FOR EACH ROW
BEGIN
    -- 只处理正常的分享记录ID（不是unknown_开头的）
    IF NEW.share_record_id NOT LIKE 'unknown_%' THEN
        -- 计算统计数据
        INSERT INTO eh_wx_share_statistics (
            id, share_record_id, total_visits, unique_visitors, new_users,
            total_stay_duration, avg_stay_duration, last_visit_time, update_time
        )
        SELECT 
            UUID(),
            NEW.share_record_id,
            COUNT(*),
            COUNT(DISTINCT visitor_openid),
            SUM(is_new_user),
            SUM(stay_duration),
            AVG(stay_duration),
            MAX(visit_time),
            NOW()
        FROM eh_wx_share_visit 
        WHERE share_record_id = NEW.share_record_id
        ON DUPLICATE KEY UPDATE
            total_visits = (
                SELECT COUNT(*) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            unique_visitors = (
                SELECT COUNT(DISTINCT visitor_openid) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            new_users = (
                SELECT SUM(is_new_user) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            total_stay_duration = (
                SELECT SUM(stay_duration) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            avg_stay_duration = (
                SELECT AVG(stay_duration) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            last_visit_time = (
                SELECT MAX(visit_time) FROM eh_wx_share_visit 
                WHERE share_record_id = NEW.share_record_id
            ),
            update_time = NOW();
    END IF;
END$$

DELIMITER ;

-- 3. 手动执行一次统计更新（处理历史数据）
CALL UpdateShareStatistics();

-- 4. 查询统计结果
SELECT 
    s.share_record_id,
    r.nickname as sharer_name,
    r.share_title,
    r.create_time as share_time,
    s.total_visits,
    s.unique_visitors,
    s.new_users,
    s.avg_stay_duration,
    s.last_visit_time
FROM eh_wx_share_statistics s
LEFT JOIN eh_wx_share_record r ON s.share_record_id = r.id
ORDER BY s.total_visits DESC;

-- 5. 清理脚本（如果需要重新统计）
-- DELETE FROM eh_wx_share_statistics;
-- CALL UpdateShareStatistics();

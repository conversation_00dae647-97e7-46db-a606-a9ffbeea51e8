<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('通知公告设置')" />
	<th:block th:include="include :: bootstrap-fileinput-css" />
    <style>
        /* 页面整体背景 */
        body {
            background-color: #f8f8f8 !important;
        }

        /* 主容器样式 */
        .wrapper.wrapper-content {
            background-color: #f8f8f8;
            padding: 0px;
            padding-bottom: 100px; /* 为固定按钮留出空间 */
        }

        /* 卡片容器 */
        .content-card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        /* 卡片标题 */
        .card-header {
            background-color: #fff;
            padding: 20px 25px 15px;
            border-bottom: 1px solid #e7eaec;
        }

        .card-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        /* 卡片内容 */
        .card-body {
            padding: 25px;
        }

        /* 固定底部按钮 */
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: #fff;
            padding: 15px;
            border-top: 1px solid #e7eaec;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .fixed-bottom-buttons .btn {
            margin: 0 8px;
            padding: 8px 20px;
        }

        /* 通知类型项样式 */
        .notice-type-item {
            border: 1px solid #e7eaec;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            background-color: #fafafa;
            transition: all 0.3s ease;
        }

        .notice-type-item:hover {
            border-color: #d0d0d0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .notice-type-item .form-group {
            margin-bottom: 15px;
        }

        /* 通知类型项内的标签样式 */
        .notice-type-item .control-label {
            font-weight: 500;
            color: #555;
            margin-bottom: 0;
        }

        /* inline表单样式 */
        .notice-type-item .form-inline {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }

        .notice-type-item .form-inline .control-label {
            white-space: nowrap;
            margin-right: 10px;
            margin-bottom: 0;
        }

        .notice-type-item .form-inline .form-control {
            flex: 1;
        }

        .notice-type-item:last-child {
            margin-bottom: 20px;
        }

        .remove-type-btn {
            float: right;
            margin-top: -5px;
        }

        /* 卡片头部按钮样式 */
        .card-header .btn-sm {
            padding: 4px 12px;
            font-size: 12px;
            line-height: 1.5;
        }

        .drag-handle {
            cursor: move;
            color: #999;
            margin-right: 10px;
        }

        .type-header {
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 12px;
            margin-bottom: 18px;
        }

        /* 表单样式优化 */
        .form-control {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            height: auto;
            min-height: 34px;
            line-height: 1.42857143;
        }

        .form-control:focus {
            border-color: #5cb85c;
            box-shadow: 0 0 0 2px rgba(92, 184, 92, 0.2);
        }

        /* 下拉框特殊样式 */
        select.form-control {
            height: 34px;
            padding: 6px 12px;
            line-height: 1.42857143;
        }

        /* 确保下拉框选项文本完整显示 */
        select.form-control option {
            padding: 4px 8px;
            line-height: normal;
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 4px;
            font-weight: 500;
        }

        /* 修复可能的样式冲突 */
        .form-group label {
            font-weight: normal;
            margin-bottom: 5px;
        }

        /* 确保表单控件有足够的空间 */
        .form-horizontal .form-group {
            margin-left: 0;
            margin-right: 0;
        }

        /* 小提示文本样式 */
        .form-text.text-muted {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <form class="form-horizontal m" id="form-maintain" name="form-maintain">
            <input name="base.oc_id" th:value="${ocId}" type="hidden">
            <input name="oc_id" th:value="${ocId}" type="hidden">

            <!-- 通知公告类型设置 -->
            <div class="content-card">
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h4>通知公告类型设置</h4>
                        <button type="button" class="btn btn-success btn-sm" onclick="addNoticeType()">
                            <i class="fa fa-plus"></i> 添加类型
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="notice-type-tips alert alert-info" style="margin-bottom: 15px; padding: 10px 15px; border-radius: 4px; background-color: #d9edf7; border: 1px solid #bce8f1; color: #31708f;">
                                <i class="fa fa-info-circle" style="margin-right: 5px;"></i>
                                <strong>参考类型：</strong>通知公告、小区新闻、社区活动、物业服务、民生关注、邻里互动、业主风采、志愿服务、生活指南、应急提醒、维修播报、节日祝福、政策解读、楼栋信息、环境卫生、文体娱乐、儿童乐园、健康关怀、财务公开、议事纪要
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <div id="notice-types-container">
                                <!-- 通知类型项将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知公告功能设置 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>通知公告功能设置</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">禁止转发：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_disable_forward" class="form-control">
                                        <option value="0">允许转发</option>
                                        <option value="1">禁止转发</option>
                                    </select>
                                    <small class="form-text text-muted">禁止用户转发通知公告到其他平台</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">公布已读人员：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_show_readers" class="form-control">
                                        <option value="0">不公布</option>
                                        <option value="1">公布已读人员</option>
                                    </select>
                                    <small class="form-text text-muted">在通知详情页显示已读人员列表</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">默认署名：</label>
                                <div class="col-sm-8">
                                    <input name="ext.notice_default_signature" class="form-control" type="text" placeholder="物业服务中心">
                                    <small class="form-text text-muted">发布通知时的默认署名</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">强制署名：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_force_signature" class="form-control">
                                        <option value="0">可选署名</option>
                                        <option value="1">强制署名</option>
                                    </select>
                                    <small class="form-text text-muted">发布通知时必须填写署名</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知公告显示设置 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>通知公告显示设置</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">显示阅读数：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_show_read_count" class="form-control">
                                        <option value="1">显示</option>
                                        <option value="0">不显示</option>
                                    </select>
                                    <small class="form-text text-muted">在通知列表和详情页显示阅读数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">显示评论数：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_show_comment_count" class="form-control">
                                        <option value="1">显示</option>
                                        <option value="0">不显示</option>
                                    </select>
                                    <small class="form-text text-muted">在通知列表和详情页显示评论数</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">全局评论开关：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_comment" class="form-control">
                                        <option value="1">开启</option>
                                        <option value="0">关闭</option>
                                    </select>
                                    <small class="form-text text-muted">关闭后所有公告都不能评论，开启后根据单个公告设置决定</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">允许点赞：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_allow_like" class="form-control">
                                        <option value="1">允许</option>
                                        <option value="0">禁止</option>
                                    </select>
                                    <small class="form-text text-muted">是否允许用户对通知进行点赞</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知公告推送设置 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>通知公告推送设置</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">启用微信推送：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_enable_wechat_push" class="form-control">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                    <small class="form-text text-muted">发布通知时自动推送微信消息</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">推送范围：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_push_scope" class="form-control">
                                        <option value="all">全部用户</option>
                                        <option value="subscribed">已订阅用户</option>
                                    </select>
                                    <small class="form-text text-muted">选择推送消息的用户范围</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">自动置顶重要通知：</label>
                                <div class="col-sm-8">
                                    <select name="ext.notice_auto_top_important" class="form-control">
                                        <option value="0">不自动置顶</option>
                                        <option value="1">自动置顶</option>
                                    </select>
                                    <small class="form-text text-muted">重要通知自动置顶显示</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">通知过期天数：</label>
                                <div class="col-sm-8">
                                    <input name="ext.notice_expire_days" class="form-control" type="number" placeholder="30" min="1" max="365">
                                    <small class="form-text text-muted">通知发布后多少天自动过期（0表示不过期）</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <!-- 固定在底部的提交按钮 -->
    <div class="fixed-bottom-buttons">
        <button type="button" class="btn btn-primary" onclick="submitHandler()">
            <i class="fa fa-save"></i> 提交保存
        </button>
        <button type="button" class="btn btn-default" onclick="closeItem()">
            <i class="fa fa-reply-all"></i> 关闭
        </button>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script th:src="@{/js/jquery-ui-1.10.4.min.js}"></script>

    <!-- 通知类型项模板 -->
    <script id="notice-type-template" type="text/x-jsrender">
        <div class="notice-type-item" data-index="{{:index}}">
            <div class="type-header">
                <i class="fa fa-bars drag-handle"></i>
                <strong>通知类型 #{{:displayIndex}}</strong>
                <button type="button" class="btn btn-danger btn-xs remove-type-btn">
                    <i class="fa fa-trash"></i> 删除
                </button>
            </div>
            <div class="row">
                <div class="col-sm-8">
                    <div class="form-group form-inline">
                        <label class="control-label" style="margin-right: 10px;">类型名称：</label>
                        <input type="text" class="form-control type-label" value="{{:label}}" placeholder="如：通知" maxlength="6" style="width: calc(100% - 80px);" onblur="validateTypeLabel(this)">
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group form-inline">
                        <label class="control-label" style="margin-right: 10px;">排序：</label>
                        <input type="number" class="form-control type-sort" value="{{:sort}}" min="1" style="width: calc(100% - 50px);">
                    </div>
                </div>
            </div>
        </div>
    </script>
    <script type="text/javascript">
	    var prefix = ctx + "oc/info";
	
	    $("#form-maintain").validate({
	    	onkeyup: false,
	        rules: {},
	        messages: {},
	        focusCleanup: true
	    });

        $(function(){
            // 初始化通知类型数据
            initNoticeTypes();

            // 只加载扩展字段数据，不加载基础字段
            $.post(prefix + '/record', {oc_id: $("input[name='oc_id']").val()}, function(rs){
                if(rs.code === 0 && rs.data && rs.data.ext_json){
                    var extJson = JSON.parse(rs.data.ext_json);
                    fillRecord(extJson, 'ext.','');

                    // 处理通知类型数据回填
                    if(extJson.notice_types){
                        // 检查是否为字符串，如果是则解析JSON
                        var noticeTypes = extJson.notice_types;
                        if(typeof noticeTypes === 'string'){
                            try {
                                noticeTypes = JSON.parse(noticeTypes);
                            } catch(e) {
                                console.error('解析notice_types失败:', e);
                                noticeTypes = [];
                            }
                        }
                        loadNoticeTypes(noticeTypes);
                    } else {
                        // 如果没有自定义类型，加载系统默认类型
                        loadDefaultNoticeTypes();
                    }
                }
            });
        });

        // 初始化通知类型数据
        function initNoticeTypes() {
            // 初始化拖拽排序
            $("#notice-types-container").sortable({
                handle: '.drag-handle',
                update: function(event, ui) {
                    updateSortOrder();
                }
            });

            // 使用事件委托绑定删除按钮点击事件
            $('#notice-types-container').on('click', '.remove-type-btn', function() {
                removeNoticeType(this);
            });
        }

        // 加载默认通知类型
        function loadDefaultNoticeTypes() {
            // 清空现有的通知类型项
            $('#notice-types-container').empty();

            // 添加默认的通知类型
            var defaultTypes = [
                {label: '通知公告', sort: 1},
                {label: '社区播报', sort: 2},
                {label: '活动广场', sort: 3}
            ];

            for(var i = 0; i < defaultTypes.length; i++){
                addNoticeTypeItem(defaultTypes[i].label, defaultTypes[i].sort);
            }
        }

        // 加载自定义通知类型
        function loadNoticeTypes(types) {
            // 清空现有的通知类型项
            $('#notice-types-container').empty();

            // 确保types是数组
            if(!Array.isArray(types)){
                console.warn('通知类型数据不是数组格式:', types);
                loadDefaultNoticeTypes();
                return;
            }

            if(types && types.length > 0){
                for(var i = 0; i < types.length; i++){
                    var type = types[i];
                    if(type && typeof type === 'object' && type.label){
                        addNoticeTypeItem(type.label, type.sort || (i + 1));
                    }
                }
            } else {
                loadDefaultNoticeTypes();
            }
        }

        // 添加通知类型
        function addNoticeType() {
            var maxSort = 0;
            $('.notice-type-item').each(function(){
                var sort = parseInt($(this).find('.type-sort').val()) || 0;
                if(sort > maxSort) maxSort = sort;
            });
            addNoticeTypeItem('', maxSort + 1);
        }

        // 添加通知类型项
        function addNoticeTypeItem(label, sort) {
            var index = $('.notice-type-item').length;
            var template = $.templates("#notice-type-template");

            var data = {
                index: index,
                displayIndex: index + 1,
                label: label || '',
                sort: sort || (index + 1)
            };

            var html = template.render(data);
            $('#notice-types-container').append(html);
        }

        // 更新类型值（与类型名称保持一致）
        function updateTypeValue(input) {
            // 类型值和类型名称保持一致，无需额外处理
        }

        // 验证类型名称长度
        function validateTypeLabel(input) {
            var value = $(input).val().trim();
            if (value.length < 2) {
                layer.msg('类型名称至少需要2个字符');
                $(input).focus();
                return false;
            }
            if (value.length > 6) {
                layer.msg('类型名称不能超过6个字符');
                $(input).val(value.substring(0, 6));
                return false;
            }
            return true;
        }

        // 删除通知类型
        function removeNoticeType(btn) {
            // 检查是否至少保留一个类型
            if($('.notice-type-item').length <= 1) {
                layer.msg('至少需要保留一个通知类型');
                return;
            }

            // 确认删除
            layer.confirm('确定要删除这个通知类型吗？', {
                icon: 3,
                title: '确认删除'
            }, function(index) {
                $(btn).closest('.notice-type-item').remove();
                updateIndexes();
                layer.close(index);
                layer.msg('删除成功');
            });
        }

        // 更新索引
        function updateIndexes() {
            $('.notice-type-item').each(function(index){
                $(this).attr('data-index', index);
                $(this).find('.type-header strong').text('通知类型 #' + (index + 1));
            });
        }

        // 更新排序
        function updateSortOrder() {
            $('.notice-type-item').each(function(index){
                $(this).find('.type-sort').val(index + 1);
            });
            updateIndexes();
        }

        function submitHandler() {
            if ($.validate.form()) {
                // 收集通知类型数据
                var noticeTypes = [];
                var hasError = false;
                $('.notice-type-item').each(function(){
                    var label = $(this).find('.type-label').val();
                    var sort = $(this).find('.type-sort').val();

                    if(label && label.trim()){
                        var trimmedLabel = label.trim();
                        // 验证类型名称长度
                        if (trimmedLabel.length < 2) {
                            layer.msg('类型名称至少需要2个字符');
                            $(this).find('.type-label').focus();
                            hasError = true;
                            return false;
                        }
                        if (trimmedLabel.length > 6) {
                            layer.msg('类型名称不能超过6个字符');
                            $(this).find('.type-label').focus();
                            hasError = true;
                            return false;
                        }

                        noticeTypes.push({
                            value: trimmedLabel,  // 类型值和类型名称一致
                            label: trimmedLabel,
                            sort: parseInt(sort) || 1
                        });
                    }
                });

                // 如果有验证错误，停止提交
                if (hasError) {
                    return;
                }

                // 移除之前可能添加的隐藏字段，避免重复
                $('input[name="ext.notice_types"]').remove();

                // 添加通知类型数据到表单
                $('<input>').attr({
                    type: 'hidden',
                    name: 'ext.notice_types',
                    value: JSON.stringify(noticeTypes)
                }).appendTo('#form-maintain');

                $.operate.save(prefix + "/maintainSave", $('#form-maintain').serialize(),function(){
                    layer.msg('保存成功');
                });
            }
        }

        function closeItem() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>
</html>

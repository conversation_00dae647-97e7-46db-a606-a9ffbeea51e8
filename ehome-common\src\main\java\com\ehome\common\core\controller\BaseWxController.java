package com.ehome.common.core.controller;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 微信小程序接口通用数据处理
 */
public class BaseWxController extends BaseController {

    protected static final Logger logger = LoggerFactory.getLogger("weixin");

    protected static ThreadLocal<LoginUser> currentUser = new ThreadLocal<>();
    
    @ModelAttribute
    public void initUser(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 先清理ThreadLocal
            currentUser.remove();
            
            // 从请求头获取token
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                // 解析token获取用户信息
                LoginUser loginUser = SecurityUtils.getLoginUser(token);
                if (loginUser != null) {
                    currentUser.set(loginUser);
                    logger.debug("Set current user: {},mobile:{}", loginUser.getUserId(),loginUser.getMobile());
                }else{
                    logger.error("Invalid token: {}", token);
                    currentUser.remove();
                }
            }
        }catch(ServiceException ex){
            logger.error("Invalid token :" + ex.getMessage(), ex);
        } catch (Exception e) {
            logger.error("Init user failed: " + e.getMessage(), e);
            currentUser.remove();
        }
    }
    
    /**
     * 获取当前登录用户ID
     */
    protected Long getCurrentUserId() {
        LoginUser loginUser = currentUser.get();
        return loginUser != null ? loginUser.getUserId() : null;
    }

    protected String getCurrentOpenId() {
        LoginUser loginUser = currentUser.get();
        return loginUser != null ? loginUser.getOpenId() : null;
    }
    
    /**
     * 获取当前登录用户信息
     */
    protected LoginUser getCurrentUser() {
        return currentUser.get();
    }

    /**
     * 外部安全更新当前线程中的LoginUser手机号
     */
    public static void setCurrentUserMobile(String mobile) {
        LoginUser loginUser = currentUser.get();
        if (loginUser != null) {
            loginUser.setMobile(mobile);
        }
    }
} 
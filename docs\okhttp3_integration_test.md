# OkHttp3集成测试文档

## 概述

本文档描述了在MarkicamSyncService中集成OkHttp3的测试方法和验证步骤。

## 集成内容

### 1. 依赖添加
在`ehome-oc/pom.xml`中添加了OkHttp3依赖：
```xml
<dependency>
    <groupId>com.squareup.okhttp3</groupId>
    <artifactId>okhttp</artifactId>
    <version>4.12.0</version>
</dependency>
```

### 2. 代码修改
- 移除了对`HttpUtils`的依赖
- 添加了`OkHttpClient`实例
- 重写了`callMarkicamAPI`方法使用OkHttp3
- 支持自定义header和JSON请求体

### 3. 主要改进
- **支持自定义Header**: 可以设置Markicam API要求的签名header
- **更好的错误处理**: 区分网络异常和其他异常
- **连接池管理**: OkHttp3自动管理连接池
- **超时配置**: 可配置连接、读取、写入超时

## 测试方法

### 1. 编译测试
```bash
cd ehome-oc
mvn clean compile
```

### 2. 功能测试
1. 启动应用
2. 访问Markicam配置页面
3. 配置API信息（组织ID、API密钥等）
4. 点击"测试连接"按钮
5. 查看日志输出

### 3. API调用测试
```java
// 示例：测试照片视频API
JSONObject params = new JSONObject();
params.put("start", "2025-01-01 00:00:00");
params.put("end", "2025-01-08 23:59:59");

JSONObject response = markicamSyncService.callMarkicamAPI("/marki/moment", params, config);
```

## 验证要点

### 1. Header设置验证
确保以下header正确设置：
- `Content-Type: application/json`
- `orgId`: 组织ID
- `timestamp`: 时间戳
- `traceId`: 跟踪ID
- `sign`: MD5签名

### 2. 签名验证
验证签名生成逻辑：
```
md5(orgId=12345&key=apikey&timestamp=1641234567&traceId=trace123&data={"param":"value"})
```

### 3. 响应处理验证
- 检查HTTP状态码
- 验证响应体解析
- 确保资源正确释放

## 常见问题

### 1. 依赖冲突
如果出现OkHttp版本冲突，检查其他模块的依赖：
```bash
mvn dependency:tree | grep okhttp
```

### 2. 网络超时
调整超时配置：
```java
.connectTimeout(30, TimeUnit.SECONDS)
.readTimeout(60, TimeUnit.SECONDS)
.writeTimeout(60, TimeUnit.SECONDS)
```

### 3. SSL证书问题
如果遇到SSL问题，可以添加信任所有证书的配置（仅开发环境）。

## 性能优化

### 1. 连接池配置
```java
ConnectionPool connectionPool = new ConnectionPool(5, 5, TimeUnit.MINUTES);
OkHttpClient client = new OkHttpClient.Builder()
    .connectionPool(connectionPool)
    .build();
```

### 2. 请求重试
```java
.retryOnConnectionFailure(true)
```

### 3. 缓存配置
```java
Cache cache = new Cache(cacheDirectory, cacheSize);
.cache(cache)
```

## 日志配置

在`logback.xml`中添加OkHttp日志配置：
```xml
<logger name="okhttp3" level="DEBUG"/>
<logger name="com.ehome.oc.service.MarkicamSyncService" level="DEBUG"/>
```

## 下一步

1. 完成数据库表创建
2. 配置实际的Markicam API信息
3. 测试完整的数据同步流程
4. 添加定时任务支持
5. 完善错误处理和重试机制

package com.ehome.oc.controller.charge.manage;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.constants.ChargeConstants;
import com.ehome.oc.service.ChargeBillService;
import com.ehome.oc.service.ChargeCommonService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@Controller
@RequestMapping("/oc/charge/manage/bill")
public class ChargeBillController extends BaseController {

    @Autowired
    private ChargeBillService chargeBillService;

    @Autowired
    private ChargeCommonService chargeCommonService;

    private static final String PREFIX = "oc/charge/manage/bill";

    /**
     * 收费账单管理页面
     */
    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    /**
     * 收费账单列表查询
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
                params.getIntValue("pageNum"),
                params.getIntValue("pageSize"),
                "select cb.*, cs.name as charge_standard_name",
                sql.toFullSql()
        );

        // 处理列表数据，添加显示字段
        for (Record record : paginate.getList()) {
            processListRecord(record);
        }

        return getDataTable(paginate);
    }

    /**
     * 作废账单
     */
    @Log(title = "作废账单", businessType = BusinessType.UPDATE)
    @PostMapping("/void")
    @ResponseBody
    public AjaxResult voidBill() {
        JSONObject params = getParams();
        Long billId = params.getLong("billId");
        if (billId == null || billId <= 0) {
            return AjaxResult.error("账单ID不能为空");
        }

        try {
            boolean result = chargeBillService.voidBill(billId, getSysUser());
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("作废账单失败：" + e.getMessage());
        }
    }

    /**
     * 手动创建账单页面
     */
    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    /**
     * 编辑账单页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        Record bill = chargeBillService.getBillDetail(id);
        if (bill != null) {
            mmap.put("bill", bill.toMap());
        }
        return PREFIX + "/edit";
    }

    /**
     * 手动创建账单
     */
    @Log(title = "手动创建账单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        try {
            Long billId = chargeBillService.createManualBill(params, getSysUser());
            return AjaxResult.success("账单创建成功", billId);
        } catch (Exception e) {
            return AjaxResult.error("账单创建失败：" + e.getMessage());
        }
    }

    /**
     * 编辑账单
     */
    @Log(title = "编辑账单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        try {
            boolean result = chargeBillService.updateBill(params, getSysUser());
            return toAjax(result);
        } catch (Exception e) {
            return AjaxResult.error("账单更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除账单
     */
    @Log(title = "删除账单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        try {
            String[] idArr = ids.split(",");
            int deleteCount = chargeBillService.deleteBills(idArr, getSysUser());
            return AjaxResult.success("成功删除 " + deleteCount + " 条账单");
        } catch (Exception e) {
            return AjaxResult.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成账单
     */
    @Log(title = "批量生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/batchGenerate")
    @ResponseBody
    public AjaxResult batchGenerate() {
        try {
            JSONObject params = getParams();
            String startMonth = params.getString("startMonth"); // 格式：2025-01
            String endMonth = params.getString("endMonth");     // 格式：2025-06

            // 使用ChargeCommonService进行时间范围验证
            AjaxResult validateResult = chargeCommonService.validateTimeRange(startMonth, endMonth);
            if (validateResult != null) {
                return validateResult;
            }

            String communityId = getSysUser().getCommunityId();

            // 获取启用的收费绑定数量
            int bindingCount = Db.queryInt(
                "select count(*) from eh_charge_binding cb " +
                "left join eh_charge_standard cs on cb.charge_standard_id = cs.id " +
                "where cb.community_id = ? and cb.is_active = 1 and cs.is_active = 1",
                communityId);

            if (bindingCount == 0) {
                return AjaxResult.error("当前没有启用的收费绑定");
            }

            // 执行批量生成
            int generateCount = chargeBillService.batchGenerateBillsByRange(startMonth, endMonth, getSysUser());

            return AjaxResult.success("找到 " + bindingCount + " 个收费绑定，成功生成 " + generateCount + " 条账单");
        } catch (Exception e) {
            return AjaxResult.error("批量生成失败：" + e.getMessage());
        }
    }



    /**
     * 根据收费绑定生成账单
     */
    @Log(title = "生成账单", businessType = BusinessType.INSERT)
    @PostMapping("/generateByBinding")
    @ResponseBody
    public AjaxResult generateByBinding() {
        JSONObject params = getParams();
        Long bindingId = params.getLong("bindingId");

        // 使用ChargeCommonService进行参数验证
        AjaxResult validateResult = chargeCommonService.validateId(bindingId, "收费绑定ID");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            Long billId = chargeBillService.generateBillByBinding(bindingId, getSysUser());
            return chargeCommonService.buildSuccessResponse("账单生成成功", billId);
        } catch (Exception e) {
            return chargeCommonService.buildErrorResponse("账单生成失败", e);
        }
    }

    /**
     * 获取收费标准列表
     */
    @PostMapping("/getChargeStandards")
    @ResponseBody
    public AjaxResult getChargeStandards() {
        try {
            java.util.List<Record> standards = chargeCommonService.getChargeStandards(getSysUser().getCommunityId());
            return chargeCommonService.buildSuccessResponse("获取成功", chargeCommonService.recordToMap(standards));
        } catch (Exception e) {
            return chargeCommonService.buildErrorResponse("获取收费标准列表失败", e);
        }
    }

    /**
     * 获取账单统计信息
     */
    @RequestMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics() {
        JSONObject params = getParams();
        try {
            Map<String, Object> stats = chargeBillService.getBillStatistics(params, getSysUser().getCommunityId());
            return AjaxResult.success(stats);
        } catch (Exception e) {
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 构建查询SQL
     */
    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_charge_bill cb");
        sql.append("left join eh_charge_standard cs on cb.charge_standard_id = cs.id");
        sql.append(getSysUser().getCommunityId(),"where cb.community_id = ?");

        // 资产类型筛选
        Integer assetType = params.getInteger("assetType");
        if (assetType != null && assetType > 0) {
            sql.append(assetType,"and cb.asset_type = ?");
        }

        // 资产名称筛选
        String assetName = params.getString("assetName");
        sql.appendLike(assetName, "and cb.asset_name like ?");

        // 收费项目名称筛选
        String chargeItemName = params.getString("chargeItemName");
        sql.appendLike(chargeItemName, "and cb.charge_item_name like ?");

        // 收费标准筛选
        Long chargeStandardId = params.getLong("chargeStandardId");
        if (chargeStandardId != null && chargeStandardId > 0) {
            sql.append(chargeStandardId, "and cb.charge_standard_id = ?");
        }

        // 支付状态筛选
        Integer payStatus = params.getInteger("payStatus");
        if (payStatus != null) {
            sql.append(payStatus,"and cb.pay_status = ?");
        }

        // 账单类型筛选
        Integer billType = params.getInteger("billType");
        if (billType != null && billType > 0) {
            sql.append(billType,"and cb.bill_type = ?");
        }

        // 账期范围筛选
        String startMonth = params.getString("startMonth");
        String endMonth = params.getString("endMonth");
        if (StringUtils.isNotEmpty(startMonth) && StringUtils.isNotEmpty(endMonth)) {
            sql.append(startMonth,"and cb.in_month >= ? ");
            sql.append(endMonth,"and cb.in_month <= ?");
        } else if (StringUtils.isNotEmpty(startMonth)) {
            sql.append(startMonth,"and cb.in_month >= ?");
        } else if (StringUtils.isNotEmpty(endMonth)) {
            sql.append(endMonth,"and cb.in_month <= ?");
        }

        // 兼容原有的单个账期筛选
        String inMonth = params.getString("inMonth");
        if (StringUtils.isNotEmpty(inMonth)) {
            sql.append("and cb.in_month = ?", inMonth);
        }

        // 时间范围筛选
        String beginTime = params.getString("beginTime");
        if (StringUtils.isNotEmpty(beginTime)) {
            sql.append(beginTime,"and cb.create_time >= ?");
        }
        String endTime = params.getString("endTime");
        if (StringUtils.isNotEmpty(endTime)) {
            sql.append(endTime,"and cb.create_time <= ?");
        }
        sql.append("order by cb.start_time desc,cb.create_time desc");
        return sql;
    }

    /**
     * 处理列表记录，添加显示字段
     */
    private void processListRecord(Record record) {
        // 处理资产类型显示
        Integer assetType = record.getInt("asset_type");
        record.set("asset_type_str", assetType != null ? ChargeConstants.getAssetTypeName(assetType) : "未知");

        // 处理支付状态显示
        Integer payStatus = record.getInt("pay_status");
        Integer isBadBill = record.getInt("is_bad_bill");

        // 支付状态优先显示作废状态
        if (isBadBill != null && isBadBill == 1) {
            record.set("pay_status_str", "已作废");
        } else {
            record.set("pay_status_str", payStatus != null ? ChargeConstants.getPayStatusName(payStatus) : "未缴");
        }

        // 处理账单类型显示
        Integer billType = record.getInt("bill_type");
        record.set("bill_type_str", billType != null ? ChargeConstants.getBillTypeName(billType) : "系统生成");

        // 处理支付方式显示
        Integer payType = record.getInt("pay_type");
        record.set("pay_type_str", payType != null ? ChargeConstants.getPayTypeName(payType) : "");

        // 处理收费类型显示
        Integer chargeItemType = record.getInt("charge_item_type");
        record.set("charge_item_type_str", chargeItemType != null ? ChargeConstants.getChargeTypeName(chargeItemType) : "周期性收费");

        // 处理时间显示（现在是yyyyMMdd格式的字符串）
        String createTime = record.getStr("create_time");
        record.set("create_time_str", StringUtils.isNotEmpty(createTime) ? createTime : "");

        String payTime = record.getStr("pay_time");
        record.set("pay_time_str", StringUtils.isNotEmpty(payTime) ? payTime : "");

        // 处理金额显示（现在直接是decimal格式的元）
        BigDecimal amount = record.getBigDecimal("amount");
        record.set("amount_yuan", amount != null ? amount.doubleValue() : 0.0);

        BigDecimal billAmount = record.getBigDecimal("bill_amount");
        record.set("bill_amount_yuan", billAmount != null ? billAmount.doubleValue() : 0.0);

        BigDecimal discountAmount = record.getBigDecimal("discount_amount");
        record.set("discount_amount_yuan", discountAmount != null ? discountAmount.doubleValue() : 0.0);

        BigDecimal lateMoneyAmount = record.getBigDecimal("late_money_amount");
        record.set("late_money_amount_yuan", lateMoneyAmount != null ? lateMoneyAmount.doubleValue() : 0.0);

        String ownerInfoStr = chargeBillService.getAssetUsersString(record.getInt("asset_type"), record.getLong("asset_id"));
        record.set("ownerInfoStr", ownerInfoStr);
    }

    /**
     * 批量收款
     */
    @Log(title = "批量收款", businessType = BusinessType.UPDATE)
    @PostMapping("/batchPayment")
    @ResponseBody
    public AjaxResult batchPayment() {
        JSONObject params = getParams();
        String billIds = params.getString("billIds");
        Integer paymentType = params.getInteger("paymentType");
        String remark = params.getString("remark");

        if (StringUtils.isEmpty(billIds)) {
            return AjaxResult.error("请选择要收款的账单");
        }

        if (paymentType == null || paymentType <= 0) {
            return AjaxResult.error("请选择支付方式");
        }

        try {
            String[] billIdArray = billIds.split(",");
            int successCount = chargeBillService.batchPayment(billIdArray, paymentType, remark, getSysUser());
            return AjaxResult.success("成功收款 " + successCount + " 条账单");
        } catch (Exception e) {
            return AjaxResult.error("批量收款失败：" + e.getMessage());
        }
    }

    /**
     * 重新计算账单金额
     */
    @Log(title = "重新计算账单金额", businessType = BusinessType.UPDATE)
    @PostMapping("/recalculateAmount")
    @ResponseBody
    public AjaxResult recalculateAmount() {
        JSONObject params = getParams();
        String billIds = params.getString("billIds");

        if (StringUtils.isEmpty(billIds)) {
            return AjaxResult.error("请选择要重新计算的账单");
        }

        try {
            String[] billIdArray = billIds.split(",");
            int successCount = 0;

            for (String billIdStr : billIdArray) {
                try {
                    Long billId = Long.parseLong(billIdStr);
                    chargeBillService.recalculateBillAmount(billId);
                    successCount++;
                } catch (Exception e) {
                    logger.error("重新计算账单金额失败，账单ID：{}，错误：{}", billIdStr, e.getMessage());
                }
            }

            return AjaxResult.success("成功重新计算 " + successCount + " 条账单的金额");
        } catch (Exception e) {
            return AjaxResult.error("重新计算账单金额失败：" + e.getMessage());
        }
    }

}

-- 更新服务电话图标为Vant图标
-- 执行此脚本将现有的服务电话数据更新为使用Vant图标

UPDATE eh_service_tel SET 
icon_url = CASE 
  WHEN service_tel_id = 'ST001' THEN 'phone-o'
  WHEN service_tel_id = 'ST002' THEN 'service-o'
  WHEN service_tel_id = 'ST003' THEN 'phone-circle-o'
  WHEN service_tel_id = 'ST004' THEN 'contact-o'
  WHEN service_tel_id = 'ST005' THEN 'chat-o'
  ELSE icon_url
END
WHERE service_tel_id IN ('ST001', 'ST002', 'ST003', 'ST004', 'ST005');

-- 如果需要为所有空图标设置默认图标，可以执行以下语句
-- UPDATE eh_service_tel SET icon_url = 'phone-o' WHERE icon_url = '' OR icon_url IS NULL;

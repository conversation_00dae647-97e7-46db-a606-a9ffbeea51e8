package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/api/wx/complaint")
public class ComplaintController extends BaseWxController {

    @PostMapping("/addData")
    public AjaxResult addData() {
        try {
            Long wxUserId = getCurrentUserId();
            JSONObject params = getParams();
            String businessId = params.getString("businessId");
            String recordId = Seq.getId("COMPLAINT");

            Record record = new Record();
            record.set("type",params.getString("type"));
            record.set("content",params.getString("content"));
            record.set("address",params.getString("address"));
            record.set("name",params.getString("name"));
            record.set("phone",params.getString("phone"));
            record.set("media_urls",params.getString("media_urls"));
            record.set("id", recordId);
            record.set("community_id", getCurrentUser().getCommunityId());
            record.set("wx_user_id", wxUserId);
            record.set("owner_id", getCurrentUser().getOwnerId());
            record.set("create_time", DateUtils.getTime());
            record.set("update_time", DateUtils.getTime());
            record.set("create_by", getCurrentUser().getMobile());
            Db.save("eh_wx_complaint", "id", record);

            // 更新文件关联
            try {
                // 方式1：如果有业务ID，更新相关文件的business_id
                if (!StringUtils.isEmpty(businessId)) {
                    Db.update("UPDATE eh_file_info SET business_id = ? WHERE business_type = 'complaint' AND business_id = ?", recordId, businessId);
                }

                // 方式2：解析media_urls中的fileId并更新关联
                String mediaUrls = params.getString("media_urls");
                if (!StringUtils.isEmpty(mediaUrls)) {
                    try {
                        com.alibaba.fastjson.JSONArray mediaArray = com.alibaba.fastjson.JSONArray.parseArray(mediaUrls);
                        for (int i = 0; i < mediaArray.size(); i++) {
                            com.alibaba.fastjson.JSONObject mediaObj = mediaArray.getJSONObject(i);
                            String fileId = mediaObj.getString("fileId");
                            if (!StringUtils.isEmpty(fileId)) {
                                Db.update("UPDATE eh_file_info SET business_id = ? WHERE file_id = ? AND business_type = 'complaint'", recordId, fileId);
                            }
                        }
                    } catch (Exception parseError) {
                        logger.warn("解析media_urls失败: " + parseError.getMessage());
                    }
                }
            } catch (Exception fileUpdateError) {
                logger.warn("更新文件业务ID失败: " + fileUpdateError.getMessage());
            }

            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error("新增失败: " + e.getMessage());
        }
    }



    @PostMapping("/history")
    public AjaxResult history() {
        try {
            Long wxUserId = getCurrentUserId();
            List<Record> list = Db.find("select id, type, content, create_time, status, media_urls, feedback, handler, handling_time from eh_wx_complaint where owner_id = ? order by create_time desc", getCurrentUser().getOwnerId());
            List<Map<String, Object>> result = new ArrayList<>();
            for (Record r : list) {
                Map<String, Object> m = new HashMap<>();
                m.put("id", r.get("id"));
                m.put("type", r.get("type"));
                m.put("content", r.get("content"));
                m.put("createTime", r.get("create_time"));
                m.put("status", r.get("status"));
                m.put("media_urls", r.get("media_urls"));
                m.put("reply_content", r.get("feedback")); // 将feedback映射为reply_content保持前端兼容
                m.put("handler", r.get("handler")); // 处理人
                m.put("handling_time", r.get("handling_time")); // 处理时间
                result.add(m);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取历史失败: " + e.getMessage());
        }
    }

    @PostMapping("/detail")
    public AjaxResult detail() {
        try {
            JSONObject params = getParams();
            String id = params.getString("id");
            if (StringUtils.isEmpty(id)) {
                return AjaxResult.error("参数错误");
            }

            // 查询投诉建议详情，确保只能查看自己的记录
            Record record = Db.findFirst("select * from eh_wx_complaint where id = ? and owner_id = ?", id, getCurrentUser().getOwnerId());
            if (record == null) {
                return AjaxResult.error("未找到投诉建议记录");
            }

            return AjaxResult.success(record.toMap());
        } catch (Exception e) {
            logger.error("获取投诉建议详情失败: " + e.getMessage(), e);
            return AjaxResult.error("获取投诉建议详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/getAttachments")
    public AjaxResult getAttachments() {
        try {
            JSONObject params = getParams();
            String businessId = params.getString("businessId");
            if (StringUtils.isEmpty(businessId)) {
                return AjaxResult.error("业务ID不能为空");
            }

            List<Record> attachments = Db.find(
                "SELECT file_id, original_name, access_url, file_size, file_type, create_time " +
                "FROM eh_file_info " +
                "WHERE business_type = 'complaint' AND business_id = ? AND status = '0' " +
                "ORDER BY create_time ASC",
                businessId
            );

            return AjaxResult.success(attachments);
        } catch (Exception e) {
            return AjaxResult.error("获取附件失败: " + e.getMessage());
        }
    }
}

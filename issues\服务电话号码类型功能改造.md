# 服务电话号码类型功能改造

## 项目概述
为服务电话功能新增号码类型分类，支持内部号码和外部号码的区分管理，并重新设计小程序界面为通讯录风格。

## 实施内容

### 1. 数据库结构调整 ✅
- **文件**: `sql/add_tel_type_to_eh_service_tel.sql`
- **修改**: 为`eh_service_tel`表新增`tel_type`字段
  - 字段类型: `varchar(10)`
  - 默认值: `'external'`
  - 取值: `'internal'`(内部号码) / `'external'`(外部号码)
- **索引**: 添加`idx_tel_type`和`idx_community_tel_type`复合索引
- **测试数据**: 添加内部号码示例数据

### 2. 后台功能改造 ✅
#### ServiceTelController改造
- **buildListQuery方法**: 支持按`tel_type`筛选
- **getTopCategories方法**: 支持按号码类型获取分类
- **batchImport方法**: 批量导入时默认设置为外部号码
- **addData/editSave方法**: 自动处理`tel_type`字段

#### WxServiceTelController改造
- **getServiceTelList方法**: 返回按类型分类的数据结构
- **buildServiceTelByType方法**: 新增方法，构建分类数据
- **数据结构**: `{internal: [], external: []}`

### 3. 后台页面标签卡 ✅
#### list.html改造
- **标签卡导航**: 使用Bootstrap tabs组件
- **筛选逻辑**: 根据当前标签卡自动筛选数据
- **导入功能**: 支持按当前类型导入号码
- **子项添加**: 自动继承父级的号码类型

#### add.html改造
- **类型提示**: 显示当前添加的号码类型
- **隐藏字段**: `tel_type`字段自动设置，不显示给用户
- **表单验证**: 保持原有验证逻辑

#### edit.html改造
- **类型显示**: 显示当前编辑记录的号码类型
- **类型锁定**: 编辑时不允许修改号码类型

### 4. 小程序界面重设计 ✅
#### 通讯录风格设计
- **标签卡切换**: 使用Vant van-tabs组件
- **联系人卡片**: 简洁的通讯录风格布局
- **头像设计**: 固定图标+随机颜色背景
- **信息层次**: 姓名 > 公司 > 号码的清晰层次

#### 功能特性
- **一键拨打**: 点击整行或拨打按钮
- **复制功能**: 三点菜单支持复制号码和联系信息
- **响应式设计**: 适配不同屏幕尺寸
- **深色模式**: 支持系统深色模式

## 技术要点

### 数据结构变化
```sql
-- 新增字段
ALTER TABLE eh_service_tel ADD COLUMN tel_type varchar(10) DEFAULT 'external';

-- 数据示例
tel_type: 'internal' | 'external'
```

### API数据格式
```javascript
// 新的返回格式
{
  "internal": [
    {
      "service_tel_id": "xxx",
      "service_name": "物业前台",
      "tel_number": "8001",
      "children": [...]
    }
  ],
  "external": [...]
}
```

### 小程序数据处理
```javascript
// 联系人数据增强
{
  ...contact,
  avatarColor: '#1989fa' // 随机分配的头像颜色
}
```

## 界面效果

### 后台管理
- 顶部标签卡：外部号码 | 内部号码
- 树形表格显示，支持分类管理
- 新增时自动继承当前标签卡类型

### 小程序界面
- 顶部标签卡切换
- 通讯录风格联系人列表
- 左侧彩色头像图标
- 右侧拨打和更多操作按钮
- 支持复制号码和联系信息

## 兼容性说明
- 现有数据默认设置为外部号码
- 保持原有树形结构和分类功能
- API向后兼容，新增字段不影响现有功能

## 部署步骤
1. 执行数据库脚本：`sql/add_tel_type_to_eh_service_tel.sql`
2. 重启后台服务
3. 发布小程序更新

## 测试要点
- [ ] 后台标签卡切换功能
- [ ] 新增/编辑时类型自动设置
- [ ] 小程序标签卡和通讯录界面
- [ ] 拨打电话功能
- [ ] 复制功能
- [ ] 数据迁移验证

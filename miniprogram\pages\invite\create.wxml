<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>

<!-- 邀请住户页面 -->
<view class="page-container">
  <!-- 顶部Banner -->
  <view class="page-banner">
    <view class="banner-content">
      <view class="banner-icon">🏠</view>
      <view class="banner-text">
        <text class="banner-title">邀请住户</text>
        <text class="banner-desc">邀请他人加入您的房屋</text>
      </view>
    </view>
  </view>

  <!-- 房屋信息卡片 -->
  <view class="info-card" wx:if="{{houseInfo}}">
    <view class="card-header">
      <van-icon name="home-o" size="32rpx" color="#6366f1" />
      <text class="card-title">房屋信息</text>
    </view>
    <view class="house-info-content">
      <view class="house-item">
        <text class="house-label">房屋</text>
        <text class="house-value">{{houseInfo.houseName}}</text>
      </view>
      <view class="house-item" wx:if="{{houseInfo.address}}">
        <text class="house-label">地址</text>
        <text class="house-value">{{houseInfo.address}}</text>
      </view>
    </view>
  </view>

  <!-- 邀请设置卡片 -->
  <view class="settings-card">
    <view class="card-header">
      <van-icon name="setting-o" size="32rpx" color="#6366f1" />
      <text class="card-title">邀请设置</text>
    </view>

    <!-- 接收人手机号 -->
    <view class="form-section">
      <view class="phone-input-wrapper">
        <van-field
          value="{{formattedPhone}}"
          placeholder="请输入接收人的手机号"
          type="number"
          maxlength="13"
          bind:change="onPhoneInput"
          border="{{false}}"
          custom-style="background: #ffffff; border: 2rpx solid #e5e7eb; border-radius: 12rpx; padding: 24rpx; font-size: 32rpx;"
        />
      </view>
      <view class="phone-tip" wx:if="{{phoneError}}">
        <van-icon name="warning-o" size="24rpx" color="#ef4444" />
        <text>{{phoneError}}</text>
      </view>
    </view>

    <!-- 关系类型选择 -->
    <view class="form-section">
      <text class="form-label">关系类型</text>
      <view class="relation-types">
        <view class="relation-item {{relType === '1' ? 'active' : ''}}"
              bindtap="selectRelType" data-type="1">
          <view class="relation-icon">🏠</view>
          <text class="relation-text">业主</text>
        </view>
        <view class="relation-item {{relType === '2' ? 'active' : ''}}"
              bindtap="selectRelType" data-type="2">
          <view class="relation-icon">👨‍👩‍👧</view>
          <text class="relation-text">家庭成员</text>
        </view>
        <view class="relation-item {{relType === '3' ? 'active' : ''}}"
              bindtap="selectRelType" data-type="3">
          <view class="relation-icon">🧳</view>
          <text class="relation-text">租户</text>
        </view>
      </view>
    </view>

    <!-- 备注信息 -->
    <view class="form-section">
      <text class="form-label">备注信息 <text class="optional">(可选)</text></text>
      <van-field
        value="{{remark}}"
        placeholder="添加备注信息，让对方更好地了解邀请详情"
        type="textarea"
        maxlength="200"
        autosize
        bind:change="onRemarkInput"
        border="{{false}}"
        custom-style="background: #f8fafc; border-radius: 12rpx; min-height: 120rpx; padding: 20rpx;"
      />
      <view class="char-count">{{remark.length}}/200</view>
    </view>
  </view>



  <!-- 创建邀请按钮 -->
  <view class="action-section">
    <van-button
      type="primary"
      size="large"
      loading="{{creating}}"
      disabled="{{!canCreate}}"
      bind:click="createInvite"
      block
      custom-style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); border: none; border-radius: 16rpx; height: 96rpx; font-size: 32rpx; font-weight: 600; box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);"
    >
      {{creating ? '创建中...' : '创建邀请'}}
    </van-button>
  </view>
</view>

<!-- 分享邀请弹窗 -->
<van-popup
  show="{{showShareModal}}"
  position="center"
  round
  closeable
  bind:close="closeShareModal"
  custom-style="width: 85%; max-width: 640rpx; border-radius: 24rpx;"
>
  <view class="share-modal">
    <!-- 成功头部 -->
    <view class="success-header">
      <view class="success-icon">✅</view>
      <text class="success-title">邀请创建成功</text>
      <text class="success-desc">请将邀请信息分享给对方</text>
    </view>

    <!-- 邀请信息预览 -->
    <view class="invite-preview" wx:if="{{shareInfo}}">
      <view class="preview-card">
        <view class="preview-header">
          <van-icon name="home-o" size="40rpx" color="#6366f1" />
          <text class="app-name">{{communityName || '智慧小区服务'}}</text>
        </view>
        <view class="preview-content">
          <view class="invite-title">{{shareInfo.shareTitle}}</view>
          <view class="invite-details">
            <view class="detail-row">
              <text class="detail-label">关系</text>
              <text class="detail-value">{{shareInfo.relTypeName}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">有效期</text>
              <text class="detail-value">{{shareInfo.expireTimeFormatted || '24小时'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分享操作 -->
    <view class="share-actions">
      <button
        class="share-btn-single"
        open-type="share"
      >
        <van-icon name="share" size="32rpx" />
        <text>分享给好友</text>
      </button>
    </view>

    <view class="modal-tips">
      <view class="tip-row">
        <van-icon name="clock-o" size="24rpx" color="#6b7280" />
        <text>邀请链接24小时内有效</text>
      </view>
      <view class="tip-row">
        <van-icon name="lock" size="24rpx" color="#6b7280" />
        <text>每个邀请只能使用一次</text>
      </view>
    </view>
  </view>
</van-popup>

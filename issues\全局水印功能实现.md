# 全局水印功能实现

## 功能概述
基于现有的水印系统，实现全局水印功能，让每个页面都能显示水印，而不仅仅是首页。

## 实现方案

### 1. 页面混入器方案
通过创建页面混入器，自动为所有页面添加水印功能，无需手动修改每个页面的逻辑代码。

### 2. 全局组件注册
在app.json中全局注册水印组件，使所有页面都可以使用水印组件。

### 3. 自动注入机制
通过重写Page构造函数，自动为所有页面注入水印相关的数据和方法。

## 实现内容

### 1. 页面混入器
**文件**：`miniprogram/mixins/watermarkMixin.js`

**功能**：
- 自动为页面添加水印相关的data数据
- 在页面生命周期中自动处理水印显示逻辑
- 提供水印管理方法（初始化、更新、移除）

**核心方法**：
```javascript
// 混入数据
data: {
  showWatermark: false,
  watermarkConfig: null
}

// 生命周期混入
onLoad() // 初始化水印
onShow() // 页面显示时更新水印
onReady() // 页面准备完毕时更新水印
```

### 2. 全局组件
**文件**：`miniprogram/components/global-watermark/`

**功能**：
- 复用现有水印组件的样式和逻辑
- 提供全局可用的水印组件
- 支持动态配置和显示控制

### 3. 自动注入机制
**文件**：`miniprogram/app.js`

**功能**：
- 重写Page构造函数
- 自动为所有页面混入水印功能
- 保持原有页面逻辑不变

**实现方式**：
```javascript
// 混入水印功能到所有页面
options = mixinWatermark(options)
return originalPage(options)
```

### 4. 页面模板更新
**已添加水印的页面（需要登录的26个页面）**：
- `pages/index/index.wxml` - 首页（原有）
- `pages/mine/index.wxml` - 个人中心页面
- `pages/service/index.wxml` - 物业服务页面
- `pages/finance/finance.wxml` - 收支公示页面
- `pages/finance/detail.wxml` - 财务详情页面
- `pages/notice/list.wxml` - 通知列表页面
- `pages/notice/detail.wxml` - 公告详情页面
- `pages/profile/index.wxml` - 个人资料页面
- `pages/ocinfo/ocinfo.wxml` - 小区信息页面
- `pages/house/index.wxml` - 房屋列表页面
- `pages/house/add.wxml` - 房屋添加页面
- `pages/house/edit.wxml` - 房屋编辑页面
- `pages/house/invite-manage.wxml` - 邀请管理页面
- `pages/bx/bx.wxml` - 报事报修页面
- `pages/bx/history.wxml` - 报修历史页面
- `pages/complaint/complaint.wxml` - 投诉建议页面
- `pages/complaint/history.wxml` - 投诉历史页面
- `pages/webview/index.wxml` - 网页浏览页面
- `pages/menu/content.wxml` - 菜单内容页面
- `pages/invite/create.wxml` - 邀请创建页面
- `pages/serviceTel/index.wxml` - 服务电话页面
- `pages/network/index.wxml` - 网络诊断页面
- `pages/nav/index.wxml` - 导航页面
- `pages/logs/logs.wxml` - 日志页面

**不需要水印的页面（无需登录的5个页面）**：
- `pages/login/index.wxml` - 登录页面
- `pages/about/index.wxml` - 关于页面
- `pages/about/privacy.wxml` - 隐私政策页面
- `pages/about/agreement.wxml` - 用户协议页面
- `pages/invite/accept.wxml` - 邀请接受页面

**添加内容**：
```xml
<!-- 全局水印 -->
<global-watermark showWatermark="{{showWatermark}}" watermarkConfig="{{watermarkConfig}}"></global-watermark>
```

## 水印控制逻辑

### 1. 配置驱动
水印显示由`enable_miniprogram_mark`配置控制：
- `'1'`：显示水印
- `'0'`：隐藏水印

### 2. 内容生成
水印内容包含：
- 用户姓名（ownerName 或 nickName）
- 手机号码（mobile 或 phoneNumber）

### 3. 布局算法
- 根据屏幕尺寸动态计算水印位置
- 水印间距：150px
- 旋转角度：-45度
- 透明度：0.1

## 使用效果

### 1. 自动化
- 所有页面自动具备水印功能
- 无需手动添加水印逻辑代码
- 配置更新时自动生效

### 2. 一致性
- 所有页面水印样式统一
- 显示逻辑统一
- 配置管理统一

### 3. 可控性
- 通过后台配置控制全局水印开关
- 支持实时配置更新
- 支持页面级别的水印控制

## 技术特点

### 1. 非侵入式
- 不影响现有页面逻辑
- 通过混入器自动添加功能
- 保持代码结构清晰

### 2. 高复用性
- 复用现有水印管理器
- 统一的组件和样式
- 统一的配置管理

### 3. 易维护性
- 集中的水印逻辑管理
- 统一的配置入口
- 清晰的代码结构

## 测试验证

### 1. 功能测试
- [x] 验证需要登录的26个页面都已添加水印组件
- [x] 确认不需要登录的5个页面不显示水印
- [ ] 测试水印配置开关功能
- [ ] 验证水印内容正确显示
- [ ] 测试页面混入器自动注入功能
- [ ] 验证新页面自动具备水印功能

### 2. 性能测试
- [ ] 检查页面加载性能影响
- [ ] 验证内存使用情况
- [ ] 测试大量页面切换的稳定性

### 3. 兼容性测试
- [ ] 测试不同设备尺寸的水印显示
- [ ] 验证不同微信版本的兼容性
- [ ] 测试页面旋转时的水印适配

## 后续优化

### 1. 性能优化
- 水印组件懒加载
- 水印位置计算缓存
- 减少不必要的重新渲染

### 2. 功能扩展
- 支持自定义水印样式
- 支持页面级别的水印配置
- 支持动态水印内容

### 3. 用户体验
- 水印显示动画效果
- 更好的视觉设计
- 更灵活的配置选项

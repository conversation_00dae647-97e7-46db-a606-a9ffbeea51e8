# 置顶显示限制功能实现

## 需求描述
置顶显示最多选择激活2个，也必须激活2个，不能是0不能1不能是3个，操作做好相关的提醒和重置。

## 实现方案
简化方案：只要前端提醒就行，还有只有var source = "indexNav";才显示置顶这一列。

## 修改内容

### 1. 前端页面修改 (list.html)

#### 显示控制：
- 置顶显示列只在 `source === 'indexNav'` 时显示
- 其他类型的导航菜单不显示置顶功能

#### nav_type颜色区分：
- 为不同的nav_type添加不同颜色标签，便于区分：
  - 文本(text): 蓝色 (label-info)
  - PDF(pdf): 橙色 (label-warning)
  - 链接(url): 绿色 (label-success)
  - 小程序(miniprogram): 深蓝色 (label-primary)
  - 内置页面(page): 灰色 (label-default)
- 菜单类型显示格式：[默认/自定义] [类型标签]

#### 菜单名称链接控制：
- 只有nav_type为text、pdf、url的菜单才支持点击查看详情
- miniprogram和page类型的菜单名称不显示为链接
- 默认菜单(is_default='0')不显示为链接

#### UI优化：
- 排序字段添加编辑图标，提示用户可以点击修改排序
- 可点击的菜单名称添加外链图标，提示用户可以点击查看详情

#### 智能提醒功能：
- 新增 `checkTopShowCount()` 函数，实时检查当前置顶菜单数量
- 修改 `setTopShow()` 和 `cancelTopShow()` 函数，添加智能提醒

#### 用户提示逻辑：
1. **设置置顶时**：
   - 当前0个置顶：提示"建议设置2个菜单为置顶显示"
   - 当前1个置顶：提示"再设置1个菜单为置顶，建议保持2个置顶菜单"
   - 当前2个或以上：警告"当前已有X个置顶菜单，建议最多保持2个置顶菜单"

2. **取消置顶时**：
   - 当前2个或以下：警告"当前只有X个置顶菜单，建议保持2个置顶菜单"

## 功能特点

1. **条件显示**：置顶功能只在首页导航(indexNav)中显示，其他类型导航不显示
2. **智能提醒**：根据当前置顶数量给出不同的操作提示和建议
3. **用户友好**：通过颜色和图标区分不同类型的提示（绿色建议、红色警告）
4. **实时检查**：每次操作前都会检查当前置顶状态，提供准确的提示信息

## 测试场景

1. **首页导航菜单**：可以看到置顶显示列和相关操作
2. **其他类型菜单**：不显示置顶显示列
3. **设置置顶提醒**：根据当前置顶数量显示不同提示
4. **取消置顶提醒**：当置顶数量较少时给出警告提示

## 部署说明

1. 刷新页面即可看到效果
2. 测试不同source类型的菜单页面
3. 测试置顶操作的提醒功能

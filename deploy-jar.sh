#!/bin/bash

# ========= 配置 =========
APP_NAME="ehome"
PROFILE="prod"
JAR_FILE="ehome.jar"
LOG_FILE="logs/console.log"
PID_FILE="$APP_NAME.pid"
JVM_OPTS="-Xms512m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseGCOverheadLimit -XX:+ExplicitGCInvokesConcurrent -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/ -Djava.awt.headless=true -Dfile.encoding=UTF-8"

# ========= 函数 =========
start() {
  if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" >/dev/null 2>&1; then
      echo "⚠️ 服务已在运行 (PID: $PID)"
      return
    else
      echo "⚠️ PID 文件存在但进程未运行，清理 $PID_FILE"
      rm -f "$PID_FILE"
    fi
  fi

  echo "🚀 启动服务..."
  nohup java $JVM_OPTS -jar "$JAR_FILE" --spring.profiles.active="$PROFILE" > "$LOG_FILE" 2>&1 < /dev/null &
  sleep 2

  PID=$(jps -l | grep "$JAR_FILE" | awk '{print $1}')
  if [[ -n "$PID" && "$PID" =~ ^[0-9]+$ ]]; then
    echo "$PID" > "$PID_FILE"
    echo "✅ 服务已启动 (PID: $PID)"
  else
    echo "❌ 启动失败，未找到进程"
    exit 1
  fi
}

stop() {
  echo "🛑 停止服务..."
  if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" >/dev/null 2>&1; then
      kill -9 "$PID"
      echo "✅ 服务已停止 (PID: $PID)"
      rm -f "$PID_FILE"
    else
      echo "⚠️ PID $PID 无效，尝试清理..."
      rm -f "$PID_FILE"
    fi
  else
    echo "⚠️ 无 PID 文件，尝试查找并停止 $JAR_FILE"
    PID=$(ps -ef | grep "$JAR_FILE" | grep -v grep | awk '{print $2}')
    if [[ -n "$PID" ]]; then
      kill -9 "$PID"
      echo "✅ 已强制停止服务 (PID: $PID)"
    else
      echo "❌ 未找到正在运行的服务"
    fi
  fi
}

restart() {
  stop
  sleep 1
  start
}

status() {
  if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 "$PID" >/dev/null 2>&1; then
      echo "✅ 服务正在运行 (PID: $PID)"
    else
      echo "⚠️ 找不到运行中的进程，但 PID 文件存在"
    fi
  else
    echo "❌ 服务未运行"
  fi
}

case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    restart
    ;;
  status)
    status
    ;;
  *)
    echo "用法: $0 {start|stop|restart|status}"
    exit 1
    ;;
esac

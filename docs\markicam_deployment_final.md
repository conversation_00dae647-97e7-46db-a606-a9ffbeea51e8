# Markicam API集成最终部署文档

## 部署清单

### ✅ 已完成的文件

#### 后端代码
- `ehome-oc/src/main/java/com/ehome/oc/service/MarkicamSyncService.java` - 数据同步服务
- `ehome-oc/src/main/java/com/ehome/oc/controller/MarkicamController.java` - 控制器
- `ehome-oc/pom.xml` - 已添加OkHttp3依赖

#### 前端页面
- `ehome-page/src/main/resources/templates/oc/markicam/index.html` - 主导航页面
- `ehome-page/src/main/resources/templates/oc/markicam/config.html` - 配置管理页面
- `ehome-page/src/main/resources/templates/oc/markicam/moment.html` - 照片视频管理页面
- `ehome-page/src/main/resources/templates/oc/markicam/member.html` - 团队成员管理页面
- `ehome-page/src/main/resources/templates/oc/markicam/illegal.html` - 违规车辆管理页面
- `ehome-page/src/main/resources/templates/oc/markicam/log.html` - 同步日志页面

#### 数据库脚本
- `sql/create_markicam_tables.sql` - 完整的建表脚本

#### 文档
- `docs/markicam_database_setup.md` - 数据库设计文档
- `docs/okhttp3_integration_test.md` - OkHttp3集成测试文档
- `docs/markicam_integration_summary.md` - 完整集成总结
- `docs/markicam_quick_start_guide.md` - 快速开始指南

## 部署步骤

### 1. 数据库准备
```bash
# 执行建表脚本
mysql -u username -p database_name < sql/create_markicam_tables.sql
```

### 2. 应用部署
1. 确保OkHttp3依赖已添加到ehome-oc模块
2. 重新编译和部署应用
3. 启动ehome系统

### 3. 配置API
1. 访问 `http://your-domain/oc/markicam/config`
2. 添加Markicam API配置：
   - 组织ID
   - API密钥
   - API基础URL
3. 测试连接确保配置正确

### 4. 开始使用
1. 访问 `http://your-domain/oc/markicam/index`
2. 使用同步功能获取数据
3. 在各管理页面查看同步结果

## 核心功能

### API接口列表

#### 页面路由
- `GET /oc/markicam/index` - 主导航页面
- `GET /oc/markicam/config` - 配置管理页面
- `GET /oc/markicam/moment` - 照片视频管理页面
- `GET /oc/markicam/member` - 团队成员管理页面
- `GET /oc/markicam/illegal` - 违规车辆管理页面
- `GET /oc/markicam/log` - 同步日志页面

#### 数据操作
- `POST /oc/markicam/config/save` - 保存配置
- `POST /oc/markicam/sync/moment` - 同步照片视频
- `POST /oc/markicam/sync/member` - 同步成员数据
- `POST /oc/markicam/sync/illegal` - 同步违规车辆
- `POST /oc/markicam/sync/all` - 同步所有数据

#### 查询接口
- `POST /oc/markicam/*/list` - 分页列表查询
- `GET /oc/markicam/*/detail/{id}` - 详情查询
- `GET /oc/markicam/status` - 系统状态
- `GET /oc/markicam/statistics` - 数据统计

### 数据库表结构

| 表名 | 功能 | 主要字段 |
|------|------|----------|
| eh_markicam_config | API配置 | org_id, api_key, base_url |
| eh_markicam_team | 团队信息 | team_id, team_name, member_count |
| eh_markicam_moment | 照片视频 | markicam_id, url, moment_type |
| eh_markicam_member | 团队成员 | uid, nickname, member_type |
| eh_markicam_illegal_park | 违规车辆 | car_plate, car_picture, report_time |
| eh_markicam_sync_log | 同步日志 | sync_type, sync_status, sync_count |

## 技术特点

### OkHttp3集成
- 支持自定义Header
- 连接池管理
- 超时控制
- 详细错误处理

### 分页查询
- 使用EasySQL构建动态查询
- 支持条件搜索
- 返回TableDataInfo格式

### 数据同步
- 增量同步策略
- 去重处理机制
- 错误重试逻辑
- 详细日志记录

### 前端界面
- 响应式设计
- 图片预览功能
- 实时状态监控
- 权限控制

## 监控和维护

### 日志查看
1. **应用日志**: `logs/ehome.log`
2. **SQL日志**: `logs/sql.log`
3. **同步日志**: 数据库表 `eh_markicam_sync_log`

### 性能监控
- 同步耗时统计
- API调用频率
- 数据量增长趋势
- 错误率监控

### 定期维护
- 清理旧的同步日志
- 检查API配置有效性
- 监控数据库存储空间
- 更新API密钥

## 扩展建议

### 定时同步
```java
@Scheduled(cron = "0 0 */1 * * ?") // 每小时执行
public void scheduledSync() {
    // 自动同步逻辑
}
```

### 消息通知
- 同步失败邮件通知
- 数据异常短信提醒
- 钉钉/企微机器人推送

### 数据分析
- 违规趋势分析
- 巡检效率统计
- 团队活跃度报表

## 安全考虑

1. **API密钥安全**
   - 加密存储API密钥
   - 定期更换密钥
   - 限制访问权限

2. **数据安全**
   - 敏感信息脱敏
   - 访问日志记录
   - 数据备份策略

3. **网络安全**
   - HTTPS通信
   - API限流
   - 异常监控

## 故障排除

### 常见问题
1. **连接超时**: 检查网络和API地址
2. **认证失败**: 验证组织ID和API密钥
3. **数据为空**: 确认时间范围和权限
4. **同步失败**: 查看详细错误日志

### 联系支持
如遇到问题，请提供：
- 错误日志信息
- API配置信息（脱敏）
- 操作步骤描述
- 系统环境信息

---

**部署完成！** Markicam API集成系统已准备就绪，可以开始同步和管理数据。

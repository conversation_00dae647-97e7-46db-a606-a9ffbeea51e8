package com.ehome.oc.service;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.http.HttpUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 微信消息推送服务
 */
@Service
public class WxMessageService {
    
    private static final Logger logger = LoggerFactory.getLogger(WxMessageService.class);
    
    @Value("${wechat.appid:}")
    private String appId;

    @Value("${wechat.secret:}")
    private String appSecret;
    
    // 消息模板ID常量
    public static final String TEMPLATE_REPAIR_NOTICE = "yLMlq8zOxgHkyUsAbE2ltdekZdvEO_U5iOl7eWP_Phk";
    public static final String TEMPLATE_PROPERTY_NOTICE = "oFsuyzgpcAvENaOqokhmD-Fa8EmBpDsh_QFLccPmRUY";
    
    // 模板类型常量
    public static final String TYPE_REPAIR_NOTICE = "repair_notice";
    public static final String TYPE_PROPERTY_NOTICE = "property_notice";

    // Token缓存
    private static final Map<String, TokenCache> tokenCacheMap = new ConcurrentHashMap<>();

    /**
     * Token缓存内部类
     */
    private static class TokenCache {
        private String accessToken;
        private long expireTime;

        public TokenCache(String accessToken, long expireTime) {
            this.accessToken = accessToken;
            this.expireTime = expireTime;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() >= expireTime;
        }
    }
    
    /**
     * 获取微信access_token（带缓存机制）
     */
    public String getAccessToken() {
        String cacheKey = appId + ":" + appSecret;

        // 检查缓存
        TokenCache cache = tokenCacheMap.get(cacheKey);
        if (cache != null && !cache.isExpired()) {
            logger.debug("使用缓存的access_token");
            return cache.getAccessToken();
        }

        // 缓存过期或不存在，重新获取
        synchronized (this) {
            // 双重检查，防止并发重复请求
            cache = tokenCacheMap.get(cacheKey);
            if (cache != null && !cache.isExpired()) {
                return cache.getAccessToken();
            }

            return refreshAccessToken(cacheKey);
        }
    }

    /**
     * 刷新access_token
     */
    private String refreshAccessToken(String cacheKey) {
        try {
            logger.info("开始获取新的access_token");
            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
                + appId + "&secret=" + appSecret;

            String response = HttpUtils.sendGet(url);
            JSONObject result = JSONObject.parseObject(response);

            if (result.containsKey("access_token")) {
                String accessToken = result.getString("access_token");
                int expiresIn = result.getIntValue("expires_in");

                // 提前5分钟过期，避免边界情况
                long expireTime = System.currentTimeMillis() + (expiresIn - 300) * 1000L;

                // 更新缓存
                tokenCacheMap.put(cacheKey, new TokenCache(accessToken, expireTime));

                logger.info("access_token获取成功，有效期: {}秒", expiresIn);
                return accessToken;
            } else {
                logger.error("获取access_token失败: {}", response);
                return null;
            }
        } catch (Exception e) {
            logger.error("获取access_token异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清除access_token缓存
     */
    public void clearTokenCache() {
        String cacheKey = appId + ":" + appSecret;
        tokenCacheMap.remove(cacheKey);
        logger.info("access_token缓存已清除");
    }

    /**
     * 获取token缓存状态
     */
    public Map<String, Object> getTokenCacheStatus() {
        String cacheKey = appId + ":" + appSecret;
        TokenCache cache = tokenCacheMap.get(cacheKey);

        Map<String, Object> status = new HashMap<>();
        if (cache != null) {
            status.put("cached", true);
            status.put("expired", cache.isExpired());
            status.put("expireTime", cache.expireTime);
            status.put("remainingSeconds", Math.max(0, (cache.expireTime - System.currentTimeMillis()) / 1000));
        } else {
            status.put("cached", false);
        }

        return status;
    }

    /**
     * 发送订阅消息
     */
    public boolean sendSubscribeMessage(String openId, String templateId, String templateType,
                                      Map<String, Object> data, String businessId, String businessType, String page) {
        String messageLogId = Seq.getId();

        try {
            // 记录发送日志
            recordMessageLog(messageLogId, openId, templateId, templateType, data, businessId, businessType, 0);

            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                updateMessageLogError(messageLogId, "获取access_token失败");
                return false;
            }

            // 构建消息内容
            JSONObject message = buildMessage(openId, templateId, data, page);

            // 发送消息
            String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + accessToken;
            String response = HttpUtils.sendPost(url, message.toJSONString());

            JSONObject result = JSONObject.parseObject(response);
            if (result.getIntValue("errcode") == 0) {
                // 发送成功
                updateMessageLogSuccess(messageLogId);
                logger.info("订阅消息发送成功: openId={}, templateId={}", openId, templateId);
                return true;
            } else {
                // 发送失败
                String errorMsg = "errcode=" + result.getIntValue("errcode") + ", errmsg=" + result.getString("errmsg");
                updateMessageLogError(messageLogId, errorMsg);
                logger.error("订阅消息发送失败: {}", errorMsg);
                return false;
            }

        } catch (Exception e) {
            updateMessageLogError(messageLogId, e.getMessage());
            logger.error("发送订阅消息异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 构建消息内容
     */
    private JSONObject buildMessage(String openId, String templateId, Map<String, Object> data, String page) {
        JSONObject message = new JSONObject();
        message.put("touser", openId);
        message.put("template_id", templateId);

        // 添加跳转页面
        if (StringUtils.isNotEmpty(page)) {
            message.put("page", page);
        }

        JSONObject dataObj = new JSONObject();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            JSONObject valueObj = new JSONObject();
            valueObj.put("value", entry.getValue());
            dataObj.put(entry.getKey(), valueObj);
        }
        message.put("data", dataObj);

        return message;
    }
    
    /**
     * 记录消息发送日志
     */
    private void recordMessageLog(String id, String openId, String templateId, String templateType,
                                Map<String, Object> data, String businessId, String businessType, int status) {
        try {
            // 根据openId获取用户信息
            Record userInfo = getUserInfoByOpenId(openId);
            String wxUserId = userInfo != null ? userInfo.getStr("user_id") : null;
            String ownerId = userInfo != null ? userInfo.getStr("owner_id") : null;
            String communityId = userInfo != null ? userInfo.getStr("community_id") : null;

            Record record = new Record();
            record.set("id", id);
            record.set("wx_user_id", wxUserId);
            record.set("owner_id", ownerId);
            record.set("openid", openId);
            record.set("template_id", templateId);
            record.set("template_type", templateType);
            record.set("business_id", businessId);
            record.set("business_type", businessType);
            record.set("message_data", JSONObject.toJSONString(data));
            record.set("send_status", status);
            record.set("community_id", communityId);
            record.set("create_time", new Date());
            record.set("update_time", new Date());

            Db.save("eh_wx_message_log", "id", record);
        } catch (Exception e) {
            logger.error("记录消息日志失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新消息发送成功状态
     */
    private void updateMessageLogSuccess(String id) {
        try {
            Db.update("UPDATE eh_wx_message_log SET send_status = 1, send_time = ? WHERE id = ?", 
                     new Date(), id);
        } catch (Exception e) {
            logger.error("更新消息日志成功状态失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新消息发送失败状态
     */
    private void updateMessageLogError(String id, String errorMsg) {
        try {
            Db.update("UPDATE eh_wx_message_log SET send_status = 2, error_msg = ?, send_time = ? WHERE id = ?", 
                     errorMsg, new Date(), id);
        } catch (Exception e) {
            logger.error("更新消息日志失败状态失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 根据openId获取用户信息
     */
    private Record getUserInfoByOpenId(String openId) {
        try {
            String sql = "SELECT wu.user_id, wu.owner_id, o.community_id " +
                        "FROM eh_wx_user wu " +
                        "LEFT JOIN eh_owner o ON wu.owner_id = o.owner_id " +
                        "WHERE wu.openid = ?";
            return Db.findFirst(sql, openId);
        } catch (Exception e) {
            logger.error("根据openId获取用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 发送报修提醒消息
     * 模板字段：报修位置{{thing1.DATA}}、报单人员{{thing8.DATA}}、联系电话{{phone_number13.DATA}}、工单描述{{thing9.DATA}}、上报时间{{date3.DATA}}
     */
    public boolean sendRepairNotice(String openId, String location, String reporter,
                                  String phone, String description, String reportTime, String businessId) {
        Map<String, Object> data = new HashMap<>();
        // 根据微信模板实际字段映射，并进行内容验证和截取
        data.put("thing1", validateAndTruncateText(location, "未指定位置", 20));           // 报修位置
        data.put("thing8", validateAndTruncateText(reporter, "业主", 20));               // 报单人员
        data.put("phone_number13", phone != null ? phone : "");                        // 联系电话
        data.put("thing9", validateAndTruncateText(description, "报修处理", 20));        // 工单描述
        // date3字段根据模板示例，可以是灵活的时间描述格式，如"2020-01-15 11点"
        String timeDesc = reportTime != null ? formatTimeDescription(reportTime) : formatTimeDescription(DateUtils.dateTimeNow());
        data.put("date3", timeDesc);                                            // 上报时间

        // 跳转到报修历史页面
        String page = "pages/bx/detail?id=" + businessId;
        return sendSubscribeMessage(openId, TEMPLATE_REPAIR_NOTICE, TYPE_REPAIR_NOTICE, data, businessId, "repair", page);
    }
    
    /**
     * 发送物业通知消息
     * 模板字段：发布内容{{thing3.DATA}}、发布时间{{date2.DATA}}、消息类型{{thing4.DATA}}、发布人{{name1.DATA}}、通知时间{{time5.DATA}}
     */
    public boolean sendPropertyNotice(String openId, String content, String publishTime,
                                    String messageType, String publisher, String noticeTime, String businessId) {
        Map<String, Object> data = new HashMap<>();
        // 根据微信模板实际字段映射，并进行内容验证和截取
        data.put("thing3", validateAndTruncateText(content, "物业通知", 20));                     // 发布内容
        // date2字段也需要中文格式，和date3一样
        String formattedPublishDate = publishTime != null ? convertToChineseDate(publishTime) : getCurrentChineseDate();
        data.put("date2", formattedPublishDate);                                                 // 发布时间
        data.put("thing4", validateAndTruncateText(messageType, "通知", 20));                    // 消息类型
        data.put("name1", validateAndTruncateText(publisher, "物业管理处", 8));                   // 发布人
        // time5字段需要HH:mm:ss格式
        String formattedNoticeTime = noticeTime != null ? extractTimeFromDateTime(noticeTime) : DateUtils.getTime();
        data.put("time5", formattedNoticeTime);                                        // 通知时间

        // 跳转到通知详情页面
        String page = "pages/notice/detail?id=" + businessId;
        return sendSubscribeMessage(openId, TEMPLATE_PROPERTY_NOTICE, TYPE_PROPERTY_NOTICE,
                                  data, businessId, "notice", page);
    }



    /**
     * 格式化时间描述为微信模板友好的格式
     * 根据微信要求：date3.value 需要是 "yyyy年MM月dd日" 格式
     */
    private String formatTimeDescription(String dateTime) {
        try {
            if (StringUtils.isEmpty(dateTime)) {
                return getCurrentChineseDate();
            }

            // 如果已经是目标格式，直接返回
            if (dateTime.contains("年") && dateTime.contains("月") && dateTime.contains("日")) {
                return dateTime;
            }

            // 提取日期部分
            String datePart = dateTime;
            if (dateTime.contains(" ")) {
                String[] parts = dateTime.split(" ");
                if (parts.length > 0) {
                    datePart = parts[0];
                }
            }

            // 转换为中文格式
            return convertToChineseDate(datePart);

        } catch (Exception e) {
            logger.warn("时间描述格式化失败，使用默认格式: {}", e.getMessage());
            return getCurrentChineseDate();
        }
    }

    /**
     * 将日期字符串转换为中文格式
     */
    private String convertToChineseDate(String dateStr) {
        try {
            if (StringUtils.isEmpty(dateStr)) {
                return "2023年12月21日";  // 直接返回默认值，避免递归
            }

            // 处理 YYYY-MM-DD 格式
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                String[] parts = dateStr.split("-");
                if (parts.length == 3) {
                    return parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
                }
            }

            // 处理 YYYY/MM/DD 格式
            if (dateStr.matches("\\d{4}/\\d{2}/\\d{2}")) {
                String[] parts = dateStr.split("/");
                if (parts.length == 3) {
                    return parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
                }
            }

            return "2023年12月21日";  // 直接返回默认值，避免递归

        } catch (Exception e) {
            logger.warn("日期转换失败: {}", e.getMessage());
            return "2023年12月21日";  // 直接返回默认值，避免递归
        }
    }

    /**
     * 获取当前日期的中文格式
     */
    private String getCurrentChineseDate() {
        try {
            String currentDate = DateUtils.dateTime();  // 获取当前日期 YYYY-MM-DD
            // 直接转换，避免递归调用
            if (StringUtils.isNotEmpty(currentDate) && currentDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                String[] parts = currentDate.split("-");
                if (parts.length == 3) {
                    return parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
                }
            }
            return "2023年12月21日";  // 默认值
        } catch (Exception e) {
            logger.warn("获取当前中文日期失败: {}", e.getMessage());
            return "2023年12月21日";  // 最后的安全默认值
        }
    }

    /**
     * 从日期时间字符串中提取时间部分
     */
    private String extractTimeFromDateTime(String dateTime) {
        try {
            if (StringUtils.isEmpty(dateTime)) {
                return DateUtils.getTime();
            }

            // 如果已经是HH:mm:ss格式，直接返回
            if (dateTime.matches("\\d{2}:\\d{2}:\\d{2}")) {
                return dateTime;
            }

            // 如果是完整的日期时间格式，提取时间部分
            if (dateTime.contains(" ")) {
                String[] parts = dateTime.split(" ");
                if (parts.length > 1) {
                    return parts[1];
                }
            }

            // 其他情况返回当前时间
            return DateUtils.getTime();

        } catch (Exception e) {
            logger.warn("时间提取失败，使用当前时间: {}", e.getMessage());
            return DateUtils.getTime();
        }
    }

    /**
     * 验证和截取文本内容，确保符合微信模板要求
     * @param text 原始文本
     * @param defaultValue 默认值
     * @param maxLength 最大长度
     * @return 处理后的文本
     */
    private String validateAndTruncateText(String text, String defaultValue, int maxLength) {
        try {
            // 如果文本为空，使用默认值
            if (StringUtils.isEmpty(text)) {
                return defaultValue;
            }

            // 清理特殊字符，只保留中文、英文、数字和常用标点
            String cleanText = text.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_，。！？、：；\\(\\)\\[\\]]", "");

            // 如果清理后为空，使用默认值
            if (StringUtils.isEmpty(cleanText.trim())) {
                return defaultValue;
            }

            // 截取到指定长度
            if (cleanText.length() > maxLength) {
                return cleanText.substring(0, maxLength - 1) + "…";
            }

            return cleanText.trim();

        } catch (Exception e) {
            logger.warn("文本验证和截取失败，使用默认值: {}", e.getMessage());
            return defaultValue;
        }
    }
}

# 公告点赞分享功能数据库更新说明

## 概述
本次更新为公告详情页面添加了点赞和分享功能，需要对数据库进行相应的结构调整。

## 数据库变更

### 1. 表结构修改

#### sys_notice表新增字段
- `like_count` int(11) DEFAULT '0' COMMENT '点赞次数'
- `share_count` int(11) DEFAULT '0' COMMENT '分享次数'

#### sys_notice_comment表新增字段
- `like_count` int(11) DEFAULT '0' COMMENT '点赞次数'

### 2. 新增表

#### sys_notice_like（公告点赞记录表）
记录用户对公告的点赞行为，支持点赞和取消点赞。

#### sys_notice_share（公告分享记录表）
记录用户的分享行为，用于统计分享次数。

#### sys_comment_like（评论点赞记录表）
记录用户对评论和回复的点赞行为。

## 部署步骤

### 1. 执行数据库脚本
```sql
-- 执行 notice_like_share_update.sql 文件中的所有SQL语句
source notice_like_share_update.sql;
```

### 2. 重启应用服务
重启ehome-oc服务以加载新的API接口。

### 3. 更新小程序代码
确保小程序端的代码已更新到最新版本。

## 新增API接口

### 公告相关
- `POST /api/wx/data/notice/{noticeId}/like` - 点赞/取消点赞公告
- `POST /api/wx/data/notice/{noticeId}/share` - 记录分享行为
- `GET /api/wx/data/notice/{noticeId}/stats` - 获取公告统计信息

### 评论相关
- `POST /api/wx/data/comment/{commentId}/like` - 点赞/取消点赞评论

## 功能特性

### 点赞功能
- 支持对公告和评论的点赞/取消点赞
- 实时更新点赞数量
- 防重复点赞机制
- 点赞状态持久化

### 分享功能
- 记录分享行为统计
- 支持微信好友和朋友圈分享
- 分享数量实时更新

### 数据完整性
- 使用唯一索引防止重复点赞
- 软删除机制保留历史数据
- 事务保证数据一致性

## 注意事项

1. 执行数据库脚本前请备份现有数据
2. 确保应用服务有足够的数据库权限
3. 建议在测试环境先验证功能正常后再部署到生产环境
4. 监控新增表的数据增长情况，必要时进行数据清理

## 回滚方案

如需回滚，可执行以下SQL：
```sql
-- 删除新增的表
DROP TABLE IF EXISTS sys_notice_like;
DROP TABLE IF EXISTS sys_notice_share;
DROP TABLE IF EXISTS sys_comment_like;

-- 删除新增的字段
ALTER TABLE sys_notice DROP COLUMN like_count;
ALTER TABLE sys_notice DROP COLUMN share_count;
ALTER TABLE sys_notice_comment DROP COLUMN like_count;
```

-- 修复收费标准字段重复问题的SQL脚本

-- 1. 清理现有数据，确保字段一致性
UPDATE eh_charge_standard 
SET 
    -- 统一计费精度字段
    unit = CASE 
        WHEN precision_type LIKE '%元%' THEN 1
        WHEN precision_type LIKE '%角%' THEN 2  
        WHEN precision_type LIKE '%分%' THEN 3
        ELSE 1
    END,
    
    -- 统一取整方式字段
    round_type = CASE 
        WHEN rounding_type = '四舍五入' THEN 0
        WHEN rounding_type = '抹零' THEN 1
        WHEN rounding_type = '向上取整' THEN 2
        ELSE 0
    END,
    
    -- 统一收费方式字段
    period_type = CASE 
        WHEN collection_method LIKE '%按月%' THEN 101
        ELSE 101
    END,
    
    -- 统一金额计算方式字段
    count_type = CASE 
        WHEN calculation_method = '固定金额' THEN 200
        WHEN calculation_method LIKE '%单价%' THEN 100
        WHEN fee_type = '走表收费' THEN 100  -- 走表收费固定为单价*使用量
        ELSE 100
    END,
    
    -- 统一违约金处理字段
    late_money_type = CASE 
        WHEN penalty_handling = '计算违约金' THEN 1
        ELSE 0
    END,
    
    -- 统一不足一月处理字段（确保是字符串格式）
    incomplete_month_handling = CASE 
        WHEN incomplete_month_handling = '按天收费' THEN '1'
        WHEN incomplete_month_handling = '按月收费' THEN '2'
        WHEN incomplete_month_handling = '不收费' THEN '3'
        WHEN incomplete_month_handling IN ('1', '2', '3') THEN incomplete_month_handling
        ELSE '1'
    END

WHERE is_deleted = 0;

-- 2. 验证数据更新结果
SELECT 
    id,
    fee_name,
    fee_type,
    unit,
    round_type,
    period_type,
    count_type,
    late_money_type,
    incomplete_month_handling
FROM eh_charge_standard 
WHERE is_deleted = 0
ORDER BY id;

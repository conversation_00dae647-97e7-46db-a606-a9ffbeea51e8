.orders-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #666;
}

.order-list {
  background: white;
  border-radius: 10rpx;
  overflow: hidden;
}

.order-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-item:last-child {
  border-bottom: none;
}

.order-title {
  font-size: 28rpx;
  color: #333;
}

.order-status {
  font-size: 24rpx;
  color: #1890ff;
  padding: 10rpx 20rpx;
  background: #e6f7ff;
  border-radius: 20rpx;
}

.empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.domain.CommunityConfig;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.service.ICommunityConfigService;
import com.ehome.oc.service.INoticeTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公告类型服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class NoticeTypeServiceImpl implements INoticeTypeService {
    
    private static final Logger logger = LoggerFactory.getLogger(NoticeTypeServiceImpl.class);
    
    @Autowired
    private ICommunityConfigService communityConfigService;
    
    /**
     * 获取公告类型列表（优先从小区配置读取，如果没有则返回默认类型）
     *
     * @param communityId 小区ID
     * @return 公告类型列表
     */
    @Override
    public List<Map<String, Object>> getNoticeTypes(String communityId) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        try {
            if (StringUtils.isNotEmpty(communityId)) {
                // 从小区配置中获取notice_types
                CommunityConfig config = communityConfigService.getConfig(communityId);
                
                if (config != null && config.containsKey("notice_types")) {
                    try {
                        String noticeTypesStr = config.getString("notice_types");
                        if (StringUtils.isNotEmpty(noticeTypesStr)) {
                            JSONArray noticeTypesArray = JSONArray.parseArray(noticeTypesStr);
                            
                            if (noticeTypesArray != null && !noticeTypesArray.isEmpty()) {
                                for (int i = 0; i < noticeTypesArray.size(); i++) {
                                    JSONObject typeObj = noticeTypesArray.getJSONObject(i);
                                    if (typeObj != null && StringUtils.isNotEmpty(typeObj.getString("label"))) {
                                        Map<String, Object> item = new HashMap<>();
                                        item.put("dictValue", typeObj.getString("value"));
                                        item.put("dictLabel", typeObj.getString("label"));
                                        item.put("dictSort", typeObj.getIntValue("sort"));
                                        result.add(item);
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.warn("解析小区配置中的notice_types失败: " + e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("获取小区配置失败: " + e.getMessage());
        }
        
        // 如果没有从小区配置中获取到数据，返回默认类型
        if (result.isEmpty()) {
            Map<String, Object> item1 = new HashMap<>();
            item1.put("dictValue", "通知公告");
            item1.put("dictLabel", "通知公告");
            item1.put("dictSort", 1);
            result.add(item1);

            Map<String, Object> item2 = new HashMap<>();
            item2.put("dictValue", "社区播报");
            item2.put("dictLabel", "社区播报");
            item2.put("dictSort", 2);
            result.add(item2);

            Map<String, Object> item3 = new HashMap<>();
            item3.put("dictValue", "活动广场");
            item3.put("dictLabel", "活动广场");
            item3.put("dictSort", 3);
            result.add(item3);
        }
        
        return result;
    }
}

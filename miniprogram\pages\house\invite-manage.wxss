/* 邀请管理页面样式 */
page {
  background: #f7f8fa;
  height: 100vh;
}

.container {
  padding: 24rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 10rpx;
  margin-top:20rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.page-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 邀请列表 */
.invite-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 24rpx 0;
}

.invite-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
  position: relative;
}

/* 邀请头部 */
.invite-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.house-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.house-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.invite-relation {
  font-size: 24rpx;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  align-self: flex-start;
}

/* 邀请状态 */
.invite-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-0 {
  background: #fff7e6;
  color: #fa8c16;
}

.status-1 {
  background: #f6ffed;
  color: #52c41a;
}

.status-2 {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-3 {
  background: #f5f5f5;
  color: #999;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

/* 邀请详情 */
.invite-details {
  margin-bottom: 24rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.invite-code {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1890ff;
  letter-spacing: 2rpx;
}

/* 过期标识 */
.expired-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: #ff4d4f;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  transform: rotate(15deg);
}

/* 邀请操作 */
.invite-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 24rpx;
  align-items: center;
}

.search-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 搜索区域 */
.search-section {
  padding: 24rpx 0;
}

.search-input {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

.search-code-input {
  font-size: 32rpx !important;
  font-weight: 600 !important;
  letter-spacing: 4rpx !important;
  text-align: center !important;
  color: #333 !important;
  text-transform: uppercase !important;
}

/* 搜索结果 */
.search-result {
  margin-top: 32rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}

/* 房屋页面样式调整 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
  padding: 0 24rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.95);
  background: #f7f8fa;
}

.action-btn text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* Vant组件样式覆盖 */
.van-tabs__content {
  padding: 0 !important;
}

.van-tab__panel {
  padding: 0 !important;
}

.van-field {
  padding: 0 !important;
  flex: 1 !important;
  margin-right: 20rpx !important;
}

.van-field__input {
  font-size: 30rpx !important;
}

.van-button--small {
  height: 64rpx !important;
  font-size: 28rpx !important;
  border-radius: 12rpx !important;
  font-weight: 500 !important;
}

.van-button--large {
  height: 88rpx !important;
  font-size: 32rpx !important;
  border-radius: 16rpx !important;
  font-weight: 500 !important;
}

.van-button--mini {
  height: 48rpx !important;
  font-size: 24rpx !important;
  padding: 0 16rpx !important;
  border-radius: 8rpx !important;
}

.van-button--default {
  background: #f7f8fa !important;
  border-color: #ebedf0 !important;
  color: #646566 !important;
}

.van-button--primary {
  background: #1890ff !important;
  border-color: #1890ff !important;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3) !important;
}

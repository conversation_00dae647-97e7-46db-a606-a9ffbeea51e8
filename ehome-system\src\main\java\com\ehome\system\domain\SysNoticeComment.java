package com.ehome.system.domain;

import com.ehome.common.annotation.Excel;
import com.ehome.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 通知公告评论表 sys_notice_comment
 * 
 * <AUTHOR>
 */
public class SysNoticeComment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 评论ID */
    private Long commentId;

    /** 公告ID */
    @Excel(name = "公告ID")
    private Long noticeId;

    /** 父评论ID（回复功能） */
    private Long parentId;

    /** 评论用户ID */
    @Excel(name = "评论用户ID")
    private String userId;

    /** 评论用户名 */
    @Excel(name = "评论用户名")
    private String userName;

    /** 用户类型（wx_user/sys_user） */
    @Excel(name = "用户类型")
    private String userType;

    /** 评论内容 */
    @Excel(name = "评论内容")
    private String content;

    /** 状态（0正常 1删除 2审核中） */
    @Excel(name = "状态")
    private String status;

    /** 关联物业ID */
    private String pmsId;

    /** 关联小区ID */
    private String communityId;

    /** 业主ID */
    private String ownerId;

    /** 房屋ID */
    private String houseId;

    /** 房屋名称 */
    @Excel(name = "房屋名称")
    private String houseName;

    /** 子评论列表 */
    private List<SysNoticeComment> children;

    /** 回复数量 */
    private Integer replyCount;

    /** 公告标题（用于显示） */
    private String noticeTitle;

    public void setCommentId(Long commentId)
    {
        this.commentId = commentId;
    }

    public Long getCommentId() 
    {
        return commentId;
    }

    public void setNoticeId(Long noticeId) 
    {
        this.noticeId = noticeId;
    }

    public Long getNoticeId() 
    {
        return noticeId;
    }

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setUserType(String userType) 
    {
        this.userType = userType;
    }

    public String getUserType() 
    {
        return userType;
    }

    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setPmsId(String pmsId) 
    {
        this.pmsId = pmsId;
    }

    public String getPmsId() 
    {
        return pmsId;
    }

    public void setCommunityId(String communityId) 
    {
        this.communityId = communityId;
    }

    public String getCommunityId()
    {
        return communityId;
    }

    public String getOwnerId()
    {
        return ownerId;
    }

    public void setOwnerId(String ownerId)
    {
        this.ownerId = ownerId;
    }

    public String getHouseId()
    {
        return houseId;
    }

    public void setHouseId(String houseId)
    {
        this.houseId = houseId;
    }

    public String getHouseName()
    {
        return houseName;
    }

    public void setHouseName(String houseName)
    {
        this.houseName = houseName;
    }

    public List<SysNoticeComment> getChildren()
    {
        return children;
    }

    public void setChildren(List<SysNoticeComment> children) 
    {
        this.children = children;
    }

    public Integer getReplyCount() 
    {
        return replyCount;
    }

    public void setReplyCount(Integer replyCount)
    {
        this.replyCount = replyCount;
    }

    public String getNoticeTitle()
    {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle)
    {
        this.noticeTitle = noticeTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("commentId", getCommentId())
            .append("noticeId", getNoticeId())
            .append("parentId", getParentId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("userType", getUserType())
            .append("content", getContent())
            .append("status", getStatus())
            .append("pmsId", getPmsId())
            .append("communityId", getCommunityId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}

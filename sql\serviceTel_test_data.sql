-- 服务电话多级目录测试数据
-- 先清空现有数据
DELETE FROM eh_service_tel WHERE community_id = 'default';

-- 插入一级分类（父级）
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('CAT001', '0', 'default', '', '保险服务', '', 'shield-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '各类保险服务电话'),
('CAT002', '0', 'default', '', '紧急服务', '', 'warning-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '紧急情况联系电话'),
('CAT003', '0', 'default', '', '生活服务', '', 'service-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '日常生活服务电话'),
('CAT004', '0', 'default', '', '政府服务', '', 'manager-o', 4, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '政府部门服务电话');

-- 插入二级服务电话（子项）
-- 保险服务下的子项
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('ST001', 'CAT001', 'default', '95518', '中国人保车险', '中国人民保险集团股份有限公司', 'phone-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST002', 'CAT001', 'default', '95511', '中国平安车险', '中国平安保险（集团）股份有限公司', 'phone-circle-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST003', 'CAT001', 'default', '95500', '中国太平洋车险', '中国太平洋保险（集团）股份有限公司', 'contact-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST004', 'CAT001', 'default', '95519', '中国人寿保险', '中国人寿保险股份有限公司', 'chat-o', 4, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '');

-- 紧急服务下的子项
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('ST005', 'CAT002', 'default', '110', '报警电话', '公安部门', 'warning-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST006', 'CAT002', 'default', '119', '火警电话', '消防部门', 'fire-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST007', 'CAT002', 'default', '120', '急救电话', '医疗急救中心', 'medic-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST008', 'CAT002', 'default', '122', '交通事故报警', '交通管理部门', 'logistics', 4, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '');

-- 生活服务下的子项
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('ST009', 'CAT003', 'default', '10086', '中国移动客服', '中国移动通信集团有限公司', 'phone-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST010', 'CAT003', 'default', '10010', '中国联通客服', '中国联合网络通信集团有限公司', 'phone-circle-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST011', 'CAT003', 'default', '10000', '中国电信客服', '中国电信集团有限公司', 'contact-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST012', 'CAT003', 'default', '95533', '中国建设银行', '中国建设银行股份有限公司', 'gold-coin-o', 4, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '');

-- 政府服务下的子项
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('ST013', 'CAT004', 'default', '12345', '政府服务热线', '政府服务中心', 'manager-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST014', 'CAT004', 'default', '12366', '税务服务热线', '国家税务总局', 'balance-list-o', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST015', 'CAT004', 'default', '12333', '人社服务热线', '人力资源和社会保障部', 'friends-o', 3, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST016', 'CAT004', 'default', '12315', '消费者投诉热线', '市场监督管理局', 'shield-o', 4, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '');

-- 插入一些独立的服务电话（没有分类的）
INSERT INTO `eh_service_tel` (`service_tel_id`, `parent_id`, `community_id`, `tel_number`, `service_name`, `company_name`, `icon_url`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES
('ST017', '0', 'default', '************', '物业管理处', '小区物业管理有限公司', 'home-o', 1, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', ''),
('ST018', '0', 'default', '************', '快递服务', '综合快递服务', 'logistics', 2, '0', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00', '');

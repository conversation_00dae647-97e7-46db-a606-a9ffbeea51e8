# 公告逻辑删除功能实现

## 任务描述
将公告系统的物理删除改为逻辑删除，新增deleted字段标记删除状态，所有查询都需要过滤已删除的公告。

## 实现方案
新增专门的逻辑删除字段deleted（0未删除 1已删除），而不是使用现有的status字段，避免语义混淆。

## 已完成的工作

### 1. 数据库结构修改
- 为sys_notice表新增deleted字段：
```sql
ALTER TABLE sys_notice ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除（0未删除 1已删除）'
```

### 2. 实体类更新
- **SysNotice.java**: 
  - 添加deleted字段属性
  - 添加getDeleted()和setDeleted()方法
  - 更新toString()方法包含deleted字段

### 3. Mapper层修改
- **SysNoticeMapper.xml**:
  - 更新resultMap添加deleted字段映射
  - 更新selectNoticeVo包含deleted字段
  - 修改selectNoticeById添加`and deleted = 0`过滤条件
  - 修改selectNoticeList添加`AND deleted = 0`过滤条件
  - 更新insert语句支持deleted字段（默认值0）
  - 将deleteNoticeByIds从物理删除改为逻辑删除（UPDATE操作）
  - 更新统计相关查询添加deleted过滤条件

### 4. Controller层修改
- **WxIndexController.java**:
  - 公告列表查询添加`and deleted = 0`过滤条件
  - 更新select字段包含deleted字段

### 5. 关联表查询更新
为确保关联查询时只显示未删除公告的相关记录，更新了以下Mapper文件：

- **SysNoticeCommentMapper.xml**:
  - selectAllCommentsByCommunity查询添加`AND n.deleted = 0`

- **SysNoticeLikeMapper.xml**:
  - selectAllLikesByCommunity查询添加`AND n.deleted = 0`

- **SysNoticeReadLogMapper.xml**:
  - selectAllReadLogsByCommunity查询添加`AND n.deleted = 0`

- **SysNoticeShareMapper.xml**:
  - selectAllSharesByCommunity查询添加`AND n.deleted = 0`

## 实现效果
1. **删除操作**：点击删除按钮时，公告不会被物理删除，而是将deleted字段设置为1
2. **查询过滤**：所有公告查询都会自动过滤已删除的记录（deleted=0）
3. **关联数据**：评论、点赞、分享等记录保留，但在管理页面查询时只显示未删除公告的相关记录
4. **前端无变化**：删除操作的前端交互保持不变，用户体验一致

## 技术要点
- 使用UPDATE语句替代DELETE语句实现逻辑删除
- 所有查询SQL都添加deleted=0过滤条件
- 关联查询通过JOIN条件过滤已删除公告
- 保持现有API接口不变，确保兼容性

## 数据一致性
- 评论、点赞、阅读记录、分享记录等子表数据保留
- 管理后台的统计和查询功能只显示未删除公告的相关数据
- 逻辑删除的公告可以通过数据库直接查询进行数据恢复

---

# 公告评论开关功能实现

## 任务描述
为公告系统新增"是否开启评论"功能，默认不开启评论。支持全局评论开关控制和单个公告评论开关控制。

## 实现逻辑
- 全局评论开关关闭：所有公告都不能评论
- 全局评论开关开启 + 单个公告开关开启：可以评论
- 全局评论开关开启 + 单个公告开关关闭：不能评论

## 已完成的工作

### 1. 数据库结构修改
- 为sys_notice表新增enable_comment字段：
```sql
ALTER TABLE sys_notice ADD COLUMN enable_comment INT(11) DEFAULT 0 COMMENT '是否开启评论（0关闭 1开启）'
```

### 2. 实体类更新
- **SysNotice.java**:
  - 添加enableComment字段属性
  - 添加getEnableComment()和setEnableComment()方法
  - 更新toString()方法包含enableComment字段

### 3. Mapper层修改
- **SysNoticeMapper.xml**:
  - 更新resultMap添加enableComment字段映射
  - 更新selectNoticeVo包含enableComment字段
  - 更新insert语句支持enableComment字段（默认值0）
  - 更新update语句支持enableComment字段

### 4. 管理后台修改
- **公告添加页面（add.html）**：添加评论开关单选框（默认关闭）
- **公告编辑页面（edit.html）**：添加评论开关单选框（绑定现有值）
- **公告列表页面（notice.html）**：添加评论开关状态列显示

### 5. 小程序API修改
- **WxIndexController.java**：
  - 公告列表和详情接口返回enableComment字段
- **WxNoticeController.java**：
  - 获取评论列表接口添加评论开关验证
  - 发表评论接口添加评论开关验证
  - 添加SysNotice导入

### 6. 功能验证
- 数据库字段添加成功，默认值为0
- 可以通过管理后台控制单个公告的评论开关
- API接口会验证评论开关状态

## 实现效果
1. **管理后台**：可以在添加/编辑公告时设置是否开启评论
2. **列表显示**：公告列表显示评论开关状态（开启/关闭）
3. **API验证**：评论相关API会检查公告评论开关状态
4. **默认行为**：新建公告默认关闭评论功能

## 全局评论开关配置
### 1. 小程序配置系统更新
- **configManager.js**：添加enable_comment默认配置（默认开启）
- **configHelper.js**：添加FeatureConfig.isCommentEnabled()方法
- **configParser.js**：添加enable_comment字段解析

### 2. 管理后台配置
- **noticesetting.html**：添加全局评论开关配置项
- **配置路径**：小区管理 → 通知公告设置 → 显示设置 → 全局评论开关
- **配置说明**：关闭后所有公告都不能评论，开启后根据单个公告设置决定

### 3. 后端API验证逻辑
- **WxNoticeController.java**：
  - 添加CommunityConfigService注入
  - 获取评论列表和发表评论都添加双重验证
  - 先检查全局开关，再检查单个公告开关

### 4. 小程序前端适配
- **detail.js**：
  - 导入FeatureConfig配置工具
  - 添加commentEnabled数据字段
  - 在获取公告详情时判断评论开关状态
  - 只在评论开启时加载评论列表
  - 为评论相关交互添加开关检查

- **detail.wxml**：
  - 评论区域添加wx:if="{{commentEnabled}}"条件显示
  - 底部评论按钮添加条件显示
  - 悬浮评论输入框添加条件显示

## 完整功能验证
### 1. 数据库验证
- enable_comment字段正常工作，默认值0（关闭）
- 可以通过管理后台和SQL更新字段值

### 2. 组合逻辑验证
- **全局关闭 + 单个开启 = 不能评论**：API返回"评论功能已被管理员关闭"
- **全局开启 + 单个关闭 = 不能评论**：API返回"该公告未开启评论功能"
- **全局开启 + 单个开启 = 可以评论**：正常显示评论功能
- **全局关闭 + 单个关闭 = 不能评论**：API返回"评论功能已被管理员关闭"

### 3. 前端显示验证
- 评论功能关闭时，小程序公告详情页面不显示评论区域
- 评论功能关闭时，底部交互栏不显示评论按钮
- 评论功能关闭时，点击相关按钮显示提示信息

## 实现效果总结
1. **管理后台**：可以设置全局评论开关和单个公告评论开关
2. **双重控制**：全局开关优先，单个公告开关次之
3. **API保护**：所有评论相关接口都有双重验证
4. **前端适配**：根据开关状态动态显示/隐藏评论功能
5. **用户体验**：开关关闭时有明确的提示信息

公告评论开关功能已完整实现，提供了灵活的评论控制能力。

---

# 附件显示布局优化

## 问题描述
关闭评论功能后，如果公告附件文件比较多，会显示在底部操作栏下面被遮挡。

## 解决方案
动态调整页面容器的底部内边距，根据评论功能开启状态设置不同的布局样式。

## 实施内容

### 1. WXML结构优化
- **detail.wxml**：为容器添加动态class `{{commentEnabled ? 'with-comment' : 'without-comment'}}`

### 2. WXSS样式优化
- **容器样式**：
  - `.container.with-comment`：有评论时底部间距120rpx
  - `.container.without-comment`：无评论时底部间距100rpx

- **附件区域样式**：
  - `.container.with-comment .attachments`：有评论时底部间距40rpx
  - `.container.without-comment .attachments`：无评论时底部间距50rpx（增加更多间距）

### 3. 布局逻辑
- 根据`commentEnabled`状态动态应用不同的CSS类
- 无评论功能时减少整体底部间距，但增加附件区域的底部间距
- 确保附件内容不被固定底部操作栏遮挡

## 实现效果
1. **有评论功能**：正常的120rpx底部间距，附件区域40rpx底部间距
2. **无评论功能**：减少到100rpx底部间距，附件区域增加到50rpx底部间距
3. **布局适配**：附件内容在任何情况下都不会被底部操作栏遮挡
4. **用户体验**：页面布局更加合理，内容显示完整

## 技术要点
- 使用动态CSS类实现响应式布局
- 针对不同功能状态设置不同的间距策略
- 保持页面整体视觉平衡和内容可访问性

附件显示布局问题已完全解决，确保了在不同评论功能状态下的良好用户体验。

---

# 公告房屋阅读统计功能

## 需求描述
为公告系统新增房屋维度的阅读统计，区分"阅读人次"和"阅读房屋数"。同一房屋读过一次公告就算一次，读十次也是一次。

## 实现方案
- 保持现有read_count字段（阅读人次）
- 新增house_read_count字段（阅读房屋数）
- sys_notice_read_log表的写入逻辑不变
- 通过SQL去重统计house_id来计算房屋阅读数

## 已完成的工作

### 1. 数据库结构修改
- 为sys_notice表新增house_read_count字段：
```sql
ALTER TABLE sys_notice ADD COLUMN house_read_count INT(11) DEFAULT 0 COMMENT '房屋阅读数（去重统计）'
```

### 2. 实体类更新
- **SysNotice.java**：
  - 添加houseReadCount字段属性
  - 添加getHouseReadCount()和setHouseReadCount()方法
  - 更新toString()方法包含houseReadCount字段

### 3. Mapper层修改
- **SysNoticeMapper.xml**：
  - 更新resultMap添加houseReadCount字段映射
  - 更新selectNoticeVo包含house_read_count字段
  - 更新insert和update语句支持houseReadCount字段
  - 新增updateNoticeHouseReadCount方法，通过去重统计更新房屋阅读数

- **SysNoticeMapper.java**：
  - 添加updateNoticeHouseReadCount方法接口

### 4. Service层修改
- **SysNoticeServiceImpl.java**：
  - 在记录阅读日志后调用updateNoticeHouseReadCount更新房屋阅读统计

### 5. API接口更新
- **WxIndexController.java**：
  - 公告列表和详情接口返回houseReadCount字段
  - 查询SQL包含house_read_count字段

### 6. 管理后台更新
- **notice.html**：
  - 阅读次数列标题改为"阅读人次"
  - 新增"阅读房屋数"列显示houseReadCount

## 统计逻辑
```sql
-- 更新房屋阅读统计的SQL
UPDATE sys_notice
SET house_read_count = (
    SELECT COUNT(DISTINCT house_id)
    FROM sys_notice_read_log
    WHERE notice_id = #{noticeId}
    AND house_id IS NOT NULL
    AND house_id != ''
)
WHERE notice_id = #{noticeId} AND deleted = 0
```

## 功能验证
- **数据库测试**：house_read_count字段正常工作
- **统计准确性**：通过去重house_id正确统计房屋阅读数
- **自动更新**：每次阅读记录后自动更新房屋阅读统计
- **接口返回**：前端可获取两个维度的阅读统计数据

## 实现效果
1. **双重统计**：
   - read_count：阅读人次（每次阅读都计数）
   - house_read_count：阅读房屋数（同一房屋只计数一次）

2. **管理后台**：显示两个统计维度，便于管理员了解公告传播效果

3. **API接口**：小程序可根据需要显示不同的统计数据

4. **数据准确性**：通过SQL去重确保房屋统计的准确性

公告房屋阅读统计功能已完整实现，为公告系统提供了更精确的阅读数据分析能力。

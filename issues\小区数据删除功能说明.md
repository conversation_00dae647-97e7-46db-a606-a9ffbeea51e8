# 小区数据删除功能说明

## ⚠️ 重要警告
**此功能将彻底删除小区的所有数据，操作不可逆！请务必谨慎使用！**

## 功能概述
提供了彻底删除某个小区所有相关数据的功能，包括：
- 房屋、业主、车辆等基础数据
- 微信小程序相关数据
- 投诉建议、维修记录等业务数据
- 文件、公告等内容数据
- 可选择是否删除小区基本信息

## API接口

### 1. 获取数据统计
**接口**: `GET /api/initCommunityData/statistics`
**说明**: 获取当前小区各表的数据统计，用于删除前确认数据量
**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "statistics": {
    "房屋信息": 150,
    "业主信息": 200,
    "车辆信息": 80,
    "楼栋信息": 12,
    "停车位信息": 180,
    "服务电话": 25,
    "微信用户": 180,
    "微信导航": 15,
    "投诉建议": 5,
    "公告通知": 10,
    "文件信息": 50
  }
}
```

### 2. 删除小区数据
**接口**: `POST /api/initCommunityData/deleteCommunityData`
**说明**: 彻底删除小区数据
**请求参数**:
```json
{
  "confirm": "DELETE_ALL_DATA",
  "includeBasicInfo": false
}
```

**参数说明**:
- `confirm`: 必须为 `"DELETE_ALL_DATA"`，用于确认删除操作
- `includeBasicInfo`: 是否删除小区基本信息（eh_community表记录），默认false

**响应示例**:
```json
{
  "code": 0,
  "msg": "成功执行XX条SQL语句"
}
```

## 删除顺序
SQL模板按照外键依赖关系设计了删除顺序：

### 第一步：删除关联关系表
- eh_house_owner_rel (房屋业主关联)
- eh_vehicle_owner_rel (车辆业主关联)
- eh_vehicle_house_rel (车辆房屋关联)
- eh_parking_owner_rel (停车位业主关联)
- eh_attachment_relation (附件关联)

### 第二步：删除业务数据表
- 微信相关数据 (eh_wx_*)
- 投诉建议 (eh_complaint)
- 维修记录 (eh_maintenance)
- 费用记录 (eh_fee)
- 支付记录 (eh_payment)
- 交易记录 (eh_tran_record)
- 公告通知 (eh_announcement)
- 服务电话 (eh_service_tel)
- 文件相关 (eh_file_*)

### 第三步：删除基础数据表
- 车辆信息 (eh_vehicle)
- 停车位信息 (eh_parking_space)
- 业主信息 (eh_owner)
- 房屋信息 (eh_house_info)
- 单元信息 (eh_unit)
- 楼栋信息 (eh_building)

### 第四步：删除小区基本信息（可选）
- 小区信息 (eh_community)

## 使用流程

### 1. 删除前准备
```bash
# 1. 获取数据统计，确认数据量
curl -X GET "http://localhost:8066/api/initCommunityData/statistics" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. 备份数据（强烈建议）
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 执行删除（仅删除业务数据，保留小区基本信息）
```bash
curl -X POST "http://localhost:8066/api/initCommunityData/deleteCommunityData" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "confirm": "DELETE_ALL_DATA",
    "includeBasicInfo": false
  }'
```

### 3. 彻底删除（包含小区基本信息）
```bash
curl -X POST "http://localhost:8066/api/initCommunityData/deleteCommunityData" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "confirm": "DELETE_ALL_DATA",
    "includeBasicInfo": true
  }'
```

### 4. 删除后检查
```sql
-- 检查是否还有残留数据
SELECT 'eh_house_info' as table_name, COUNT(*) as count FROM eh_house_info WHERE community_id = 'YOUR_COMMUNITY_ID'
UNION ALL
SELECT 'eh_owner' as table_name, COUNT(*) as count FROM eh_owner WHERE community_id = 'YOUR_COMMUNITY_ID'
UNION ALL
SELECT 'eh_vehicle' as table_name, COUNT(*) as count FROM eh_vehicle WHERE community_id = 'YOUR_COMMUNITY_ID';
```

## 安全措施

### 1. 确认参数
必须提供 `confirm: "DELETE_ALL_DATA"` 参数才能执行删除

### 2. 事务保护
所有删除操作在一个事务中执行，任何失败都会回滚

### 3. 详细日志
记录详细的执行日志和错误信息

### 4. 权限控制
只有登录用户才能删除自己所属小区的数据

## 注意事项

### 1. 数据备份
**删除前务必备份数据！**

### 2. 员工数据
员工可能服务多个小区，SQL模板中默认不删除员工数据，如需删除请手动执行

### 3. 系统数据
部分系统表数据（如操作日志）默认不删除，可根据需要手动清理

### 4. 外键约束
如果数据库设置了外键约束，可能需要调整删除顺序

### 5. 性能考虑
大量数据删除可能耗时较长，建议在业务低峰期执行

## 故障排除

### 1. 外键约束错误
```sql
-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;
-- 执行删除操作
-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
```

### 2. 权限不足
确保数据库用户有DELETE权限

### 3. 事务超时
对于大量数据，可能需要调整事务超时时间

## 恢复数据
如果误删数据，只能通过备份恢复：
```bash
# 恢复备份
mysql -u username -p database_name < backup_file.sql
```

## 联系支持
如遇问题，请联系技术支持并提供：
- 错误日志
- 操作时间
- 小区ID
- 数据统计信息

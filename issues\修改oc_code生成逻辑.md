# 修改oc_code生成逻辑

## 任务背景
用户要求将 `oc_code` 的生成逻辑从原来的基于时间戳的16位序列号改为五位随机小写字母。

## 需求分析
- 原来使用 `Seq.getId()` 生成基于时间戳的序列号
- 需要改为生成5位随机小写字母
- 需要支持大小写参数（小写、大写、不区分大小写）

## 实施方案
在 `Seq.java` 类中添加新的静态方法 `getRandomLetters(int length, int caseType)`，支持：
- `length`: 字符串长度
- `caseType`: 大小写类型
  - 1 = 小写字母 (a-z)
  - 2 = 大写字母 (A-Z)  
  - 3 = 不区分大小写 (随机大小写混合)

## 修改内容

### 1. 修改 Seq.java
- 添加 `import java.util.Random`
- 新增 `getRandomLetters(int length, int caseType)` 方法

### 2. 修改 PmsInfoController.java
- 第141行：`Seq.getId()` → `Seq.getRandomLetters(5, 1)`

### 3. 修改 OcInfoController.java  
- 第213行：`Seq.getId()` → `Seq.getRandomLetters(5, 1)`

## 代码示例

```java
// 生成5位小写字母
String ocCode = Seq.getRandomLetters(5, 1); // 例如: "abcde"

// 生成5位大写字母
String ocCode = Seq.getRandomLetters(5, 2); // 例如: "ABCDE"

// 生成5位混合大小写字母
String ocCode = Seq.getRandomLetters(5, 3); // 例如: "AbCdE"
```

## 影响范围
- `PmsInfoController.addData()` 方法中的小区代码生成
- `OcInfoController.addData()` 方法中的小区代码生成
- 不影响其他使用 `Seq.getId()` 的地方

## 测试建议
1. 测试新增物业信息时 oc_code 是否为5位小写字母
2. 测试新增小区信息时 oc_code 是否为5位小写字母
3. 验证生成的字母是否为随机且不重复
4. 确认其他使用 Seq.getId() 的功能不受影响

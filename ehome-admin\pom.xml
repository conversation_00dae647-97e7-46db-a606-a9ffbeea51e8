<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ehome</artifactId>
        <groupId>com.ehome</groupId>
        <version>4.8.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>ehome-admin</artifactId>

    <description>
        admin服务入口
    </description>

    <dependencies>
        <!-- framework 依赖 -->
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-jfinal</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
       <!-- <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-generator</artifactId>
        </dependency>-->

        <!-- oc模块 -->
        <dependency>
            <groupId>com.ehome</groupId>
            <artifactId>ehome-oc</artifactId>
        </dependency>

    </dependencies>

</project>

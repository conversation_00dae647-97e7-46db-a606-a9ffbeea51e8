UPDATE eh_community
SET ext_json = CASE
    WHEN ext_json IS NULL OR ext_json = '' THEN
        JSON_OBJECT('notice_types', JSON_ARRAY(
            JSON_OBJECT('value', '公示公告', 'label', '公示公告', 'sort', 1),
            JSON_OBJECT('value', '物业公告', 'label', '物业公告', 'sort', 2),
            JSON_OBJECT('value', '停水停电', 'label', '停水停电', 'sort', 3),
            JSON_OBJECT('value', '安全提醒', 'label', '安全提醒', 'sort', 4),
            JSON_OBJECT('value', '社区活动', 'label', '社区活动', 'sort', 5),
            JSON_OBJECT('value', '收费通知', 'label', '收费通知', 'sort', 6),
            JSON_OBJECT('value', '维修通知', 'label', '维修通知', 'sort', 7),
            JSON_OBJECT('value', '紧急通知', 'label', '紧急通知', 'sort', 8)
        ))
    ELSE
        JSON_SET(ext_json, '$.notice_types', JSON_ARRAY(
            JSON_OBJECT('value', '公示公告', 'label', '公示公告', 'sort', 1),
            JSON_OBJECT('value', '物业公告', 'label', '物业公告', 'sort', 2),
            JSON_OBJECT('value', '停水停电', 'label', '停水停电', 'sort', 3),
            JSON_OBJECT('value', '安全提醒', 'label', '安全提醒', 'sort', 4),
            JSON_OBJECT('value', '社区活动', 'label', '社区活动', 'sort', 5),
            JSON_OBJECT('value', '收费通知', 'label', '收费通知', 'sort', 6),
            JSON_OBJECT('value', '维修通知', 'label', '维修通知', 'sort', 7),
            JSON_OBJECT('value', '紧急通知', 'label', '紧急通知', 'sort', 8)
        ))
END
WHERE oc_id = '{community_id}';

import { handleError, getLoadingManager } from '../../utils/errorHandler.js'
import { getStateManager } from '../../utils/stateManager.js'
import { getSystemInfoSyncCompat } from '../../utils/systemInfoCompat.js'
import fileAccessManager from '../../utils/fileAccessManager.js'

const app = getApp()
const loadingManager = getLoadingManager()
const stateManager = getStateManager()

Page({
  data: {
    loading: true,
    activeKey: 0,
    categories: [],
    currentCategory: null,
    statusBarHeight: 0,
    communityInfo: {
      communityName: '智慧小区'
    },
    sidebarCollapsed: false,
    // 二级菜单相关
    hasSecondLevel: false,
    activeTabIndex: 0,
    tabList: [],
    tabRows: [], // 用于换行显示的tab行数据
    currentTabContent: []
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = getSystemInfoSyncCompat()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })
    this.initializePage()
  },

  async onShow() {
    // 设置tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({ selected: 1 })
    }

    // 检查是否需要刷新数据
    if (stateManager.checkAndClearRefresh()) {
      console.log('[Service] 检测到全局刷新标记，重新获取数据')
      await this.getPropertyServices()
    }

    // 更新页面状态
    this.updatePageState()
  },

  // 初始化页面
  async initializePage() {
    try {
      await this.getPropertyServices()
      // 更新页面状态
      this.updatePageState()
    } catch (error) {
      handleError(error, '页面初始化')
    }
  },

  // 更新页面状态
  updatePageState() {
    try {
      const state = stateManager.getState()
      // 更新社区信息
      this.setData({
        communityInfo: state.communityInfo || this.data.communityInfo
      })
    } catch (error) {
      console.warn('[Service] 更新页面状态失败:', error)
    }
  },

  // 获取物业服务数据
  async getPropertyServices() {
    try {
      loadingManager.show('加载中...')
      
      const res = await app.request({
        url: '/api/wx/data/getPropertyServices',
        method: 'POST',
        data: {
          source: 'infoNav' // 使用infoNav来源
        }
      })
      
      if (res.code === 0 && res.data) {
        const categories = res.data.categories || []

        // 检查是否有二级菜单
        const hasSecondLevel = categories.some(cat => cat.hasChildren)

        // 选择默认分类：如果有二级菜单，优先选择第一个有子菜单的分类
        let defaultCategory = null
        let defaultActiveKey = 0

        if (categories.length > 0) {
          if (hasSecondLevel) {
            // 有二级菜单时，选择第一个有子菜单的分类
            const hasChildrenIndex = categories.findIndex(cat => cat.hasChildren)
            defaultActiveKey = hasChildrenIndex >= 0 ? hasChildrenIndex : 0
            defaultCategory = categories[defaultActiveKey]
          } else {
            // 没有二级菜单时，选择第一个分类
            defaultActiveKey = 0
            defaultCategory = categories[0]
          }
        }



        this.setData({
          categories: categories,
          activeKey: defaultActiveKey,
          currentCategory: defaultCategory,
          hasSecondLevel: hasSecondLevel
        })

        // 如果有二级菜单，初始化Tab数据
        if (hasSecondLevel && defaultCategory) {
          this.initTabData(defaultCategory)
        }
      } else {
        throw new Error(res.msg || '获取信息公开数据失败')
      }
    } catch (error) {
      handleError(error, '获取信息公开数据')
      this.showErrorState()
    } finally {
      this.setData({ loading: false })
      loadingManager.hide()
    }
  },



  // 处理分类切换
  handleCategoryChange(e) {
    const activeKey = e.detail // 这是索引，不是ID
    const categories = this.data.categories
    const selectedCategory = categories[activeKey] // 直接使用索引获取分类



    this.setData({
      activeKey: activeKey,
      currentCategory: selectedCategory
    })

    // 如果有二级菜单，重新初始化Tab数据
    if (this.data.hasSecondLevel && selectedCategory) {
      this.initTabData(selectedCategory)
    }
  },

  // 初始化Tab数据
  initTabData(category) {
    if (!category.hasChildren) {
      // 一级菜单，清空Tab数据
      this.setData({
        tabList: [],
        tabRows: [],
        activeTabIndex: 0,
        currentTabContent: category.content || []
      })
      return
    }

    // 二级菜单，构建Tab列表（不包含"全部"Tab）
    const tabList = []

    // 添加子菜单作为Tab
    if (category.children && category.children.length > 0) {
      category.children.forEach((child, index) => {
        tabList.push({
          title: child.text,
          key: child.id,
          data: child,
          originalIndex: index, // 保存原始索引
          displayIndex: index + 1 // 显示序号（从1开始）
        })
      })
    }

    // 将tabList分组为行，根据tab数量动态调整每行显示数量
    const tabRows = []
    let itemsPerRow = 3

    // 根据tab总数优化布局
    if (tabList.length <= 4) {
      itemsPerRow = 2 // 4个以内时每行显示2个，更美观
    } else if (tabList.length <= 6) {
      itemsPerRow = 3 // 6个以内时每行显示3个
    } else {
      itemsPerRow = 3 // 超过6个时每行显示3个
    }

    for (let i = 0; i < tabList.length; i += itemsPerRow) {
      tabRows.push(tabList.slice(i, i + itemsPerRow))
    }



    // 默认显示第一个Tab的内容
    let defaultContent = []
    if (tabList.length > 0 && tabList[0].data) {
      defaultContent = tabList[0].data.content || []
    }



    this.setData({
      tabList: tabList,
      tabRows: tabRows,
      activeTabIndex: 0,
      currentTabContent: defaultContent
    })
  },

  // Tab切换
  handleTabChange(e) {
    const index = e.detail.index
    const tab = this.data.tabList[index]

    // 显示选中Tab的内容
    const content = tab.data ? (tab.data.content || []) : []

    this.setData({
      activeTabIndex: index,
      currentTabContent: content
    })
  },

  // Grid Tab切换（用于超过2个tab的情况）
  handleGridTabChange(e) {
    const index = e.currentTarget.dataset.index
    const tab = this.data.tabList[index]

    // 显示选中Tab的内容
    const content = tab.data ? (tab.data.content || []) : []

    this.setData({
      activeTabIndex: index,
      currentTabContent: content
    })
  },

  // 自定义Tab切换（用于新的换行布局）
  handleCustomTabChange(e) {
    const index = e.currentTarget.dataset.index
    const tab = this.data.tabList[index]

    // 显示选中Tab的内容
    const content = tab.data ? (tab.data.content || []) : []

    this.setData({
      activeTabIndex: index,
      currentTabContent: content
    })
  },

  // 处理PDF项点击（从模板调用）
  previewPdf(e) {
    const item = e.currentTarget.dataset.item
    this.handlePdfPreview(item)
  },

  // 处理文本项点击（从模板调用）
  showTextContent(e) {
    const item = e.currentTarget.dataset.item
    this.handleTextContent(item)
  },

  // 预览PDF文件
  async handlePdfPreview(item) {
    // 优先使用fileId，如果没有则使用pdf_file_path
    const fileId = item.fileId;
    const fallbackUrl = item.pdf_file_path
    const fileName = item.pdf_file || item.text

    if (!fileId && !fallbackUrl) {
      wx.showToast({
        title: 'PDF文件路径无效',
        icon: 'none'
      })
      return
    }

    try {
      loadingManager.show('下载中...')

      // 使用新的智能预览方法，基于文件ID
      await fileAccessManager.previewFile(fileId, fallbackUrl, fileName)

      wx.showToast({
        title: '打开成功',
        icon: 'success'
      })

    } catch (error) {
      handleError(error, 'PDF预览')
    } finally {
      loadingManager.hide()
    }
  },

  // 显示文本内容
  handleTextContent(item) {
    if (!item.content) {
      wx.showToast({
        title: '暂无内容',
        icon: 'none'
      })
      return
    }

    // 跳转到内容详情页面
    wx.navigateTo({
      url: `/pages/menu/content?id=${item.id}`
    })
  },



  // 显示错误状态
  showErrorState() {
    wx.showModal({
      title: '加载失败',
      content: '物业服务数据加载失败，请重试',
      showCancel: true,
      cancelText: '返回',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.initializePage()
        } else {
          wx.navigateBack()
        }
      }
    })
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''
    
    try {
      const date = new Date(timeStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      
      return `${year}.${month}.${day} ${hour}:${minute}`
    } catch (error) {
      return timeStr
    }
  },

  // 跳转到房屋列表
  goToHouseList() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  },

  // 切换侧边栏折叠状态
  toggleSidebar() {
    this.setData({
      sidebarCollapsed: !this.data.sidebarCollapsed
    })
  }
})

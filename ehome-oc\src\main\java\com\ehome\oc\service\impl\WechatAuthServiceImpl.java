package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.CacheUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.utils.WxCryptUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IWechatAuthService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


import java.util.HashMap;
import java.util.Map;

@Service
public class WechatAuthServiceImpl implements IWechatAuthService {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    private static final String WECHAT_CACHE = "wechat-cache";
    private static final String ACCESS_TOKEN_KEY = "access_token";

    @Value("${wechat.appid}")
    private String appId;

    @Value("${wechat.secret}")
    private String secret;

    @Autowired
    private RestTemplate restTemplate;

    // 微信接口地址
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";
    private static final String PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";



    private Record wechatUserAdd(WxUser wechatUser){
        String openid = wechatUser.getOpenId();
        Record record = Db.findFirst("select * from eh_wx_user where openid = ?", openid);
        if(record == null){
            Record record1 = new Record().set("openid", openid);
            record1.set("nickname", wechatUser.getNickName());
            record1.set("avatar_url", wechatUser.getAvatarUrl());
            Db.save("eh_wx_user", "user_id", record1);
        } else {
            Record record2 = new Record().set("openid", openid);
            record2.set("user_id", record.getLong("id"));
            record2.set("nickname", wechatUser.getNickName());
            record2.set("avatar_url", wechatUser.getAvatarUrl());
            Db.update("eh_wx_user", "user_id", record2);
        }
        return Db.findFirst("select * from eh_wx_user where openid = ?", openid);
    }

    @Override
    public AjaxResult decryptPhoneNumber(String code, String phoneCode) {
        try {
            // 1. 获取接口调用凭证access_token
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                return AjaxResult.error("获取access_token失败");
            }

            // 2. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 3. 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", phoneCode);

            // 4. 发送请求获取手机号
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            String url = PHONE_NUMBER_URL + "?access_token=" + accessToken;
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            // 5. 解析响应
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.getInteger("errcode") == 0) {
                JSONObject phoneInfo = jsonResponse.getJSONObject("phone_info");
                String phoneNumber = phoneInfo.getString("phoneNumber");

                Map<String, Object> result = new HashMap<>();
                result.put("phoneNumber", phoneNumber);
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("获取手机号失败: " + jsonResponse.getString("errmsg"));
            }
        } catch (Exception e) {
            return AjaxResult.error("获取手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信接口调用凭证
     */
    private String getAccessToken() {
        try {
            // 先从缓存获取
            String accessToken = (String) CacheUtils.get(WECHAT_CACHE, ACCESS_TOKEN_KEY);
            if (StringUtils.isNotEmpty(accessToken)) {
                return accessToken;
            }

            // 缓存中没有，则请求微信服务器获取
            Map<String, String> params = new HashMap<>();
            params.put("appid", appId);
            params.put("secret", secret);

            ResponseEntity<String> response = restTemplate.getForEntity(
                ACCESS_TOKEN_URL,
                String.class,
                params
            );

            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.containsKey("access_token")) {
                accessToken = jsonResponse.getString("access_token");
                // 存入缓存
                CacheUtils.put(WECHAT_CACHE, ACCESS_TOKEN_KEY, accessToken);
                return accessToken;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public AjaxResult updateUserPhone(String userId,String phoneNumber) {
        try {
            Db.update("update eh_wx_user set mobile = ? where user_id = ?", phoneNumber, userId);
            return AjaxResult.success("更新手机号成功");
        } catch (Exception e) {
            return AjaxResult.error("更新手机号失败: " + e.getMessage());
        }
    }

    @Override
    public AjaxResult getAuthStatus() {
        Map<String, Object> authStatus = new HashMap<>();
        authStatus.put("isHouseAuth", true);
        return AjaxResult.success(authStatus);
    }

    @Override
    public AjaxResult decryptUserInfo(String sessionKey, String encryptedData, String iv) {
        try {
            if (StringUtils.isEmpty(sessionKey) || StringUtils.isEmpty(encryptedData) || StringUtils.isEmpty(iv)) {
                return AjaxResult.error("解密参数不能为空");
            }

            String decryptedData = WxCryptUtils.decrypt(sessionKey, encryptedData, iv);
            JSONObject userInfo = JSON.parseObject(decryptedData);

            return AjaxResult.success("解密成功", userInfo);
        } catch (Exception e) {
            logger.error("解密用户信息失败: " + e.getMessage(), e);
            return AjaxResult.error("解密用户信息失败: " + e.getMessage());
        }
    }

    private String[] getOpenId(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        String responseBody = response.getBody();

        if (response.getStatusCode() == HttpStatus.OK && responseBody != null) {
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("openid");
            String sessionKey = jsonObject.getString("session_key");
            String unionId = jsonObject.getString("unionid"); // 尝试获取unionid
            return new String[]{openid, sessionKey, unionId};
        } else {
            return null;
        }
    }

}
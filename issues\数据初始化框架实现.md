# 数据初始化框架实现

## 任务背景
用户需要实现一个通用的数据初始化框架，支持多个表的初始化数据，要求能够兼容SQL注释，支持参数替换。

## 实施方案
采用基于SQL模板文件的通用初始化框架，核心特性：
- SQL模板文件管理
- 参数替换（{参数名}格式）
- 注释兼容（支持--和/* */）
- 事务安全
- 重复检查

## 实施内容

### 1. 目录结构
```
ehome-web/src/main/resources/sql-templates/
├── init_service_tel.sql     # 服务电话初始化
├── init_wx_nav.sql          # 微信导航初始化
└── init_notice_types.sql    # 公告类型初始化
```

### 2. 核心工具类

#### SqlTemplateUtils.java
- 位置：`ehome-common/src/main/java/com/ehome/common/utils/sql/SqlTemplateUtils.java`
- 功能：
  - 读取classpath下的SQL模板文件
  - 参数替换（{community_id}等）
  - 移除SQL注释（--单行注释和/* */块注释）
  - 按分号分割SQL语句
  - 模板存在性检查

#### SqlExecutorUtils.java
- 位置：`ehome-common/src/main/java/com/ehome/common/utils/sql/SqlExecutorUtils.java`
- 功能：
  - 批量SQL执行
  - 事务管理（全部成功或全部回滚）
  - 执行结果封装
  - 错误信息收集
  - 数据存在性检查

### 3. 服务层

#### IDataInitService.java
- 位置：`ehome-oc/src/main/java/com/ehome/oc/service/IDataInitService.java`
- 功能：定义数据初始化服务接口

#### DataInitServiceImpl.java
- 位置：`ehome-oc/src/main/java/com/ehome/oc/service/impl/DataInitServiceImpl.java`
- 功能：
  - 各种表的初始化方法
  - 重复初始化检查
  - 批量初始化支持
  - 初始化状态查询

### 4. 控制器层

#### InitCommunityDataController.java
- 位置：`ehome-oc/src/main/java/com/ehome/oc/controller/common/InitCommunityDataController.java`
- 功能：
  - `/api/initCommunityData/initServiceTel` - 初始化服务电话
  - `/api/initCommunityData/initWxNav` - 初始化微信导航
  - `/api/initCommunityData/initNoticeTypes` - 初始化公告类型
  - `/api/initCommunityData/initAll` - 批量初始化
  - `/api/initCommunityData/status` - 获取初始化状态
  - `/api/initCommunityData/templates` - 获取可用模板
  - `/api/initCommunityData/statistics` - 获取数据统计
  - `/api/initCommunityData/deleteCommunityData` - 删除小区数据（⚠️危险操作）

### 5. SQL模板文件

#### init_service_tel.sql
- 从原有的`sql/init_service_tel.sql`移动而来
- 包含服务电话的完整初始化数据
- 支持{community_id}参数替换

#### init_wx_nav.sql
- 微信小程序导航菜单初始化
- 包含首页导航、顶部菜单、服务中心菜单等
- 支持多种导航类型和来源

#### init_notice_types.sql
- 公告类型字典数据初始化
- 包含系统公告、物业公告、安全提醒等类型
- 同时初始化公告状态和优先级字典

#### delete_community_data.sql
- **⚠️ 危险操作**: 彻底删除小区所有数据
- 按外键依赖关系设计删除顺序
- 支持选择是否删除小区基本信息
- 包含详细的安全提示和使用说明

## 使用方法

### 1. 单表初始化
```javascript
// 初始化服务电话
POST /api/initCommunityData/initServiceTel

// 初始化微信导航
POST /api/initCommunityData/initWxNav

// 初始化公告类型
POST /api/initCommunityData/initNoticeTypes
```

### 2. 批量初始化
```javascript
// 初始化所有数据
POST /api/initCommunityData/initAll

// 初始化指定模板
POST /api/initCommunityData/initAll
{
  "templates": ["init_service_tel.sql", "init_wx_nav.sql"]
}
```

### 3. 状态查询
```javascript
// 获取初始化状态
GET /api/initCommunityData/status

// 获取可用模板
GET /api/initCommunityData/templates
```

## 技术特性

### 1. 注释兼容
- 支持`--`单行注释
- 支持`/* */`块注释
- 自动过滤注释内容

### 2. 参数替换
- 使用`{参数名}`格式
- 自动转义单引号防止SQL注入
- 支持任意参数数量

### 3. 事务安全
- 每个模板文件的SQL在一个事务中执行
- 任何SQL失败都会回滚整个事务
- 详细的错误信息记录

### 4. 重复检查
- 执行前检查数据是否已存在
- 避免重复初始化相同数据
- 支持自定义检查逻辑

### 5. 扩展性
- 新增表初始化只需添加SQL模板文件
- 支持复杂的初始化逻辑
- 模板文件可打包到JAR中

## 注意事项

1. SQL模板文件必须放在`ehome-web/src/main/resources/sql-templates/`目录下
2. 参数替换使用`{参数名}`格式，参数名不能包含特殊字符
3. 每个SQL语句必须以分号结尾
4. 建议在生产环境使用前先在测试环境验证
5. 如果表结构发生变化，需要相应更新SQL模板文件

## 优化更新

### 1. SQL模板优化
- **init_service_tel.sql**: 简化了服务电话分类，重点关注紧急服务、公共服务和便民服务
- **init_notice_types.sql**: 改为更新eh_community表的ext_json字段，符合项目实际架构
- **init_wx_nav.sql**: 提供了完整的微信小程序导航菜单结构

### 2. 数据检查逻辑优化
- 公告类型检查改为检查eh_community表的ext_json字段中的notice_types
- 使用JSON_EXTRACT函数检查JSON字段内容
- 移除了未使用的导入

### 3. 注释和文档完善
- 在SQL模板中添加了详细的使用说明
- 标注了需要根据实际情况修改的内容
- 提供了备用方案以兼容不同MySQL版本

## 后续扩展建议

1. 添加更多表的初始化模板
2. 支持条件初始化（根据配置决定是否初始化某些数据）
3. 添加初始化进度显示
4. 支持初始化数据的版本管理
5. 添加初始化数据的备份和恢复功能

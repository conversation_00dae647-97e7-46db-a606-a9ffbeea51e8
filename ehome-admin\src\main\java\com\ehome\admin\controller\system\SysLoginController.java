package com.ehome.admin.controller.system;

import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.text.Convert;
import com.ehome.common.utils.ServletUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.framework.web.service.ConfigService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@Controller
public class SysLoginController extends BaseController
{
    /**
     * 是否开启记住我功能
     */
    @Value("${shiro.rememberMe.enabled: false}")
    private boolean rememberMe;

    @Autowired
    private ConfigService configService;

    /**
     * 管理员登录页面 - 角色选择页面
     */
    @GetMapping("/admin-login")
    public String adminLogin()
    {
        return "admin-login";
    }

    @GetMapping("/login")
    public String login(HttpServletRequest request, HttpServletResponse response, ModelMap mmap)
    {
        // 如果是Ajax请求，返回Json字符串。
        if (ServletUtils.isAjaxRequest(request))
        {
            return ServletUtils.renderString(response, "{\"code\":\"1\",\"msg\":\"未登录或登录超时。请重新登录\"}");
        }

        // 获取角色参数
        String role = request.getParameter("role");
        if (StringUtils.isNotEmpty(role))
        {
            mmap.put("userRole", role);
            // 根据角色设置不同的页面标题和提示信息
            switch (role)
            {
                case "supervisor":
                    mmap.put("roleTitle", "监察人登录");
                    mmap.put("roleDesc", "监察人员专用登录入口");
                    break;
                case "committee":
                    mmap.put("roleTitle", "业委会成员登录");
                    mmap.put("roleDesc", "业主委员会成员专用登录入口");
                    break;
                case "property":
                    mmap.put("roleTitle", "物业人员登录");
                    mmap.put("roleDesc", "物业管理人员专用登录入口");
                    break;
                default:
                    mmap.put("roleTitle", "系统登录");
                    mmap.put("roleDesc", "智慧物业，让生活更美好");
                    break;
            }
        }
        else
        {
            mmap.put("roleTitle", "系统登录");
            mmap.put("roleDesc", "智慧物业，让生活更美好");
        }

        // 是否开启记住我
        mmap.put("isRemembered", rememberMe);
        // 是否开启用户注册
        mmap.put("isAllowRegister", Convert.toBool(configService.getKey("sys.account.registerUser"), false));
        return "login";
    }

    @PostMapping("/login")
    @ResponseBody
    public AjaxResult ajaxLogin(String username, String password, Boolean rememberMe, String userRole, HttpServletRequest request)
    {
        UsernamePasswordToken token = new UsernamePasswordToken(username, password, rememberMe);
        Subject subject = SecurityUtils.getSubject();
        try
        {
            subject.login(token);

            // 如果有角色信息，可以在这里进行角色相关的处理
            if (StringUtils.isNotEmpty(userRole))
            {
                // 将角色信息存储到session中，供后续使用
                request.getSession().setAttribute("userRole", userRole);

                // 可以根据角色进行不同的处理逻辑
                switch (userRole)
                {
                    case "supervisor":
                        // 监察人登录后的特殊处理
                        break;
                    case "committee":
                        // 业委会成员登录后的特殊处理
                        break;
                    case "property":
                        // 物业人员登录后的特殊处理
                        break;
                    default:
                        break;
                }
            }

            return success();
        }
        catch (AuthenticationException e)
        {
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage()))
            {
                msg = e.getMessage();
            }
            return error(msg);
        }
    }

    @GetMapping("/unauth")
    public String unauth()
    {
        return "error/unauth";
    }
}

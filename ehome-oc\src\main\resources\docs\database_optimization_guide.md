# 数据库优化后的代码适配指南

## 概述

本文档说明了数据库优化后需要修改的Java代码，主要涉及金额字段类型变更和时间字段统一。

## 1. 金额字段优化影响

### 1.1 数据库变更
- **变更前**: `bigint(20)` 存储分（如：10000 表示 100.00元）
- **变更后**: `decimal(10,2)` 存储元（如：100.00 表示 100.00元）

### 1.2 需要修改的代码

#### ChargeBillService.java
```java
// 修改前：需要乘以100转换为分
long amountFen = billAmount.multiply(new BigDecimal(100)).longValue();
bill.set("amount", amountFen);

// 修改后：直接存储元
bill.set("amount", billAmount);
```

#### BillCreateRequest.java
```java
// 修改前：
private BigDecimal billAmount; // 元，需要在存储时转换为分

// 修改后：
private BigDecimal billAmount; // 元，直接存储
```

#### 前端显示逻辑
```javascript
// 修改前：需要除以100显示
var displayAmount = data.amount / 100;

// 修改后：直接显示
var displayAmount = data.amount;
```

## 2. 时间字段优化影响

### 2.1 数据库变更
- **统一使用**: `DATETIME` 类型
- **移除**: `varchar(19)` 和 `bigint(20)` 时间字段

### 2.2 需要修改的代码

#### 时间设置逻辑
```java
// 修改前：
bill.set("create_time", DateUtils.getTime()); // 返回字符串
bill.set("update_time", System.currentTimeMillis() / 1000); // 时间戳

// 修改后：
bill.set("create_time", new Date()); // 直接使用Date对象
bill.set("update_time", new Date());
```

## 3. 具体修改清单

### 3.1 ChargeBillService.java 修改

```java
/**
 * 通用的账单创建方法（优化后）
 */
private Long createBillRecord(BillCreateRequest request) {
    Record bill = new Record();
    
    // 设置基本信息（无需修改）
    bill.set("community_id", request.getCommunityId());
    bill.set("asset_type", request.getAssetType());
    // ... 其他基本字段
    
    // 金额信息（修改：直接存储元，不再转换）
    BigDecimal billAmount = request.getBillAmount() != null ? request.getBillAmount() : BigDecimal.ZERO;
    bill.set("amount", billAmount);           // 直接存储元
    bill.set("bill_amount", billAmount);      // 直接存储元
    bill.set("discount_amount", BigDecimal.ZERO);
    bill.set("late_money_amount", BigDecimal.ZERO);
    bill.set("deposit_amount", BigDecimal.ZERO);
    
    // 时间信息（修改：使用Date对象）
    bill.set("create_time", new Date());
    bill.set("update_time", new Date());
    bill.set("last_op_time", new Date());
    
    // 保存账单
    Db.save("eh_charge_bill", "id", bill);
    return bill.getLong("id");
}
```

### 3.2 金额计算方法修改

```java
/**
 * 计算账单金额（优化后）
 */
private BigDecimal calculateBillAmount(Record binding, Map<String, Object> billPeriod) {
    BigDecimal amount = BigDecimal.ZERO;
    
    Integer countType = binding.getInt("count_type");
    
    if (countType != null && countType == 200) {
        // 固定金额：直接使用decimal字段
        BigDecimal fixedAmount = binding.getBigDecimal("fixed_amount");
        if (fixedAmount != null) {
            amount = fixedAmount; // 直接使用，无需转换
        }
    } else if (countType != null && countType == 100) {
        // 单价*计量：直接计算
        BigDecimal price = binding.getBigDecimal("price");
        BigDecimal area = binding.getBigDecimal("total_area");
        
        if (price != null && area != null) {
            amount = price.multiply(area); // 直接计算，无需转换
        }
    }
    
    return amount;
}
```

### 3.3 查询和显示逻辑修改

```java
/**
 * 获取账单详情（优化后）
 */
public Map<String, Object> getBillDetail(Long billId) {
    Record bill = Db.findFirst("select * from eh_charge_bill where id = ?", billId);
    
    if (bill != null) {
        Map<String, Object> result = bill.toMap();
        
        // 金额字段无需转换，直接返回
        // 修改前：result.put("displayAmount", bill.getBigDecimal("amount").divide(new BigDecimal(100)));
        // 修改后：
        result.put("displayAmount", bill.getBigDecimal("amount"));
        
        return result;
    }
    
    return null;
}
```

## 4. 前端适配

### 4.1 JavaScript 修改

```javascript
// 金额显示（修改前）
function formatAmount(amount) {
    return (amount / 100).toFixed(2) + '元';
}

// 金额显示（修改后）
function formatAmount(amount) {
    return parseFloat(amount).toFixed(2) + '元';
}

// 金额输入验证（修改前）
function validateAmount(inputAmount) {
    var amountInFen = Math.round(inputAmount * 100);
    return amountInFen;
}

// 金额输入验证（修改后）
function validateAmount(inputAmount) {
    return parseFloat(inputAmount).toFixed(2);
}
```

## 5. 测试验证

### 5.1 单元测试更新

```java
@Test
public void testBillAmountCalculation() {
    // 测试金额计算（修改后）
    BillCreateRequest request = new BillCreateRequest();
    request.setBillAmount(new BigDecimal("100.50")); // 直接设置元
    
    Long billId = chargeBillService.createBillRecord(request);
    
    Record bill = Db.findFirst("select amount from eh_charge_bill where id = ?", billId);
    assertEquals(new BigDecimal("100.50"), bill.getBigDecimal("amount"));
}
```

### 5.2 集成测试

1. **账单创建测试**：验证金额正确存储
2. **账单查询测试**：验证金额正确显示
3. **时间字段测试**：验证时间格式统一
4. **前端显示测试**：验证页面金额显示正确

## 6. 部署注意事项

1. **数据备份**：执行优化脚本前务必备份数据
2. **分步执行**：建议在测试环境先执行验证
3. **代码同步**：数据库优化和代码修改需要同步部署
4. **回滚准备**：准备回滚方案以应对紧急情况

## 7. 性能影响评估

### 7.1 优势
- **精度提升**：`decimal(10,2)` 避免浮点数精度问题
- **可读性**：直接存储元，便于理解和维护
- **一致性**：时间字段类型统一，减少转换错误

### 7.2 注意事项
- **存储空间**：`decimal` 比 `bigint` 占用更多空间
- **计算性能**：`decimal` 计算比整数稍慢
- **索引影响**：需要重建相关索引

## 8. 总结

数据库优化后，代码将更加简洁和可维护：
- 金额处理逻辑简化，减少转换错误
- 时间字段统一，提高数据一致性
- 代码可读性提升，便于后续维护

建议在测试环境充分验证后再部署到生产环境。

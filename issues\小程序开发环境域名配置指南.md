# 小程序开发环境域名配置指南

## 问题描述

在小程序开发过程中，当使用本地开发服务器（如 `localhost:8066`）时，会遇到以下错误：

```
downloadFile 合法域名校验出错
http://localhost:8066 不在以下 downloadFile 合法域名列表中
```

这是微信小程序的安全机制，要求所有网络请求的域名都必须在小程序管理后台配置的合法域名列表中。

## 解决方案

### 方案1：开发工具设置（推荐用于开发环境）

1. 打开微信开发者工具
2. 点击右上角的"详情"按钮
3. 在"本地设置"选项卡中，勾选：
   - ✅ **不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书**
4. 重新编译项目

**优点**：快速解决开发环境问题
**缺点**：仅在开发工具中有效，真机预览和发布版本仍需配置合法域名

### 方案2：配置合法域名（推荐用于生产环境）

1. 登录微信公众平台（mp.weixin.qq.com）
2. 进入小程序管理后台
3. 导航到：开发管理 → 开发设置 → 服务器域名
4. 在"downloadFile合法域名"中添加你的域名
5. 保存配置

**注意事项**：
- 域名必须是 HTTPS 协议
- 不能使用 IP 地址
- 不能使用 localhost 或内网地址
- 配置后需要重新发布小程序

### 方案3：使用外网域名

将本地开发服务器部署到外网，或使用内网穿透工具：

#### 使用 ngrok（推荐）
```bash
# 安装 ngrok
npm install -g ngrok

# 启动内网穿透
ngrok http 8066
```

#### 使用 frp
```bash
# 配置 frp 客户端
./frpc -c frpc.ini
```

#### 使用花生壳等工具
按照相应工具的文档配置内网穿透

### 方案4：修改开发配置

在开发环境中使用测试域名：

```javascript
// config/dev.js
const config = {
  // 开发环境使用测试域名
  baseUrl: 'https://test-api.yourdomain.com',
  // 生产环境使用正式域名
  // baseUrl: 'https://api.yourdomain.com'
}
```

## fileAccessManager.js 的改进

我们已经在 `fileAccessManager.js` 中添加了开发环境的智能提示：

### 自动检测和提示
```javascript
// 自动检测开发环境和本地域名
const isDevEnv = __wxConfig && __wxConfig.envVersion === 'develop'
const isLocalDomain = hostname === 'localhost' || hostname.includes('127.0.0.1')

if (isDevEnv && isLocalDomain) {
  console.warn('⚠️ 开发环境警告: 本地域名可能不在小程序合法域名列表中')
}
```

### 错误处理增强
```javascript
// 检查是否为域名校验错误
if (errMsg.includes('合法域名') || errMsg.includes('domain')) {
  const errorMessage = `下载失败: 域名校验错误，请确保域名已添加到合法域名列表中`
  // 显示配置提示弹窗
  this.showDomainTips(url)
}
```

### 配置提示弹窗
当遇到域名校验错误时，会自动显示配置指南弹窗，包含：
- 开发工具设置方法
- 管理后台配置方法
- 相关文档链接

## 最佳实践

### 开发阶段
1. 使用开发工具的"不校验合法域名"选项
2. 确保代码中的域名配置是可切换的
3. 定期测试真机预览功能

### 测试阶段
1. 配置测试环境的合法域名
2. 在真机上测试所有文件下载功能
3. 验证不同网络环境下的表现

### 生产阶段
1. 配置正式环境的合法域名
2. 确保所有域名都使用 HTTPS
3. 监控文件下载的成功率

## 常见问题

### Q: 为什么配置了合法域名还是报错？
A: 可能的原因：
- 域名配置后需要重新发布小程序
- 检查域名是否包含端口号（不允许）
- 确认使用的是 HTTPS 协议

### Q: 可以配置多个域名吗？
A: 可以，在管理后台可以配置多个合法域名，每行一个

### Q: 开发工具设置对真机有效吗？
A: 不会，开发工具的设置仅在开发工具中有效，真机预览和发布版本仍需配置合法域名

### Q: 如何在代码中判断是否为开发环境？
A: 可以使用：
```javascript
const isDevEnv = __wxConfig && __wxConfig.envVersion === 'develop'
```

## 相关文档

- [微信小程序网络请求文档](https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html)
- [服务器域名配置](https://developers.weixin.qq.com/miniprogram/dev/framework/ability/network.html#%E6%9C%8D%E5%8A%A1%E5%99%A8%E5%9F%9F%E5%90%8D%E9%85%8D%E7%BD%AE)
- [开发者工具配置](https://developers.weixin.qq.com/miniprogram/dev/devtools/settings.html)

## 总结

域名配置是小程序开发中的常见问题，通过合理的配置和代码优化，可以在保证安全性的同时提供良好的开发体验。建议：

1. **开发环境**：使用开发工具的域名校验关闭功能
2. **测试环境**：配置测试域名，确保功能正常
3. **生产环境**：配置正式域名，监控下载成功率
4. **代码层面**：添加智能提示和错误处理，提升开发体验

<view class="dashboard-container">
  <view class="header">
    <text class="title">{{title}}</text>
  </view>
  
  <view class="stats-grid">
    <view class="stat-item" bindtap="goToOrders">
      <text class="stat-number">{{stats.pendingOrders}}</text>
      <text class="stat-label">待处理工单</text>
    </view>
    
    <view class="stat-item" bindtap="goToPatrol">
      <text class="stat-number">{{stats.todayPatrol}}</text>
      <text class="stat-label">今日巡检</text>
    </view>
    
    <view class="stat-item">
      <text class="stat-number">{{stats.monthlyRevenue}}</text>
      <text class="stat-label">本月收入</text>
    </view>
  </view>
  
  <view class="quick-actions">
    <text class="section-title">快捷操作</text>
    <view class="action-grid">
      <view class="action-item" bindtap="goToOrders">
        <text class="action-text">工单管理</text>
      </view>
      <view class="action-item" bindtap="goToPatrol">
        <text class="action-text">巡检记录</text>
      </view>
    </view>
  </view>
</view>

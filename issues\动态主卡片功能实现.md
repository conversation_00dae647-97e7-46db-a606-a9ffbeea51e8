# 动态主卡片功能实现

## 需求描述
根据getMenus接口的top_show字段动态显示main-cards区域，支持菜单名称、图标、remark描述和点击事件。如果top_show=1的菜单不足2个，则用默认菜单补充；如果超过2个，则取前2个。

## 实现方案

### 1. 数据结构设计
将原有的静态菜单转换为JSON默认数据：
```javascript
const DEFAULT_MAIN_CARDS = [
  {
    nav_id: 'default_repair',
    nav_name: '报事报修',
    icon_name: 'setting-o',
    remark: '一键维修',
    tap_name: 'goToRepair',
    nav_type: 'function',
    cardStyle: 'repair-card',
    isDefault: true
  },
  {
    nav_id: 'default_contact',
    nav_name: '联系物业', 
    icon_name: 'service',
    remark: '一键搞定',
    tap_name: 'goToPropertyPhone',
    nav_type: 'function',
    cardStyle: 'contact-card',
    isDefault: true
  }
]
```

### 2. 核心逻辑实现

#### processMainCards方法
处理主卡片数据的核心逻辑：
- 筛选top_show=1的菜单，按sort排序，最多取2个
- 根据数量决定显示策略：
  - 2个：全部使用动态菜单
  - 1个：1个动态+1个默认
  - 0个：全部使用默认菜单

#### handleMainCardTap方法
统一处理主卡片点击事件：
- 默认功能卡片：直接调用对应方法
- 动态菜单卡片：获取详情后统一导航处理

#### handleMenuNavigation方法
统一处理菜单导航逻辑，支持：
- miniprogram：小程序跳转
- url：webview跳转
- page：内置页面跳转
- text/pdf：内容页面跳转

### 3. 前端模板修改
将静态的main-cards改为动态遍历：
```xml
<view class="main-cards" wx:if="{{showBigCard}}">
  <view class="card-item {{card.cardStyle}}" 
        wx:for="{{dynamicMainCards}}" 
        wx:key="nav_id"
        wx:for-item="card"
        bindtap="handleMainCardTap"
        data-card="{{card}}">
    <view class="card-content">
      <view class="card-text">
        <text class="card-title">{{card.nav_name}}</text>
        <text class="card-desc">{{card.remark}}</text>
      </view>
      <view class="card-icon">
        <van-icon name="{{card.icon_name}}" size="48rpx" color="#fff" />
      </view>
    </view>
  </view>
</view>
```

### 4. 实现的文件修改

#### miniprogram/pages/index/index.js
- 添加DEFAULT_MAIN_CARDS常量
- 添加dynamicMainCards数据字段
- 实现processMainCards方法
- 实现filterMainCardMenus方法
- 修改getMenuList方法，集成主卡片处理和菜单过滤
- 添加handleMainCardTap方法
- 添加handleMenuNavigation通用方法
- 在onLoad中初始化默认主卡片数据

#### miniprogram/pages/index/index.wxml
- 修改main-cards区域为动态遍历
- 使用统一的数据绑定和事件处理

#### 后台管理页面
- **add.html**: 添加remark字段输入框，最多20个字符
- **edit.html**: 添加remark字段输入框，支持编辑现有描述
- **list.html**: 在首页导航列表中添加菜单描述列显示

#### 数据库结构
- **sql/add_remark_field.sql**: 添加remark字段到eh_wx_nav表

#### filterMainCardMenus方法
过滤掉已在主卡片中显示的菜单，避免重复显示：
- 获取主卡片中非默认菜单的nav_id
- 从菜单列表中过滤掉这些菜单

### 5. 功能特性
- ✅ 向后兼容：没有top_show=1菜单时显示原有默认菜单
- ✅ 灵活配置：支持1-2个动态菜单配置
- ✅ 统一样式：动态菜单使用与原有卡片相同的样式
- ✅ 统一事件：所有菜单类型都通过统一的导航处理
- ✅ 缓存支持：动态主卡片数据与菜单数据一起缓存
- ✅ 避免重复：已在主卡片显示的菜单不会在下方菜单列表中重复出现

### 6. 测试要点
1. 测试没有top_show=1菜单的情况（应显示默认菜单）
2. 测试有1个top_show=1菜单的情况（1个动态+1个默认）
3. 测试有2个top_show=1菜单的情况（2个动态菜单）
4. 测试超过2个top_show=1菜单的情况（只显示前2个）
5. 测试各种菜单类型的点击跳转功能
6. 测试缓存机制是否正常工作

## 后续优化建议
1. 可以考虑为动态菜单添加更多样式选项
2. 可以支持自定义卡片背景色配置
3. 可以添加菜单使用统计功能

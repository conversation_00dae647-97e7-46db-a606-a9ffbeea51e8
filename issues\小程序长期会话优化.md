# 小程序长期会话优化任务

## 任务目标
实现小程序会话长期不退出，只要用户不主动退出登录，就不会跳转到登录页面。

## 优化内容

### 1. Token配置优化
**文件：** `ehome-web/src/main/resources/application-dev.yml`
- ✅ 将开发环境token有效期从1小时延长到24小时
- ✅ 调整刷新阈值为4小时，确保有足够时间进行自动刷新

### 2. 前端Token管理增强
**文件：** `miniprogram/utils/tokenManager.js`
- ✅ 添加定时检查token状态功能（5分钟间隔）
- ✅ 实现智能重试机制，网络失败时不立即清除token
- ✅ 增加网络错误判断逻辑
- ✅ 添加主动刷新机制，token剩余时间少于4小时时主动刷新

### 3. 小程序生命周期管理优化
**文件：** `miniprogram/app.js`
- ✅ 在onLaunch时初始化token管理器
- ✅ 在onShow时检查后台运行时间，超过30分钟验证token状态
- ✅ 在onHide时记录隐藏时间
- ✅ 优化网络异常处理，网络错误时保持登录状态

### 4. 登录状态检查逻辑改进
**文件：** `miniprogram/pages/login/index.js`
- ✅ 优化自动登录检查，增加重试机制（最多2次）
- ✅ 网络超时时不清除token，给用户重试机会
- ✅ 添加网络错误判断方法

### 5. 状态管理器增强
**文件：** `miniprogram/utils/stateManager.js`
- ✅ 实现智能清除策略，网络问题时不清除登录状态
- ✅ 添加网络状态检查功能
- ✅ 增加最近网络错误记录判断

### 6. 后端优化
**文件：** `ehome-framework/src/main/java/com/ehome/framework/interceptor/WxTokenInterceptor.java`
- ✅ 优化自动刷新逻辑，增加异常处理
- ✅ 增加详细日志记录，便于问题排查
- ✅ 添加token状态调试信息

## 核心特性

### 自动刷新机制
- 后端：token剩余时间少于4小时时自动刷新
- 前端：定时检查token状态，主动刷新即将过期的token
- 响应头：自动检测并更新新token

### 智能重试策略
- 网络错误时不立即清除登录状态
- 最多重试3次，递增延迟重试
- 区分网络错误和认证错误

### 生命周期感知
- 小程序后台运行超过30分钟时验证token
- 启动时自动检查登录状态
- 网络恢复后自动验证

### 容错机制
- 网络异常时保持登录状态
- 智能判断是否应该清除状态
- 提供强制清除选项

## 预期效果
1. 用户正常使用时会话永不过期
2. 网络异常时保持登录状态，恢复后自动继续
3. 长时间后台运行后重新打开小程序无需重新登录
4. 只有在用户主动退出或token确实无效时才跳转登录页

## 配置说明

### Token配置参数（永不过期机制）
```yaml
token:
  expire-time: 2592000000   # 30天，极长有效期确保永不过期
  refresh-threshold: 604800000 # 7天，提前刷新确保连续性
  auto-refresh-enabled: true
```

### 关键时间参数
- Token有效期：30天（极长有效期）
- 自动刷新阈值：7天（提前刷新）
- 定时检查间隔：5分钟
- 后台运行检查阈值：30分钟
- 网络错误保护时间：1分钟

### 永不过期机制核心特性
- ✅ **永不主动清除**：系统永远不主动清除token
- ✅ **无限重试**：网络错误时无限重试，直到成功
- ✅ **用户控制**：只有用户主动操作才能退出登录
- ✅ **容错优先**：所有异常情况都优先保持登录状态
- ✅ **双重退出**：普通退出 + 紧急强制清除（长按）

## 系统重启保护机制

### 为什么系统重启不会影响用户登录：
1. **JWT Token无状态**：使用JWT token，服务器不存储会话状态
2. **本地存储持久化**：token和用户信息存储在小程序本地存储中
3. **数据库持久化**：用户认证信息存储在数据库中
4. **无服务器端会话依赖**：不依赖服务器内存或Redis会话

### 额外保护措施：
- ✅ 小程序启动时自动检测可能的系统重启
- ✅ Token管理器启动时立即进行状态检查
- ✅ 增强的token验证和自动恢复机制

### 用户退出机制
- ✅ **唯一退出方式**：mine页面点击"退出登录"按钮
- ✅ **强制清除**：退出时强制清除所有状态，绕过保护机制
- ✅ **确认弹窗**：退出前有确认弹窗，防止误操作

## 修复的关键问题
1. ✅ **配置不一致**：统一所有环境token配置为30天有效期
2. ✅ **前端阈值不一致**：统一使用7天刷新阈值
3. ✅ **违背需求的强制退出**：移除所有自动清除token的逻辑
4. ✅ **网络状态检查问题**：修复异步调用问题，默认保留状态
5. ✅ **JWT解析容错性**：解析失败时保持登录状态而非清除

## 测试建议
1. 测试长时间使用不退出登录（30天内）
2. 测试网络异常恢复后的状态保持
3. 测试后台运行后重新打开的体验
4. 测试token自动刷新的透明性
5. **测试服务器重启后用户登录状态保持**
6. **测试系统维护后用户无需重新登录**
7. **测试只有主动退出才能清除登录状态**
8. **测试紧急清除功能在异常情况下的有效性**

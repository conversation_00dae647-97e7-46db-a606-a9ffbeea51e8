package com.ehome.system.service;

import com.ehome.system.domain.SysNoticeShare;
import java.util.List;

/**
 * 公告分享记录 服务层
 * 
 * <AUTHOR>
 */
public interface ISysNoticeShareService
{
    /**
     * 查询分享记录信息
     * 
     * @param shareId 分享记录ID
     * @return 分享记录信息
     */
    public SysNoticeShare selectShareById(Long shareId);

    /**
     * 查询分享记录列表
     * 
     * @param share 分享记录信息
     * @return 分享记录集合
     */
    public List<SysNoticeShare> selectShareList(SysNoticeShare share);

    /**
     * 根据公告ID查询分享记录列表
     * 
     * @param noticeId 公告ID
     * @return 分享记录集合
     */
    public List<SysNoticeShare> selectShareByNoticeId(Long noticeId);

    /**
     * 查询指定小区所有公告的分享记录列表（包含公告标题）
     * 
     * @param share 分享记录信息（包含小区ID等查询条件）
     * @return 分享记录集合
     */
    public List<SysNoticeShare> selectAllSharesByCommunity(SysNoticeShare share);

    /**
     * 新增分享记录
     * 
     * @param share 分享记录信息
     * @return 结果
     */
    public int insertShare(SysNoticeShare share);

    /**
     * 修改分享记录
     * 
     * @param share 分享记录信息
     * @return 结果
     */
    public int updateShare(SysNoticeShare share);

    /**
     * 删除分享记录信息
     * 
     * @param shareIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteShareByIds(String shareIds);

    /**
     * 根据公告ID删除分享记录
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteShareByNoticeId(Long noticeId);
}

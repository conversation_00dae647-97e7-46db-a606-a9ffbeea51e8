const app = getApp()

Page({
  data: {
    activeTab: 'external',
    serviceTelData: null,
    externalContacts: [],
    internalContacts: [],
    // 搜索相关
    searchKeyword: '',
    filteredExternalContacts: [],
    filteredInternalContacts: [],
    loading: true,
    error: null,
    showActionSheet: false,
    actionSheetActions: [],
    currentContact: null,
    // 头像颜色数组 - 更多美观的颜色
    avatarColors: [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
      '#F39C12', '#E74C3C', '#9B59B6', '#3498DB', '#1ABC9C',
      '#2ECC71', '#F1C40F', '#E67E22', '#34495E', '#95A5A6'
    ]
  },

  onLoad() {
    this.loadServiceTelList()
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadServiceTelList()
  },

  /**
   * 加载服务电话列表
   */
  async loadServiceTelList() {
    try {
      this.setData({ loading: true, error: null })

      const res = await app.request({
        url: '/api/wx/serviceTel/list',
        method: 'GET'
      })

      if (res.code === 0) {
        const serviceTelData = res.data || {}
        console.log('接收到的数据:', serviceTelData)

        // 处理外部号码数据
        const externalContacts = this.processContactsData(serviceTelData.external || [], 'external')
        // 处理内部号码数据
        const internalContacts = this.processContactsData(serviceTelData.internal || [], 'internal')

        console.log('处理后的外部联系人:', externalContacts)
        console.log('处理后的内部联系人:', internalContacts)

        this.setData({
          serviceTelData: serviceTelData,
          externalContacts: externalContacts,
          internalContacts: internalContacts,
          filteredExternalContacts: externalContacts,
          filteredInternalContacts: internalContacts,
          loading: false
        })
      } else {
        throw new Error(res.msg || '获取服务电话列表失败')
      }
    } catch (error) {
      console.error('加载服务电话列表失败:', error)
      this.setData({
        loading: false,
        error: error.message || '加载失败，请重试',
        serviceTelData: null,
        externalContacts: [],
        internalContacts: []
      })

      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  /**
   * 处理联系人数据，转换为通讯录结构
   */
  processContactsData(contactsData, type) {
    const sections = []
    const singleItems = []

    contactsData.forEach(contact => {
      if (contact.children && contact.children.length > 0) {
        // 有子项，作为分类处理
        const childrenWithColors = contact.children.map(child => {
          return {
            ...child,
            avatarColor: this.getAvatarColor(child.service_name),
            avatarText: this.getAvatarText(child.service_name)
          }
        })

        sections.push({
          type: 'category',
          title: contact.service_name,
          items: childrenWithColors
        })
      } else {
        // 没有子项，作为独立项处理
        singleItems.push({
          ...contact,
          avatarColor: this.getAvatarColor(contact.service_name),
          avatarText: this.getAvatarText(contact.service_name)
        })
      }
    })

    // 如果有独立项，添加到sections中
    if (singleItems.length > 0) {
      const title = type === 'internal' ? '常用内部号码' : '常用外部号码'
      sections.push({
        type: 'category',
        title: title,
        items: singleItems
      })
    }

    return sections
  },

  /**
   * 根据服务名称生成固定的头像颜色
   */
  getAvatarColor(serviceName) {
    if (!serviceName) return this.data.avatarColors[0]

    let hash = 0
    for (let i = 0; i < serviceName.length; i++) {
      const char = serviceName.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    const colorIndex = Math.abs(hash) % this.data.avatarColors.length
    return this.data.avatarColors[colorIndex]
  },

  /**
   * 获取头像文字（服务名称的第一个字符）
   */
  getAvatarText(serviceName) {
    if (!serviceName) return '?'
    return serviceName.charAt(0)
  },

  /**
   * 标签卡切换
   */
  onTabChange(event) {
    console.log('标签卡切换到:', event.detail.name)
    this.setData({
      activeTab: event.detail.name
    })
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const { telNumber, serviceName } = e.currentTarget.dataset

    if (!telNumber) {
      wx.showToast({
        title: '电话号码无效',
        icon: 'error'
      })
      return
    }

    // 记录拨打日志
    this.logPhoneCall(telNumber, serviceName)

    wx.makePhoneCall({
      phoneNumber: telNumber,
      success: () => {
        console.log('拨打电话成功:', telNumber)
      },
      fail: (error) => {
        console.error('拨打电话失败:', error)
        wx.showToast({
          title: '拨打失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 显示更多操作
   */
  showMoreActions(e) {
    const { telNumber, serviceName, companyName } = e.currentTarget.dataset

    this.setData({
      currentContact: {
        telNumber,
        serviceName,
        companyName
      },
      actionSheetActions: [
        {
          name: '复制号码',
          value: 'copy_phone'
        },
        {
          name: '复制联系信息',
          value: 'copy_info'
        },
        {
          name: '保存到通讯录',
          value: 'save_contact'
        }
      ],
      showActionSheet: true
    })
  },

  /**
   * 关闭操作面板
   */
  onActionSheetClose() {
    this.setData({
      showActionSheet: false,
      currentContact: null
    })
  },

  /**
   * 选择操作
   */
  onActionSheetSelect(event) {
    const { value } = event.detail
    const contact = this.data.currentContact

    if (!contact) return

    switch (value) {
      case 'copy_phone':
        this.copyToClipboard(contact.telNumber, '号码已复制')
        break
      case 'copy_info':
        let info = `${contact.serviceName}\n${contact.telNumber}`
        if (contact.companyName) {
          info += `\n${contact.companyName}`
        }
        this.copyToClipboard(info, '联系信息已复制')
        break
      case 'save_contact':
        this.saveToPhoneContact(contact)
        break
    }

    this.onActionSheetClose()
  },

  /**
   * 复制到剪贴板
   */
  copyToClipboard(text, successMsg) {
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: successMsg,
          icon: 'success'
        })
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 保存联系人到手机通讯录
   */
  saveToPhoneContact(contact) {
    wx.addPhoneContact({
      firstName: contact.serviceName,
      mobilePhoneNumber: contact.telNumber,
      organization: contact.companyName || '',
      success: () => {
        wx.showToast({
          title: '已保存到通讯录',
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('保存到通讯录失败:', err)
        if (err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '取消保存',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '保存失败',
            icon: 'error'
          })
        }
      }
    })
  },

  /**
   * 记录拨打电话日志
   */
  logPhoneCall(telNumber, serviceName) {
    app.request({
      url: '/wx/serviceTel/logCall',
      method: 'POST',
      data: {
        telNumber: telNumber,
        serviceName: serviceName
      }
    }).then(res => {
      console.log('拨打日志记录成功')
    }).catch(err => {
      console.error('拨打日志记录失败:', err)
    })
  },

  /**
   * 重新加载
   */
  onRetry() {
    this.loadServiceTelList()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadServiceTelList().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({
      searchKeyword: keyword
    })
    this.filterContacts(keyword)
  },

  /**
   * 搜索
   */
  onSearch(event) {
    const keyword = event.detail
    this.filterContacts(keyword)
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    })
    this.filterContacts('')
  },

  /**
   * 取消搜索
   */
  onSearchCancel() {
    this.setData({
      searchKeyword: ''
    })
    this.filterContacts('')
  },

  /**
   * 过滤联系人数据
   */
  filterContacts(keyword) {
    if (!keyword) {
      // 没有搜索关键词，显示所有数据
      this.setData({
        filteredExternalContacts: this.data.externalContacts,
        filteredInternalContacts: this.data.internalContacts
      })
      return
    }

    const filterSections = (sections) => {
      return sections.map(section => {
        const filteredItems = section.items.filter(item => {
          return item.service_name.toLowerCase().includes(keyword.toLowerCase()) ||
                 item.tel_number.includes(keyword) ||
                 (item.company_name && item.company_name.toLowerCase().includes(keyword.toLowerCase()))
        })

        return {
          ...section,
          items: filteredItems
        }
      }).filter(section => section.items.length > 0) // 只保留有匹配项的分类
    }

    this.setData({
      filteredExternalContacts: filterSections(this.data.externalContacts),
      filteredInternalContacts: filterSections(this.data.internalContacts)
    })
  }
})

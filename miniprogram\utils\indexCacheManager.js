/**
 * 首页缓存管理器
 * 统一管理首页相关的所有缓存逻辑
 */

import { getStateManager } from './stateManager.js'

// 缓存类型常量
export const CACHE_TYPES = {
  NEWS: 'news',
  MENU: 'menu', 
  DICT: 'dict',
  AUTH_STATUS: 'auth_status'
}

// 缓存时间配置（毫秒）
export const CACHE_DURATION = {
  [CACHE_TYPES.NEWS]: 5 * 60 * 1000,      // 新闻：5分钟
  [CACHE_TYPES.MENU]: 30 * 60 * 1000,     // 菜单：30分钟
  [CACHE_TYPES.DICT]: 60 * 60 * 1000,     // 字典：60分钟
  [CACHE_TYPES.AUTH_STATUS]: 5 * 60 * 1000 // 认证状态：5分钟
}

// 缓存配置
const CACHE_CONFIG = {
  MAX_SIZE: 50,           // 最大缓存条目数
  CLEANUP_THRESHOLD: 40   // 清理阈值
}

/**
 * LRU缓存实现
 */
class LRUCache {
  constructor(maxSize = CACHE_CONFIG.MAX_SIZE) {
    this.maxSize = maxSize
    this.cache = new Map()
    this.accessOrder = new Map() // 记录访问顺序
  }

  get(key) {
    if (this.cache.has(key)) {
      // 更新访问时间
      this.accessOrder.set(key, Date.now())
      return this.cache.get(key)
    }
    return null
  }

  set(key, value) {
    // 如果缓存已满，删除最久未使用的项
    if (this.cache.size >= this.maxSize) {
      this._evictLRU()
    }

    this.cache.set(key, value)
    this.accessOrder.set(key, Date.now())
  }

  has(key) {
    return this.cache.has(key)
  }

  delete(key) {
    this.cache.delete(key)
    this.accessOrder.delete(key)
  }

  clear() {
    this.cache.clear()
    this.accessOrder.clear()
  }

  size() {
    return this.cache.size
  }

  // 删除最久未使用的项
  _evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  // 清理过期项
  cleanup(duration) {
    const now = Date.now()
    const keysToDelete = []

    for (const [key, item] of this.cache) {
      if (item.timestamp && (now - item.timestamp >= duration)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.delete(key))
    return keysToDelete.length
  }
}

/**
 * 首页缓存管理器
 */
class IndexCacheManager {
  constructor() {
    this.caches = {
      [CACHE_TYPES.NEWS]: new LRUCache(),
      [CACHE_TYPES.MENU]: new LRUCache(),
      [CACHE_TYPES.DICT]: new LRUCache(),
      [CACHE_TYPES.AUTH_STATUS]: new LRUCache()
    }
    
    // 定期清理过期缓存
    this._startCleanupTimer()
  }

  /**
   * 获取缓存
   * @param {string} type 缓存类型
   * @param {string} key 缓存键
   * @returns {any|null} 缓存数据或null
   */
  get(type, key) {
    if (!this.caches[type]) {
      console.warn(`[IndexCacheManager] 未知的缓存类型: ${type}`)
      return null
    }

    const fullKey = this._generateKey(type, key)
    const cached = this.caches[type].get(fullKey)

    if (!cached) {
      return null
    }

    // 检查是否过期
    const duration = CACHE_DURATION[type]
    if (duration && Date.now() - cached.timestamp >= duration) {
      this.caches[type].delete(fullKey)
      return null
    }

    console.log(`[IndexCacheManager] 缓存命中: ${type}/${key}`)
    return cached.data
  }

  /**
   * 设置缓存
   * @param {string} type 缓存类型
   * @param {string} key 缓存键
   * @param {any} data 缓存数据
   * @param {object} options 选项
   */
  set(type, key, data, options = {}) {
    if (!this.caches[type]) {
      console.warn(`[IndexCacheManager] 未知的缓存类型: ${type}`)
      return
    }

    const fullKey = this._generateKey(type, key)
    const cacheItem = {
      data,
      timestamp: Date.now(),
      ...options
    }

    this.caches[type].set(fullKey, cacheItem)
    console.log(`[IndexCacheManager] 缓存已设置: ${type}/${key}`)
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} type 缓存类型
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  has(type, key) {
    return this.get(type, key) !== null
  }

  /**
   * 删除缓存
   * @param {string} type 缓存类型
   * @param {string} key 缓存键
   */
  delete(type, key) {
    if (!this.caches[type]) {
      return
    }

    const fullKey = this._generateKey(type, key)
    this.caches[type].delete(fullKey)
    console.log(`[IndexCacheManager] 缓存已删除: ${type}/${key}`)
  }

  /**
   * 清空指定类型的所有缓存
   * @param {string} type 缓存类型
   */
  clear(type) {
    if (type && this.caches[type]) {
      this.caches[type].clear()
      console.log(`[IndexCacheManager] 已清空缓存类型: ${type}`)
    } else if (!type) {
      // 清空所有缓存
      Object.values(this.caches).forEach(cache => cache.clear())
      console.log('[IndexCacheManager] 已清空所有缓存')
    }
  }

  /**
   * 清空用户相关的所有缓存
   */
  clearUserCache() {
    const userKey = this._getUserKey()
    Object.keys(this.caches).forEach(type => {
      const cache = this.caches[type]
      const keysToDelete = []
      
      for (const [key] of cache.cache) {
        if (key.includes(userKey)) {
          keysToDelete.push(key)
        }
      }
      
      keysToDelete.forEach(key => cache.delete(key))
    })
    
    console.log(`[IndexCacheManager] 已清空用户缓存: ${userKey}`)
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    const stats = {}
    Object.keys(this.caches).forEach(type => {
      stats[type] = {
        size: this.caches[type].size(),
        maxSize: this.caches[type].maxSize
      }
    })
    return stats
  }

  /**
   * 生成完整的缓存键
   * @param {string} type 缓存类型
   * @param {string} key 原始键
   * @returns {string} 完整键
   */
  _generateKey(type, key) {
    const userKey = this._getUserKey()
    return `${userKey}_${type}_${key}`
  }

  /**
   * 获取用户标识
   * @returns {string} 用户键
   */
  _getUserKey() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()
      return state.userInfo?.userId || 'anonymous'
    } catch (error) {
      console.warn('[IndexCacheManager] 获取用户键失败:', error)
      return 'anonymous'
    }
  }

  /**
   * 启动定期清理定时器
   */
  _startCleanupTimer() {
    // 每5分钟清理一次过期缓存
    setInterval(() => {
      this._performCleanup()
    }, 5 * 60 * 1000)
  }

  /**
   * 执行缓存清理
   */
  _performCleanup() {
    let totalCleaned = 0
    
    Object.keys(this.caches).forEach(type => {
      const duration = CACHE_DURATION[type]
      const cleaned = this.caches[type].cleanup(duration)
      totalCleaned += cleaned
    })

    if (totalCleaned > 0) {
      console.log(`[IndexCacheManager] 定期清理完成，清理了 ${totalCleaned} 个过期缓存项`)
    }
  }
}

// 单例实例
let instance = null

/**
 * 获取缓存管理器实例
 * @returns {IndexCacheManager}
 */
export function getIndexCacheManager() {
  if (!instance) {
    instance = new IndexCacheManager()
  }
  return instance
}

export default IndexCacheManager

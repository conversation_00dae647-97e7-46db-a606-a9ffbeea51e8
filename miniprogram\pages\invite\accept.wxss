/* 接受邀请页面样式 */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 24rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  color: white;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 登录提示 */
.login-tip-section {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.tip-card {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 0 20rpx;
}

.tip-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin: 20rpx 0 10rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 邀请信息 */
.invite-section {
  width: 100%;
}

.invite-card {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  border: 1rpx solid #e5e7eb;
}

/* 卡片头部 */
.card-header {
  background: #f8fafc;
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f1f5f9;
}

.header-text {
  margin-left: 20rpx;
  flex: 1;
}

.invite-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.invite-subtitle {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 卡片内容 */
.card-content {
  padding: 32rpx 24rpx;
}

.house-info {
  text-align: left;
}

.house-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 26rpx;
  line-height: 1.3;
}

.divider {
  width: 100%;
  height: 1rpx;
  background: #e5e7eb;
  margin: 16rpx 0;
}

.house-meta {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.meta-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 14rpx;
}

.meta-item text {
  color: #374151;
  word-break: break-all;
  flex: 1;
}



.rel-badge {
  position: absolute;
  right: 0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}

/* 卡片操作 */
.card-actions {
  padding: 24rpx;
  background: #ffffff;
}

/* 温馨提示 */
.tips-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #e5e7eb;
}

.tips-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-title text {
  margin-left: 8rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  position: relative;
}

.tip-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background: #6366f1;
  border-radius: 50%;
}

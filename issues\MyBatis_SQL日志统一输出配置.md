# MyBatis SQL日志统一输出配置

## 任务背景
用户要求实现MyBatis的SQL日志统一输出到指定日志文件，包括MyBatis、JFinal和Druid的所有SQL日志。

## 需求分析
项目中使用了多套数据访问框架：
1. **MyBatis** - 传统Mapper接口方式
2. **JFinal ActiveRecord** - Model操作方式  
3. **Druid** - 数据源连接池的SQL监控

需要将所有SQL相关日志统一输出到一个专门的日志文件中。

## 实施方案
创建专门的SQL日志配置，统一收集所有SQL相关日志到 `sql.log` 文件。

## 修改内容

### 1. 修改 logback-spring.xml
添加了以下配置：

#### SQL日志文件输出配置
```xml
<!-- SQL日志统一输出  -->
<appender name="sql-log-file" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.path}/sql.log</file>
    <!-- 循环政策：基于文件大小创建日志文件 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
        <!-- 日志文件名格式 -->
        <fileNamePattern>${log.path}/sql.%i.log</fileNamePattern>
        <!-- 最小索引 -->
        <minIndex>1</minIndex>
        <!-- 最大索引 -->
        <maxIndex>${log.max.history}</maxIndex>
    </rollingPolicy>
    <!-- 触发政策：基于文件大小 -->
    <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
        <!-- 文件最大大小 -->
        <maxFileSize>${log.max.file.size}</maxFileSize>
    </triggeringPolicy>
    <encoder>
        <pattern>${log.pattern}</pattern>
    </encoder>
</appender>

<!-- SQL异步日志 -->
<appender name="sql-log" class="ch.qos.logback.classic.AsyncAppender">
    <appender-ref ref="sql-log-file"/>
    <queueSize>256</queueSize>
    <discardingThreshold>0</discardingThreshold>
    <includeCallerData>true</includeCallerData>
    <neverBlock>true</neverBlock>
</appender>
```

#### Logger配置
```xml
<!-- SQL日志统一输出 -->
<logger name="sql-log" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
    <appender-ref ref="console"/>
</logger>

<!-- MyBatis SQL日志 -->
<logger name="com.ehome.**.mapper" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>

<!-- MyBatis框架SQL日志 -->
<logger name="org.apache.ibatis" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>

<!-- Druid SQL日志 -->
<logger name="druid.sql.Statement" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>

<logger name="druid.sql.DataSource" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>

<!-- JFinal SQL日志 -->
<logger name="com.jfinal.plugin.activerecord" level="debug" additivity="false">
    <appender-ref ref="sql-log"/>
</logger>
```

### 2. 更新各环境配置文件

#### 开发环境 (application-dev.yml)
```yaml
logging:
  level:
    # 统一SQL日志配置
    sql-log: debug
    com.ehome.**.mapper: debug
    org.apache.ibatis: debug
    druid.sql.Statement: debug
    druid.sql.DataSource: debug
    com.jfinal.plugin.activerecord: debug
```

#### 生产环境 (application-prod.yml)
```yaml
logging:
  level:
    # 统一SQL日志配置 - 生产环境使用info级别减少日志量
    sql-log: info
    com.ehome.**.mapper: info
    org.apache.ibatis: info
    druid.sql.Statement: info
    druid.sql.DataSource: info
    com.jfinal.plugin.activerecord: info
```

#### 测试环境 (application-test.yml)
```yaml
logging:
  level:
    # 统一SQL日志配置
    sql-log: debug
    com.ehome.**.mapper: debug
    org.apache.ibatis: debug
    druid.sql.Statement: debug
    druid.sql.DataSource: debug
    com.jfinal.plugin.activerecord: debug
```

## 功能特性

### 1. 统一日志文件
- 所有SQL日志统一输出到 `sql.log` 文件
- 支持文件轮转：最大100MB，保留5个历史文件

### 2. 异步日志处理
- 使用异步appender提高性能
- 队列大小256，不阻塞主线程

### 3. 多框架支持
- **MyBatis**: 捕获Mapper接口的SQL执行日志
- **JFinal**: 捕获ActiveRecord的SQL执行日志  
- **Druid**: 捕获数据源层面的SQL监控日志

### 4. 环境差异化配置
- **开发/测试环境**: debug级别，详细SQL日志
- **生产环境**: info级别，减少日志量

## SQL执行时间增强功能

### 1. MyBatis执行时间拦截器
**文件：** `ehome-framework/src/main/java/com/ehome/framework/interceptor/SqlExecutionTimeInterceptor.java`
- ✅ 拦截所有MyBatis SQL执行
- ✅ 记录详细的执行时间信息
- ✅ 根据执行时间自动分级记录日志：
  - DEBUG: 正常SQL（<500ms）
  - INFO: 性能关注SQL（500ms-1000ms）
  - WARN: 慢SQL警告（>1000ms）
  - ERROR: 异常SQL

### 2. JFinal执行时间记录
**文件：** `ehome-jfinal/src/main/java/com/ehome/jfinal/model/BaseModel.java`
- ✅ 重写save、update、delete方法
- ✅ 添加执行时间统计
- ✅ 统一输出到SQL日志文件

**文件：** `ehome-jfinal/src/main/java/com/ehome/jfinal/utils/Db.java`
- ✅ 创建增强版Db类，包装所有原生Db静态方法
- ✅ 解决直接使用Db.save()等方法无法拦截的问题
- ✅ 提供完整的JFinal SQL执行时间记录
- ✅ 保持与原生API完全一致的使用方式

### 3. Druid执行时间配置增强
**文件：** `ehome-web/src/main/resources/application-druid.yml`
- ✅ 启用statement-executable-sql-log-enable
- ✅ 启用connection-log-enabled
- ✅ 慢SQL阈值设置为1000ms

## 日志内容示例
SQL日志文件将包含：
- **SQL语句执行记录** - 完整的SQL语句
- **参数绑定信息** - SQL参数值（自动截断过长参数）
- **执行时间统计** - 精确到毫秒的执行时间
- **结果统计** - 查询结果数量或影响行数
- **性能分级** - 根据执行时间自动分级
- **异常信息** - SQL执行异常的详细堆栈
- **数据源连接信息** - 通过Druid记录连接事件
- **慢SQL警告** - 超过阈值的SQL自动标记

## 日志输出示例

### MyBatis SQL日志示例
```
2025-01-10 14:30:25.123 [http-nio-8066-exec-1] DEBUG sql-log - [MyBatis] SQL执行 - 类型: SELECT, 方法: com.ehome.oc.mapper.OcInfoMapper.selectList, 执行时间: 45ms, 参数: {ocName=测试小区}, 结果数量: 5

2025-01-10 14:30:25.890 [http-nio-8066-exec-2] WARN sql-log - [MyBatis] SQL执行 - 类型: SELECT, 方法: com.ehome.oc.mapper.PmsInfoMapper.selectAll, 执行时间: 1250ms, 参数: {}, 结果数量: 1000 [慢SQL警告]
```

### JFinal SQL日志示例
```
2025-01-10 14:30:26.456 [http-nio-8066-exec-3] DEBUG sql-log - [JFinal] SQL执行 - 表: eh_community, 操作: save, 执行时间: 32ms, 结果: 成功

2025-01-10 14:30:26.789 [http-nio-8066-exec-4] INFO sql-log - [JFinal] SQL执行 - 表: eh_pms_info, 操作: update, 执行时间: 650ms, 结果: 成功 [性能关注]

2025-01-10 14:30:27.123 [http-nio-8066-exec-5] DEBUG sql-log - [JFinal-Enhanced] SQL执行 - 操作: find, 执行时间: 28ms, SQL: SELECT * FROM eh_owner WHERE community_id = ?, 参数: OC001, 结果: List(15条记录)
```

### Druid SQL日志示例
```
2025-01-10 14:30:27.123 [http-nio-8066-exec-5] DEBUG druid.sql.Statement - {conn-10001, pstmt-20001} executed. SELECT * FROM eh_community WHERE oc_name LIKE ? [测试%] cost 23 ms.

2025-01-10 14:30:27.456 [http-nio-8066-exec-6] WARN druid.sql.Statement - {conn-10002, pstmt-20002} slow sql 1100 ms. SELECT COUNT(*) FROM eh_owner o LEFT JOIN eh_community c ON o.community_id = c.oc_id
```

## 使用说明
1. 重启应用后，SQL日志将自动输出到 `${log.path}/sql.log` 文件
2. 可通过修改各环境配置文件中的日志级别来控制SQL日志的详细程度
3. 日志文件会自动轮转，避免单个文件过大
4. 开发调试时可查看控制台，生产环境主要依赖日志文件
5. **执行时间分级**：
   - 正常SQL（<500ms）：DEBUG级别
   - 性能关注SQL（500ms-1000ms）：INFO级别
   - 慢SQL（>1000ms）：WARN级别，自动添加警告标记
   - 异常SQL：ERROR级别，包含完整异常堆栈

## JFinal使用注意事项

### ⚠️ 重要：Db静态方法调用问题
直接使用JFinal原生的 `com.jfinal.plugin.activerecord.Db` 类的静态方法**无法被拦截**，不会记录执行时间。

**解决方案：**
1. **推荐方式**：使用增强版Db类替代原生Db类
   ```java
   // ❌ 原生Db，无法拦截执行时间
   import com.jfinal.plugin.activerecord.Db;
   Db.save("eh_community", record);

   // ✅ 增强版Db，自动记录执行时间
   import com.ehome.jfinal.utils.Db;
   Db.save("eh_community", record);  // API完全一致！
   ```

2. **Model方式**：继承BaseModel的实体类会自动记录执行时间
   ```java
   // ✅ 自动记录执行时间
   OcInfoModel model = new OcInfoModel();
   model.save();
   ```

**迁移说明：**
只需要将现有代码中的import语句从：
```java
import com.jfinal.plugin.activerecord.Db;
```
改为：
```java
import com.ehome.jfinal.utils.Db;
```
其他代码无需任何修改！

### 覆盖范围说明
- **MyBatis**: ✅ 100%覆盖，所有SQL都会被拦截
- **JFinal Model**: ✅ 继承BaseModel的实体操作会被拦截
- **JFinal Db静态方法**: ✅ 使用增强版Db类，API完全一致
- **Druid**: ✅ 数据源层面100%覆盖，但日志格式不同

## 性能监控建议
1. **生产环境**：建议将SQL日志级别设置为INFO，只记录性能关注和慢SQL
2. **开发环境**：使用DEBUG级别，记录所有SQL执行详情
3. **慢SQL优化**：定期检查WARN级别的慢SQL日志，进行性能优化
4. **异常监控**：关注ERROR级别的SQL异常，及时处理数据库问题
5. **代码规范**：建议统一使用增强版Db类替代原生Db类（只需修改import语句）

package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 房屋小程序卡片邀请控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/wx/house/card-invite")
public class HouseCardInviteController extends BaseWxController {

    private static final Logger logger = LoggerFactory.getLogger(HouseCardInviteController.class);
    
    /**
     * 创建小程序卡片邀请
     */
    @Log(title = "创建小程序卡片邀请", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createCardInvite(@RequestBody JSONObject params) {
        try {
            // 获取当前用户
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }
            
            String houseId = params.getString("houseId");
            Integer relType = params.getInteger("relType");
            String invitePhone = params.getString("invitePhone");
            String remark = params.getString("remark");

            // 参数验证
            if (StringUtils.isEmpty(houseId)) {
                return AjaxResult.error("房屋ID不能为空");
            }

            if (relType == null || (relType < 1 || relType > 3)) {
                return AjaxResult.error("关系类型无效");
            }

            if (StringUtils.isEmpty(invitePhone)) {
                return AjaxResult.error("接收人手机号不能为空");
            }

            // 验证手机号格式
            if (!invitePhone.matches("^1[3-9]\\d{9}$")) {
                return AjaxResult.error("手机号格式不正确");
            }
            
            // 验证用户是否有权限邀请该房屋
            Record houseRel = Db.findFirst(
                "SELECT * FROM eh_house_owner_rel WHERE house_id = ? AND owner_id = ? AND check_status = 1",
                houseId, currentUser.getOwnerId()
            );

            if (houseRel == null) {
                return AjaxResult.error("您没有权限邀请该房屋的住户");
            }

            // 检查该手机号是否已经关联该房屋
            Record existOwner = Db.findFirst(
                "SELECT o.owner_id FROM eh_owner o " +
                "JOIN eh_house_owner_rel r ON o.owner_id = r.owner_id " +
                "WHERE o.mobile = ? AND r.house_id = ? AND r.check_status = 1",
                invitePhone, houseId
            );

            if (existOwner != null) {
                return AjaxResult.error("该手机号用户已关联此房屋");
            }
            
            // 查询房屋信息
            Record houseInfo = Db.findFirst(
                "SELECT h.*, b.name as building_name, u.name as unit_name " +
                "FROM eh_house_info h " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "WHERE h.house_id = ?",
                houseId
            );
            
            if (houseInfo == null) {
                return AjaxResult.error("房屋信息不存在");
            }
            
            // 生成邀请令牌
            String inviteToken = UUID.randomUUID().toString();
            
            // 计算过期时间（24小时）
            Date expireTime = DateUtils.addHours(new Date(), 24);
            
            // 创建邀请记录
            Record invite = new Record();
            invite.set("invite_id", Seq.getId());
            invite.set("house_id", houseId);
            invite.set("inviter_id", currentUser.getOwnerId());
            invite.set("invite_token", inviteToken);
            invite.set("invite_phone", invitePhone);
            invite.set("invite_type", 1); // 1-小程序卡片邀请
            invite.set("rel_type", relType);
            invite.set("expire_time", expireTime);
            invite.set("status", 0); // 0-待接受
            invite.set("remark", remark);
            invite.set("create_time", new Date());
            invite.set("create_by", currentUser.getOwnerId());
            
            boolean success = Db.save("eh_house_invite", "invite_id", invite);
            
            if (success) {
                // 构建房屋名称
                String houseName = buildHouseName(houseInfo);
                
                // 构建分享信息
                JSONObject shareInfo = new JSONObject();
                shareInfo.put("inviteToken", inviteToken);
                shareInfo.put("houseId", houseId);
                shareInfo.put("relType", relType);
                shareInfo.put("houseName", houseName);
                shareInfo.put("relTypeName", getRelTypeName(relType));
                shareInfo.put("expireTime", expireTime);

                // 构建小程序分享路径 - 只传递token参数
                String sharePath = String.format("/pages/invite/accept?token=%s", inviteToken);
                shareInfo.put("sharePath", sharePath);
                
                // 构建分享标题
                String shareTitle = String.format("邀请你加入房屋 %s", houseName);
                shareInfo.put("shareTitle", shareTitle);
                
                logger.info("小程序卡片邀请创建成功，邀请令牌: {}, 房屋: {}", inviteToken, houseName);
                
                return AjaxResult.success("邀请创建成功", shareInfo);
            } else {
                return AjaxResult.error("邀请创建失败");
            }
            
        } catch (Exception e) {
            logger.error("创建小程序卡片邀请失败", e);
            return AjaxResult.error("创建邀请失败：" + e.getMessage());
        }
    }

    /**
     * 获取我发出的邀请列表
     */
    @GetMapping("/my-invites")
    public AjaxResult getMyInvites() {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            List<Record> invites = Db.find(
                "SELECT i.*, h.room, h.building_id, h.unit_id, " +
                "b.name as building_name, u.name as unit_name " +
                "FROM eh_house_invite i " +
                "LEFT JOIN eh_house_info h ON i.house_id = h.house_id " +
                "LEFT JOIN eh_building b ON h.building_id = b.building_id " +
                "LEFT JOIN eh_unit u ON h.unit_id = u.unit_id " +
                "WHERE i.inviter_id = ? AND i.invite_type = 1 " +
                "ORDER BY i.create_time DESC",
                currentUser.getOwnerId()
            );

            List<Map<String, Object>> result = new ArrayList<>();
            // 处理数据
            for (Record invite : invites) {
                // 构建房屋名称
                invite.set("house_name", buildHouseName(invite));

                // 设置关系类型名称
                invite.set("rel_type_name", getRelTypeName(invite.getInt("rel_type")));

                // 设置状态名称
                invite.set("status_name", getStatusMessage(invite.getInt("status")));

                // 格式化时间
                invite.set("create_time_formatted", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", invite.getDate("create_time")));
                invite.set("expire_time_formatted", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm", invite.getDate("expire_time")));

                // 检查是否过期并更新状态
                if (invite.getInt("status") == 0 && isExpired(invite.getDate("expire_time"))) {
                    Db.update("UPDATE eh_house_invite SET status = 3 WHERE invite_id = ?", invite.getStr("invite_id"));
                    invite.set("status", 3);
                    invite.set("status_name", "已过期");
                }
                result.add(invite.toMap());
            }

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("获取邀请列表失败", e);
            return AjaxResult.error("获取邀请列表失败：" + e.getMessage());
        }
    }

    // 工具方法
    private String buildHouseName(Record houseInfo) {
        StringBuilder name = new StringBuilder();
        
        if (StringUtils.isNotEmpty(houseInfo.getStr("building_name"))) {
            name.append(houseInfo.getStr("building_name"));
        }
        
        if (StringUtils.isNotEmpty(houseInfo.getStr("unit_name"))) {
            if (name.length() > 0) name.append("/");
            name.append(houseInfo.getStr("unit_name"));
        }
        
        if (StringUtils.isNotEmpty(houseInfo.getStr("room"))) {
            if (name.length() > 0) name.append("/");
            name.append(houseInfo.getStr("room"));
        }
        
        return name.toString();
    }
    
    private String getRelTypeName(Integer relType) {
        if (relType == null) return "";
        switch (relType) {
            case 1: return "业主";
            case 2: return "家庭成员";
            case 3: return "租户";
            default: return "未知";
        }
    }
    
    private String getStatusMessage(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 1: return "接受";
            case 2: return "拒绝";
            case 3: return "过期";
            default: return "处理";
        }
    }
    
    private boolean isExpired(Date expireTime) {
        return expireTime != null && expireTime.before(new Date());
    }
}

@echo off

REM E-Home 环境配置文件模板 (Windows)
REM 复制此文件为 env-dev.bat, env-test.bat, env-prod.bat 并修改相应的值

REM ===========================================
REM 数据库配置
REM ===========================================
set DB_HOST=*************
set DB_PORT=3306
set DB_USERNAME=smarthome
set DB_PASSWORD=your_password_here

REM ===========================================
REM 微信小程序配置
REM ===========================================
REM 开发环境
set WECHAT_DEV_APPID=wxf279bf8df3d4d470
set WECHAT_DEV_SECRET=your_dev_secret_here

REM 测试环境
set WECHAT_TEST_APPID=wxf279bf8df3d4d470
set WECHAT_TEST_SECRET=your_test_secret_here

REM 生产环境
set WECHAT_PROD_APPID=wxf279bf8df3d4d470
set WECHAT_PROD_SECRET=your_prod_secret_here

REM ===========================================
REM Token安全配置
REM ===========================================
set TOKEN_SECRET=your_complex_secret_key_here

REM ===========================================
REM 使用方法
REM ===========================================
REM 1. 复制此文件：
REM    copy env-template.bat env-prod.bat
REM
REM 2. 修改配置值：
REM    notepad env-prod.bat
REM
REM 3. 启动应用：
REM    deploy-jar.bat start prod

echo 环境变量已加载完成
echo 当前配置：
echo   数据库主机: %DB_HOST%:%DB_PORT%
echo   数据库用户: %DB_USERNAME%
echo   微信AppID: %WECHAT_PROD_APPID%
echo   Token密钥: %TOKEN_SECRET:~0,10%...

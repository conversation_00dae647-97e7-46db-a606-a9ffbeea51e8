package com.ehome.oc.mapper;

import com.ehome.oc.domain.Unit;
import java.util.List;

public interface UnitMapper {
    public Unit selectUnitById(String unitId);
    
    public List<Unit> selectUnitList(Unit unit);
    
    public List<Unit> selectUnitsByBuildingId(String buildingId);
    
    public int insertUnit(Unit unit);
    
    public int updateUnit(Unit unit);
    
    public int deleteUnitById(String unitId);
    
    public int deleteUnitByIds(String[] unitIds);
} 
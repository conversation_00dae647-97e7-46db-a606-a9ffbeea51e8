.dashboard-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.stat-item {
  flex: 1;
  background: white;
  padding: 30rpx;
  margin: 0 10rpx;
  border-radius: 10rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.quick-actions {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.action-grid {
  display: flex;
  flex-wrap: wrap;
}

.action-item {
  width: 48%;
  padding: 40rpx 20rpx;
  margin: 1%;
  background: #f8f9fa;
  border-radius: 10rpx;
  text-align: center;
}

.action-text {
  font-size: 28rpx;
  color: #333;
}

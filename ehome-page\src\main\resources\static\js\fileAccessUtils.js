/**
 * PC端文件访问工具类
 * 统一处理文件ID和URL的访问逻辑，支持OSS和本地文件
 */

var FileAccessUtils = {

    /**
     * 获取文件下载URL（用于直接下载）
     * @param {string} fileId 文件ID
     * @returns {string} 下载URL
     */
    getFileDownloadUrl: function(fileId) {
        if (!fileId) {
            throw new Error('文件ID不能为空');
        }
        return ctx + 'common/download/' + fileId;
    },

    /**
     * 预览文件
     * @param {string} fileIdOrUrl 文件ID或URL
     * @param {number} expiration 过期时间（秒），仅对文件ID有效
     * @param {string} fileName 文件名（可选，用于显示标题）
     */
    previewFile: function(fileIdOrUrl, expiration, fileName) {
        FileAccessUtils.getSmartFileUrl(fileIdOrUrl, expiration)
            .then(function(url) {
                // 使用通用的智能URL打开功能
                $.modal.openUrl(url, fileName ? '📄 ' + fileName : '文件预览');
            })
            .catch(function(error) {
                console.error('文件预览失败:', error);
                $.modal.alertError('文件预览失败: ' + error.message);
            });
    },

    /**
     * 检查是否为图片文件
     * @param {string} fileName 文件名
     * @returns {boolean}
     */
    isImageFile: function(fileName) {
        if (!fileName) return false;
        var imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        var fileExtension = fileName.split('.').pop().toLowerCase();
        return imageTypes.indexOf(fileExtension) !== -1;
    },

    /**
     * 检查是否为文档文件
     * @param {string} fileName 文件名
     * @returns {boolean}
     */
    isDocumentFile: function(fileName) {
        if (!fileName) return false;
        var docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
        var fileExtension = fileName.split('.').pop().toLowerCase();
        return docTypes.indexOf(fileExtension) !== -1;
    },

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize: function(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        
        var k = 1024;
        var sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 获取文件图标HTML
     * @param {string} fileType 文件类型
     * @returns {string} 图标HTML
     */
    getFileIconHtml: function(fileType) {
        if (!fileType) return '<i class="fa fa-file-o"></i>';
        
        var iconMap = {
            'pdf': '<i class="fa fa-file-pdf-o" style="color: #d9534f;"></i>',
            'doc': '<i class="fa fa-file-word-o" style="color: #2b579a;"></i>',
            'docx': '<i class="fa fa-file-word-o" style="color: #2b579a;"></i>',
            'xls': '<i class="fa fa-file-excel-o" style="color: #207245;"></i>',
            'xlsx': '<i class="fa fa-file-excel-o" style="color: #207245;"></i>',
            'ppt': '<i class="fa fa-file-powerpoint-o" style="color: #d24726;"></i>',
            'pptx': '<i class="fa fa-file-powerpoint-o" style="color: #d24726;"></i>',
            'txt': '<i class="fa fa-file-text-o" style="color: #5bc0de;"></i>',
            'zip': '<i class="fa fa-file-archive-o" style="color: #f0ad4e;"></i>',
            'rar': '<i class="fa fa-file-archive-o" style="color: #f0ad4e;"></i>',
            'jpg': '<i class="fa fa-file-image-o" style="color: #5cb85c;"></i>',
            'jpeg': '<i class="fa fa-file-image-o" style="color: #5cb85c;"></i>',
            'png': '<i class="fa fa-file-image-o" style="color: #5cb85c;"></i>',
            'gif': '<i class="fa fa-file-image-o" style="color: #5cb85c;"></i>'
        };
        
        return iconMap[fileType.toLowerCase()] || '<i class="fa fa-file-o"></i>';
    },


    // ==================== 文件上传和选择功能 ====================

    /**
     * 统一的文件对象标准化函数（使用标准驼峰命名）
     * @param {Object} fileData 原始文件数据
     * @returns {Object} 标准化后的文件对象
     */
    normalizeFileObject: function(fileData) {
        if (!fileData) return null;

        return {
            fileId: fileData.fileId || fileData.file_id,
            fileType: fileData.fileType || fileData.file_type,
            fileName: fileData.fileName || fileData.file_name || fileData.original_name,
            fileSize: fileData.fileSize || fileData.file_size,
            fileUrl: fileData.fileUrl || fileData.access_url,
            uploadTime: fileData.uploadTime || fileData.upload_time || new Date().getTime(),
            // 保留原始字段以兼容现有代码
            file_id: fileData.file_id || fileData.fileId,
            file_type: fileData.file_type || fileData.fileType,
            file_name: fileData.file_name || fileData.fileName || fileData.original_name,
            file_size: fileData.file_size || fileData.fileSize,
            access_url: fileData.access_url || fileData.fileUrl || fileData.url,
            upload_time: fileData.upload_time || fileData.uploadTime || new Date().getTime(),
            original_name: fileData.original_name || fileData.fileName || fileData.file_name,
            isUpload: fileData.isUpload || 0
        };
    },

    /**
     * 检查文件是否已存在（通过fileId判重）
     * @param {Object} newFile 新文件对象
     * @param {Array} existingFiles 现有文件数组
     * @returns {boolean} 是否存在
     */
    isFileExists: function(newFile, existingFiles) {
        if (!newFile || !existingFiles || !Array.isArray(existingFiles)) {
            return false;
        }

        var newFileId = newFile.fileId || newFile.file_id;
        if (!newFileId) {
            return false;
        }

        return existingFiles.some(function(existing) {
            var existingFileId = existing.fileId || existing.file_id;
            return existingFileId === newFileId;
        });
    },

    /**
     * 打开文件上传器
     * @param {Object} options 上传选项
     * @param {string} options.fileType 文件类型限制 (如: 'pdf', 'image', 'all')
     * @param {string} options.source 来源标识
     * @param {string} options.bucketType 存储桶类型 (默认: 'public')
     * @param {string} options.businessId 业务ID (可选)
     * @param {string} options.folderId 文件夹ID (可选)
     * @param {string} options.communityId 小区ID (可选)
     * @param {Function} callback 上传完成回调函数
     */
    openFileUploader: function(options, callback) {
        options = options || {};
        var fileType = options.fileType || 'all';
        var source = options.source || 'default';
        var bucketType = options.bucketType || 'public';
        var businessId = options.businessId || '';
        var folderId = options.folderId || '';
        var communityId = options.communityId || '';

        var url = ctx + 'file/uploader?fileType=' + fileType + '&source=' + source + '&bucketType=' + bucketType;
        if (businessId) {
            url += '&businessId=' + businessId;
        }
        if (folderId) {
            url += '&folderId=' + folderId;
        }
        if (communityId) {
            url += '&communityId=' + communityId;
        }

        layer.open({
            type: 2,
            title: '上传文件',
            shadeClose: false,
            shade: 0.1,
            offset: '20px',
            scrollbar: false,
            area: ['650px', '450px'],
            content: url,
            btn: ['确定', '取消'],
            yes: function(index, layero) {
                try {
                    var uploadedFiles = top.globalUploadedFiles || [];
                    if (uploadedFiles.length > 0) {
                        if (callback && typeof callback === 'function') {
                            callback(uploadedFiles);
                        }
                    } else {
                        $.modal.msgWarning('没有上传任何文件');
                    }
                } catch (e) {
                    console.error('处理上传文件时发生错误:', e);
                    $.modal.msgError('处理上传文件失败');
                }
                layer.close(index);
            }
        });
    },

    /**
     * 打开文件选择器
     * @param {Object} options 选择选项
     * @param {string} options.mode 选择模式 ('single' | 'multiple')
     * @param {string} options.fileType 文件类型限制
     * @param {string} options.title 对话框标题
     * @param {Function} callback 选择完成回调函数
     */
    openFileSelector: function(options, callback) {
        options = options || {};
        var mode = options.mode || 'single';
        var fileType = options.fileType || '';
        var title = options.title || '选择文件';

        var content = '';
        if (mode === 'single') {
            content = ctx + 'file/select';
            title = title + '（单选）';
        } else {
            content = ctx + 'file/multiSelect';
            title = title + '（多选）';
        }

        if (fileType) {
            content += '?fileType=' + fileType;
        }

        // 设置临时回调函数
        if (callback && typeof callback === 'function') {
            if (mode === 'single') {
                window.tempFileSelectCallback = callback;
            } else {
                window.tempFileMultiSelectCallback = callback;
            }
        }

        layer.open({
            title: title,
            type: 2,
            shade: 0.1,
            offset: '20px',
            maxmin: false,
            scrollbar: false,
            shadeClose: false,
            title: false,
            closeBtn: false,
            area: ['80%', '90%'],
            content: content
        });
    },

    /**
     * 处理富文本编辑器文件上传
     * @param {File} file 上传的文件
     * @param {Object} editor 富文本编辑器对象
     * @param {string} source 来源标识
     * @param {string} bucketType 存储桶类型
     */
    handleSummernoteUpload: function(file, editor, source, bucketType) {
        source = source || 'default';
        bucketType = bucketType || 'public';

        var data = new FormData();
        data.append("file", file);
        data.append("source", source);
        data.append("bucketType", bucketType);

        $.ajax({
            type: "POST",
            url: ctx + "common/upload",
            data: data,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    $(editor).summernote('editor.insertImage', result.accessUrl, result.fileName);
                } else {
                    $.modal.alertError(result.msg);
                }
            },
            error: function(error) {
                $.modal.alertWarning("文件上传失败。");
            }
        });
    },

    /**
     * 设置富文本编辑器的文件上传功能
     * @param {string} selector 富文本编辑器选择器
     * @param {string} source 来源标识
     * @param {string} bucketType 存储桶类型
     */
    setupSummernoteUpload: function(selector, source, bucketType) {
        var self = this;
        $(selector).summernote({
            callbacks: {
                onImageUpload: function(files) {
                    if (files && files.length > 0) {
                        self.handleSummernoteUpload(files[0], this, source, bucketType);
                    }
                }
            }
        });
    },

    /**
     * 创建文件显示HTML
     * @param {Array} files 文件数组
     * @param {Object} options 显示选项
     * @param {boolean} options.showRemove 是否显示删除按钮
     * @param {string} options.removeCallback 删除回调函数名
     * @returns {string} HTML字符串
     */
    createFileDisplayHtml: function(files, options) {
        options = options || {};
        var showRemove = options.showRemove !== false; // 默认显示删除按钮
        var removeCallback = options.removeCallback || 'removeFile';

        if (!files || !Array.isArray(files) || files.length === 0) {
            return '<div class="attachment-empty">暂无文件</div>';
        }

        var html = '';
        files.forEach(function(file) {
            var fileType = file.fileType || file.file_type || 'unknown';
            var fileName = file.fileName || file.file_name || file.original_name || '未知文件';
            var fileId = file.fileId || file.file_id;
            var isUploadFile = file.isUpload === 1 || file.isUpload === '1';

            var icon = FileAccessUtils.getFileIconHtml(fileType);
            var displayName = fileName.length > 30 ? fileName.substring(0, 30) + '...' : fileName;

            html += '<div class="attachment-item" id="file-item-' + fileId + '" data-file-id="' + fileId + '">' +
                   '<span class="file-icon">' + icon + '</span>' +
                   '<a class="file-name" target="_blank" href="' + ctx + 'attachment/download/' + fileId + '" title="' + fileName + '">' + displayName + '</a>';

            if (showRemove) {
                // 根据isUpload字段决定删除回调的参数：上传文件传fileId，选择文件传fileId和false标识
                var removeParams = isUploadFile ? "'" + fileId + "'" : "'" + fileId + "', false";
                html += '<span class="remove-btn" onclick="' + removeCallback + '(' + removeParams + ')" title="删除">' +
                       '<i class="fa fa-times"></i>' +
                       '</span>';
            }

            html += '</div>';
        });

        return html;
    },

    /**
     * 更新文件ID隐藏字段
     * @param {Array} files 文件数组
     * @param {string} fieldSelector 隐藏字段选择器
     */
    updateFileIds: function(files, fieldSelector) {
        try {
            var ids = files.map(function(file) {
                return file.fileId || file.file_id || '';
            }).filter(function(id) {
                return id !== '';
            }).join(',');

            $(fieldSelector).val(ids);
            console.log('文件ID已更新:', ids);
        } catch (e) {
            console.error('更新文件ID时发生错误:', e);
        }
    },

    /**
     * 根据文件ID从文件数组中移除文件
     * @param {Array} files 文件数组
     * @param {string} fileId 要移除的文件ID
     * @returns {Array} 移除文件后的新数组
     */
    removeFileById: function(files, fileId) {
        if (!files || !Array.isArray(files) || !fileId) {
            return files || [];
        }

        return files.filter(function(file) {
            var currentFileId = file.fileId || file.file_id;
            return currentFileId !== fileId;
        });
    },

    /**
     * 根据文件ID查找文件在数组中的索引
     * @param {Array} files 文件数组
     * @param {string} fileId 文件ID
     * @returns {number} 文件索引，未找到返回-1
     */
    findFileIndexById: function(files, fileId) {
        if (!files || !Array.isArray(files) || !fileId) {
            return -1;
        }

        return files.findIndex(function(file) {
            var currentFileId = file.fileId || file.file_id;
            return currentFileId === fileId;
        });
    },

    /**
     * 删除文件
     * @param {Array} files 文件数组
     * @param {string} fileId 要删除的文件ID
     * @param {boolean} needDeleteServer 是否需要删除服务器文件（默认true）
     * @param {Function} callback 删除完成回调
     * @returns {Array} 更新后的文件数组
     */
    removeFile: function(files, fileId, needDeleteServer, callback) {
        if (!files || !Array.isArray(files) || !fileId) {
            return files || [];
        }

        // 如果第三个参数是函数，说明没有传needDeleteServer参数
        if (typeof needDeleteServer === 'function') {
            callback = needDeleteServer;
            needDeleteServer = true; // 默认删除服务器文件
        }

        // 先从数组中移除
        var updatedFiles = FileAccessUtils.removeFileById(files, fileId);

        if (needDeleteServer !== false) {
            // 需要调用删除接口
            $.ajax({
                url: ctx + 'file/remove',
                type: 'POST',
                data: {
                    ids: fileId
                },
                success: function(result) {
                    if (result.code === 0) {
                        console.log('服务器文件删除成功:', fileId);
                        if (callback) callback(updatedFiles, true);
                    } else {
                        console.warn('服务器文件删除失败:', result.msg);
                        if (callback) callback(updatedFiles, false, result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('删除文件请求失败:', error);
                    if (callback) callback(updatedFiles, false, '删除文件请求失败: ' + error);
                }
            });
        } else {
            // 不需要删除服务器文件，只从数组移除
            if (callback) callback(updatedFiles, true);
        }

        return updatedFiles;
    },

    /**
     * 简化的单文件选择
     * @param {Object} options 选择选项
     * @param {Function} callback 选择完成回调
     */
    selectSingleFile: function(options, callback) {
        options = options || {};
        options.mode = 'single';
        this.openFileSelector(options, callback);
    },

    /**
     * 简化的多文件选择
     * @param {Object} options 选择选项
     * @param {Function} callback 选择完成回调
     */
    selectMultipleFiles: function(options, callback) {
        options = options || {};
        options.mode = 'multiple';
        this.openFileSelector(options, callback);
    },

    /**
     * 简化的文件上传
     * @param {Object} options 上传选项
     * @param {Function} callback 上传完成回调
     */
    uploadFiles: function(options, callback) {
        this.openFileUploader(options, callback);
    },

    /**
     * 打开公共文档库选择器
     * @param {Object} options 选择选项
     * @param {string} options.mode 选择模式 ('single' | 'multiple')
     * @param {string} options.fileType 文件类型限制
     * @param {string} options.title 对话框标题
     * @param {Function} callback 选择完成回调函数
     */
    openDocumentLibrarySelector: function(options, callback) {
        options = options || {};
        var mode = options.mode || 'single';
        var fileType = options.fileType || '';
        var title = options.title || '选择公共文档库文件';

        var content = '';
        if (mode === 'single') {
            content = ctx + 'document/select';
            title = title + '（单选）';
        } else {
            content = ctx + 'document/multiSelect';
            title = title + '（多选）';
        }

        if (fileType) {
            content += '?fileType=' + fileType;
        }

        // 设置临时回调函数
        if (callback && typeof callback === 'function') {
            if (mode === 'single') {
                window.tempDocumentSelectCallback = callback;
            } else {
                window.tempDocumentMultiSelectCallback = callback;
            }
        }

        layer.open({
            title: title,
            type: 2,
            shade: 0.1,
            offset: '20px',
            maxmin: false,
            title:false,
            closeBtn:false,
            scrollbar: false,
            shadeClose: false,
            area: ['80%', '90%'],
            content: content
        });
    },

    /**
     * 简化的公共文档库单文件选择
     * @param {Object} options 选择选项
     * @param {Function} callback 选择完成回调
     */
    selectDocumentLibrarySingleFile: function(options, callback) {
        options = options || {};
        options.mode = 'single';
        this.openDocumentLibrarySelector(options, callback);
    },

    /**
     * 简化的公共文档库多文件选择
     * @param {Object} options 选择选项
     * @param {Function} callback 选择完成回调
     */
    selectDocumentLibraryFiles: function(options, callback) {
        options = options || {};
        options.mode = 'multiple';
        this.openDocumentLibrarySelector(options, callback);
    }
};

// ==================== 全局回调处理机制 ====================

/**
 * 全局文件选择回调处理（单选模式）
 * 由file/select.html调用
 */
window.setSelectedFile = function(fileUrl, fileName, fileId, fileData) {
    if (window.tempFileSelectCallback && typeof window.tempFileSelectCallback === 'function') {
        var normalizedFile = FileAccessUtils.normalizeFileObject(fileData);
        window.tempFileSelectCallback(normalizedFile);
        window.tempFileSelectCallback = null; // 清除临时回调
    }

    // 同时处理公共文档库单选回调
    if (window.tempDocumentSelectCallback && typeof window.tempDocumentSelectCallback === 'function') {
        var normalizedFile = FileAccessUtils.normalizeFileObject(fileData);
        window.tempDocumentSelectCallback(normalizedFile);
        window.tempDocumentSelectCallback = null; // 清除临时回调
    }
};

/**
 * 全局文件选择回调处理（多选模式）
 * 由file/multiSelect.html调用
 */
window.setSelectedAttachments = function(attachments) {
    if (window.tempFileMultiSelectCallback && typeof window.tempFileMultiSelectCallback === 'function') {
        var normalizedFiles = [];
        if (attachments && attachments.length > 0) {
            attachments.forEach(function(attachment) {
                var normalizedFile = FileAccessUtils.normalizeFileObject(attachment);
                if (normalizedFile) {
                    normalizedFiles.push(normalizedFile);
                }
            });
        }
        window.tempFileMultiSelectCallback(normalizedFiles);
        window.tempFileMultiSelectCallback = null; // 清除临时回调
    }

    // 同时处理公共文档库多选回调
    if (window.tempDocumentMultiSelectCallback && typeof window.tempDocumentMultiSelectCallback === 'function') {
        var normalizedFiles = [];
        if (attachments && attachments.length > 0) {
            attachments.forEach(function(attachment) {
                var normalizedFile = FileAccessUtils.normalizeFileObject(attachment);
                if (normalizedFile) {
                    normalizedFiles.push(normalizedFile);
                }
            });
        }
        window.tempDocumentMultiSelectCallback(normalizedFiles);
        window.tempDocumentMultiSelectCallback = null; // 清除临时回调
    }
};

// ==================== 兼容性函数 ====================

// 兼容旧的全局函数
window.getFileDownloadUrl = FileAccessUtils.getFileDownloadUrl;
window.formatFileSize = FileAccessUtils.formatFileSize;
window.getFileIconHtml = FileAccessUtils.getFileIconHtml;

// 兼容旧的文件处理函数
window.normalizeFileObject = FileAccessUtils.normalizeFileObject;
window.isFileExists = FileAccessUtils.isFileExists;

// 兼容旧的富文本上传函数
window.sendFile = function(file, obj, source, bucketType) {
    FileAccessUtils.handleSummernoteUpload(file, obj, source, bucketType);
};

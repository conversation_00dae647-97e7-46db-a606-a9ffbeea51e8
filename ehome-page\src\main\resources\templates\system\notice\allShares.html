<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('小区公告分享记录')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="share-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                住户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                公告标题：<input type="text" name="noticeTitle"/>
                            </li>
                            <li>
                                用户类型：<select name="userType">
                                    <option value="">所有</option>
                                    <option value="wx_user">微信用户</option>
                                    <option value="sys_user">系统用户</option>
                                </select>
                            </li>
                            <li>
                                分享平台：<select name="sharePlatform">
                                    <option value="">所有</option>
                                    <option value="wechat">微信好友</option>
                                    <option value="timeline">朋友圈</option>
                                    <option value="other">其他</option>
                                </select>
                            </li>
                            <li>
                                分享时间：<input type="text" class="time-input" name="shareTime"/>
                            </li>
                            <li>
                                房屋名称：<input type="text" name="houseName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-success" onclick="viewShareStats()">
                    <i class="fa fa-bar-chart"></i> 查看统计
                </a>
                <a class="btn btn-info" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "system/notice/allShares";

        $(function() {
            var options = {
                url: prefix + "/list",
                modalName: "分享记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'shareId',
                    title: '序号'
                },
                {
                    field: 'noticeTitle',
                    title: '公告标题',
                    formatter: function(value, row, index) {
                        if (value && value.length > 30) {
                            return '<span title="' + value + '">' + value.substring(0, 30) + '...</span>';
                        }
                        return value || '未知公告';
                    }
                },
                {
                    field: 'userName',
                    title: '住户用户',
                    formatter: function(value, row, index) {
                        var badge = row.userType === 'sys_user' ? 
                            '<span class="badge badge-danger">管理员</span>' : 
                            '<span class="badge badge-success">用户</span>';
                        return value + ' ' + badge;
                    }
                },
                {
                    field: 'houseName',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="badge badge-info">' + value + '</span>';
                        }
                        return '<span class="text-muted">未绑定</span>';
                    }
                },
                {
                    field: 'sharePlatform',
                    title: '分享平台',
                    formatter: function(value, row, index) {
                        switch(value) {
                            case 'wechat':
                                return '<span class="badge badge-success">微信好友</span>';
                            case 'timeline':
                                return '<span class="badge badge-info">朋友圈</span>';
                            case 'other':
                                return '<span class="badge badge-secondary">其他</span>';
                            default:
                                return '<span class="badge badge-default">' + (value || '未知') + '</span>';
                        }
                    }
                },
                {
                    field: 'shareTime',
                    title: '分享时间',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, 'yyyy-MM-dd HH:mm:ss');
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewShare(\'' + row.shareId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-warning btn-xs" href="javascript:void(0)" onclick="viewNoticeShareStats(\'' + row.noticeId + '\')"><i class="fa fa-bar-chart"></i>统计</a> ');
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewNotice(\'' + row.noticeId + '\')"><i class="fa fa-file-text"></i>查看公告</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 查看分享详情
        function viewShare(shareId) {
            // 从表格中获取分享数据
            var table = $("#bootstrap-table");
            var data = table.bootstrapTable('getData');
            var share = null;

            for (var i = 0; i < data.length; i++) {
                if (data[i].shareId == shareId) {
                    share = data[i];
                    break;
                }
            }

            if (!share) {
                $.modal.msgError("分享数据不存在");
                return;
            }

            var platformText = '';
            switch(share.sharePlatform) {
                case 'wechat':
                    platformText = '<span class="badge badge-success">微信好友</span>';
                    break;
                case 'timeline':
                    platformText = '<span class="badge badge-info">朋友圈</span>';
                    break;
                case 'other':
                    platformText = '<span class="badge badge-secondary">其他</span>';
                    break;
                default:
                    platformText = '<span class="badge badge-default">' + (share.sharePlatform || '未知') + '</span>';
            }

            var userTypeText = share.userType === 'sys_user' ?
                '<span class="badge badge-danger">管理员</span>' :
                '<span class="badge badge-success">用户</span>';

            var shareTime = $.common.dateFormat(share.shareTime, 'yyyy-MM-dd HH:mm:ss');

            var content = '<div class="row">' +
                '<div class="col-sm-12">' +
                '<table class="table table-bordered">' +
                '<tr><td width="120px"><strong>分享ID</strong></td><td>' + share.shareId + '</td></tr>' +
                '<tr><td><strong>所属公告</strong></td><td>' + (share.noticeTitle || '未知公告') + '</td></tr>' +
                '<tr><td><strong>分享用户</strong></td><td>' + share.userName + ' ' + userTypeText + '</td></tr>' +
                '<tr><td><strong>分享平台</strong></td><td>' + platformText + '</td></tr>' +
                '<tr><td><strong>分享时间</strong></td><td>' + shareTime + '</td></tr>' +
                '</table>' +
                '</div>' +
                '</div>';

            // 使用自定义模态框
            var modalHtml = '<div class="modal fade" id="shareDetailModal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog modal-lg" role="document">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<h4 class="modal-title">分享详情</h4>' +
                '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
                '</div>' +
                '<div class="modal-body">' + content + '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 移除已存在的模态框
            $('#shareDetailModal').remove();

            // 添加新的模态框并显示
            $('body').append(modalHtml);
            $('#shareDetailModal').modal('show');
        }

        // 查看公告详情
        function viewNotice(noticeId) {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/view/" + noticeId;
            $.modal.openTab("公告详情", url);
        }

        // 查看整体分享统计
        function viewShareStats() {
            var url = ctx + "system/notice/shareStats";
            $.modal.openTab("分享统计", url);
        }

        // 查看单个公告的分享统计
        function viewNoticeShareStats(noticeId) {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/shareStats/" + noticeId;
            $.modal.openTab("公告分享统计", url);
        }

        // 导出数据
        function exportData() {
            $.modal.confirm("确定要导出分享记录数据吗？", function() {
                $.post(prefix + "/export", $("#share-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('违规车辆管理')" />
    <style>
        .car-image {
            max-width: 100px;
            max-height: 60px;
            cursor: pointer;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>团队：</label>
                                <select name="team_id" id="teamSelect"><option value="">全部团队</option></select>
                            </li>
                            <li>
                                <label>车牌号：</label>
                                <input type="text" name="car_plate" placeholder="请输入车牌号"/>
                            </li>
                            <li>
                                <label>上报人ID：</label>
                                <input type="text" name="report_uid" placeholder="请输入上报人ID"/>
                            </li>
                            <li class="select-time">
                                <label>上报时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="start_time"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="end_time"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="syncIllegalData()" shiro:hasPermission="oc:markicam:sync">
                    <i class="fa fa-refresh"></i> 同步违规车辆
                </a>
                <a class="btn btn-info" onclick="viewDetails()" shiro:hasPermission="oc:markicam:view">
                    <i class="fa fa-eye"></i> 查看详情
                </a>
                <a class="btn btn-warning" onclick="exportData()" shiro:hasPermission="oc:markicam:export">
                    <i class="fa fa-download"></i> 导出数据
                </a>
                <a class="btn btn-primary" onclick="statisticsReport()" shiro:hasPermission="oc:markicam:report">
                    <i class="fa fa-bar-chart"></i> 统计报表
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="detailModalLabel">违规车辆详情</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="detailContent">
                    <!-- 详情内容将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";
        
        $(function() {
            var options = {
                url: prefix + "/illegal/list",
                createUrl: prefix + "/illegal/add",
                updateUrl: prefix + "/illegal/edit/{id}",
                removeUrl: prefix + "/illegal/remove",
                exportUrl: prefix + "/illegal/export",
                modalName: "违规车辆",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'team_id',
                    title: '团队ID',
                    width: 100
                },
                {
                    field: 'team_name',
                    title: '团队名称',
                    width: 120
                },
                {
                    field: 'car_plate',
                    title: '车牌号',
                    width: 120,
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<span class="badge badge-warning" style="font-size: 12px;">' + value + '</span>';
                        }
                        return '-';
                    }
                },
                {
                    field: 'car_picture',
                    title: '违规照片',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        if (value) {
                            return '<img src="' + value + '" class="car-image" onclick="previewImage(\'' + value + '\')" alt="违规照片">';
                        }
                        return '-';
                    }
                },
                {
                    field: 'report_uid',
                    title: '上报人ID',
                    width: 100
                },
                {
                    field: 'report_time_str',
                    title: '上报时间',
                    width: 150
                },
                {
                    field: 'sync_time',
                    title: '同步时间',
                    width: 150
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        if (row.car_picture) {
                            actions.push('<a class="btn btn-primary btn-xs" href="' + row.car_picture + '" target="_blank"><i class="fa fa-external-link"></i>查看</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            
            // 初始化时间选择器
            laydate.render({
                elem: '#startTime',
                type: 'datetime'
            });
            laydate.render({
                elem: '#endTime',
                type: 'datetime'
            });
        });

        function syncIllegalData() {
            $.modal.confirm("确定要同步违规车辆数据吗？", function() {
                $.modal.loading("正在同步数据...");
                $.post(prefix + "/sync/illegal", {}, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("同步成功");
                        $.table.refresh();
                    } else {
                        $.modal.alertError("同步失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("同步失败");
                });
            });
        }

        function viewDetails() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请选择一条记录");
                return;
            }
            viewDetail(rows[0].id);
        }

        function viewDetail(id) {
            $.get(prefix + "/illegal/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>团队ID:</strong> ' + (data.team_id || '-') + '</p>';
                    content += '<p><strong>团队名称:</strong> ' + (data.team_name || '-') + '</p>';
                    content += '<p><strong>车牌号:</strong> ' + (data.car_plate || '-') + '</p>';
                    content += '<p><strong>上报人ID:</strong> ' + (data.report_uid || '-') + '</p>';
                    content += '</div>';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>上报时间:</strong> ' + (data.report_time_str || '-') + '</p>';
                    content += '<p><strong>同步时间:</strong> ' + (data.sync_time || '-') + '</p>';
                    content += '<p><strong>创建时间:</strong> ' + (data.created_at || '-') + '</p>';
                    content += '<p><strong>更新时间:</strong> ' + (data.updated_at || '-') + '</p>';
                    content += '</div>';
                    content += '</div>';
                    
                    if (data.car_picture) {
                        content += '<div class="row"><div class="col-md-12">';
                        content += '<p><strong>违规照片:</strong></p>';
                        content += '<div class="text-center">';
                        content += '<img src="' + data.car_picture + '" style="max-width: 100%; max-height: 400px;" alt="违规照片">';
                        content += '</div>';
                        content += '</div></div>';
                    }
                    
                    $('#detailContent').html(content);
                    $('#detailModal').modal('show');
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function previewImage(url) {
            var content = '<div class="text-center"><img src="' + url + '" style="max-width: 100%; max-height: 500px;" alt="违规照片预览"></div>';
            $('#detailContent').html(content);
            $('#detailModalLabel').text('违规照片预览');
            $('#detailModal').modal('show');
        }

        function exportData() {
            $.modal.confirm("确定要导出违规车辆数据吗？", function() {
                var formData = $("#formId").serialize();
                window.location.href = prefix + "/illegal/export?" + formData;
            });
        }

        function statisticsReport() {
            // 生成统计报表
            var formData = $("#formId").serialize();
            $.get(prefix + "/illegal/statistics?" + formData, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">';
                    content += '<div class="col-md-3 text-center">';
                    content += '<h4 class="text-primary">' + (data.totalCount || 0) + '</h4>';
                    content += '<p>违规总数</p>';
                    content += '</div>';
                    content += '<div class="col-md-3 text-center">';
                    content += '<h4 class="text-warning">' + (data.todayCount || 0) + '</h4>';
                    content += '<p>今日违规</p>';
                    content += '</div>';
                    content += '<div class="col-md-3 text-center">';
                    content += '<h4 class="text-info">' + (data.weekCount || 0) + '</h4>';
                    content += '<p>本周违规</p>';
                    content += '</div>';
                    content += '<div class="col-md-3 text-center">';
                    content += '<h4 class="text-success">' + (data.monthCount || 0) + '</h4>';
                    content += '<p>本月违规</p>';
                    content += '</div>';
                    content += '</div>';
                    
                    if (data.topCarPlates && data.topCarPlates.length > 0) {
                        content += '<hr><h5>违规次数最多的车辆</h5>';
                        content += '<table class="table table-striped">';
                        content += '<thead><tr><th>车牌号</th><th>违规次数</th></tr></thead>';
                        content += '<tbody>';
                        for (var i = 0; i < data.topCarPlates.length; i++) {
                            var item = data.topCarPlates[i];
                            content += '<tr><td>' + item.carPlate + '</td><td>' + item.count + '</td></tr>';
                        }
                        content += '</tbody></table>';
                    }
                    
                    $.modal.open("违规车辆统计", content, "modal-lg");
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    </script>
</body>
</html>

package com.ehome.system.service;

import com.ehome.system.domain.SysNoticeLike;
import java.util.List;

/**
 * 公告点赞记录 服务层
 * 
 * <AUTHOR>
 */
public interface ISysNoticeLikeService
{
    /**
     * 查询点赞记录信息
     * 
     * @param likeId 点赞记录ID
     * @return 点赞记录信息
     */
    public SysNoticeLike selectLikeById(Long likeId);

    /**
     * 查询点赞记录列表
     * 
     * @param like 点赞记录信息
     * @return 点赞记录集合
     */
    public List<SysNoticeLike> selectLikeList(SysNoticeLike like);

    /**
     * 根据公告ID查询点赞记录列表
     * 
     * @param noticeId 公告ID
     * @return 点赞记录集合
     */
    public List<SysNoticeLike> selectLikeByNoticeId(Long noticeId);

    /**
     * 查询指定小区所有公告的点赞记录列表（包含公告标题）
     * 
     * @param like 点赞记录信息（包含小区ID等查询条件）
     * @return 点赞记录集合
     */
    public List<SysNoticeLike> selectAllLikesByCommunity(SysNoticeLike like);

    /**
     * 新增点赞记录
     * 
     * @param like 点赞记录信息
     * @return 结果
     */
    public int insertLike(SysNoticeLike like);

    /**
     * 修改点赞记录
     * 
     * @param like 点赞记录信息
     * @return 结果
     */
    public int updateLike(SysNoticeLike like);

    /**
     * 删除点赞记录信息
     * 
     * @param likeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteLikeByIds(String likeIds);

    /**
     * 根据公告ID删除点赞记录
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteLikeByNoticeId(Long noticeId);
}

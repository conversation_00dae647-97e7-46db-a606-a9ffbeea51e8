<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改服务电话信息')" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vant@latest/lib/index.css" />
    <style>
        .btn-group .btn-outline-primary {
            border-color: #007bff;
            color: #007bff;
        }
        .btn-group .btn-outline-primary.active {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
        .btn-group .btn-outline-primary:hover {
            background-color: #007bff;
            border-color: #007bff;
            color: #fff;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-serviceTel-edit">
            <input name="service_tel_id" type="hidden" th:value="${serviceTel.service_tel_id}">
            <div class="form-group">
                <label class="col-sm-2 control-label">号码类型：</label>
                <div class="col-sm-4">
                    <div class="btn-group" data-toggle="buttons">
                        <label class="btn btn-outline-primary" id="internalTypeBtn">
                            <input type="radio" name="tel_type" value="internal" autocomplete="off"> 内部号码
                        </label>
                        <label class="btn btn-outline-primary" id="externalTypeBtn">
                            <input type="radio" name="tel_type" value="external" autocomplete="off"> 外部号码
                        </label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">父级分类：</label>
                <div class="col-sm-10">
                    <div class="input-group">
                        <input class="form-control" type="text" placeholder="选择父级分类（不选择则为顶级分类）" id="parentName" readonly>
                        <input type="hidden" name="parent_id" th:value="${serviceTel.parent_id}" />
                        <div class="input-group-btn" style="float: none;">
                            <button type="button" class="btn btn-white" id="selectParentBtn" th:disabled="${serviceTel.hasChildren}">
                                <i class="fa fa-search"></i> 选择
                            </button>
                            <button type="button" class="btn btn-white" id="clearParentBtn" th:disabled="${serviceTel.hasChildren}">
                                <i class="fa fa-remove"></i> 清空
                            </button>
                        </div>
                    </div>
                    <small class="help-block">选择父级分类可创建二级服务电话，不选择则创建顶级分类</small>
                    <small class="help-block text-danger" th:if="${serviceTel.hasChildren}">该项有子项，不能修改父级</small>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required" id="serviceNameLabel">服务名称：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="如：物业服务中心" name="service_name" id="serviceNameInput" required>
                </div>
                <label class="col-sm-2 control-label" id="telNumberLabel">电话号码：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="如：95518" name="tel_number" id="telNumberInput">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">公司全称：</label>
                <div class="col-sm-4">
                    <input class="form-control" type="text" placeholder="如：市政府" name="company_name">
                </div>
                <label class="col-sm-2 control-label">状态：</label>
                <div class="col-sm-4">
                    <select class="form-control" name="status">
                        <option value="0">正常</option>
                        <option value="1">停用</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">备注：</label>
                <div class="col-sm-10">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/serviceTel";
        
        // 动态验证规则
        var validationRules = {
            service_name: {
                required: true
            }
        };

        var validationMessages = {
            "service_name": {
                required: "请输入服务名称"
            }
        };

        $("#form-serviceTel-edit").validate({
            onkeyup: false,
            rules: validationRules,
            messages: validationMessages,
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                if($("input[name='sort_order']").val() == ""){
                    $("input[name='sort_order']").val(0);
                }
                var formData = $('#form-serviceTel-edit').serialize();
                $.operate.save(prefix + "/editSave", formData);
            }
        }



        // 选择父级分类
        function selectParent() {
            // 获取当前选中的号码类型
            var telType = $('input[name="tel_type"]:checked').val();
            var url = prefix + "/tree?tel_type=" + telType;
            var options = {
                title: '选择父级分类',
                width: "380",
                url: url,
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        // 清空父级分类
        function clearParent() {
            $("input[name='parent_id']").val('');
            $("#parentName").val('');
            updateFormByParent('');
        }

        // 父级分类选择回调
        function doSubmit(index, layero) {
            var body = $.modal.getChildFrame(index);
            var parentId = body.find('#treeId').val();
            var parentName = body.find('#treeName').val();
            $("input[name='parent_id']").val(parentId);
            $("#parentName").val(parentName);
            updateFormByParent(parentId);
            $.modal.close(index);
        }

        // 根据父级更新表单
        function updateFormByParent(parentId) {
            if (parentId && parentId != '0') {
                // 选择了父级，说明是二级项
                $("#serviceNameLabel").text("联系人名称：");
                $("#serviceNameInput").attr("placeholder", "如：中国人保车险");
                $("#telNumberLabel").addClass("is-required");
                $("#telNumberInput").prop("required", true);

                // 更新验证规则
                validationRules.tel_number = {
                    required: true,
                    remote: {
                        url: prefix + "/checkTelNumber",
                        type: "post",
                        dataType: "json",
                        data: {
                            "service_tel_id": function() {
                                return $("input[name='service_tel_id']").val();
                            },
                            "tel_number": function() {
                                return $.common.trim($("input[name='tel_number']").val());
                            }
                        },
                        dataFilter: function(result) {
                            return result;
                        }
                    }
                };
                validationMessages.tel_number = {
                    required: "请输入电话号码",
                    remote: "电话号码已经存在"
                };
            } else {
                // 顶级分类
                $("#serviceNameLabel").text("分类名称：");
                $("#serviceNameInput").attr("placeholder", "如：公共服务");
                $("#telNumberLabel").removeClass("is-required");
                $("#telNumberInput").prop("required", false);
                delete validationRules.tel_number;
                delete validationMessages.tel_number;
            }

            // 重新初始化验证
            $("#form-serviceTel-edit").validate().resetForm();
            $("#form-serviceTel-edit").validate({
                onkeyup: false,
                rules: validationRules,
                messages: validationMessages,
                focusCleanup: true
            });
        }

        $(function() {
            $('#form-serviceTel-edit').renderForm({url:prefix+'/record'},function (result){
                // 初始化号码类型选择
                var telType = result.data.tel_type || 'external';
                if (telType === 'internal') {
                    $('#internalTypeBtn').addClass('active');
                    $('#internalTypeBtn input').prop('checked', true);
                } else {
                    $('#externalTypeBtn').addClass('active');
                    $('#externalTypeBtn input').prop('checked', true);
                }
                // 设置父级信息
                var parentId = result.data.parent_id;
                if (parentId && parentId != '0') {
                    // 获取父级名称
                    $.post(prefix + "/record", {service_tel_id: parentId}, function(parentResult) {
                        if (parentResult.code == 0) {
                            $("#parentName").val(parentResult.data.service_name);
                        }
                    });
                }

                // 根据父级更新表单
                updateFormByParent(parentId);
            });

            // 绑定选择父级按钮事件
            $("#selectParentBtn").click(function() {
                selectParent();
            });

            // 绑定清空父级按钮事件
            $("#clearParentBtn").click(function() {
                clearParent();
            });
        });
    </script>
</body>
</html>

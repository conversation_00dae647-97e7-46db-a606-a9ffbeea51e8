package com.ehome.oc.domain;

import com.ehome.common.core.domain.entity.SysUser;

import java.math.BigDecimal;

/**
 * 账单创建请求类
 * 用于封装创建账单所需的所有参数
 */
public class BillCreateRequest {
    
    /** 社区ID */
    private String communityId;
    
    /** 资产类型 */
    private Integer assetType;
    
    /** 资产ID */
    private Long assetId;
    
    /** 资产名称 */
    private String assetName;
    
    /** 收费标准ID */
    private Long chargeStandardId;
    
    /** 收费标准版本 */
    private Integer chargeStandardVersion;
    
    /** 收费绑定ID */
    private Long chargeBindingId;
    
    /** 收费项目名称 */
    private String chargeItemName;
    
    /** 收费项目类型 */
    private Integer chargeItemType;
    
    /** 账期开始时间（格式：yyyyMMdd） */
    private Integer startTime;

    /** 账期结束时间（格式：yyyyMMdd） */
    private Integer endTime;
    
    /** 账期月份（格式：YYYY-MM） */
    private String inMonth;
    
    /** 账单类型（1-手工账单，2-导入账单，3-系统生成） */
    private Integer billType;
    
    /** 账单金额（元） */
    private BigDecimal billAmount;
    
    /** 当前用户 */
    private SysUser currentUser;
    
    // 构造函数
    public BillCreateRequest() {}
    
    // Getter 和 Setter 方法
    public String getCommunityId() {
        return communityId;
    }
    
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    
    public Integer getAssetType() {
        return assetType;
    }
    
    public void setAssetType(Integer assetType) {
        this.assetType = assetType;
    }
    
    public Long getAssetId() {
        return assetId;
    }
    
    public void setAssetId(Long assetId) {
        this.assetId = assetId;
    }
    
    public String getAssetName() {
        return assetName;
    }
    
    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }
    
    public Long getChargeStandardId() {
        return chargeStandardId;
    }
    
    public void setChargeStandardId(Long chargeStandardId) {
        this.chargeStandardId = chargeStandardId;
    }
    
    public Integer getChargeStandardVersion() {
        return chargeStandardVersion;
    }
    
    public void setChargeStandardVersion(Integer chargeStandardVersion) {
        this.chargeStandardVersion = chargeStandardVersion;
    }
    
    public Long getChargeBindingId() {
        return chargeBindingId;
    }
    
    public void setChargeBindingId(Long chargeBindingId) {
        this.chargeBindingId = chargeBindingId;
    }
    
    public String getChargeItemName() {
        return chargeItemName;
    }
    
    public void setChargeItemName(String chargeItemName) {
        this.chargeItemName = chargeItemName;
    }
    
    public Integer getChargeItemType() {
        return chargeItemType;
    }
    
    public void setChargeItemType(Integer chargeItemType) {
        this.chargeItemType = chargeItemType;
    }
    
    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }
    
    public String getInMonth() {
        return inMonth;
    }
    
    public void setInMonth(String inMonth) {
        this.inMonth = inMonth;
    }
    
    public Integer getBillType() {
        return billType;
    }
    
    public void setBillType(Integer billType) {
        this.billType = billType;
    }
    
    public BigDecimal getBillAmount() {
        return billAmount;
    }
    
    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }
    
    public SysUser getCurrentUser() {
        return currentUser;
    }
    
    public void setCurrentUser(SysUser currentUser) {
        this.currentUser = currentUser;
    }
    
    @Override
    public String toString() {
        return "BillCreateRequest{" +
                "communityId='" + communityId + '\'' +
                ", assetType=" + assetType +
                ", assetId=" + assetId +
                ", assetName='" + assetName + '\'' +
                ", chargeStandardId=" + chargeStandardId +
                ", chargeStandardVersion=" + chargeStandardVersion +
                ", chargeBindingId=" + chargeBindingId +
                ", chargeItemName='" + chargeItemName + '\'' +
                ", chargeItemType=" + chargeItemType +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", inMonth='" + inMonth + '\'' +
                ", billType=" + billType +
                ", billAmount=" + billAmount +
                ", currentUser=" + (currentUser != null ? currentUser.getUserName() : null) +
                '}';
    }
}

-- 更新收费绑定表数据，用于测试批量生成账单功能

-- 1. 首先查看当前收费绑定数据
SELECT 
    id,
    asset_name,
    charge_standard_id,
    start_time,
    end_time,
    next_bill_time,
    is_active
FROM eh_charge_binding 
WHERE is_active = 1
LIMIT 10;

-- 2. 更新收费绑定的next_bill_time，设置为当前时间之前，触发账单生成
-- 将next_bill_time设置为昨天的时间戳
UPDATE eh_charge_binding 
SET next_bill_time = UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY))
WHERE is_active = 1 
AND (next_bill_time IS NULL OR next_bill_time = 0);

-- 3. 如果没有收费绑定数据，创建一些测试数据
-- 注意：需要根据实际的资产数据和收费标准数据来调整

-- 查看是否有房屋数据
SELECT COUNT(*) as house_count FROM eh_house_info WHERE check_status = 1 LIMIT 5;

-- 查看是否有收费标准数据
SELECT id, name, charge_type FROM eh_charge_standard WHERE is_active = 1 AND is_deleted = 0 LIMIT 5;

-- 如果需要创建测试数据，可以执行以下语句（请根据实际情况调整）
/*
-- 创建测试收费绑定数据
INSERT INTO eh_charge_binding (
    asset_type,
    asset_id,
    asset_name,
    charge_standard_id,
    start_time,
    end_time,
    period_num,
    natural_period,
    next_bill_time,
    community_id,
    is_active,
    create_time,
    create_by
) 
SELECT 
    1 as asset_type,
    hi.house_id as asset_id,
    CONCAT(b.name, '/', u.name, '/', hi.room) as asset_name,
    cs.id as charge_standard_id,
    UNIX_TIMESTAMP('2024-01-01') as start_time,
    UNIX_TIMESTAMP('2025-12-31') as end_time,
    1 as period_num,
    0 as natural_period,
    UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) as next_bill_time,
    hi.community_id,
    1 as is_active,
    UNIX_TIMESTAMP(NOW()) as create_time,
    'SYSTEM' as create_by
FROM eh_house_info hi
LEFT JOIN eh_unit u ON hi.unit_id = u.unit_id
LEFT JOIN eh_building b ON u.building_id = b.building_id
CROSS JOIN (SELECT id FROM eh_charge_standard WHERE is_active = 1 AND is_deleted = 0 LIMIT 1) cs
WHERE hi.check_status = 1
AND hi.community_id IS NOT NULL
LIMIT 10;
*/

-- 4. 验证更新结果
SELECT 
    id,
    asset_name,
    charge_standard_id,
    FROM_UNIXTIME(start_time) as start_time_str,
    FROM_UNIXTIME(end_time) as end_time_str,
    FROM_UNIXTIME(next_bill_time) as next_bill_time_str,
    is_active
FROM eh_charge_binding 
WHERE is_active = 1
AND next_bill_time <= UNIX_TIMESTAMP(NOW())
ORDER BY next_bill_time DESC
LIMIT 20;

-- 5. 检查是否已存在账单（避免重复生成）
SELECT 
    cb.id,
    cb.asset_name,
    cb.charge_item_name,
    cb.in_month,
    FROM_UNIXTIME(cb.create_time) as create_time_str
FROM eh_charge_bill cb
ORDER BY cb.create_time DESC
LIMIT 10;

package com.ehome.oc.service;

import com.ehome.common.utils.sql.SqlExecutorUtils;

import java.util.List;

/**
 * 数据初始化服务接口
 * 
 * <AUTHOR>
 */
public interface IDataInitService {
    
    /**
     * 初始化服务电话数据
     * 
     * @param communityId 小区ID
     * @return 执行结果
     */
    SqlExecutorUtils.ExecutionResult initServiceTelData(String communityId);
    
    /**
     * 初始化微信导航数据
     * 
     * @param communityId 小区ID
     * @return 执行结果
     */
    SqlExecutorUtils.ExecutionResult initWxNavData(String communityId);
    
    /**
     * 初始化公告类型数据
     * 
     * @param communityId 小区ID
     * @return 执行结果
     */
    SqlExecutorUtils.ExecutionResult initNoticeTypesData(String communityId);
    
    /**
     * 批量初始化所有数据
     * 
     * @param communityId 小区ID
     * @param templateNames 要初始化的模板名称列表，为空则初始化所有
     * @return 执行结果列表
     */
    List<SqlExecutorUtils.ExecutionResult> initAllData(String communityId, List<String> templateNames);
    
    /**
     * 检查数据是否已初始化
     * 
     * @param communityId 小区ID
     * @param dataType 数据类型（service_tel, wx_nav, notice_types等）
     * @return 是否已初始化
     */
    boolean isDataInitialized(String communityId, String dataType);
    
    /**
     * 获取可用的初始化模板列表
     * 
     * @return 模板名称列表
     */
    List<String> getAvailableTemplates();
    
    /**
     * 获取初始化状态
     *
     * @param communityId 小区ID
     * @return 各数据类型的初始化状态
     */
    java.util.Map<String, Boolean> getInitializationStatus(String communityId);

    /**
     * 彻底删除某个小区的所有数据
     * 警告：此操作不可逆，请谨慎使用！
     *
     * @param communityId 小区ID
     * @param includeBasicInfo 是否删除小区基本信息（eh_community表记录）
     * @return 执行结果
     */
    SqlExecutorUtils.ExecutionResult deleteCommunityData(String communityId, boolean includeBasicInfo);

    /**
     * 获取小区数据统计信息
     * 用于删除前确认数据量
     *
     * @param communityId 小区ID
     * @return 各表的数据统计
     */
    java.util.Map<String, Long> getCommunityDataStatistics(String communityId);
}

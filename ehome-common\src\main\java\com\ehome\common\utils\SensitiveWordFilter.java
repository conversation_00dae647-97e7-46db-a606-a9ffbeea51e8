package com.ehome.common.utils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 敏感词过滤工具类
 * 使用DFA算法实现高效的敏感词检测和替换
 * 
 * <AUTHOR>
 */
public class SensitiveWordFilter {
    
    /** 敏感词库 */
    private static final Set<String> SENSITIVE_WORDS = new HashSet<>();
    
    /** DFA算法状态机 */
    private static final Map<Character, Object> WORD_MAP = new HashMap<>();
    
    /** 替换字符 */
    private static final String REPLACEMENT = "*";
    
    /** 结束标志 */
    private static final String END_FLAG = "isEnd";
    
    static {
        // 初始化敏感词库
        initSensitiveWords();
        // 构建DFA状态机
        buildWordMap();
    }
    
    /**
     * 初始化敏感词库
     */
    private static void initSensitiveWords() {
        // 政治敏感词
        SENSITIVE_WORDS.add("法轮功");
        SENSITIVE_WORDS.add("共产党");
        SENSITIVE_WORDS.add("习近平");
        SENSITIVE_WORDS.add("毛泽东");
        SENSITIVE_WORDS.add("邓小平");
        SENSITIVE_WORDS.add("江泽民");
        SENSITIVE_WORDS.add("胡锦涛");
        SENSITIVE_WORDS.add("温家宝");
        SENSITIVE_WORDS.add("李克强");
        SENSITIVE_WORDS.add("反政府");
        SENSITIVE_WORDS.add("推翻");
        SENSITIVE_WORDS.add("颠覆");
        SENSITIVE_WORDS.add("暴动");
        SENSITIVE_WORDS.add("革命");
        SENSITIVE_WORDS.add("起义");
        
        // 暴力恐怖词汇
        SENSITIVE_WORDS.add("杀人");
        SENSITIVE_WORDS.add("爆炸");
        SENSITIVE_WORDS.add("恐怖主义");
        SENSITIVE_WORDS.add("恐怖分子");
        SENSITIVE_WORDS.add("炸弹");
        SENSITIVE_WORDS.add("枪支");
        SENSITIVE_WORDS.add("毒品");
        SENSITIVE_WORDS.add("海洛因");
        SENSITIVE_WORDS.add("冰毒");
        SENSITIVE_WORDS.add("摇头丸");
        SENSITIVE_WORDS.add("大麻");
        SENSITIVE_WORDS.add("吸毒");
        SENSITIVE_WORDS.add("贩毒");
        
        // 色情词汇
        SENSITIVE_WORDS.add("色情");
        SENSITIVE_WORDS.add("黄色");
        SENSITIVE_WORDS.add("裸体");
        SENSITIVE_WORDS.add("性交");
        SENSITIVE_WORDS.add("做爱");
        SENSITIVE_WORDS.add("强奸");
        SENSITIVE_WORDS.add("卖淫");
        SENSITIVE_WORDS.add("嫖娼");
        SENSITIVE_WORDS.add("援交");
        
        // 赌博词汇
        SENSITIVE_WORDS.add("赌博");
        SENSITIVE_WORDS.add("赌场");
        SENSITIVE_WORDS.add("赌钱");
        SENSITIVE_WORDS.add("博彩");
        SENSITIVE_WORDS.add("六合彩");
        SENSITIVE_WORDS.add("时时彩");
        SENSITIVE_WORDS.add("彩票");
        SENSITIVE_WORDS.add("老虎机");
        
        // 诈骗词汇
        SENSITIVE_WORDS.add("诈骗");
        SENSITIVE_WORDS.add("骗钱");
        SENSITIVE_WORDS.add("传销");
        SENSITIVE_WORDS.add("洗钱");
        SENSITIVE_WORDS.add("高利贷");
        SENSITIVE_WORDS.add("套现");
        SENSITIVE_WORDS.add("刷单");
        
        // 脏话词汇
        SENSITIVE_WORDS.add("傻逼");
        SENSITIVE_WORDS.add("操你妈");
        SENSITIVE_WORDS.add("去死");
        SENSITIVE_WORDS.add("滚蛋");
        SENSITIVE_WORDS.add("白痴");
        SENSITIVE_WORDS.add("智障");
        SENSITIVE_WORDS.add("脑残");
        SENSITIVE_WORDS.add("垃圾");
        SENSITIVE_WORDS.add("废物");
        SENSITIVE_WORDS.add("混蛋");
        SENSITIVE_WORDS.add("王八蛋");
        SENSITIVE_WORDS.add("狗屎");
        SENSITIVE_WORDS.add("妈的");
        SENSITIVE_WORDS.add("他妈的");
        SENSITIVE_WORDS.add("草泥马");
        SENSITIVE_WORDS.add("尼玛");
        SENSITIVE_WORDS.add("卧槽");
        SENSITIVE_WORDS.add("我靠");
        SENSITIVE_WORDS.add("靠你妈");
        SENSITIVE_WORDS.add("日你妈");
        SENSITIVE_WORDS.add("艹");
        SENSITIVE_WORDS.add("屌");
        SENSITIVE_WORDS.add("逼");
        SENSITIVE_WORDS.add("鸡巴");
        SENSITIVE_WORDS.add("牛逼");
        SENSITIVE_WORDS.add("装逼");
        SENSITIVE_WORDS.add("煞笔");
        SENSITIVE_WORDS.add("沙比");
        SENSITIVE_WORDS.add("傻比");
        SENSITIVE_WORDS.add("蠢货");
        SENSITIVE_WORDS.add("蠢猪");
        SENSITIVE_WORDS.add("猪头");
        SENSITIVE_WORDS.add("死人");
        SENSITIVE_WORDS.add("去你妈的");
        SENSITIVE_WORDS.add("滚你妈的");
    }
    
    /**
     * 构建DFA状态机
     */
    private static void buildWordMap() {
        for (String word : SENSITIVE_WORDS) {
            if (StringUtils.isEmpty(word)) {
                continue;
            }
            
            Map<Character, Object> currentMap = WORD_MAP;
            char[] chars = word.toCharArray();
            
            for (int i = 0; i < chars.length; i++) {
                char c = chars[i];
                Object tempMap = currentMap.get(c);
                
                if (tempMap != null) {
                    currentMap = (Map<Character, Object>) tempMap;
                } else {
                    Map<Character, Object> newMap = new HashMap<>();
                    currentMap.put(c, newMap);
                    currentMap = newMap;
                }
                
                if (i == chars.length - 1) {
                    currentMap.put(END_FLAG.charAt(0), END_FLAG);
                }
            }
        }
    }
    
    /**
     * 检查文本是否包含敏感词
     * 
     * @param text 待检查的文本
     * @return true-包含敏感词，false-不包含敏感词
     */
    public static boolean containsSensitiveWord(String text) {
        if (StringUtils.isEmpty(text)) {
            return false;
        }
        
        char[] chars = text.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            int length = checkSensitiveWord(chars, i);
            if (length > 0) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 过滤敏感词，用*替换
     * 
     * @param text 待过滤的文本
     * @return 过滤后的文本
     */
    public static String filterSensitiveWord(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        
        char[] chars = text.toCharArray();
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < chars.length; i++) {
            int length = checkSensitiveWord(chars, i);
            if (length > 0) {
                // 发现敏感词，用*替换
                for (int j = 0; j < length; j++) {
                    result.append(REPLACEMENT);
                }
                i += length - 1;
            } else {
                result.append(chars[i]);
            }
        }
        
        return result.toString();
    }
    
    /**
     * 检查从指定位置开始是否存在敏感词
     * 
     * @param chars 字符数组
     * @param startIndex 开始位置
     * @return 敏感词长度，0表示不存在敏感词
     */
    private static int checkSensitiveWord(char[] chars, int startIndex) {
        Map<Character, Object> currentMap = WORD_MAP;
        int wordLength = 0;
        boolean isEnd = false;
        
        for (int i = startIndex; i < chars.length; i++) {
            char c = chars[i];
            Object tempMap = currentMap.get(c);
            
            if (tempMap != null) {
                wordLength++;
                if (((Map<Character, Object>) tempMap).containsKey(END_FLAG.charAt(0))) {
                    isEnd = true;
                }
                currentMap = (Map<Character, Object>) tempMap;
            } else {
                break;
            }
        }
        
        if (!isEnd) {
            wordLength = 0;
        }
        
        return wordLength;
    }
    
    /**
     * 获取文本中的敏感词列表
     * 
     * @param text 待检查的文本
     * @return 敏感词列表
     */
    public static List<String> getSensitiveWords(String text) {
        List<String> sensitiveWords = new ArrayList<>();
        if (StringUtils.isEmpty(text)) {
            return sensitiveWords;
        }
        
        char[] chars = text.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            int length = checkSensitiveWord(chars, i);
            if (length > 0) {
                String word = new String(chars, i, length);
                sensitiveWords.add(word);
                i += length - 1;
            }
        }
        
        return sensitiveWords;
    }
}

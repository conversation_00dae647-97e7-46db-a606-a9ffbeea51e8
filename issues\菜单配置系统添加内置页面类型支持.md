# 菜单配置系统添加内置页面类型支持

## 任务概述
为菜单配置系统添加 `page` 类型支持，允许管理员在后台直接配置使用内置函数的菜单项。

## 实施内容

### 1. 前端页面修改 (edit.html)
- 添加 "内置页面" 单选选项 (nav_type = "page")
- 创建内置页面选择区域，包含预定义函数下拉列表
- 更新 JavaScript 逻辑处理新的 nav_type
- 添加相应的验证逻辑

### 2. 后端控制器修改
- **WxNavController.java**: 在新增和编辑方法中添加对 page 类型的验证，确保 tap_name 字段在 page 类型时必填
- **WxDataController.java**: 修复 menuContent API，在查询和返回结果中添加 tap_name 字段

### 3. 小程序端修改
- 在 index/index.js 中添加 goServiceTel 函数
- 在 nav/index.js 中添加 goServiceTel 和 goToNewsDetail 函数
- 在菜单处理逻辑中添加对 page 类型的支持
- 确保 nav/index.js 具有完整的菜单跳转函数

### 4. 预定义函数列表
- goToOcInfo - 小区信息
- goToRepair - 物业报修
- goToPayment - 物业缴费
- goToVisitor - 邀请住户
- goToServicePhone - 服务电话页面
- goToComplaint - 投诉建议
- goServiceTel - 直拨服务电话

## 功能说明

### 类型区分
- **服务电话页面** (goToServicePhone): 跳转到服务电话页面 (/pages/serviceTel/index)
- **直拨服务电话** (goServiceTel): 从小区信息中获取服务电话并直接拨打

### 菜单处理逻辑
1. **is_default = 0**: 默认菜单，直接使用 tap_name 调用对应方法
2. **is_default = 1**: 自定义菜单，根据 nav_type 进行不同处理：
   - `text`: 跳转到内容页面
   - `pdf`: 跳转到内容页面显示PDF
   - `url`: 跳转到webview页面
   - `miniprogram`: 跳转到指定小程序
   - `page`: 调用指定的内置函数

**重要修复**: 修复了 `/api/wx/data/menuContent` API，在查询和返回结果中添加了 `tap_name` 字段，确保小程序端能正确获取到 tap_name 并调用对应的内置函数。

## 测试要点
1. 验证新类型的保存和读取
2. 确保小程序端正确识别和调用内置函数
3. 验证 infoNav 类型限制（只支持 text 和 pdf）
4. 测试各种内置函数的正确执行

## 修改文件清单
- ehome-page/src/main/resources/templates/oc/wx/config/nav/edit.html
- ehome-page/src/main/resources/templates/oc/wx/config/nav/add.html
- ehome-oc/src/main/java/com/ehome/oc/controller/wxconfig/WxNavController.java
- ehome-oc/src/main/java/com/ehome/oc/controller/wx/WxDataController.java
- miniprogram/pages/index/index.js
- miniprogram/pages/nav/index.js

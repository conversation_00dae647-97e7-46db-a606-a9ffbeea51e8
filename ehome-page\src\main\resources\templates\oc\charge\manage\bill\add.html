<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('手动创建账单')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-bill-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">资产类型：</label>
                <div class="col-sm-8">
                    <select name="assetType" class="form-control" required>
                        <option value="">请选择资产类型</option>
                        <option value="1">房屋</option>
                        <option value="2">车位</option>
                        <option value="3">商铺</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">资产ID：</label>
                <div class="col-sm-8">
                    <input name="assetId" class="form-control" type="number" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">资产名称：</label>
                <div class="col-sm-8">
                    <input name="assetName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">收费标准ID：</label>
                <div class="col-sm-8">
                    <input name="chargeStandardId" class="form-control" type="number" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">收费项目名称：</label>
                <div class="col-sm-8">
                    <input name="chargeItemName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">账单金额(元)：</label>
                <div class="col-sm-8">
                    <input name="amount" class="form-control" type="number" step="0.01" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">账期开始时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="startTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">账期结束时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="endTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">账期月份：</label>
                <div class="col-sm-8">
                    <input name="inMonth" class="form-control" type="text" placeholder="YYYY-MM" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/charge/manage/bill";
        $("#form-bill-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                // 转换时间格式
                var startTime = $("input[name='startTime']").val();
                var endTime = $("input[name='endTime']").val();
                
                if (startTime) {
                    $("input[name='startTime']").val(new Date(startTime).getTime() / 1000);
                }
                if (endTime) {
                    $("input[name='endTime']").val(new Date(endTime).getTime() / 1000);
                }
                
                $.operate.save(prefix, $('#form-bill-add').serialize());
            }
        }

        $("input[name='startTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            language: 'zh-CN',
            autoclose: true
        });

        $("input[name='endTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            language: 'zh-CN',
            autoclose: true
        });
    </script>
</body>
</html>

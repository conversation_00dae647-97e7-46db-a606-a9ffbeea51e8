<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增收费标准')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-fee-add">
            <!-- 收费类型字段隐藏 -->
            <input type="hidden" name="base.charge_type" th:value="${chargeType}" id="charge_type_code">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">收费名称：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" name="base.name" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">收费分类：</label>
                <div class="col-sm-8">
                    <select name="base.category_id" class="form-control" required id="category_select">
                        <option value="">请选择收费分类</option>
                    </select>
                </div>
            </div>
            <div class="form-group charge-field periodic-field meter-field temporary-field">
                <label class="col-sm-3 control-label is-required">计费精度：</label>
                <div class="col-sm-4">
                    <select name="base.unit" class="form-control" required id="precision_type_select">
                    </select>
                </div>
                <div class="col-sm-4">
                    <select name="base.round_type" class="form-control" id="rounding_type_select">
                    </select>
                </div>
            </div>
            <div class="form-group charge-field periodic-field meter-field temporary-field">
                <div class="col-sm-offset-3 col-sm-9">
                    <small class="form-text text-muted" id="rounding_description"></small>
                </div>
            </div>
            <!-- 周期性收费字段 -->
            <div class="form-group charge-field periodic-field">
                <label class="col-sm-3 control-label is-required">收费方式：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-6">
                            <input class="form-control" type="text" value="按月生成账单（推荐）" readonly>
                            <input type="hidden" name="base.period_type" value="101">
                        </div>
                        <div class="col-sm-6">
                            <div class="input-group">
                                <div class="input-group-addon">不足一月：</div>
                                <select name="base.incomplete_month_handling" class="form-control" required>
                                    <option value="1" selected>按天收费</option>
                                    <option value="2">按月收费</option>
                                    <option value="3">不收费</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 周期性收费和临时性收费的金额计算方式 -->
            <div class="form-group charge-field periodic-field temporary-field">
                <label class="col-sm-3 control-label is-required">金额计算方式：</label>
                <div class="col-sm-8">
                    <select class="form-control" required id="calculation_method_select" data-name="base.count_type">
                        <option value="">请选择计算方式</option>
                        <option value="200">固定金额</option>
                        <option value="100">单价*计量方式</option>
                    </select>
                </div>
            </div>

            <!-- 走表收费的金额计算方式 -->
            <div class="form-group charge-field meter-field">
                <label class="col-sm-3 control-label is-required">金额计算方式：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-6">
                            <select class="form-control" data-name="base.count_type" required>
                                <option value="100" selected>单价*使用量</option>
                            </select>
                        </div>
                        <div class="col-sm-6">
                            <div class="input-group">
                                <div class="input-group-addon">单价：</div>
                                <input class="form-control" type="number" id="meter_price" step="0.01" placeholder="请输入单价" required data-name="countInfo.meter_price">
                                <span class="input-group-addon">元/度</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 固定金额子选项 -->
            <div class="form-group" id="fixed_amount_group" style="display:none;">
                <label class="col-sm-3 control-label"></label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-addon">固定金额：</span>
                        <input class="form-control" type="number" name="base.fixed_amount" step="0.01" placeholder="请输入固定金额">
                        <span class="input-group-addon">元</span>
                    </div>
                </div>
            </div>

            <!-- 单价*计量方式子选项（周期性收费和临时性收费） -->
            <div id="unit_price_group" style="display:none;">
                <div class="form-group">
                    <label class="col-sm-3 control-label"></label>
                    <div class="col-sm-8">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <span class="input-group-addon">计量方式：</span>
                                    <select name="countInfo.area_type" class="form-control" required>
                                        <option value="1" selected>房屋建筑面积</option>
                                        <option value="2">房屋使用面积</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <span class="input-group-addon">单价：</span>
                                    <input class="form-control" type="number" name="countInfo.price" step="0.01" placeholder="请输入单价">
                                    <span class="input-group-addon">元/月</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group charge-field periodic-field">
                <label class="col-sm-3 control-label is-required">费用账单生成日期：</label>
                <div class="col-sm-8">
                    <select name="base.accounting_period_day" class="form-control" required>
                        <option value="">请选择</option>
                        <option value="1">每月1号</option>
                        <option value="5">每月5号</option>
                        <option value="10">每月10号</option>
                        <option value="15">每月15号</option>
                        <option value="20">每月20号</option>
                        <option value="25">每月25号</option>
                        <option value="30">每月30号</option>
                    </select>
                </div>
            </div>

            <!-- 违约金配置区域 -->
            <div class="form-group charge-field periodic-field meter-field">
                <label class="col-sm-3 control-label is-required">违约金：</label>
                <div class="col-sm-8">
                    <select name="base.late_money_type" class="form-control" required id="penalty_handling_select">
                        <option value="0" selected>不计算违约金</option>
                        <option value="1">计算违约金</option>
                    </select>
                </div>
            </div>

            <!-- 违约金详细配置（当选择计算违约金时显示） -->
            <div id="penalty_config_group" style="display:none;">
                <div class="form-group">
                    <label class="col-sm-3 control-label">违约金计算方式：</label>
                    <div class="col-sm-8">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <span class="input-group-addon">生成账单后</span>
                                    <input class="form-control" type="number" name="base.late_money_after_day" placeholder="请输入天数" min="1">
                                    <span class="input-group-addon">天开始计算</span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <span class="input-group-addon">违约金收取比例（每天）</span>
                                    <input class="form-control" type="number" name="base.late_money_proportion" step="0.01" placeholder="0" min="0">
                                    <span class="input-group-addon">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group charge-field periodic-field meter-field temporary-field">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea class="form-control" name="base.remark" placeholder="请输入备注"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script type="text/javascript">
        var prefix = ctx + "oc/charge/setting/standard";

        // 使用配置化的选项数据
        const CHARGE_OPTIONS = {
            precision: [
                { id: 1, name: "元（不保留小数）" },
                { id: 2, name: "角（保留一位小数）" },
                { id: 3, name: "分（保留两位小数）" }
            ],
            rounding: [
                { id: 0, name: "四舍五入", expand: "在账单金额中，X下一位的数值小于5会进行舍弃，大于等于5则会进一位" },
                { id: 1, name: "抹零", expand: "在账单金额中，X下一位数值不管小于5还是大于5都直接会进行舍弃" },
                { id: 2, name: "向上取整", expand: "在账单金额中，X下一位数值不管小于5还是大于5都直接会进一位" }
            ],
            category: [
                { id: 1, name: "物业管理费" },
                { id: 2, name: "水费" },
                { id: 3, name: "电费" },
                { id: 4, name: "燃气费" },
                { id: 5, name: "停车费" },
                { id: 6, name: "维修基金" },
            { id: 7, name: "其他费用" }
            ]
        };

        // 初始化选项
        function initOptions() {
            // 初始化计费精度选项，默认选择第一个
            var precisionSelect = $('#precision_type_select');
            $.each(CHARGE_OPTIONS.precision, function(index, item) {
                var selected = (index === 0) ? 'selected' : '';
                precisionSelect.append('<option value="' + item.id + '" ' + selected + '>' + item.name + '</option>');
            });

            // 初始化取整方式选项，默认选择"四舍五入"
            var roundingSelect = $('#rounding_type_select');
            $.each(CHARGE_OPTIONS.rounding, function(index, item) {
                var selected = (item.name === '四舍五入') ? 'selected' : '';
                roundingSelect.append('<option value="' + item.id + '" data-expand="' + item.expand + '" ' + selected + '>' + item.name + '</option>');
            });

            // 初始化收费分类选项
            var categorySelect = $('#category_select');
            $.each(CHARGE_OPTIONS.category, function(index, item) {
                categorySelect.append('<option value="' + item.id + '">' + item.name + '</option>');
            });

            // 默认显示四舍五入的说明
            var defaultOption = roundingSelect.find('option[value="0"]');
            var defaultExpand = defaultOption.data('expand');
            $('#rounding_description').text(defaultExpand || '');
        }

        // 取整方式变化时显示说明
        $('#rounding_type_select').change(function() {
            var selectedOption = $(this).find('option:selected');
            var expand = selectedOption.data('expand');
            $('#rounding_description').text(expand || '');
        });

        // 违约金处理方式变化时显示/隐藏详细配置
        $('#penalty_handling_select').change(function() {
            if ($(this).val() === '1') {
                $('#penalty_config_group').show();
            } else {
                $('#penalty_config_group').hide();
            }
        });

        // 页面加载时初始化选项
        $(document).ready(function() {
            initOptions();
            initFieldsByChargeType();
        });

        // 根据收费类型初始化字段显示
        function initFieldsByChargeType() {
            var chargeTypeCode = $('#charge_type_code').val();

            // 先隐藏所有字段
            $('.charge-field').hide();

            // 隐藏所有子选项组
            $('#fixed_amount_group').hide();
            $('#unit_price_group').hide();

            // 清除所有name属性
            $('[data-name]').removeAttr('name');

            if (chargeTypeCode === '1') {
                // 显示周期性收费字段
                $('.periodic-field').show();
            } else if (chargeTypeCode === '2') {
                // 显示走表收费字段
                $('.meter-field').show();
                // 走表收费不需要显示unit_price_group（计量方式选择）
            } else if (chargeTypeCode === '3') {
                // 显示临时性收费字段
                $('.temporary-field').show();
            }

            // 显示通用字段（收费名称、计费精度、金额计算方式、备注）
            $('.charge-field.periodic-field.meter-field.temporary-field').show();

            // 为显示的字段设置name属性
            $('.charge-field:visible [data-name]').each(function() {
                $(this).attr('name', $(this).data('name'));
            });
        }

        $("#form-fee-add").validate({
            focusCleanup: true
        });

        // 收费方式变化时显示/隐藏不足一月选项
        $('select[name="base.period_type"]').change(function() {
            if ($(this).val() === '101') {
                $('#incomplete_month_group').show();
            } else {
                $('#incomplete_month_group').hide();
            }
        });

        // 金额计算方式变化时显示/隐藏相关字段（仅对周期性收费和临时性收费）
        $('#calculation_method_select').change(function() {
            var chargeTypeCode = $('#charge_type_code').val();

            // 只对周期性收费和临时性收费处理子选项显示
            if (chargeTypeCode === '1' || chargeTypeCode === '3') {
                if ($(this).val() === '200') {
                    $('#fixed_amount_group').show();
                    $('#unit_price_group').hide();
                } else if ($(this).val() === '100') {
                    $('#fixed_amount_group').hide();
                    $('#unit_price_group').show();
                } else {
                    $('#fixed_amount_group').hide();
                    $('#unit_price_group').hide();
                }
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                var data =  form.getJSONObject('#form-fee-add');
                console.log(JSON.stringify(data));
                if(data['base.fixed_amount']==''){
                    data['base.fixed_amount'] = '0';
                }
                $.operate.save(prefix + "/add", data);
            }
        }
    </script>
</body>
</html>
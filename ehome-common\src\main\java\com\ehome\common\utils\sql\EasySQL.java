package com.ehome.common.utils.sql;


import com.ehome.common.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 生成SQL类
 *
 */
public class EasySQL {

	private StringBuffer buf = new StringBuffer();
	
	private List<Object> params = new ArrayList<Object>();
	

	public EasySQL(){
	}
	public EasySQL(String sql){
		buf.append(sql);
	}
	

	
	/**
	 * append一个sql
	 * @param paramValue  参数值，如果这个值为空，则不append
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * 默认跳过参数为空的
	 */
	public EasySQL append(Object paramValue,String sql){
		return append(paramValue,sql,true);
	}
	/**
	 * append一个sql
	 * @param paramValue  参数值
	 * @param sql  sql，可带?的SQL，程序自动会根据?的个数来生成参数个数
	 * @param skipEmpty 是否跳过参数为空的sql
	 */
	public EasySQL append(Object paramValue,String sql,boolean skipEmpty){
		if(skipEmpty){
			if(paramValue!=null&&StringUtils.isNotBlank(StringUtils.trimToEmpty(paramValue.toString()))){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add(paramValue);
				}
				buf.append(" ").append(sql);
			}
		}else{
			params.add(paramValue);
			buf.append(" ").append(sql);
		}
		return this;
	}
	/**
	 * 
	 * @param paramValue 参数
	 * @param sql SQL语句
	 * @param defaultVal参数为空的时候取默认值
	 * @return
	 */
	public EasySQL append(Object paramValue,String sql,String defaultVal){
		if(paramValue == null||StringUtils.isBlank(StringUtils.trimToEmpty(paramValue.toString()))){
			paramValue = defaultVal;
		}
		return append(paramValue,sql,false);
	}
	/**
	 * 添加排序
	 * @param sortName 需要排序的字段
	 * @param sortType 排序类型 ASC DESC
	 * @return
	 */
	public EasySQL appendSort(String sortName,String sortType){
		return appendSort(sortName,sortType,null);
	}
	public EasySQL appendSort(String sortName,String sortType,String defaultSort){
		if(StringUtils.isBlank(defaultSort)&&StringUtils.isNotBlank(sortName)){
			buf.append(" ORDER BY ").append(sortName).append(" ").append(sortType);
		}else if(StringUtils.isNotBlank(defaultSort)&&StringUtils.isNotBlank(sortName)){
			buf.append(" ORDER BY ").append(sortName).append(" ").append(sortType).append(",").append(defaultSort);
		}else if(StringUtils.isNotBlank(defaultSort)&&StringUtils.isBlank(sortName)){
			buf.append(" ORDER BY ").append(defaultSort);
		}
		return this;
	}
	
	/**
	 * append sql 不带参数,可用于在sql最后加入order by 等参数
	 * @param sql
	 * @return
	 */
	public EasySQL  append(String sql){
		if(sql!=null) {
			buf.append(" ").append(sql);
		}
		return this;
	}
	public EasySQL  appendIn(int[] paramValues,String sql){
		if(paramValues!=null){
			String[] strs=new String[paramValues.length];
			for(int i=0;i<paramValues.length;i++){
				strs[i]=paramValues[i]+"";
			}
			return appendIn(strs, sql);
		}
		return this;
	}
	/**
	 * in sql拼接 注意sql不需要添加in语句 
	 * examle : append(new String[]{"1","2"},"and state")
	 * @param paramValues
	 * @param sql
	 * @return
	 */
	public EasySQL  appendIn(String[] paramValues,String sql){
		if(paramValues != null){
				int count = paramValues.length;
				if(count==0)return this;
				if(count==1){
					buf.append(" ").append(sql).append(" = ?");
					params.add(paramValues[0]);
					return this;
				}
				StringBuffer buffer=new StringBuffer();
				buffer.append(" ").append(sql).append(" in (");
				for(int i = 0 ;i <count ;i++){
					buffer.append("?,");
				}
				buffer.delete(buffer.length()-1, buffer.length());
				buffer.append(")");
				for(int i = 0 ;i <count ;i++){
					params.add(paramValues[i]);
				}
				buf.append(" ").append(buffer);
	    }
		return this;
	}
	
	/**
	 * append 左like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.isNotBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add("%"+paramValue);
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	
	/**
	 * append  右like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendRLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.isNotBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add(paramValue+"%");
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	
	
	/**
	 * append like
	 * @param paramValue
	 * @param sql
	 * @return
	 */
	public EasySQL  appendLike(Object paramValue,String sql){
		if(paramValue != null){
			if(StringUtils.isNotBlank(paramValue.toString())){
				int count = countParam(sql,"?");
				for(int i = 0 ;i <count ;i++){
					params.add("%"+paramValue+"%");
				}
				buf.append(" ").append(sql);
			}
		}
		return this;
	}
	/**
	 * 计算有多个少参数
	 * @param str
	 * @param substr
	 * @return
	 */
	private int countParam(String str, String substr) {
		int index = 0;
		int count = 0;
		int fromindex = 0;
		while ((index = str.indexOf(substr, fromindex)) != -1) {
			fromindex = index + substr.length();
			count++;
		}
		return count;
	}
	
	/**
	 * 获得执行的SQL
	 * @return
	 */
	public String getSQL(){
		return buf.toString();
	}
	
	/**
	 * 获得执行的参数
	 * @return
	 */
	public Object[] getParams(){
		return params.toArray();
	}
	
	public StringBuffer getSqlBuf() {
		return buf;
	}
	public List<Object> getParamsList() {
		return params;
	}
	
	public void addParams(Object param){
		params.add(param);
	}

	public  String toFullSql() {
		String sql = getSQL();
		Object[] params = getParams();
		// 参数校验
		if (sql == null || sql.isEmpty()) {
			return "";
		}
		if (params == null || params.length == 0) {
			return sql;
		}

		StringBuilder result = new StringBuilder(sql.length() + 20);
		int paramIndex = 0;

		try {
			for (int i = 0; i < sql.length(); i++) {
				char c = sql.charAt(i);
				if (c == '?' && paramIndex < params.length) {
					// 处理参数
					Object param = params[paramIndex++];
					if (param == null) {
						result.append("NULL");
					} else if (param instanceof String || param instanceof java.sql.Date
							|| param instanceof java.sql.Timestamp || param instanceof java.util.Date) {
						result.append('\'').append(param).append('\'');
					} else {
						result.append(param);
					}
				} else {
					result.append(c);
				}
			}
			return result.toString();
		} catch (Exception e) {
			// 发生异常时返回原始SQL和参数
			return String.format("Original SQL: %s, Params: %s", sql, paramToString(params));
		}
	}

	public  String paramToString(Object[] params){
		if(params == null) return "{}";
		StringBuilder buf = new StringBuilder(params.length * 16);
		buf.append("{");
		for (int i = 0; i < params.length; i++) {
			if(i == params.length -1)
				buf.append(params[i]);
			else
				buf.append(params[i]).append(",");
		}
		buf.append("}");
		return buf.toString();
	}

}

# 二级菜单 tree-select 实现

## 实现概述
当二级菜单大于2个时，右侧内容改为使用 Vant 的 tree-select 组件，点击二级菜单再显示对应的内容。

## 已完成的修改

### 1. 组件引入 (miniprogram/pages/service/index.json)
- 添加了 `van-tree-select` 组件引用

### 2. 数据结构扩展 (miniprogram/pages/service/index.js)
- 添加了 tree-select 相关数据属性：
  - `useTreeSelect`: 是否使用 tree-select
  - `treeSelectNav`: tree-select 左侧导航数据
  - `treeSelectItems`: tree-select 右侧内容数据
  - `treeSelectActiveId`: 当前选中的导航ID
  - `treeSelectMainActiveIndex`: 当前选中的内容索引

### 3. 核心方法实现
- `initTabData(category)`: 修改为根据子菜单数量（>2个）决定使用哪种显示方式
- `convertToTreeSelectData(category)`: 转换数据为 tree-select 格式
- `convertContentToTreeSelectItems(content)`: 将内容数组转换为 tree-select items 格式
- `onTreeSelectNavClick(e)`: 处理左侧导航点击
- `onTreeSelectItemClick(e)`: 处理右侧内容点击

### 4. 模板修改 (miniprogram/pages/service/index.wxml)
- 在二级菜单区域添加条件判断
- 子菜单 ≤ 2个时保持现有 van-tabs 逻辑
- 子菜单 > 2个时使用 van-tree-select 组件

### 5. 样式调整 (miniprogram/pages/service/index.wxss)
- 添加 `.tree-select-container` 样式
- 覆盖 van-tree-select 默认样式确保布局协调

## 功能逻辑
1. 当分类有子菜单且子菜单数量 > 2 时，`useTreeSelect` 设为 true
2. tree-select 左侧显示子菜单列表
3. 右侧显示选中子菜单的内容
4. 支持 PDF 和文本两种内容类型的点击处理
5. 保持与原有 tabs 模式相同的用户体验

## 预期效果
- 当二级菜单 ≤ 2个：保持现有 tabs 显示方式
- 当二级菜单 > 2个：使用 tree-select 显示，提供更好的用户体验
- 所有现有功能保持不变（PDF预览、文本查看等）
- 界面风格统一，用户体验流畅

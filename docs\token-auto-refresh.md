# Token自动刷新机制

## 概述

为了提升用户体验，避免用户频繁重新登录，系统实现了Token自动刷新机制。当Token即将过期时，系统会自动刷新Token，对用户完全透明。

## 实现原理

### 1. 后端实现

#### 1.1 Token过期检测
- 在`WxTokenInterceptor`拦截器中检测Token是否即将过期
- 当Token剩余有效期小于2小时时，自动触发刷新

#### 1.2 自动刷新机制
- 使用现有Token生成新Token
- 在响应头中返回新Token：`New-Token: Bearer xxx`
- 设置刷新标识：`Token-Refreshed: true`

#### 1.3 配置参数
```yaml
# application.yml 或 application-token.yml
token:
  expire-time: 86400000      # Token有效期（毫秒），默认24小时
  refresh-threshold: 7200000 # 刷新阈值（毫秒），默认2小时
  auto-refresh-enabled: true # 是否启用自动刷新
  secret: ehomeSecretKey2024!@# # JWT密钥，生产环境请使用复杂密钥
  token-prefix: "Bearer "    # Token前缀
```

#### 1.4 配置说明
- `expire-time`: Token有效期，单位毫秒
- `refresh-threshold`: 当Token剩余有效期小于此值时触发自动刷新
- `auto-refresh-enabled`: 是否启用自动刷新功能，可用于紧急关闭
- `secret`: JWT签名密钥，生产环境建议使用环境变量
- `token-prefix`: Token前缀，通常为"Bearer "

### 2. 前端实现

#### 2.1 自动检测响应头
- 在`app.js`的`request`方法中检测响应头
- 自动更新本地存储的Token
- 同步更新状态管理器

#### 2.2 Token管理工具
- `tokenManager.js`提供Token管理功能
- 支持手动刷新Token
- 防止并发刷新

## 使用方式

### 1. 后端配置

#### 1.1 启用拦截器
确保`WxTokenInterceptor`已正确配置在Spring Boot中：

```java
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new WxTokenInterceptor())
                .addPathPatterns("/api/wx/**")
                .excludePathPatterns("/api/wx/auth/login", "/api/wx/auth/loginComplete");
    }
}
```

#### 1.2 配置参数
在`application.yml`中配置Token相关参数，或者引入`application-token.yml`：

```yaml
# 方式1：直接在application.yml中配置
token:
  expire-time: 86400000      # 24小时（毫秒）
  refresh-threshold: 7200000 # 2小时（毫秒）
  auto-refresh-enabled: true
  secret: ${TOKEN_SECRET:ehomeSecretKey2024!@#}

# 方式2：引入专门的配置文件
spring:
  profiles:
    include: token
```

#### 1.3 环境变量配置（推荐生产环境）
```bash
# 设置环境变量
export TOKEN_SECRET="your-complex-secret-key-here"
export TOKEN_EXPIRE_TIME=86400000
export TOKEN_REFRESH_THRESHOLD=7200000
```

### 2. 前端使用

#### 2.1 自动刷新（推荐）
正常使用`app.request()`方法，系统会自动处理Token刷新：

```javascript
const app = getApp()
app.request({
  url: '/api/wx/data/getUserInfo',
  method: 'POST'
}).then(res => {
  // 处理响应，Token会自动刷新（如果需要）
})
```

#### 2.2 手动刷新
如果需要手动刷新Token：

```javascript
import tokenManager from '../utils/tokenManager.js'

// 手动刷新Token
try {
  const newToken = await tokenManager.refreshToken()
  console.log('Token刷新成功:', newToken)
} catch (error) {
  console.error('Token刷新失败:', error)
  // 跳转到登录页
}
```

#### 2.3 检查Token状态
```javascript
import tokenManager from '../utils/tokenManager.js'

// 检查Token是否即将过期
if (tokenManager.isTokenNearExpiry()) {
  console.log('Token即将过期，建议刷新')
}
```

## 工作流程

### 1. 正常请求流程
```
用户发起请求 → 拦截器检查Token → Token有效 → 继续处理请求
```

### 2. 自动刷新流程
```
用户发起请求 → 拦截器检查Token → Token即将过期 → 生成新Token → 
在响应头返回新Token → 前端自动更新Token → 继续处理请求
```

### 3. Token失效流程
```
用户发起请求 → 拦截器检查Token → Token无效 → 返回401 → 
前端清除登录状态 → 跳转登录页
```

## 注意事项

### 1. 安全考虑
- Token刷新只在原Token有效时进行
- 新Token继承原Token的所有用户信息
- 刷新过程中保持用户会话连续性

### 2. 性能考虑
- 防止并发刷新，同一时间只允许一个刷新请求
- 刷新阈值设置合理，避免频繁刷新
- 前端缓存Token，减少存储访问

### 3. 兼容性
- 向后兼容现有登录逻辑
- 不影响现有API接口
- 可通过配置开关控制是否启用

## 监控和日志

### 1. 后端日志
- Token刷新成功：`Token已自动刷新，用户ID: {userId}`
- Token刷新失败：`Token刷新失败，用户ID: {userId}`

### 2. 前端日志
- 自动更新：`检测到新token，自动更新`
- 手动刷新：`Token刷新成功/失败`

## 故障排除

### 1. Token无法刷新
- 检查拦截器是否正确配置
- 确认Token格式正确（Bearer前缀）
- 查看后端日志确认错误原因

### 2. 前端未更新Token
- 检查响应头是否包含新Token
- 确认前端检测逻辑是否正确
- 查看浏览器网络面板确认响应头

### 3. 频繁刷新
- 调整刷新阈值配置
- 检查系统时间是否正确
- 确认Token有效期设置合理

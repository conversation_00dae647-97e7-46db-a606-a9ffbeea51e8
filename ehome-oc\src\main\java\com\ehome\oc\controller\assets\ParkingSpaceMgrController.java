package com.ehome.oc.controller.assets;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.annotation.Log;
import com.ehome.common.core.controller.BaseController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.page.TableDataInfo;
import com.ehome.common.enums.BusinessType;
import com.ehome.common.utils.DateUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.common.utils.uuid.Seq;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping("/oc/parking")
public class ParkingSpaceMgrController extends BaseController {

    private static final String PREFIX = "oc/parking";

    @GetMapping("/mgr")
    public String mgr() {
        return PREFIX + "/list";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list() {
        JSONObject params = getParams();
        EasySQL sql = buildListQuery(params);
        Page<Record> paginate = Db.paginate(
            params.getIntValue("pageNum"),
            params.getIntValue("pageSize"),"select t1.*",
            sql.toFullSql()
        );
        return getDataTable(paginate);
    }

    @PostMapping("/record")
    @ResponseBody
    public AjaxResult record() {
        JSONObject params = getParams();
        String parkingId = params.getString("parking_id");
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }
        Record parking = Db.findFirst("select * from eh_parking_space where parking_id = ?", parkingId);
        return AjaxResult.success(null, parking.toMap());
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @GetMapping("/edit/{parkingId}")
    public String edit(@PathVariable("parkingId") String parkingId, ModelMap mmap) {
        Record parking = Db.findFirst("select * from eh_parking_space where parking_id = ?", parkingId);
        mmap.put("parking", parking.toMap());
        return PREFIX + "/edit";
    }

    @Log(title = "新增车位", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave() {
        JSONObject params = getParams();
        Record parking = new Record();
        parking.setColumns(params);
        parking.set("parking_id", Seq.getId());
        setCreateAndUpdateInfo(parking);
        Db.save("eh_parking_space", "parking_id", parking);
        return AjaxResult.success();
    }

    @Log(title = "修改车位", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave() {
        JSONObject params = getParams();
        Record parking = new Record();
        parking.setColumns(params);
        setUpdateInfo(parking);
        return toAjax(Db.update("eh_parking_space", "parking_id", parking));
    }

    @Log(title = "删除车位", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return error("参数id不能为空");
        }
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            Db.deleteById("eh_parking_space", "parking_id", id);
        }
        return success();
    }

    /**
     * 查看车位绑定的业主页面
     */
    @GetMapping("/owners/{parkingId}")
    public String owners(@PathVariable("parkingId") String parkingId, ModelMap mmap) {
        mmap.put("parkingId", parkingId);
        return PREFIX + "/owners";
    }

    /**
     * 查询车位绑定的业主列表
     */
    @PostMapping("/ownerList")
    @ResponseBody
    public TableDataInfo ownerList() {
        JSONObject params = getParams();
        String parkingId = params.getString("parkingId");
        if (StringUtils.isEmpty(parkingId)) {
            return getDataTable(new Page<>());
        }
        
        EasySQL sql = new EasySQL();
        sql.append("SELECT r.*, o.owner_name, o.mobile, o.id_card, o.gender FROM eh_parking_owner_rel r");
        sql.append("LEFT JOIN eh_owner o ON r.owner_id = o.owner_id");
        sql.append(parkingId,"WHERE r.parking_id = ?");
        
        sql.appendLike(params.getString("owner_name"), "AND o.owner_name LIKE ?");
        sql.append(params.getString("rel_type"), "AND r.rel_type = ?");
        sql.append(params.getString("check_status"), "AND r.check_status = ?");
        
        sql.append("ORDER BY r.is_default DESC, r.create_time DESC");
        
        List<Record> list = Db.find(sql.getSQL(),sql.getParams());
        return getDataList(list);
    }

    /**
     * 绑定业主
     */
    @Log(title = "绑定业主", businessType = BusinessType.INSERT)
    @PostMapping("/bindOwner")
    @ResponseBody
    public AjaxResult bindOwner() {
        JSONObject params = getParams();
        String parkingId = params.getString("parkingId");
        String ownerId = params.getString("ownerId");
        Integer relType = params.getInteger("relType");
        Integer isDefault = params.getInteger("isDefault");
        String remark = params.getString("remark");
        
        if (StringUtils.isEmpty(parkingId)) {
            return AjaxResult.error("车位ID不能为空");
        }
        if (StringUtils.isEmpty(ownerId)) {
            return AjaxResult.error("业主ID不能为空");
        }
        if (relType == null) {
            relType = 1;
        }
        if (isDefault == null) {
            isDefault = 0;
        }
        
        Record existRel = Db.findFirst(
            "SELECT * FROM eh_parking_owner_rel WHERE parking_id = ? AND owner_id = ?",
            parkingId, ownerId
        );
        
        if (existRel != null) {
            return AjaxResult.error("该业主已绑定到此车位");
        }
        
        if (isDefault == 1) {
            Db.update("UPDATE eh_parking_owner_rel SET is_default = 0 WHERE parking_id = ?", parkingId);
        }
        
        Record rel = new Record();
        rel.set("rel_id", Seq.getId());
        rel.set("parking_id", parkingId);
        rel.set("owner_id", ownerId);
        rel.set("rel_type", relType);
        rel.set("is_default", isDefault);
        rel.set("check_status", 0);
        rel.set("remark", remark);
        setCreateAndUpdateInfo(rel);
        
        boolean success = Db.save("eh_parking_owner_rel", "rel_id", rel);
        if(success) {
            // 更新车位的绑定业主数量和业主名称
            updateParkingOwnerInfo(parkingId);
        }
        
        return toAjax(success);
    }

    /**
     * 查询房屋列表
     */
    @PostMapping("/queryHouse")
    @ResponseBody
    public AjaxResult queryHouse() {
        JSONObject params = getParams();
        EasySQL sql = new EasySQL();
        sql.append("from eh_house_info where 1=1");
        sql.append(getSysUser().getCommunityId(), "and community_id = ?");
        sql.appendLike(params.getString("search"), "and room like ?");
        sql.append("order by room");

        List<Record> houses = Db.find("select house_id as value, room as text", sql.toFullSql());
        return AjaxResult.success(recordToMap(houses));
    }

    private EasySQL buildListQuery(JSONObject params) {
        EasySQL sql = new EasySQL();
        sql.append("from eh_parking_space t1");
        sql.append("where 1=1");
        sql.append(getSysUser().getCommunityId(), "and t1.community_id = ?");
        sql.appendLike(params.getString("parking_no"), "and t1.parking_no like ?");
        sql.append(params.getString("parking_type"), "and t1.parking_type = ?");
        sql.append(params.getString("parking_status"), "and t1.parking_status = ?");
        sql.append(params.getString("check_status"), "and t1.check_status = ?");
        sql.appendLike(params.getString("house_name"), "and t1.house_name like ?");

        sql.append("order by t1.create_time desc");
        return sql;
    }

    private void setCreateAndUpdateInfo(Record record) {
        String now = DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS);
        String loginName = getSysUser().getLoginName();
        record.set("community_id", getSysUser().getCommunityId());
        record.set("create_time", now);
        record.set("update_time", now);
        record.set("create_by", loginName);
        record.set("update_by", loginName);
    }

    private void setUpdateInfo(Record record) {
        record.set("update_time", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.set("update_by", getSysUser().getLoginName());
    }

    /**
     * 更新车位表中的业主信息
     */
    private void updateParkingOwnerInfo(String parkingId) {
        try {
            // 获取车位绑定的业主数量
            int ownerCount = Db.queryInt(
                "SELECT COUNT(*) FROM eh_parking_owner_rel WHERE parking_id = ?",
                parkingId
            );

            // 获取所有绑定的业主名称（使用GROUP_CONCAT）
            String ownerName = "";
            Record owner = Db.findFirst(
                "SELECT GROUP_CONCAT(o.name ORDER BY r.create_time ASC SEPARATOR ',') as names " +
                "FROM eh_parking_owner_rel r " +
                "LEFT JOIN eh_owner o ON r.owner_id = o.owner_id " +
                "WHERE r.parking_id = ?",
                parkingId
            );
            if (owner != null && owner.getStr("names") != null) {
                ownerName = owner.getStr("names");
            }

            // 更新车位表中的业主信息
            Db.update(
                "UPDATE eh_parking_space SET owner_count = ?, owner_name = ? WHERE parking_id = ?",
                ownerCount, ownerName, parkingId
            );

            logger.info("更新车位业主信息成功，车位ID: {}, 业主数量: {}, 业主名称: {}", parkingId, ownerCount, ownerName);
        } catch (Exception e) {
            logger.error("更新车位业主信息失败", e);
        }
    }
}
<!DOCTYPE html>
<html>
<head>
    <title>房屋卡片布局测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        
        /* 模拟小程序样式 */
        .house-list {
            padding: 20rpx;
            background: #fafafa;
            width: 375px; /* 模拟手机宽度 */
            margin: 0 auto;
        }

        .house-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px; /* 16rpx转换为8px */
            justify-content: space-between;
        }

        .house-card {
            width: calc(50% - 4px); /* calc(50% - 8rpx) */
            background: #fff;
            border-radius: 6px; /* 12rpx转换为6px */
            padding: 10px; /* 20rpx转换为10px */
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            border: 1px solid transparent;
            min-height: 60px; /* 120rpx转换为60px */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .house-card.arrear {
            border-color: #ff4757;
            background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
        }

        .house-card.normal {
            border-color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #fff 100%);
        }

        .house-unit {
            margin-bottom: 8px; /* 16rpx转换为8px */
        }

        .unit-text {
            font-size: 14px; /* 28rpx转换为14px */
            font-weight: 600;
            color: #333;
            display: block;
        }

        .house-amount {
            text-align: center;
            margin-top: auto;
        }

        .amount-text {
            font-size: 12px; /* 24rpx转换为12px */
            font-weight: 500;
            display: block;
            padding: 4px 8px; /* 8rpx 16rpx转换 */
            border-radius: 10px; /* 20rpx转换为10px */
        }

        .amount-text.arrear {
            color: #fff;
            background: #ff4757;
        }

        .amount-text.normal {
            color: #fff;
            background: #52c41a;
        }

        .demo-section {
            margin: 20px 0;
            padding: 20px;
            background: #fff;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>房屋卡片布局效果预览</h1>
    
    <div class="demo-section">
        <h3>修改后的布局（一排2个房屋）</h3>
        <div class="house-list">
            <div class="house-grid">
                <!-- 欠费房屋 -->
                <div class="house-card arrear">
                    <div class="house-unit">
                        <span class="unit-text">1单元/101</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text arrear">欠费:1200.00元</span>
                    </div>
                </div>
                
                <!-- 正常房屋 -->
                <div class="house-card normal">
                    <div class="house-unit">
                        <span class="unit-text">1单元/102</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text normal">正常</span>
                    </div>
                </div>
                
                <!-- 欠费房屋 -->
                <div class="house-card arrear">
                    <div class="house-unit">
                        <span class="unit-text">1单元/103</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text arrear">欠费:800.50元</span>
                    </div>
                </div>
                
                <!-- 正常房屋 -->
                <div class="house-card normal">
                    <div class="house-unit">
                        <span class="unit-text">1单元/104</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text normal">正常</span>
                    </div>
                </div>
                
                <!-- 欠费房屋 -->
                <div class="house-card arrear">
                    <div class="house-unit">
                        <span class="unit-text">1单元/201</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text arrear">欠费:2500.00元</span>
                    </div>
                </div>
                
                <!-- 正常房屋 -->
                <div class="house-card normal">
                    <div class="house-unit">
                        <span class="unit-text">1单元/202</span>
                    </div>
                    <div class="house-amount">
                        <span class="amount-text normal">正常</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>✅ 修改要点</h3>
        <ul>
            <li><strong>布局</strong>: 使用 <code>justify-content: space-between</code> 确保2列均匀分布</li>
            <li><strong>宽度</strong>: <code>width: calc(50% - 8rpx)</code> 确保一排显示2个</li>
            <li><strong>间距</strong>: <code>gap: 16rpx</code> 控制卡片间距</li>
            <li><strong>内容</strong>: 只显示"1单元/房间号"，移除多余信息</li>
            <li><strong>状态</strong>: 用背景色区分欠费/正常状态</li>
            <li><strong>高度</strong>: <code>min-height: 120rpx</code> 保证卡片高度一致</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h3>🎯 效果说明</h3>
        <p>现在的布局确保：</p>
        <ul>
            <li>✅ 一排显示2个房屋卡片</li>
            <li>✅ 只显示单元/房间号信息</li>
            <li>✅ 用颜色区分缴费状态</li>
            <li>✅ 卡片大小一致，布局整齐</li>
            <li>✅ 响应式设计，适配不同屏幕</li>
        </ul>
    </div>
</body>
</html>

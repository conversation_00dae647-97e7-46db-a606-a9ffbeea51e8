package com.ehome.jfinal.model;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Table;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class BaseModel<M extends BaseModel> extends Model<M> {

    private static final Logger logger = LoggerFactory.getLogger("sys-ds");
    private static final Logger sqlLogger = LoggerFactory.getLogger("sql-log");

    public  void setColumns(JSONObject params){
      if(params!=null){
          Table table = _getTable();
          Set<String> keys =  params.keySet();
          for (String key:keys){
              if(table.hasColumnLabel(key)){
                  this.set(key,params.get(key));
              }else{
                  logger.error("表"+table.getName()+"没有字段"+key);
              }
          }
      }
    }

    /**
     * 重写save方法，添加执行时间记录
     */
    @Override
    public boolean save() {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;

        try {
            result = super.save();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logJFinalOperation("save", executionTime, null, result, exception);
        }
    }

    /**
     * 重写update方法，添加执行时间记录
     */
    @Override
    public boolean update() {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;

        try {
            result = super.update();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logJFinalOperation("update", executionTime, null, result, exception);
        }
    }

    /**
     * 重写delete方法，添加执行时间记录
     */
    @Override
    public boolean delete() {
        long startTime = System.currentTimeMillis();
        boolean result = false;
        Throwable exception = null;

        try {
            result = super.delete();
            return result;
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = System.currentTimeMillis() - startTime;
            logJFinalOperation("delete", executionTime, null, result, exception);
        }
    }

    /**
     * 记录JFinal操作日志
     */
    private void logJFinalOperation(String operation, long executionTime, Object params, Object result, Throwable exception) {
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("[JFinal] ");
        logMessage.append("SQL执行 - ");
        logMessage.append("表: ").append(_getTable().getName()).append(", ");
        logMessage.append("操作: ").append(operation).append(", ");
        logMessage.append("执行时间: ").append(executionTime).append("ms");

        if (params != null) {
            logMessage.append(", 参数: ").append(params.toString());
        }

        if (result != null) {
            logMessage.append(", 结果: ").append(result instanceof Boolean ? (((Boolean) result) ? "成功" : "失败") : result.toString());
        }

        if (exception != null) {
            logMessage.append(", 异常: ").append(exception.getMessage());
            sqlLogger.error(logMessage.toString(), exception);
        } else {
            // 根据执行时间选择日志级别
            if (executionTime > 1000) {
                sqlLogger.warn(logMessage.toString() + " [慢SQL警告]");
            } else if (executionTime > 500) {
                sqlLogger.info(logMessage.toString() + " [性能关注]");
            } else {
                sqlLogger.debug(logMessage.toString());
            }
        }
    }
}

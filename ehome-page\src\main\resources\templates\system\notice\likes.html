<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('公告点赞记录')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <!-- 公告信息 -->
            <div class="col-sm-12" th:if="${notice}" style="padding: 0px;padding-top: 10px;">
                <div class="ibox float-e-margins" style="margin-bottom: 0px;">
                    <div class="ibox-title">
                        <h5>公告信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="row">
                            <div class="col-sm-8">
                                <h4 th:text="${notice.noticeTitle}">公告标题</h4>
                                <p class="text-muted">
                                    发布时间：<span th:text="${#dates.format(notice.createTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                                    | 发布人：<span th:text="${notice.createBy}"></span>
                                </p>
                            </div>
                            <div class="col-sm-4 text-right">
                                <div class="btn-group">
                                    <a class="btn btn-info btn-sm" onclick="goBack()">
                                        <i class="fa fa-arrow-left"></i> 返回
                                    </a>
                                    <a class="btn btn-primary btn-sm" onclick="viewNotice()">
                                        <i class="fa fa-eye"></i> 查看公告
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 点赞记录列表 -->
            <div class="col-sm-12 search-collapse">
                <form id="like-form">
                    <input type="hidden" name="noticeId" th:value="${notice.noticeId}" />
                    <div class="select-list">
                        <ul>
                            <li>
                                住户名称：<input type="text" name="userName"/>
                            </li>
                            <li>
                                来源：<select name="userType">
                                    <option value="">所有</option>
                                    <option value="wx_user">微信用户</option>
                                    <option value="sys_user">系统用户</option>
                                </select>
                            </li>
                            <li>
                                点赞状态：<select name="status">
                                    <option value="">所有</option>
                                    <option value="0">有效</option>
                                    <option value="1">已取消</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-default" onclick="refreshTable()">
                    <i class="fa fa-refresh"></i> 刷新
                </a>
                <a class="btn btn-info" onclick="exportData()">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/notice";
        var noticeId = [[${notice.noticeId}]];

        $(function() {
            var options = {
                url: prefix + "/likes/list",
                modalName: "点赞记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'likeId',
                    title: '序号'
                },
                {
                    field: 'userName',
                    title: '点赞住户',
                    formatter: function(value, row, index) {
                        var badge = row.userType === 'sys_user' ? 
                            '<span class="badge badge-danger">管理员</span>' : 
                            '<span class="badge badge-success">用户</span>';
                        return value + ' ' + badge;
                    }
                },
                {
                    field: 'houseName',
                    title: '房屋信息',
                    formatter: function(value, row, index) {
                        return value || '未绑定';
                    }
                },
                {
                    field: 'status',
                    title: '点赞状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '0') {
                            return '<span class="badge badge-success">有效</span>';
                        } else {
                            return '<span class="badge badge-warning">已取消</span>';
                        }
                    }
                },
                {
                    field: 'likeTime',
                    title: '点赞时间',
                    formatter: function(value, row, index) {
                        if (!value) return '';
                        var date = new Date(value);
                        return date.getFullYear() + '-' +
                               String(date.getMonth() + 1).padStart(2, '0') + '-' +
                               String(date.getDate()).padStart(2, '0') + ' ' +
                               String(date.getHours()).padStart(2, '0') + ':' +
                               String(date.getMinutes()).padStart(2, '0') + ':' +
                               String(date.getSeconds()).padStart(2, '0');
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewLike(\'' + row.likeId + '\')"><i class="fa fa-eye"></i>查看</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        // 查看点赞详情
        function viewLike(likeId) {
            // 从表格中获取点赞数据
            var table = $("#bootstrap-table");
            var data = table.bootstrapTable('getData');
            var like = null;

            for (var i = 0; i < data.length; i++) {
                if (data[i].likeId == likeId) {
                    like = data[i];
                    break;
                }
            }

            if (!like) {
                $.modal.msgError("点赞数据不存在");
                return;
            }

            var statusText = like.status === '0' ? 
                '<span class="badge badge-success">有效</span>' : 
                '<span class="badge badge-warning">已取消</span>';

            var userTypeText = like.userType === 'sys_user' ?
                '<span class="badge badge-danger">管理员</span>' :
                '<span class="badge badge-success">用户</span>';

            // 格式化日期
            function formatDate(dateStr) {
                if (!dateStr) return '';
                var date = new Date(dateStr);
                return date.getFullYear() + '-' +
                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                       String(date.getDate()).padStart(2, '0') + ' ' +
                       String(date.getHours()).padStart(2, '0') + ':' +
                       String(date.getMinutes()).padStart(2, '0') + ':' +
                       String(date.getSeconds()).padStart(2, '0');
            }

            var content =
                '<div class="row">' +
                    '<div class="col-sm-6">' +
                        '<p><strong>点赞用户：</strong>' + like.userName + ' ' + userTypeText + '</p>' +
                        '<p><strong>房屋信息：</strong>' + (like.houseName || '未绑定') + '</p>' +
                        '<p><strong>点赞状态：</strong>' + statusText + '</p>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                        '<p><strong>点赞时间：</strong>' + formatDate(like.likeTime) + '</p>' +
                        '<p><strong>用户类型：</strong>' + userTypeText + '</p>' +
                    '</div>' +
                '</div>';

            $.modal.open("点赞详情", content);
        }

        // 查看公告详情
        function viewNotice() {
            if (!noticeId) {
                $.modal.msgError("公告ID无效");
                return;
            }
            var url = ctx + "system/notice/view/" + noticeId;
            $.modal.openTab("公告详情", url);
        }

        // 返回
        function goBack() {
            window.history.back();
        }

        // 刷新表格
        function refreshTable() {
            $("#bootstrap-table").bootstrapTable('refresh');
        }

        // 导出数据
        function exportData() {
            $.modal.confirm("确定要导出该公告的点赞记录数据吗？", function() {
                $.post(prefix + "/likes/export", $("#like-form").serialize(), function(result) {
                    if (result.code == 0) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else {
                        $.modal.msgError(result.msg);
                    }
                });
            });
        }
    </script>
</body>
</html>

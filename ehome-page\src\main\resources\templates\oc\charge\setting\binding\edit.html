<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改收费绑定')" />
    <th:block th:include="include :: layout-latest-css" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-binding-edit">
        <input name="id" th:value="${binding.id}" type="hidden">
        
        <div class="form-group">
            <label class="col-sm-3 control-label">资产类型：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" th:value="${@dict.getLabel('asset_type', binding.asset_type)}" readonly>
            </div>
        </div>
        
        <div class="form-group">
            <label class="col-sm-3 control-label">资产名称：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" th:value="${binding.asset_name}" readonly>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">绑定收费标准：</label>
            <div class="col-sm-8">
                <select name="chargeStandardId" class="form-control" required>
                    <option value="">请选择收费标准</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label is-required">收费开始日期：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="startTimeStr" class="form-control time-input" placeholder="yyyy-MM-dd" type="text" required data-callback="checkStartDate">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 从选择的日期开始，生成每个月的账单，且未来每个月也会定期生成账单</span>

                <!-- 自然月账单选项 -->
                <div id="naturalPeriodOption" style="margin-top: 10px;">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" id="naturalPeriodCheck" name="naturalPeriod" value="1" th:checked="${binding.natural_period == 1}"> 按照自然月生成账单
                            <i class="fa fa-question-circle" style="color: #999; cursor: pointer; margin-left: 5px;" onmouseover="showNaturalPeriodTip(this)" onmouseout="layer.closeAll('tips')"></i>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">结束时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="endTimeStr" class="form-control time-input" placeholder="yyyy-MM-dd，留空表示无结束时间" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="noEndTime" onchange="toggleEndTime()"> 无结束时间
                    </label>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">收费周期：</label>
            <div class="col-sm-8">
                <select name="periodNum" class="form-control">
                    <option value="1">1个月</option>
                    <option value="2">2个月</option>
                    <option value="3">3个月</option>
                    <option value="4">4个月</option>
                    <option value="5">5个月</option>
                    <option value="6">6个月</option>
                    <option value="7">7个月</option>
                    <option value="8">8个月</option>
                    <option value="9">9个月</option>
                    <option value="10">10个月</option>
                    <option value="11">11个月</option>
                    <option value="12">12个月</option>
                </select>
                <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 每X个月作为一个周期进行收费</span>
            </div>
        </div>




    </form>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: layout-latest-js" />
<script th:inline="javascript">
    var prefix = ctx + "oc/charge/setting/binding";
    var bindingData = [[${binding}]];

    $("#form-binding-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            // 临时启用被禁用的naturalPeriodCheck以确保其值能被提交
            var naturalPeriodCheck = $("#naturalPeriodCheck");
            var wasDisabled = naturalPeriodCheck.prop('disabled');
            if (wasDisabled && naturalPeriodCheck.prop('checked')) {
                naturalPeriodCheck.prop('disabled', false);
            }

            var formData = $("#form-binding-edit").serializeArray();
            var data = {};
            $.each(formData, function(i, field) {
                data[field.name] = field.value;
            });

            // 恢复naturalPeriodCheck的禁用状态
            if (wasDisabled) {
                naturalPeriodCheck.prop('disabled', true);
            }

            // 处理时间转换
            if (data.startTimeStr) {
                data.startTime = new Date(data.startTimeStr).getTime() / 1000;
            }
            if (data.endTimeStr && !$("#noEndTime").is(":checked")) {
                data.endTime = new Date(data.endTimeStr).getTime() / 1000;
            } else {
                data.endTime = 0;
            }

            $.operate.save(prefix+"/edit", data);
        }
    }

    function toggleEndTime() {
        var noEndTime = $("#noEndTime").is(":checked");
        $("input[name='endTimeStr']").prop("disabled", noEndTime);
        if (noEndTime) {
            $("input[name='endTimeStr']").val("");
        }
    }

    $(function() {
        $("#form-binding-edit").renderForm({url: prefix + "/record", data: {id: bindingData.id}},function(result) {
            var data = result.data;
            $("[name='periodNum']").val(data.period_num);
        });
        // 加载收费标准下拉选项
        $.post(prefix + "/getChargeStandards", {}, function(result) {
            if (result.code == 0) {
                var select = $("select[name='chargeStandardId']");
                select.empty();
                select.append('<option value="">请选择收费标准</option>');
                $.each(result.data, function(index, item) {
                    var selected = item.id == bindingData.charge_standard_id ? 'selected' : '';
                    select.append('<option value="' + item.id + '" ' + selected + '>' + item.name + '</option>');
                });
            }
        });

        // 初始化时间显示
        if (bindingData.start_time && bindingData.start_time > 0) {
            var startDate = new Date(bindingData.start_time * 1000);
            $("input[name='startTimeStr']").val(formatDate(startDate));
        }

        if (bindingData.end_time && bindingData.end_time > 0) {
            var endDate = new Date(bindingData.end_time * 1000);
            $("input[name='endTimeStr']").val(formatDate(endDate));
        } else {
            $("#noEndTime").prop("checked", true);
            toggleEndTime();
        }

        // 页面加载时检查开始日期
        checkStartDate();
    });

    // 检查开始日期
    function checkStartDate() {
        var startDate = $("input[name='startTimeStr']").val();
        if (startDate) {
            var naturalPeriodCheck = $("#naturalPeriodCheck");

            // 检查日期是否以01结尾（即1号）
            if (startDate.endsWith('-01')) {
                // 如果是1号，自动勾选并禁用
                naturalPeriodCheck.prop('checked', true);
                naturalPeriodCheck.prop('disabled', true);
            } else {
                // 如果不是1号，启用选择
                naturalPeriodCheck.prop('disabled', false);
            }
        }
    }

    function formatDate(date) {
        var year = date.getFullYear();
        var month = (date.getMonth() + 1).toString().padStart(2, '0');
        var day = date.getDate().toString().padStart(2, '0');
        return year + '-' + month + '-' + day;
    }

    // 显示自然月账单说明
    function showNaturalPeriodTip(element) {
        var tipContent = '<div style="text-align: left; line-height: 1.4; font-size: 12px;">';
        tipContent += '<strong>以开始时间2023.03.08为例：</strong><br><br>';
        tipContent += '<table style="width: 100%; border-collapse: collapse; font-size: 11px;">';
        tipContent += '<tr>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">账单类型</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第1期</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第2期</th>';
        tipContent += '<th style="border: 1px solid #ddd; padding: 6px; text-align: center;">第3期</th>';
        tipContent += '</tr>';
        tipContent += '<tr>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; font-weight: bold;">按自然月生成账单</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">03.08~03.31</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">04.01~04.30</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">05.01~05.31</td>';
        tipContent += '</tr>';
        tipContent += '<tr>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; font-weight: bold;">按非自然月生成账单</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">03.08~04.07</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">04.08~05.07</td>';
        tipContent += '<td style="border: 1px solid #ddd; padding: 6px; text-align: center;">05.08~06.07</td>';
        tipContent += '</tr>';
        tipContent += '</table>';
        tipContent += '</div>';

        layer.tips(tipContent, element, {
            tips: [1, '#3595CC'],
            time: 0,
            area: ['400px', 'auto']
        });
    }
</script>
</body>
</html>

package com.ehome.common.config;

import com.ehome.common.utils.ServletUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 服务相关配置
 * 
 * <AUTHOR>
 *
 */
@Component
public class ServerConfig
{


    /**
     * 获取完整的请求路径，包括：域名，端口，上下文访问路径
     * 
     * @return 服务地址
     */
    public String getUrl()
    {
        HttpServletRequest request = ServletUtils.getRequest();
        return getDomain(request);
    }

    public static String getDomain(HttpServletRequest request)
    {
        StringBuffer url = request.getRequestURL();
        String contextPath = request.getServletContext().getContextPath();

        // 处理反向代理的协议问题
        String scheme = getActualScheme(request);
        String originalScheme = url.substring(0, url.indexOf("://"));

        // 如果协议不匹配，替换为正确的协议
        if (!scheme.equals(originalScheme)) {
            url.replace(0, originalScheme.length(), scheme);
        }

        return url.delete(url.length() - request.getRequestURI().length(), url.length()).append(contextPath).toString();
    }

    /**
     * 获取真实的请求协议，考虑反向代理的情况
     *
     * @param request 请求对象
     * @return 真实的协议 (http 或 https)
     */
    private static String getActualScheme(HttpServletRequest request)
    {
        // 首先检查 X-Forwarded-Proto 头（标准的代理协议头）
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        if (forwardedProto != null && !forwardedProto.isEmpty()) {
            return forwardedProto.toLowerCase();
        }

        // 检查 X-Forwarded-Ssl 头
        String forwardedSsl = request.getHeader("X-Forwarded-Ssl");
        if ("on".equalsIgnoreCase(forwardedSsl)) {
            return "https";
        }

        // 检查 X-Url-Scheme 头
        String urlScheme = request.getHeader("X-Url-Scheme");
        if (urlScheme != null && !urlScheme.isEmpty()) {
            return urlScheme.toLowerCase();
        }

        // 如果没有代理头信息，使用原始的协议
        return request.getScheme();
    }
}

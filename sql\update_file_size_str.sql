-- 更新文件大小格式化字段的SQL脚本
-- 这个脚本需要在Java应用中执行，因为需要使用FileUtils.formatFileSize方法

-- 临时解决方案：手动更新一些常见的文件大小格式
UPDATE eh_file_info SET file_size_str = 
CASE 
    WHEN file_size >= 1099511627776 THEN CONCAT(ROUND(file_size / 1099511627776, 1), ' TB')
    WHEN file_size >= 1073741824 THEN CONCAT(ROUND(file_size / 1073741824, 1), ' GB')
    WHEN file_size >= 1048576 THEN 
        CASE 
            WHEN file_size / 1048576 > 100 THEN CONCAT(ROUND(file_size / 1048576, 0), ' MB')
            ELSE CONCAT(ROUND(file_size / 1048576, 1), ' MB')
        END
    WHEN file_size >= 1024 THEN 
        CASE 
            WHEN file_size / 1024 > 100 THEN CONCAT(ROUND(file_size / 1024, 0), ' KB')
            ELSE CONCAT(ROUND(file_size / 1024, 1), ' KB')
        END
    ELSE CONCAT(file_size, ' B')
END
WHERE file_size_str = '' OR file_size_str IS NULL;
